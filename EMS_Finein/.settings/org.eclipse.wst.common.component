<?xml version="1.0" encoding="UTF-8"?><project-modules id="moduleCoreId" project-version="1.5.0">
    <wb-module deploy-name="EMS_Finein">
        <wb-resource deploy-path="/" source-path="/WebContent" tag="defaultRootSource"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/租户管理/java"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/租户管理/resource"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/后台配置/resource"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/后台配置/java"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/物业监控/java"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/物业监控/resource"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/系统核心/java"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/系统核心/resource"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/尚格云组件/java"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/定制项目/中电仪表/java"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/报表打印/java"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/报表打印/resource"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/main/java"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/main/resources"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/test/java"/>
        <wb-resource deploy-path="/WEB-INF/classes" source-path="/src/test/resources"/>
        <dependent-module archiveName="EMS_Finein_Common.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/EMS_Finein_Common/EMS_Finein_Common">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <dependent-module archiveName="EMS_Finein_Communication.jar" deploy-path="/WEB-INF/lib" handle="module:/resource/EMS_Finein_Communication/EMS_Finein_Communication">
            <dependency-type>uses</dependency-type>
        </dependent-module>
        <property name="context-root" value="EMS_Finein"/>
        <property name="java-output-path" value="/EMS_Finein/build/classes"/>
    </wb-module>
</project-modules>
