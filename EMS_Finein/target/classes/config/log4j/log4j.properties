log4j.rootLogger=<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,RollingLogFileAppender

## class setting
log4j.logger.org.springframework=INFO
log4j.logger.org.apache.axis=INFO
log4j.logger.org.apache.cxf=INFO
log4j.logger.com.mchange.v2=INFO

log4j.appender.RollingLogFileAppender=org.apache.log4j.DailyRollingFileAppender
log4j.appender.RollingLogFileAppender.Threshold=INFO
log4j.appender.RollingLogFileAppender.ImmediateFlush=true
log4j.appender.RollingLogFileAppender.File=../logs/EMS_Finein/log.log
log4j.appender.RollingLogFileAppender.DatePattern=yyyy-MM-dd'.log'
log4j.appender.RollingLogFileAppender.Append=true
log4j.appender.RollingLogFileAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.RollingLogFileAppender.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss} %m%n

log4j.appender.ConsoleAppender=org.apache.log4j.ConsoleAppender
log4j.appender.ConsoleAppender.Threshold=INFO
log4j.appender.ConsoleAppender.ImmediateFlush=true
log4j.appender.ConsoleAppender.Target=System.out
log4j.appender.ConsoleAppender.layout=org.apache.log4j.PatternLayout
log4j.appender.ConsoleAppender.layout.ConversionPattern=%d{yyyy-MM-dd HH\:mm\:ss} %m%n