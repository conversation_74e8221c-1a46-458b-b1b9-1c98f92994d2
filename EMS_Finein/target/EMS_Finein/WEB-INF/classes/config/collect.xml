﻿<?xml version="1.0" encoding="utf-8"?>
<root>
	<LowFrequency><!-- 低频采集端口 -->
		<Server address="0.0.0.0" port="80" tcp="false" udp="false" />

		<Collect monthly="true" offset="true" upload="false"
			upload_table="uploadrecorddata" />

		<SplitRules>
			<SplitRule>
				<Condition building="1234567890" sign="100001" funcid="99999" />
				<Operation building="1234567890" sign="55555" funcid="88888"
					expression="#100001.99999# + (#100002.99999# - #100003.99999#) -20" />
			</SplitRule>
		</SplitRules>

		<CollectRules>
			<CollectRule>
				<Condition funcid="99999" />
				<Operation monthly="true" offset="true" upload="true"
					upload_table="uploadrecorddata" />
			</CollectRule>
			<CollectRule>
				<Condition funcid="9999" />
				<Operation funcid_new="999" />
			</CollectRule>
			<CollectRule>
				<Condition building="1234567890" funcid="9998" />
				<Operation funcid_new="998" />
			</CollectRule>
			<CollectRule>
				<Condition building="1234567890" sign="9997" funcid="9997" />
				<Operation sign_new="997" funcid_new="997" />
			</CollectRule>
		</CollectRules>

		<Check_receivetime>false</Check_receivetime><!-- 检查收数时间 -->
		<Check_interval_day>180</Check_interval_day><!-- 默认180天，当收到数据时间与当前时间相差180
			时，用当时间 -->

		<ReturnFullPackage>false</ReturnFullPackage>

		<Mode>mina</Mode>
		<Max_size>1000</Max_size>
		<SleepCount>1000</SleepCount>
		<Separate>false</Separate>
		<Separate_begin>(</Separate_begin>
		<Separate_end>)</Separate_end>
	</LowFrequency>
	<HighFrequency><!-- 高频采集端口 -->
		<Server address="0.0.0.0" port="8887" tcp="false" udp="false" />
		<Collect save_queue_size="10000" interval="true" time_span="10000"
			offset="true" delete="true" delete_before_second="86400" />
		<!--interval 是否存储固定间隔的数据 true 是，false 否 ; time_span 两个数据之间的存储间隔，单位毫秒，默认
			60000 毫秒 -->
		<ReturnFullPackage>false</ReturnFullPackage>

		<Check_receivetime>false</Check_receivetime><!-- 检查收数时间 -->
		<Check_interval_day>180</Check_interval_day><!-- 默认180天，当收到数据时间与当前时间相差180
			时，用当时间 -->

		<Mode>mina</Mode>
		<Max_size>1000</Max_size>
		<SleepCount>1000</SleepCount>
		<Separate>false</Separate>
		<Separate_begin>(</Separate_begin>
		<Separate_end>)</Separate_end>
	</HighFrequency>
	<Statistics>
		<IsStatistics>true</IsStatistics><!-- 是否统计，默认false -->
		<Upload upload="false" upload_table="uploadrecorddata" /><!-- 时间类型:5min
			值类型:real -->
		<Span>600000</Span><!-- 统计时间间隔，默认600000毫秒，相当于十分钟 -->
		<Sleep>5000</Sleep><!-- 统计休眠时间，默认5000毫秒，相当于5秒 -->

		<Real minute5="false" minute15="true" hour="true" day="true" />
		<Max hour="true" day="true" />
		<Min hour="true" day="true" />
		<Avg hour="false" day="false" />

		<UnCompute><!-- 瞬时量不进行统计的功能列表 -->
			<Function id="10101" />
			<Function id="10704" />
			<Function id="10201" />
			<Function id="10202" />
			<Function id="10203" />
			<Function id="10204" />
			<Function id="10205" />
			<Function id="10206" />

			<Function id="10305" />
			<Function id="10306" />
			<Function id="10307" />
			<Function id="10308" />
			<Function id="10309" />
			<Function id="10310" />
			<Function id="10311" />
			<Function id="10312" />
			<Function id="10313" />
			<Function id="10314" />
			<Function id="10315" />
			<Function id="10316" />

		</UnCompute>
		<SpecialCompute>
			<!-- <Function id="209" tableFlag="monthdata"/> <Function id="267" tableFlag="monthdata"
				/> -->
		</SpecialCompute>
	</Statistics>
</root>
