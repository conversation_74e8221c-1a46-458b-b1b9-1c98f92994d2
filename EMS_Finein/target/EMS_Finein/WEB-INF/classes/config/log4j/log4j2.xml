<?xml version="1.0" encoding="UTF-8"?>
<!-- eclipse测试日志配置，部署时需修改strom log4j2配置 -->
<configuration status="info">
    <Properties>
        <Property name="fileName">log.log</Property>
    </Properties>
    <!--先定义所有的appender-->
    <appenders>
        <!--这个输出控制台的配置-->
        <Console name="Console" target="SYSTEM_OUT">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch）-->
            <ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
            <!-- 输出日志的格式-->
            <PatternLayout pattern="%d{HH:mm:ss.SSS} %-5level %class{36} %L %M - %msg%xEx%n"/>
        </Console>

        <!--这个会打印出所有的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档-->
        <RollingFile name="RollingFile" fileName="${fileName}"
                     filePattern="log-$${date:yyyy-MM}/log-%d{yyyyMMddHHmmssSSS}.log.gz">
            <PatternLayout
                    pattern="%d{yyyy.MM.dd 'at' HH:mm:ss.SSS z} %-5level %class{36} %L %M - %msg%xEx%n"/>

            <!-- 日志文件大小 -->
            <SizeBasedTriggeringPolicy size="20MB"/>
            <!-- 最多保留文件数 -->
            <DefaultRolloverStrategy max="20"/>
        </RollingFile>
    </appenders>

    <!--然后定义logger，只有定义了logger并引入的appender，appender才会生效-->
    <loggers>

        <Root level="info">
            <AppenderRef ref="Console"/>
            <AppenderRef ref="RollingFile"/>
        </Root>
    </loggers>
</configuration>