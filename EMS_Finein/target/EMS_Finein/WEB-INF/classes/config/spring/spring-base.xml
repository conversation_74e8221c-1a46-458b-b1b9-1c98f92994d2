<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
	   xmlns:cache="http://www.springframework.org/schema/cache" xmlns:aop="http://www.springframework.org/schema/aop"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans-3.2.xsd    
	http://www.springframework.org/schema/context    
	http://www.springframework.org/schema/context/spring-context-3.2.xsd    
	http://www.springframework.org/schema/tx
    http://www.springframework.org/schema/tx/spring-tx-3.2.xsd
    http://www.springframework.org/schema/cache
    http://www.springframework.org/schema/cache/spring-cache.xsd
    http://www.springframework.org/schema/aop
    http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
    ">

	<!-- spring3 @value 特性加载属性文件内容 -->
	<context:property-placeholder location="classpath:config/system.properties" />
	<!-- 使用spring注解 -->
	<context:annotation-config />
	<!-- spring扫描目录 -->
	<context:component-scan base-package="com.persagy">
		<!-- 不包括@Controller -->
		<context:exclude-filter type="annotation"
								expression="org.springframework.stereotype.Controller" />
	</context:component-scan>
	<!-- 切面编程 -->
	<aop:aspectj-autoproxy />

	<!-- 数据库连接池 -->
	<bean id="dataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource"
		  destroy-method="close">
		<!-- 数据库驱动 -->
		<property name="driverClass" value="${jdbc.driverClass}" />
		<!-- 数据库连接串 -->
		<property name="jdbcUrl" value="${jdbc.jdbcUrl}" />
		<!-- 用户名 -->
		<property name="user" value="${jdbc.user}" />
		<!-- 密码 -->
		<property name="password" value="${jdbc.password}" />
		<!-- 连接池维持连接数最小值 -->
		<property name="minPoolSize" value="${jdbc.miniPoolSize}" />
		<!-- 连接池维持链接数最大值 -->
		<property name="maxPoolSize" value="${jdbc.maxPoolSize}" />
		<!-- 连接池初始化链接数 -->
		<property name="initialPoolSize" value="${jdbc.initialPoolSize}" />
		<!-- 最大空闲时间,设置秒内未使用则连接被丢弃。若为0则永不丢弃。Default: 0 -->
		<property name="maxIdleTime" value="${jdbc.maxIdleTime}" />
		<!-- 当连接池中的连接耗尽的时候c3p0一次同时获取的连接数。Default: 3 -->
		<property name="acquireIncrement" value="${jdbc.acquireIncrement}" />
		<!-- 定义在从数据库获取新连接失败后重复尝试的次数。Default: 30 -->
		<property name="acquireRetryAttempts" value="${jdbc.acquireRetryAttempts}" />
		<!-- 两次连接中间隔时间，单位毫秒。Default: 1000 -->
		<property name="acquireRetryDelay" value="${jdbc.acquireRetryDelay}" />
		<!-- 如果设为true那么在取得连接的同时将校验连接的有效性。Default: false -->
		<property name="testConnectionOnCheckin" value="${jdbc.testConnectionOnCheckin}" />
		<!-- c3p0将建一张名为设置表名的空表，并使用其自带的查询语句进行测试。Default: null -->
		<property name="automaticTestTable" value="${jdbc.automaticTestTable}" />
		<!--每设置秒检查所有连接池中的空闲连接。Default: 0 -->
		<property name="idleConnectionTestPeriod" value="${jdbc.idleConnectionTestPeriod}" />
		<!--当连接池用完时客户端调用getConnection()后等待获取新连接的时间，超时后将抛出SQLException,如设为0则无限期等待。单位毫秒。Default:
			0 -->
		<property name="checkoutTimeout" value="${jdbc.checkoutTimeout}" />
	</bean>


	<bean id="dataSource1" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
		<!-- 配置连接池属性 -->
		<property name="driverClassName" value="${jdbc.driverClass}"/>
		<property name="url" value="${jdbc.jdbcUrl}"/>
		<property name="username" value="${jdbc.user}"/>
		<property name="password" value="${jdbc.password}"/>

		<!-- 配置初始化大小、最小、最大 -->
		<property name="initialSize" value="1"/>
		<property name="minIdle" value="1"/>
		<property name="maxActive" value="20"/>

		<!-- 配置获取连接等待超时的时间 -->
		<property name="maxWait" value="60000"/>

		<!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
		<property name="timeBetweenEvictionRunsMillis" value="60000"/>

		<!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
		<property name="minEvictableIdleTimeMillis" value="300000"/>

		<!-- 验证连接有效与否的SQL，不同的数据配置不同 -->
		<property name="validationQuery" value="SELECT 1" />

		<!-- 如果空闲时间大于timeBetweenEvictionRunsMillis，执行validationQuery检测连接是否有效 -->
		<property name="testWhileIdle" value="true"/>

		<!-- 这里建议配置为TRUE，防止取到的连接不可用 -->
		<property name="testOnBorrow" value="true"/>
		<property name="testOnReturn" value="false"/>

		<!-- 打开PSCache，并且指定每个连接上PSCache的大小 -->
		<!-- 如果用Oracle，则把poolPreparedStatements配置为true，mysql可以配置为false。 -->
		<property name="poolPreparedStatements" value="false"/>
		<property name="maxPoolPreparedStatementPerConnectionSize" value="20"/>

		<!-- 这里配置提交方式，默认就是TRUE，可以不用配置 -->
		<property name="defaultAutoCommit" value="true" />

		<!-- 合并多个DruidDataSource的监控数据 -->
		<property name="useGloalDataSourceStat" value="true"/>

		<!-- 开启Druid的监控统计功能,StatFilter可以和其他的Filter配置使用 -->
		<property name="filters" value="stat,log4j2"/>

	</bean>

	<bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
		<property name="dataSource" ref="dataSource1" />
	</bean>

	<!-- 调度工厂 -->
	<bean id="scheduler"
		  class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
	</bean>

	<bean id="transactionManager"
		  class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
		<property name="dataSource" ref="dataSource" />
	</bean>

	<tx:annotation-driven transaction-manager="transactionManager" />

	<!-- 缓存 属性 -->
	<bean id="cacheManagerFactory"
		  class="org.springframework.cache.ehcache.EhCacheManagerFactoryBean">
		<property name="configLocation" value="classpath:config/ehcache/ehcache.xml" />
	</bean>

	<!-- 默认是cacheManager -->
	<bean id="cacheManager" class="org.springframework.cache.ehcache.EhCacheCacheManager">
		<property name="cacheManager" ref="cacheManagerFactory" />
	</bean>

	<!-- 支持缓存注解 -->
	<cache:annotation-driven cache-manager="cacheManager" />

	<!-- Sqlite用于数据字典版本 -->
	<bean id="sqliteJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
		<property name="dataSource" ref="sqliteDataSource" />
	</bean>

	<!--Jedis连接池的相关配置 -->
	<bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
		<!--新版是maxTotal，旧版是maxActive -->
		<property name="maxTotal">
			<value>${redis.pool.maxActive}</value>
		</property>
		<property name="maxIdle">
			<value>${redis.pool.maxIdle}</value>
		</property>
		<property name="testOnBorrow" value="true" />
		<property name="testOnReturn" value="true" />
	</bean>

	<bean id="jedisPool" class="redis.clients.jedis.JedisPool">
		<constructor-arg name="poolConfig" ref="jedisPoolConfig" />
		<constructor-arg name="host" value="${redis.host}" />
		<constructor-arg name="port" value="${redis.port}"
						 type="int" />
		<constructor-arg name="timeout" value="${redis.timeout}"
						 type="int" />
		<constructor-arg name="password" value="${redis.password}" />
		<constructor-arg name="database" value="${redis.database}"
						 type="int" />
	</bean>
</beans>	