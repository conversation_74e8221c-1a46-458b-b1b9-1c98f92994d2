######################### database setting ########################

##Mysql
jdbc.driverClass=com.mysql.cj.jdbc.Driver
jdbc.jdbcUrl=jdbc\:mysql\://************\:3306/finein_ems?useUnicode=true&characterEncoding=UTF-8
#jdbc.jdbcUrl=jdbc\:mysql\://***************\:3306/test

##SqlServer
#jdbc.driverClass=com.microsoft.sqlserver.jdbc.SQLServerDriver
#jdbc.jdbcUrl=jdbc\:sqlserver\://localhost\:1433;databaseName\=tempdb

##Oracle
#jdbc.driverClass=oracle.jdbc.driver.OracleDriver
#jdbc.jdbcUrl=jdbc\:oracle\:thin\:@localhost\:1521\:orcl

jdbc.user=root
jdbc.password=1qaz2WSX@123!
#jdbc.password=123456
jdbc.miniPoolSize=10
jdbc.maxPoolSize=20
jdbc.initialPoolSize=5
jdbc.maxIdleTime=7200
jdbc.acquireIncrement=5
jdbc.acquireRetryAttempts=10
jdbc.acquireRetryDelay=1000
jdbc.testConnectionOnCheckin=true
jdbc.automaticTestTable=c3p0TestTable
jdbc.idleConnectionTestPeriod=3600
jdbc.checkoutTimeout=10000
######################### system parameter #########################
system.printSql=false
system.repairTable=false
system.cacheInMemory=100
server.port=5000
######################## zookeeper parameter #######################
zookeeper.server.ip=localhost
zookeeper.server.port=2181
zookeeper.session.timeout=3000000
zookeeper.connection.timeout=1800000
######################## file parameter ############################
#file.storage.directory=/home/<USER>/file
file.storage.directory=/Users/<USER>/backend_src/file
isUsePersagyDictionary=false

######################## redis ############################
redis.pool.maxActive=200
redis.pool.maxIdle=50
redis.pool.minIdle=10
redis.pool.maxWaitMillis=20000
redis.pool.maxWait=300
redis.host = ************
redis.port = 6379
redis.timeout=30000
redis.password=
redis.database = 1

######################## aliyunApp ############################
aliyun.appKey=25806241
aliyun.AppSecret=2048d659d7e78faafd21a7d1932ccc00
aliyun.uuid=3a2af531b73344b4a8f91cc4dd6cc127
aliyun.host=apis.1cno.com

#UDPClientUtil timeout_default 等待时间(毫秒)
UDPClientUtil.timeout_default=15000


updatePrice.isOpen=true




