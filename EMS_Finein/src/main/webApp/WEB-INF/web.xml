<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns="http://java.sun.com/xml/ns/javaee"
	xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd"
	id="EMS_Finein" version="3.0">
	<display-name>EMS_Finein</display-name>
	<context-param>
	    <param-name>webAppRootKey</param-name>
	    <param-value>EMS_Finein</param-value>
	</context-param>
	<!-- 加载Spring配置 -->
	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>classpath:config/spring/spring-base.xml</param-value>
	</context-param>

<!--	&lt;!&ndash; 加载Log4j配置 &ndash;&gt;-->
<!--	<context-param>-->
<!--		<param-name>log4jConfigLocation</param-name>-->
<!--		<param-value>classpath:config/log4j/log4j.properties</param-value>-->
<!--	</context-param>-->

<!--	&lt;!&ndash; 启动Log4j &ndash;&gt;-->
<!--	<listener>-->
<!--		<listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>-->
<!--	</listener>-->

	<listener>
		<listener-class>org.apache.logging.log4j.web.Log4jServletContextListener</listener-class>
	</listener>
	<filter>
		<filter-name>log4jServletFilter</filter-name>
		<filter-class>org.apache.logging.log4j.web.Log4jServletFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>log4jServletFilter</filter-name>
		<url-pattern>/*</url-pattern>
		<dispatcher>REQUEST</dispatcher>
		<dispatcher>FORWARD</dispatcher>
		<dispatcher>INCLUDE</dispatcher>
		<dispatcher>ERROR</dispatcher>
	</filter-mapping>

	<!-- 启动Spring -->
	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	
	<!-- 系统初始化 -->
	<servlet>
		<servlet-name>SystemInitServlet</servlet-name>
		<servlet-class>com.persagy.servlet.SystemInitServlet</servlet-class>
		<load-on-startup>0</load-on-startup>
	</servlet>

	<!-- 解决乱码 -->
	<filter>
		<filter-name>CharacterEncodingFilter</filter-name>
		<filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
		<init-param>
			<param-name>encoding</param-name>
			<param-value>utf-8</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>CharacterEncodingFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	
	<!--配置过滤器-->
	<filter>
	    <filter-name>RegisterFilter</filter-name>
	    <filter-class>com.persagy.finein.core.filter.RegisterFilter</filter-class>
	</filter>
	  
	<!--映射过滤器-->
	<filter-mapping>
	    <filter-name>RegisterFilter</filter-name>
	    <url-pattern>/*</url-pattern>
	</filter-mapping>

	<!--配置跨域过滤器-->
<!--	<filter>-->
<!--		<filter-name>HeaderFilter</filter-name>-->
<!--		<filter-class>com.persagy.finein.core.filter.HeaderFilter</filter-class>-->
<!--	</filter>-->
<!--	&lt;!&ndash;映射跨域过滤器&ndash;&gt;-->
<!--	<filter-mapping>-->
<!--		<filter-name>HeaderFilter</filter-name>-->
<!--		<url-pattern>/*</url-pattern>-->
<!--	</filter-mapping>-->

	<!-- Spring MVC -->
	<servlet>
		<servlet-name>SpringMVC</servlet-name>
		<servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
		<init-param>
			<param-name>contextConfigLocation</param-name>
			<param-value>classpath:config/spring/spring-mvc.xml</param-value>
		</init-param>
		<load-on-startup>1</load-on-startup>
	</servlet>

	<servlet-mapping>
		<servlet-name>SpringMVC</servlet-name>
		<url-pattern>/Spring/MVC/*</url-pattern>
	</servlet-mapping>

	<servlet>
		<servlet-name>DruidStatView</servlet-name>
		<servlet-class>com.alibaba.druid.support.http.StatViewServlet</servlet-class>
	</servlet>
	<servlet-mapping>
		<servlet-name>DruidStatView</servlet-name>
		<url-pattern>/druid/*</url-pattern>
	</servlet-mapping>

	<filter>
		<filter-name>DruidWebStatFilter</filter-name>
		<filter-class>com.alibaba.druid.support.http.WebStatFilter</filter-class>
		<init-param>
			<param-name>exclusions</param-name>
			<param-value>*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*</param-value>
		</init-param>
		<init-param>
			<param-name>profileEnable</param-name>
			<param-value>true</param-value>
		</init-param>
		<init-param>
			<param-name>principalCookieName</param-name>
			<param-value>USER_COOKIE</param-value>
		</init-param>
		<init-param>
			<param-name>principalSessionName</param-name>
			<param-value>USER_SESSION</param-value>
		</init-param>
	</filter>
	<filter-mapping>
		<filter-name>DruidWebStatFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
</web-app>