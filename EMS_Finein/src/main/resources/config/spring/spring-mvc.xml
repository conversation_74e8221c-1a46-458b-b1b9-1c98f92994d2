<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:mvc="http://www.springframework.org/schema/mvc"
	xsi:schemaLocation="http://www.springframework.org/schema/beans     
	http://www.springframework.org/schema/beans/spring-beans-3.2.xsd    
	http://www.springframework.org/schema/context    
	http://www.springframework.org/schema/context/spring-context-3.2.xsd    
	http://www.springframework.org/schema/mvc  
    http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd 
    ">

	<!-- Spring MVC 扫描目录 -->
	<context:component-scan base-package="com.persagy.web.controller,com.persagy.finein,com.persagy.ac.controller">
		<!-- 只包括@Controller -->
		<context:include-filter type="annotation" expression="org.springframework.stereotype.Controller" />
	</context:component-scan>

	<!-- 文件上传解析器 -->
	<bean id="multipartResolver"
		class="org.springframework.web.multipart.commons.CommonsMultipartResolver">
		<!-- 表示用来解析request请求的默认编码格式，当没有指定的时候根据Servlet规范会使用默认值ISO-8859-1。当request自己指明了它的编码格式的时候就会忽略这里指定的defaultEncoding -->
		<property name="defaultEncoding" value="UTF-8" />
		<!-- 设置上传文件时的临时目录，默认是Servlet容器的临时目录。 -->
		<!--<property name="uploadTempDir" value=""/> -->
		<!-- 设置允许上传的最大文件大小，以字节为单位计算。当设为-1时表示无限制，默认是-1。 -->
		<property name="maxUploadSize" value="-1" />
		<!-- 设置在文件上传时允许写到内存中的最大值，以字节为单位计算，默认是10240。 -->
		<!--<property name="maxInMemorySize" value="200000"/> -->
	</bean>

	<bean id="mappingJackson2HttpMessageConverter" class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter">
		<property name="objectMapper" ref="objectMapper"></property>
		<property name="supportedMediaTypes">
			<list>
				<value>application/json;charset=UTF-8</value>
				<value>text/html;charset=UTF-8</value><!-- 避免IE出现下载JSON文件的情况 -->
			</list>
		</property>
	</bean>

	<!-- 二进制数据 -->
	<bean id="byteArrayHttpMessageConverter" class="org.springframework.http.converter.ByteArrayHttpMessageConverter">
	</bean>

	<!-- 文本数据 -->
	<bean id="stringHttpMessageConverter" class="org.springframework.http.converter.StringHttpMessageConverter">
		<!--避免出现乱码 -->
		<constructor-arg value="UTF-8" index="0"></constructor-arg>
		<property name="supportedMediaTypes">
			<list>
				<value>text/plain;charset=UTF-8</value>
				<value>text/html;charset=UTF-8</value>
			</list>
		</property>
	</bean>

	<mvc:annotation-driven>
		<mvc:message-converters>
			<ref bean="stringHttpMessageConverter" />
			<ref bean="byteArrayHttpMessageConverter" />
			<ref bean="mappingJackson2HttpMessageConverter" />
		</mvc:message-converters>
	</mvc:annotation-driven>

	<bean class="org.springframework.web.servlet.view.InternalResourceViewResolver">
		<property name="viewClass" value="org.springframework.web.servlet.view.JstlView" />
		<property name="prefix" value="/WEB-INF/jsp/" />
		<property name="suffix" value=".jsp" />
	</bean>

</beans>