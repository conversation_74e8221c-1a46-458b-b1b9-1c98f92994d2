package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnProtocol;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumCommunicationType;
import com.persagy.finein.fnconfig.handler.FNCConfigDownloadMeterReShuiHandler;
import com.persagy.finein.service.FNMeterService;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:基础处理
*/
@Component("FNCConfigDownloadMeterReShuiHandler")
public class FNCConfigDownloadMeterReShuiHandlerImpl extends CoreServiceImpl implements FNCConfigDownloadMeterReShuiHandler {
	
	@Resource(name="FNMeterService")
	private FNMeterService FNMeterService;
	
	@Override
	public void process(File srcFile,File destFile) throws Exception {
		FileInputStream is = new FileInputStream(srcFile);// 创建文件流  
        XSSFWorkbook wb = new XSSFWorkbook(is);// 加载文件流  
        XSSFSheet sheet = wb.getSheetAt(0);
        FileOutputStream fout = null;
        List<FnMeter> meterList = FNMeterService.queryMeterByEnergyTypeId(FineinConstant.EnergyType.ReShui);
		if(meterList != null && meterList.size() > 0){
			for(int i=0;i<meterList.size();i++){
				XSSFRow row = sheet.getRow(3+i);
				if(row == null){
					row = sheet.createRow(3+i);
				}
                row.createCell(1).setCellValue(meterList.get(i).getId()); 
                String address = null;
                String config_user_id = null;
                Long ljlIsCt = null;
                Long syjIsCt = null;
                try {
                	JSONObject extendObj = (JSONObject)JSONValue.parse(meterList.get(i).getExtend());
                	ljlIsCt = (Long)extendObj.get("ljlIsCt");
                    syjIsCt = (Long)extendObj.get("syjIsCt");
                	address = (String)extendObj.get("address");
                    config_user_id = (String)extendObj.get("config_user_id");
				} catch (Exception e) {
					e.printStackTrace();
				}
                row.createCell(2).setCellValue(meterList.get(i).getRadio()); 
                row.createCell(3).setCellValue(EnumCommunicationType.valueOf(meterList.get(i).getCommunicationType()).getView()); 
                row.createCell(4).setCellValue(meterList.get(i).getClientIp()); 
                row.createCell(5).setCellValue(meterList.get(i).getClientPort()==null ? "" : meterList.get(i).getClientPort()+""); 
                row.createCell(6).setCellValue(meterList.get(i).getServerIp()); 
                row.createCell(7).setCellValue(meterList.get(i).getServerPort()==null ?"" : meterList.get(i).getServerPort()+"");
                row.createCell(8).setCellValue(address); 
                row.createCell(9).setCellValue(config_user_id); 
                FnProtocol protocol = ConstantDBBaseData.ProtocolMap.get(meterList.get(i).getProtocolId());
                row.createCell(10).setCellValue(protocol.getId()+"#"+protocol.getName()); 
                row.createCell(11).setCellValue(meterList.get(i).getInstallAddress()); 
                row.createCell(12).setCellValue(ljlIsCt == null || ljlIsCt== 1? "Y" : "N"); 
                row.createCell(13).setCellValue(syjIsCt == null || syjIsCt== 1? "Y" : "N"); 
			}
		}
        
		try {
			fout = new FileOutputStream(destFile);  
			wb.write(fout);  
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			if(fout!=null){
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if(wb!=null){
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
			if(is!=null){
				try {
					is.close();
				} catch (IOException e1) {
				}
			}
		}
	}
}

