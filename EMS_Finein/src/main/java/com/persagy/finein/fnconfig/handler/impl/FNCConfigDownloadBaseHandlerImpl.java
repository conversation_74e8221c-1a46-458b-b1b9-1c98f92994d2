package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.finein.enumeration.EnumConfigType;
import com.persagy.finein.fnconfig.handler.FNCConfigDownloadBaseHandler;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:基础处理
*/
@Component("FNCConfigDownloadBaseHandler")
public class FNCConfigDownloadBaseHandlerImpl extends CoreServiceImpl implements FNCConfigDownloadBaseHandler {
	
	@Resource(name = "FNCConfigDownloadBaseFloorHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigDownloadBaseFloorHandler FNCConfigDownloadBaseFloorHandler;
	
	@Resource(name = "FNCConfigDownloadBaseRoomHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigDownloadBaseRoomHandler FNCConfigDownloadBaseRoomHandler;

	@Resource(name = "FNCConfigDownloadBaseTenantHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigDownloadBaseTenantHandler FNCConfigDownloadBaseTenantHandler;
	
	@Override
	public void process(EnumConfigType configType, File srcFile,File destFile,String buildingId) throws Exception {
		switch (configType) {
		case Base_Floor_Config:
			FNCConfigDownloadBaseFloorHandler.process(srcFile,destFile,buildingId);
			break;
		case Base_Room_Config:
			FNCConfigDownloadBaseRoomHandler.process(srcFile,destFile,buildingId);
			break;
		case Base_Tenant_Config:
			FNCConfigDownloadBaseTenantHandler.process(srcFile,destFile,buildingId);
			break;
		default:
			throw new Exception("配置类型不存在");
		}
	}
	
}

