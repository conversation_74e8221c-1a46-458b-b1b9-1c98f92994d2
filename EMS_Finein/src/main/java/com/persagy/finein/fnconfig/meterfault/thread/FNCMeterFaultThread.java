package com.persagy.finein.fnconfig.meterfault.thread;


import com.persagy.core.thread.BaseThread;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnEnergyType;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.fnconfig.meterfault.handler.FNCMeterFaultHandler;
import com.persagy.finein.service.FNMeterService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月21日 下午12:58:25

* 说明:租户管理-后台配置-仪表数据中断
*/
@Component("FNCMeterFaultThread")
public class FNCMeterFaultThread extends BaseThread{

	private static boolean CONSTANT_THREAD_IS_OPEN = true;
	private static long CONSTANT_SLEEP = 180;
	private static long CONSTANT_INSTANT_TOLERATER_SECOND = 600;
	private static long CONSTANT_CUMULANT_TOLERATER_SECOND = 3600;
	
	private static Logger log = Logger.getLogger(FNCMeterFaultThread.class);
	
	@Resource(name="FNMeterService")
	private FNMeterService FNMeterService;
	
	@Resource(name="FNCMeterFaultHandler")
	private FNCMeterFaultHandler FNCMeterFaultHandler;

	private static boolean IsSysParamValueInited = false;
	
	@Override
	protected void business() throws Exception {
		try {
			this.initSysParamValue();
			
			if(!CONSTANT_THREAD_IS_OPEN){
				this.setStop(true);
				log.error("【租户管理】租户报警仪表数据中断线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}
			
			this.process();
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}
	}
	
	private void initSysParamValue(){
		if(IsSysParamValueInited){
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_MeterFaultThreadIsOpen);
			if(threadIsOpen != null){
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Long sleep = (Long)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_MeterFaultThreadSleepSecond);
			if(sleep != null){
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}
		try {
			Long cumulantToleraterSecond = (Long)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_MeterFaultCumulantToleraterSecond);
			if(cumulantToleraterSecond != null){
				CONSTANT_CUMULANT_TOLERATER_SECOND = cumulantToleraterSecond;
			}
		} catch (Exception e1) {
		}
		try {
			Long instantToleraterSecond = (Long)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_MeterFaultInstantToleraterSecond);
			if(instantToleraterSecond != null){
				CONSTANT_INSTANT_TOLERATER_SECOND = instantToleraterSecond;
			}
		} catch (Exception e1) {
		}
		
		IsSysParamValueInited = true;
		log.error("【租户管理】租户报警仪表数据中断线程开始运行。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}
	
	private void process(){
		try {
			for(FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList){
				List<FnMeter> meterList = FNMeterService.queryMeterByEnergyTypeId(energyType.getId());
				if(meterList != null && meterList.size() > 0){
					for(FnMeter meter : meterList){
						this.FNCMeterFaultHandler.handle(meter, CONSTANT_INSTANT_TOLERATER_SECOND, CONSTANT_CUMULANT_TOLERATER_SECOND);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}

