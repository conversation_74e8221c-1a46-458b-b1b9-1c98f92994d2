package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.exception.ConfigUploadException;
import com.persagy.finein.enumeration.EnumConfigUploadResult;
import com.persagy.finein.fnconfig.handler.FNCConfigUploadBaseHandler;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:基础处理
*/
@Component("FNCConfigUploadBaseHandler")
public class FNCConfigUploadBaseHandlerImpl extends CoreServiceImpl implements FNCConfigUploadBaseHandler {
	
	@Resource(name = "FNCConfigUploadBaseFloorHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigUploadBaseFloorHandler FNCConfigUploadBaseFloorHandler;
	
	@Resource(name = "FNCConfigUploadBaseRoomHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigUploadBaseRoomHandler FNCConfigUploadBaseRoomHandler;
	
	@Resource(name = "FNCConfigUploadBaseTenantHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigUploadBaseTenantHandler FNCConfigUploadBaseTenantHandler;
	
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void process(Map<String,Object> contentObj,String buildingId,Integer type,String logicCode,Workbook workbook,FileResource fileResource,String userId) throws Exception,ConfigUploadException{
		switch (logicCode) {
		case FineinConstant.BackConfigType.Base_Floor_Config:
			FNCConfigUploadBaseFloorHandler.process(contentObj,buildingId, type, logicCode, workbook, fileResource,userId);
			break;
		case FineinConstant.BackConfigType.Base_Room_Config:
			FNCConfigUploadBaseRoomHandler.process(contentObj,buildingId, type, logicCode, workbook, fileResource,userId);
			break;
		case FineinConstant.BackConfigType.Base_Tenant_Config:
			FNCConfigUploadBaseTenantHandler.process(contentObj,buildingId, type, logicCode, workbook, fileResource,userId);
			break;
		default:
			contentObj.put("result", EnumConfigUploadResult.Code_2.getValue());
			contentObj.put("message", EnumConfigUploadResult.Code_2.getMessage());
			break;
		}
	}
	
}

