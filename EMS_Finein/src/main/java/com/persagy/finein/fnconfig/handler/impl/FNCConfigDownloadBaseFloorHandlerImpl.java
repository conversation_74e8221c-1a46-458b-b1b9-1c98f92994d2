package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.finein.FnFloor;
import com.persagy.finein.fnconfig.handler.FNCConfigDownloadBaseFloorHandler;
import com.persagy.finein.service.FNFloorService;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:基础处理
*/
@Component("FNCConfigDownloadBaseFloorHandler")
public class FNCConfigDownloadBaseFloorHandlerImpl extends CoreServiceImpl implements FNCConfigDownloadBaseFloorHandler {
	
	@Resource(name="FNFloorService")
	private FNFloorService FNFloorService;
	
	@Override
	public void process(File srcFile,File destFile,String buildingId) throws Exception {
		FileInputStream is = new FileInputStream(srcFile);// 创建文件流  
        XSSFWorkbook wb = new XSSFWorkbook(is);// 加载文件流  
        XSSFSheet sheet = wb.getSheetAt(0);
        FileOutputStream fout = null;
		List<FnFloor> floorList = FNFloorService.queryList(buildingId, EMSOrder.Asc);
		if(floorList != null && floorList.size() > 0){
			for(int i=0;i<floorList.size();i++){
				XSSFRow row = sheet.getRow(3+i);
				if(row == null){
					row = sheet.createRow(3+i);
				}
                row.createCell(1).setCellValue(floorList.get(i).getOrderBy());  
                row.createCell(2).setCellValue(floorList.get(i).getId());   
                row.createCell(3).setCellValue(floorList.get(i).getName());  
			}
		}
        
		try {
			fout = new FileOutputStream(destFile);  
			wb.write(fout);  
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			if(fout!=null){
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if(wb!=null){
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
			if(is!=null){
				try {
					is.close();
				} catch (IOException e1) {
				}
			}
		}
	}
}

