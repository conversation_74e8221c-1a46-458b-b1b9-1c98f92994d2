package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.pojo.finein.FnBackConfigMeter;
import com.persagy.ems.pojo.finein.FnPriceTemplate;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.exception.ConfigUploadException;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.enumeration.EnumConfigUploadResult;
import com.persagy.finein.enumeration.EnumExsistStatus;
import com.persagy.finein.enumeration.EnumPriceType;
import com.persagy.finein.enumeration.EnumUseStatus;
import com.persagy.finein.fnconfig.handler.FNCConfigUploadCommonPriceHandler;
import com.persagy.finein.service.FNBackConfigMeterService;
import com.persagy.finein.service.FNPriceTemplateService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月17日 下午3:14:12
 * 
 * 说明:基础处理-价格模板
 */
@Component("FNCConfigUploadCommonPriceHandler")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNCConfigUploadCommonPriceHandlerImpl extends FNCConfigUploadHandlerImpl
		implements FNCConfigUploadCommonPriceHandler {

	@Resource(name = "FNPriceTemplateService")
	private FNPriceTemplateService FNPriceTemplateService;

	@Resource(name = "FNBackConfigMeterService")
	private FNBackConfigMeterService FNBackConfigMeterService;

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void process(Map<String, Object> contentObj, Integer type, String logicCode, Workbook workbook,
			FileResource fileResource, String userId) throws Exception, ConfigUploadException {
		Sheet sheet = workbook.getSheetAt(0);
		int rowMaxIndex = sheet.getLastRowNum();
		HashMap<String, FnPriceTemplate> fnPriceTemplateMap = new HashMap<String, FnPriceTemplate>();
		List<FnPriceTemplate> priceTemplateList = new ArrayList<FnPriceTemplate>();
		for (int rowIndex = 3; rowIndex <= rowMaxIndex; rowIndex++) {
			Row row = sheet.getRow(rowIndex);
			if(row==null){
				break;
			}
			FnPriceTemplate fnPriceTemplate = new FnPriceTemplate();
			fnPriceTemplate.setCreateTime(new Date());
			fnPriceTemplate.setLastUpdateTime(new Date());
			fnPriceTemplate.setIsValid(EnumUseStatus.Not_In_Use.getValue());
			// 模板名称
			Cell cell = row.getCell(1);
			String value = getCellStringValue(cell, false);
			if (value == null || "".equals(value.trim())) {
				cell = row.getCell(2);
				value = getCellStringValue(cell, false);
				if (value == null || "".equals(value.trim())) {
					break;
				} else {
					throw new ConfigUploadException((rowIndex + 1) + "行2列模板名称不能为空");
				}
			}
			fnPriceTemplate.setId(UUID.randomUUID().toString());
			fnPriceTemplate.setName(value.trim());

			cell = row.getCell(2);
			value = getCellStringValue(cell, false);
			checkNull(rowIndex, 2, value);
			String energyTypeId = EnergyTypeUtil.queryEnergyTypeIdByName(value.trim());
			if (energyTypeId == null) {
				throw new ConfigUploadException((rowIndex + 1) + "行3列解析能耗类型不存在:" + value);
			}
			fnPriceTemplate.setEnergyTypeId(energyTypeId);

			// 价格类型
			cell = row.getCell(3);
			value = getCellStringValue(cell, false);
			checkNull(rowIndex, 3, value);
			fnPriceTemplate.setType(this.getPriceType(rowIndex, 3, value));
			// 只有电能耗上传分时价格
			if (!energyTypeId.equals(FineinConstant.EnergyType.Dian) && value.trim().equals("分时")) {
				throw new ConfigUploadException((rowIndex + 1) + "行4列水，热水，燃气价格只有平均价格，没有分时价格:" + value);
			}
			cell = row.getCell(4);
			value = getCellStringValue(cell, true);
			if (fnPriceTemplate.getType().intValue() == EnumPriceType.AVG.getValue().intValue()) {
				checkNull(rowIndex, 4, value);
				Double data = DoubleFormatUtil.Instance().getDoubleData(value);
				if (data == null) {
					throw new ConfigUploadException((rowIndex + 1) + "行5列解析平均价格需要为double型:" + value);
				}
				fnPriceTemplate.setContent(data + "");
				cell = row.getCell(5);
				value = getCellStringValue(cell, true);
				if (!"".equals(value.trim())) {
					throw new ConfigUploadException((rowIndex + 1) + "行6列平均价格类型不能填写分时价格:" + value);
				}
				cell = row.getCell(6);
				value = getCellStringValue(cell, true);
				if (!"".equals(value.trim())) {
					throw new ConfigUploadException((rowIndex + 1) + "行7列平均价格类型不能填写分时价格:" + value);
				}
				cell = row.getCell(7);
				value = getCellStringValue(cell, true);
				if (!"".equals(value.trim())) {
					throw new ConfigUploadException((rowIndex + 1) + "行8列平均价格类型不能填写分时价格:" + value);
				}
				cell = row.getCell(8);
				value = getCellStringValue(cell, true);
				if (!"".equals(value.trim())) {
					throw new ConfigUploadException((rowIndex + 1) + "行9列平均价格类型不能填写分时价格:" + value);
				}

			} else {
				if (value != null && !"".equals(value)) {
					throw new ConfigUploadException((rowIndex + 1) + "行5列分时价格和平均价格只能选择一个");
				}
				Double j = null;
				Double f = null;
				Double g = null;
				Double p = null;
				{
					cell = row.getCell(5);
					value = getCellStringValue(cell, true);
					checkNull(rowIndex, 5, value);
					j = DoubleFormatUtil.Instance().getDoubleData(value);
					if (j == null) {
						throw new ConfigUploadException((rowIndex + 1) + "行6列解析价格值需要为double型:" + value);
					}
				}
				{
					cell = row.getCell(6);
					value = getCellStringValue(cell, true);
					checkNull(rowIndex, 6, value);
					f = DoubleFormatUtil.Instance().getDoubleData(value);
					if (f == null) {
						throw new ConfigUploadException((rowIndex + 1) + "行7列解析价格值需要为double型:" + value);
					}
				}
				{
					cell = row.getCell(7);
					value = getCellStringValue(cell, true);
					checkNull(rowIndex, 7, value);
					g = DoubleFormatUtil.Instance().getDoubleData(value);
					if (g == null) {
						throw new ConfigUploadException((rowIndex + 1) + "行8列解析价格值需要为double型:" + value);
					}
				}
				{
					cell = row.getCell(8);
					value = getCellStringValue(cell, true);
					checkNull(rowIndex, 8, value);
					p = DoubleFormatUtil.Instance().getDoubleData(value);
					if (p == null) {
						throw new ConfigUploadException((rowIndex + 1) + "行9列解析价格值需要为double型:" + value);
					}
				}
				JSONArray array = new JSONArray();
				{
					JSONObject obj = new JSONObject();
					obj.put("type", "J");
					obj.put("value", j);
					array.add(obj);
				}
				{
					JSONObject obj = new JSONObject();
					obj.put("type", "F");
					obj.put("value", f);
					array.add(obj);
				}
				{
					JSONObject obj = new JSONObject();
					obj.put("type", "G");
					obj.put("value", g);
					array.add(obj);
				}
				{
					JSONObject obj = new JSONObject();
					obj.put("type", "P");
					obj.put("value", p);
					array.add(obj);
				}
				fnPriceTemplate.setContent(array.toString());
			}
			if (fnPriceTemplateMap.containsKey(fnPriceTemplate.getName())) {
				throw new ConfigUploadException((rowIndex + 1) + "行2列模板名称重复" + fnPriceTemplate.getName());
			}
			fnPriceTemplateMap.put(fnPriceTemplate.getName(), fnPriceTemplate);
			priceTemplateList.add(fnPriceTemplate);
		}

		List<FnPriceTemplate> dbPriceTemplateList = FNPriceTemplateService.queryList();
		Map<String, FnPriceTemplate> priceTemplateMap = new HashMap<String, FnPriceTemplate>();
		if (dbPriceTemplateList != null) {
			for (FnPriceTemplate priceTemplate : dbPriceTemplateList) {
				priceTemplateMap.put(priceTemplate.getName(), priceTemplate);
			}
		}
		StringBuffer exsitPriceTemplateSb = new StringBuffer();
		List<FnPriceTemplate> newPriceTemplateList = new ArrayList<FnPriceTemplate>();
		for (FnPriceTemplate fnPriceTemplate : priceTemplateList) {
			if (priceTemplateMap.containsKey(fnPriceTemplate.getName())) {
				FnPriceTemplate query = priceTemplateMap.get(fnPriceTemplate.getName());
				fnPriceTemplate.setId(null);
				fnPriceTemplate.setCreateTime(null);
				FNPriceTemplateService.update(query, fnPriceTemplate);

			} else {
				newPriceTemplateList.add(fnPriceTemplate);
			}
		}
		if (newPriceTemplateList.size() > 0) {
			FNPriceTemplateService.save(newPriceTemplateList);
		}
		FnBackConfigMeter query = new FnBackConfigMeter();
		query.setLogicCode(logicCode);

		FnBackConfigMeter upload = new FnBackConfigMeter();
		upload.setFileId(fileResource.getId());
		upload.setFileName(fileResource.getName());
		upload.setIsExsit(EnumExsistStatus.Exsist.getValue());
		upload.setUploadTime(new Date());
		upload.setUserId(userId);

		FNBackConfigMeterService.update(query, upload);

		contentObj.put("result", EnumConfigUploadResult.Code_0.getValue());
		contentObj.put("message", "");

		if ("".equals(exsitPriceTemplateSb.toString())) {
			contentObj.put("数据库已存在编码", exsitPriceTemplateSb.toString());
		}
	}
}
