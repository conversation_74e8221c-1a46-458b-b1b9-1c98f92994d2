package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.component.JsonObjectMapper;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.pojo.finein.FnBackConfigMeter;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnProtocol;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.exception.ConfigUploadException;
import com.persagy.finein.enumeration.EnumConfigUploadResult;
import com.persagy.finein.enumeration.EnumExsistStatus;
import com.persagy.finein.fnconfig.handler.FNCConfigUploadMeterReShuiHandler;
import com.persagy.finein.service.FNBackConfigMeterService;
import com.persagy.finein.service.FNMeterService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月17日 下午3:14:12
 * 
 * 说明:基础处理
 */
@Component("FNCConfigUploadMeterReShuiHandler")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNCConfigUploadMeterReShuiHandlerImpl extends FNCConfigUploadHandlerImpl
		implements FNCConfigUploadMeterReShuiHandler {

	@Resource(name = "objectMapper")
	private JsonObjectMapper objectMapper;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNBackConfigMeterService")
	private FNBackConfigMeterService FNBackConfigMeterService;


	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void process(Map<String, Object> contentObj, Integer type, String logicCode, Workbook workbook,
			FileResource fileResource,String userId) throws Exception, ConfigUploadException {
		Sheet sheet = workbook.getSheetAt(0);
		// 检查数据准确性
		List<String> ids = new ArrayList<String>();
		List<FnMeter> meterList = new ArrayList<FnMeter>();
		int rowMaxIndex = sheet.getLastRowNum();
		for (int rowIndex = 3; rowIndex <= rowMaxIndex; rowIndex++) {
			Row row = sheet.getRow(rowIndex);
			if(row==null){
				break;
			}
			FnMeter fnMeter = new FnMeter();
			String address = null;
			String config_user_id = null;

			// 仪表id
			Cell cell = row.getCell(1);
			String value = this.getCellStringValue(cell, false);
			if (value == null || "".equals(value.trim())) {
				cell = row.getCell(2);
				value = getCellStringValue(cell, false);
				if (value == null || "".equals(value.trim())) {
					break;
				} else {
					throw new ConfigUploadException((rowIndex+1) + "行2列仪表id不能为空:");
				}
			}
			if(ids.contains(value.trim())){
				 throw new ConfigUploadException((rowIndex+1)+"行2列仪表id重复:"+value);
			}
			ids.add(value.trim());
			fnMeter.setId(value.trim());
			fnMeter.setEnergyTypeId(FineinConstant.EnergyType.ReShui);

			//倍率
			cell = row.getCell(2);
			value = this.getCellStringValue(cell, true);
			this.checkNull(rowIndex, 2, value);
			Double radio = null;
			try {
				radio = DoubleFormatUtil.Instance().getDoubleData(value);
			} catch (Exception e) {
			}
			if (radio == null) {
				throw new ConfigUploadException((rowIndex+1) + "行3列解析异常" + value);
			}
			fnMeter.setRadio(radio);
           
			//通讯类型
			cell = row.getCell(3);
			value = this.getCellStringValue(cell, false);
			this.checkNull(rowIndex, 3, value);
			Integer commType = getCommunicationTypeValue(rowIndex, 3, value);
			fnMeter.setCommunicationType(commType);

			// 通讯地址
			cell = row.getCell(8);
			value = this.getCellStringValue(cell, false);
			address = value;

			// userId
			cell = row.getCell(9);
			value = this.getCellStringValue(cell, false);
			config_user_id = value;

			// 协议
			cell = row.getCell(10);
			value = this.getCellStringValue(cell, false);
			String protocolId = value.split("#")[0].trim();
			FnProtocol fnProtocol = ConstantDBBaseData.ProtocolMap.get(protocolId);
			if(fnProtocol==null){
				throw new ConfigUploadException((rowIndex+1)+"行16列热水表协议不存在" );
			}
			if(!FineinConstant.EnergyType.ReShui.equals(fnProtocol.getEnergyTypeId())){
				throw new ConfigUploadException((rowIndex+1)+"行16列使用非热水表类型协议" );
			}       
			fnMeter.setProtocolId(protocolId);
			// 查询协议
			FnProtocol protocol = getProtocol(rowIndex, 10, protocolId);

			// 客户端ip
			cell = row.getCell(4);
			value = this.getCellStringValue(cell, false);
			this.check(commType, protocol, rowIndex, 4, value);
			fnMeter.setClientIp(value.trim());

			// 客户端端口
			cell = row.getCell(5);
			value = this.getCellStringValue(cell, false);
			this.check(commType, protocol, rowIndex, 5, value);
			try {
				if(value==null||"".equals(value.trim())){
					fnMeter.setClientPort(null);
				}else{
					int post = Integer.parseInt(value.trim());
					if(post > 0 && post < 65535){
						fnMeter.setClientPort(post);
					}else{
						throw new Exception();
					}
				}
				
			} catch (Exception e) {
			throw new ConfigUploadException((rowIndex+1)+"行6列客户端端口号格式不正确：" +value);
			}

			// 服务端Ip
			cell = row.getCell(6);
			value = this.getCellStringValue(cell, false);
			this.check(commType, protocol, rowIndex, 6, value);
			fnMeter.setServerIp(value.trim());

			// 服务端端口
			cell = row.getCell(7);
			value = this.getCellStringValue(cell, false);
			this.check(commType, protocol, rowIndex, 7, value);
			try {
				if(value==null||"".equals(value.trim())){
					fnMeter.setServerPort(null);
				}else{
					int post = Integer.parseInt(value.trim());
					if(post > 0 && post < 65535){
						fnMeter.setServerPort(post);
					}else{
						throw new Exception();
					}
				}
				
			} catch (Exception e) {
			throw new ConfigUploadException((rowIndex+1)+"行8列服务端端口号格式不正确：" +value);
			}

			fnMeter.setBillingMode(protocol.getBillingMode());
			fnMeter.setIsUse(0);
			fnMeter.setPayType(protocol.getPayType());
			fnMeter.setMeterType(protocol.getMeterType());

			// 安装位置
			cell = row.getCell(11);
			value = this.getCellStringValue(cell, false);
			this.checkNull(rowIndex, 11, value);
			fnMeter.setInstallAddress(value);

			Integer ljlIsCt;
			Integer syjIsCt;
			cell = row.getCell(12);
			value = this.getCellStringValue(cell, false);
			this.checkNull(rowIndex, 12, value);
			ljlIsCt=this.getYorNIntValue(rowIndex, 12, value);
			//剩余量乘倍率
			cell = row.getCell(13);
			value = this.getCellStringValue(cell, false);
			this.checkNull(rowIndex, 13, value);
			syjIsCt=this.getYorNIntValue(rowIndex, 13, value);
			Map<String, Object> extendMap = new HashMap<>();
			extendMap.put("ljlIsCt", ljlIsCt);
			extendMap.put("syjIsCt", syjIsCt);
			extendMap.put("address", address);
			extendMap.put("config_user_id", config_user_id);

			fnMeter.setExtend(objectMapper.writeValueAsString(extendMap));
			meterList.add(fnMeter);

//			{// 数据字典
//				Meter meter = new Meter();
//				meter.setId(fnMeter.getId());
//
//				if (dictionaryLogicService.staticSelect(meter).size() == 0) {
//					meter.setName(fnMeter.getInstallAddress());
//					meter.setType(FineinConstant.MeterType.ReShui);
//					meter.setCt(fnMeter.getRadio());
//					dictionaryLogicService.staticInsert(meter);
//				} else {
//					Meter meterUpdate = new Meter();
//					meterUpdate.setName(fnMeter.getInstallAddress());
//					meterUpdate.setType(FineinConstant.MeterType.ReShui);
//					meterUpdate.setCt(fnMeter.getRadio());
//					dictionaryLogicService.staticUpate(meter, meterUpdate);
//				}
//			}
		}
		if (meterList.size() == 0) {
			throw new ConfigUploadException("上传仪表数据行数为0");
		}

		// 查询所有仪表
		List<FnMeter> dbMeterList = FNMeterService.query(new FnMeter());
		Map<String, FnMeter> meterMap = new HashMap<String, FnMeter>();
		if (dbMeterList != null) {
			for (FnMeter meter : dbMeterList) {
				meterMap.put(meter.getId(), meter);
			}
		}
		StringBuffer exsitMeterSb = new StringBuffer();
		List<FnMeter> exsitMeterList = new ArrayList<FnMeter>();
		List<FnMeter> newMeterList = new ArrayList<FnMeter>();
		for (FnMeter meter : meterList) {
			if (meterMap.containsKey(meter.getId())) {
				FnMeter dbMeter = meterMap.get(meter.getId());
				if(!FineinConstant.EnergyType.ReShui.equals(dbMeter.getEnergyTypeId())){
					throw new ConfigUploadException("数据已存在相同Id的非热水类型仪表，请核实修改后上传:"+meter.getId());
				}
				meter.setIsUse(dbMeter.getIsUse());
				exsitMeterList.add(meter);
				if ("".equals(exsitMeterSb.toString())) {
					exsitMeterSb.append(meter.getId());
				} else {
					exsitMeterSb.append(",").append(meter.getId());
				}
			} else {
				newMeterList.add(meter);
			}
		}
		if (newMeterList.size() > 0) {
			FNMeterService.save(newMeterList);
		}
		//更新
		for (FnMeter updata : exsitMeterList) {
			FnMeter query = new FnMeter();
			query.setId(updata.getId());
			FNMeterService.update(query, updata);
		}
		FnBackConfigMeter query = new FnBackConfigMeter();
		query.setLogicCode(logicCode);

		FnBackConfigMeter upload = new FnBackConfigMeter();
		upload.setFileId(fileResource.getId());
		upload.setFileName(fileResource.getName());
		upload.setIsExsit(EnumExsistStatus.Exsist.getValue());
		upload.setUploadTime(new Date());
		upload.setUserId(userId);

		FNBackConfigMeterService.update(query, upload);

		contentObj.put("result", EnumConfigUploadResult.Code_0.getValue());
		contentObj.put("message", "");

		if (!"".equals(exsitMeterSb.toString())) {
			contentObj.put("数据库已存在编码", exsitMeterSb.toString());
		}
	}
}
