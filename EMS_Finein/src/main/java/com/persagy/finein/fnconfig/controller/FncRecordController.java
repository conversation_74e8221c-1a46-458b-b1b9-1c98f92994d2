package com.persagy.finein.fnconfig.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.SystemPropertiesUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.core.util.PathUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.hssf.usermodel.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;

/**
 * 操作记录
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FncRecordController extends BaseController {

    @Resource(name = "FNRecordFaultService")
    private FNRecordFaultService fnRecordFaultService;

    @Resource(name = "SystemPropertiesUtil")
    private SystemPropertiesUtil systemPropertiesUtil;

    @Resource(name = "FNFileResourceService")
    private FNFileResourceService fnFileResourceService;

    @Resource(name = "FNChargeMessageService")
    private FNChargeMessageService fnChargeMessageService;

    @Resource(name = "FNRecordMeterOperateService")
    private FNRecordMeterOperateService fnRecordMeterOperateService;

    @Resource(name = "PathUtil")
    private PathUtil pathUtil;

    @Resource(name = "FNRecordMeterSetService")
    private FNRecordMeterSetService fnRecordMeterSetService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNRecordPostPayService")
    private FNRecordPostPayService fnRecordPostPayService;

    @Resource(name = "FNRecordPostClearingPayService")
    private FNRecordPostClearingPayService fnRecordPostClearingPayService;

    @Resource(name = "FNRecordPrePayService")
    private FNRecordPrePayService fnRecordPrePayService;

    @Resource(name = "FNRecordPayChannelService")
    private FNRecordPayChannelService fnRecordPayChannelService;

    @Resource(name = "FNRecordPriceChangeService")
    private FNRecordPriceChangeService fnRecordPriceChangeService;

    @Resource(name = "FNRecordReturnService")
    private FNRecordReturnService fnRecordReturnService;

    @Resource(name = "FNRecordTenantOperateService")
    private FNRecordTenantOperateService fnRecordTenantOperateService;

    private String getPath() throws Exception {
        return systemPropertiesUtil.getFileStorageDirectory();
    }

    /**
     * 操作记录 —— 故障记录
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCRecordFaultService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult recordFault(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String) dto.get("energyTypeId");
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            String meterId = (String) dto.get("meterId");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");
            Integer isDownload = (Integer) dto.get("isDownload");

            if (timeFrom == null || timeTo == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            if (pageIndex == null || pageSize == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnRecordFault query = new FnRecordFault();
            query.setEnergyTypeId(energyTypeId);
            query.setSpecialOperation("faultTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("faultTime", SpecialOperator.$lt, standard.parse(timeTo));
            query.setEnergyTypeId(energyTypeId);
            query.setFaultType(EnumFaultType.TXYC.getValue());

            if (meterId != null && !"".equals(meterId.trim())) {
                query.setSpecialOperation("faultSrcId", SpecialOperator.$like, "%" + meterId.trim() + "%");
            }
            int count = fnRecordFaultService.count(query);
            if(isDownload ==0){
                query.setSkip(Long.valueOf(pageIndex) * pageSize);
                query.setLimit(Long.valueOf(pageSize));
            }
            query.setSort("faultTime", EMSOrder.Desc);

            List<FnRecordFault> dataList = fnRecordFaultService.query(query);
            List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();

            if (dataList != null) {
                for (FnRecordFault fnRecordFault : dataList) {
                    Map<String, Object> dataMap = new HashMap<String, Object>();
                    dataMap.put("faultTime", standard.format(fnRecordFault.getFaultTime()));
                    dataMap.put("code", fnRecordFault.getFaultSrcId());
                    dataMap.put("name", fnRecordFault.getFaultSrcName());
                    dataMap.put("type", fnRecordFault.getFaultType());
                    dataMap.put("energyTypeId", fnRecordFault.getEnergyTypeId());
                    resultList.add(dataMap);
                }
            }
            if (isDownload == 0) {
                HashMap<String, Object> contentMap = new HashMap<>();
                contentMap.put("count", count);
                contentMap.put("dataList", resultList);
                content.add(contentMap);
            } else {
                Map<String, String> result = new HashMap<String, String>();
                result.put("id", this.recordFaultBuildExcel(resultList,energyTypeId,timeFrom,timeTo));
                content.add(result);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCRecordFaultService");
        }
    }

    /**
     * 操作记录 —— 故障记录 —— 下载
     * @throws Exception
     */
    @SuppressWarnings("deprecation")
    private String recordFaultBuildExcel(List<Map<String, Object>> resultList,String energyTypeId,String timeFrom,String timeTo) throws Exception {
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");

        HSSFCellStyle style = wb.createCellStyle();
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        {// 创建标题
            HSSFRow row = sheet.createRow((short) 0);
            String[] titleArray = { "操作时间", "仪表编码", "故障源名称(安装位置)", "故障类型" };
            for (int i = 0; i < titleArray.length; i++) {
                HSSFCell cell = row.createCell((short) i);
                cell.setCellStyle(style);
                cell.setCellValue(titleArray[i]);
            }
        }
        if (resultList != null && resultList.size() > 0) {
            for (int rowIndex = 0; rowIndex < resultList.size(); rowIndex++) {
                HSSFRow row = sheet.createRow(rowIndex + 1);
                {
                    HSSFCell cell = row.createCell(0);
                    cell.setCellValue((String) resultList.get(rowIndex).get("faultTime"));
                }
                {
                    HSSFCell cell = row.createCell(1);
                    cell.setCellValue((String) resultList.get(rowIndex).get("code"));
                }
                {
                    HSSFCell cell = row.createCell(2);
                    cell.setCellValue((String) resultList.get(rowIndex).get("name"));
                }
                {
                    HSSFCell cell = row.createCell(3);
                    cell.setCellValue((Integer) resultList.get(rowIndex).get("type")==0?EnumFaultType.TXYC.getView():"未知异常");
                }
            }
        }
        String path = getPath();
        String subdirectory = getRecordFaultSubdirectory();
        String resourceId = UUID.randomUUID().toString();
        String fileDir = Paths.get(path, subdirectory).toString();
        File file = new File(fileDir);
        if (!file.exists()) {
            file.mkdirs();
        }
        String newFilePath = Paths.get(path, subdirectory, resourceId).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fout != null) {
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
        // 保存文件
        StringBuilder name = new StringBuilder("故障记录");
        if(energyTypeId!=null){
            for(FnEnergyType fnEnergyType: ConstantDBBaseData.EnergyTypeList){
                if(fnEnergyType.getId().equals(energyTypeId)){
                    name.append("-").append(fnEnergyType.getName());
                }
            }
        }
        Date to = DateUtils.addDays(standard.parse(timeTo), -1);
        Date from = standard.parse(timeFrom);
        timeFrom = standard.format(from);
        name.append("-").append(timeFrom.substring(0, 10)).append("~").append(standard.format(to).substring(0, 10));
        FileResource resource = new FileResource();
        resource.setId(resourceId);
        resource.setName(name.toString());
        resource.setSuffix("xls");
        resource.setSubdirectory(subdirectory);
        fnFileResourceService.save(resource);
        // 返回资源ID
        return resourceId;
    }



    private String getRecordFaultSubdirectory() {
        return File.separator + "finein" + File.separator + "tenant_manage" + File.separator + "back_config"
                + File.separator + "record";
    }

    /**
     * 操作记录 —— 发送短信记录
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCRecordMessageService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult recordMessage(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            String tenantName = (String) dto.get("tenantName");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer isDownload = (Integer) dto.get("isDownload");
            Integer pageSize = (Integer) dto.get("pageSize");

            if (timeFrom == null || timeTo == null || isDownload == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            if (pageIndex == null || pageSize == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnChargeMessage query = new FnChargeMessage();
            query.setSpecialOperation("createTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("createTime", SpecialOperator.$lt, standard.parse(timeTo));
            int count = fnChargeMessageService.count(query);
            if (tenantName != null && !"".equals(tenantName.trim())) {
                query.setSpecialOperation("tenantName", SpecialOperator.$like, "%" + tenantName.trim() + "%");
            }

            query.setSkip(Long.valueOf(pageIndex) * pageSize);
            query.setLimit(Long.valueOf(pageSize));
            query.setSort("buildingId", EMSOrder.Asc);
            query.setSort("createTime", EMSOrder.Desc);
            List<FnChargeMessage> dataList = fnChargeMessageService.query(query);
            List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
            if (dataList != null) {
                for (FnChargeMessage chargeMessage : dataList) {
                    Map<String, Object> dataMap = new HashMap<String, Object>();
                    dataMap.put("createTime", standard.format(chargeMessage.getCreateTime()));
                    dataMap.put("tenantName", chargeMessage.getTenantName());
                    dataMap.put("tenantId", chargeMessage.getTenantId());
                    dataMap.put("message", chargeMessage.getContent());
                    dataMap.put("contactName", chargeMessage.getContactName());
                    dataMap.put("mobile", chargeMessage.getContactMobile());
                    dataMap.put("sendStatus", EnumMessageSendStatus.valueOf(chargeMessage.getStatus()).getView());
                    resultList.add(dataMap);
                }
            }
            HashMap<String, Object> contentMap = new HashMap<>();
            contentMap.put("count", count);
            contentMap.put("dataList", resultList);
            if (isDownload.intValue() == 0) {
                content.add(contentMap);
            } else {
                Map<String, String> result = new HashMap<String, String>();
                result.put("id", this.recordMessageBuildExcel(resultList,timeFrom, timeTo));
                content.add(result);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCRecordMessageService");
        }
    }

    /**
     * 操作记录 —— 发送短信记录 —— 下载
     * @throws Exception
     */
    @SuppressWarnings("deprecation")
    private String recordMessageBuildExcel(List<Map<String,Object>> dataList,String timeFrom ,String timeTo) throws Exception{
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");

        HSSFCellStyle style = wb.createCellStyle();
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        {//创建标题
            HSSFRow row = sheet.createRow((short) 0);
            String[] titleArray = {"短信时间","租户名称","租户编号","短信内容","收信人","手机号","发送状态"};
            for(int i=0;i<titleArray.length;i++)
            {
                HSSFCell cell = row.createCell((short)i);
                cell.setCellStyle(style);
                cell.setCellValue(titleArray[i]);
            }
        }
        //创建内容
        if(dataList != null){
            for(int i=0;i<dataList.size();i++){
                HSSFRow row = sheet.createRow((short) i+1);
                Map<String,Object> obj = dataList.get(i);
                {
                    HSSFCell cell = row.createCell((short)0);
                    cell.setCellValue((String)obj.get("createTime"));
                }
                {
                    HSSFCell cell = row.createCell((short)1);
                    cell.setCellValue((String)obj.get("tenantName"));
                }
                {
                    HSSFCell cell = row.createCell((short)2);
                    cell.setCellValue((String)obj.get("tenantId"));
                }
                {
                    HSSFCell cell = row.createCell((short)3);
                    cell.setCellValue((String)obj.get("message"));
                }

                {
                    HSSFCell cell = row.createCell((short)4);
                    cell.setCellValue((String)obj.get("contactName"));
                }
                {
                    HSSFCell cell = row.createCell((short)5);
                    cell.setCellValue((String)obj.get("mobile"));
                }
                {
                    HSSFCell cell = row.createCell((short)6);
                    cell.setCellValue((String)obj.get("sendStatus"));
                }
            }
        }
        String path = getPath();
        String subdirectory = getRecordMessageSubdirectory();
        String resourceId = UUID.randomUUID().toString();

        String fileDir = Paths.get(path, subdirectory).toString();
        File file = new File(fileDir);
        if(!file.exists()){
            file.mkdirs();
        }

        String newFilePath = Paths.get(path, subdirectory,resourceId).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            if(fout!=null){
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if(wb!=null){
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
        //保存文件
        StringBuilder name = new StringBuilder("短信记录");
        Date to = DateUtils.addDays(standard.parse(timeTo), -1);
        Date from = standard.parse(timeFrom);
        timeFrom = standard.format(from);
        name.append("-").append(timeFrom.substring(0, 10)).append("~").append(standard.format(to).substring(0,10));
        FileResource resource = new FileResource();
        resource.setId(resourceId);
        resource.setName(name.toString());
        resource.setSuffix("xls");
        resource.setSubdirectory(subdirectory);
        fnFileResourceService.save(resource);
        //返回资源ID
        return resourceId;
    }


    private String getRecordMessageSubdirectory(){
        return File.separator + "finein" + File.separator + "tenant_manage"+File.separator + "back_config"+File.separator + "record";
    }


    /**
     * 操作记录 —— 仪表操作记录
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCRecordMeterOperateService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult recordMeterOperate(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");
            Integer isDownload = (Integer) dto.get("isDownload");

            if (timeFrom == null || timeTo == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnRecordMeterOperate query = new FnRecordMeterOperate();
            query.setSpecialOperation("operateTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("operateTime", SpecialOperator.$lt, standard.parse(timeTo));
            int count = fnRecordMeterOperateService.count(query);
            if (isDownload.intValue() == 0) {
                if (pageIndex == null || pageSize == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
                query.setSkip(Long.valueOf(pageIndex) * pageSize);
                query.setLimit(Long.valueOf(pageSize));
            }
            query.setSort("operateTime", EMSOrder.Desc);
            List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
            List<FnRecordMeterOperate> quetyList = fnRecordMeterOperateService.query(query);
            for (FnRecordMeterOperate fnRecordMeterOperate : quetyList) {
                HashMap<String, Object> map = new HashMap<String, Object>();
                map.put("meterId", fnRecordMeterOperate.getMeterId());
                map.put("protocolId", fnRecordMeterOperate.getProtocolId());
                map.put("tenantId", fnRecordMeterOperate.getTenantId());
                map.put("tenantName", fnRecordMeterOperate.getTenantName());
                map.put("operateType", fnRecordMeterOperate.getOperateType());
                String str = fnRecordMeterOperate.getValue();
                Map valueMap = SystemConstant.jsonMapper.readValue(str, Map.class);
                StringBuffer stringBuffer = new StringBuffer();
                if (valueMap.get("type") != null) {
                    EnumPriceDetail priceDetail = EnumPriceDetail.parse((String) valueMap.get("type"));
                    stringBuffer.append(priceDetail.getView() + ":");
                }
                stringBuffer.append(valueMap.get("value"));
                if (valueMap.get("unit") != null) {
                    stringBuffer.append((String) valueMap.get("unit"));
                }
                map.put("value", stringBuffer.toString());
                map.put("createTime", fnRecordMeterOperate.getOperateTime());
                map.put("request", fnRecordMeterOperate.getRequest());
                map.put("respond", fnRecordMeterOperate.getRespond());
                map.put("userName", fnRecordMeterOperate.getUserName());
                resultList.add(map);
            }
            if (isDownload == 0) {
                HashMap<String, Object> contentMap = new HashMap<>();
                contentMap.put("count", count);
                contentMap.put("dataList", resultList);
                content.add(contentMap);
            } else {
                String subdirectory = pathUtil.getTempDownloadSubDir();
                String path = pathUtil.getPath();
                String fileDir = Paths.get(path, subdirectory).toString();
                File file = new File(fileDir);
                if (!file.exists()) {
                    file.mkdirs();
                }
                FileResource fileResource = new FileResource();
                String id = UUID.randomUUID().toString();
                fileResource.setId(id);
                StringBuffer stringBuffer = new StringBuffer();

                stringBuffer.append("仪表操作记录").append("-").append(standard.format(standard.parse(timeFrom)).substring(0, 10));
                Date date = DateUtils.addDays(standard.parse(timeTo), -1);
                stringBuffer.append("_").append(standard.format(date).substring(0,10));
                fileResource.setName(stringBuffer.toString());
                fileResource.setSubdirectory(subdirectory);
                fileResource.setSuffix("xls");
                this.meterOperateBuildExcel(path, subdirectory, id, resultList);
                fnFileResourceService.save(fileResource);
                Map<String, Object> newContentObj = new HashMap<>();
                newContentObj.put("id", id);
                content.add(newContentObj);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCRecordMeterOperateService");
        }
    }

    /**
     * 操作记录 —— 仪表操作记录 —— 下载
     */
    @SuppressWarnings("deprecation")
    private void meterOperateBuildExcel(String path, String subdirectory, String id, List<Map<String, Object>> result) {
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        // 创建标题
        HSSFRow row0 = sheet.createRow((short) 0);

        {
            HSSFCell cell = row0.createCell((short) 0);
            cell.setCellStyle(style);
            cell.setCellValue("仪表ID");
        }

        {
            HSSFCell cell = row0.createCell((short) 1);
            cell.setCellStyle(style);
            cell.setCellValue("协议编码");
        }
        {
            HSSFCell cell = row0.createCell((short) 2);
            cell.setCellStyle(style);
            cell.setCellValue("租户ID");
        }
        {
            HSSFCell cell = row0.createCell((short) 3);
            cell.setCellStyle(style);
            cell.setCellValue("租户名称");
        }
        {
            HSSFCell cell = row0.createCell((short) 4);
            cell.setCellStyle(style);
            cell.setCellValue("操作类型");
        }
        {
            HSSFCell cell = row0.createCell((short) 5);
            cell.setCellStyle(style);
            cell.setCellValue("操作值");
        }

        {
            HSSFCell cell = row0.createCell((short) 6);
            cell.setCellStyle(style);
            cell.setCellValue("操作时间");
        }

        {
            HSSFCell cell = row0.createCell((short) 7);
            cell.setCellStyle(style);
            cell.setCellValue("请求");
        }

        {
            HSSFCell cell = row0.createCell((short) 8);
            cell.setCellStyle(style);
            cell.setCellValue("呼应");
        }

        {
            HSSFCell cell = row0.createCell((short) 9);
            cell.setCellStyle(style);
            cell.setCellValue("操作人");
        }

        if (result != null) {
            int currentRow = 1;
            for (int i = 0; i < result.size(); i++) {
                Map<String, Object> object = result.get(i);
                String protocolId = (String) object.get("protocolId");
                String tenantId = (String) object.get("tenantId");
                String tenantName = (String) object.get("tenantName");
                String meterId = (String) object.get("meterId");
                Integer operateType = (Integer) object.get("operateType");
                String value = (String) object.get("value");
                Date operateTime = (Date) object.get("createTime");
                String request = (String) object.get("request");
                String respond = (String) object.get("respond");
                String userName = (String) object.get("userName");
                HSSFRow row = sheet.createRow((short) currentRow);
                {
                    HSSFCell cell = row.createCell((short) (0));
                    cell.setCellValue(meterId == null ? " " : meterId);
                }
                {
                    HSSFCell cell = row.createCell((short) (1));
                    cell.setCellValue(protocolId == null ? " " : protocolId);
                }
                {
                    HSSFCell cell = row.createCell((short) (2));
                    cell.setCellValue(tenantId == null ? " " : tenantId);
                }
                {
                    HSSFCell cell = row.createCell((short) (3));
                    cell.setCellValue(tenantName == null ? " " : tenantName);
                }
                {
                    HSSFCell cell = row.createCell((short) (4));
                    cell.setCellValue(operateType == null ? " " : EnumMeterSetType.valueOf(operateType).getView());
                }
                {
                    HSSFCell cell = row.createCell((short) (5));
                    cell.setCellValue(value == null ? " " : value);
                }

                {
                    HSSFCell cell = row.createCell((short) (6));
                    cell.setCellValue(operateTime == null ? " " : standard.format(operateTime));
                }
                {
                    HSSFCell cell = row.createCell((short) (7));
                    cell.setCellValue(request == null ? " " : request);
                }
                {
                    HSSFCell cell = row.createCell((short) (8));
                    cell.setCellValue(respond == null ? " " : respond);
                }
                {
                    HSSFCell cell = row.createCell((short) (9));
                    cell.setCellValue(userName == null ? " " : userName);
                }
                currentRow++;
            }
        }
        String newFilePath = Paths.get(path, subdirectory, id).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fout != null) {
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }

    }

    /**
     * 操作记录 —— 仪表设置记录
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCRecordMeterSetService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult recordMeterSet(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");
            Integer isDownload = (Integer) dto.get("isDownload");
            Integer setType = (Integer) dto.get("setType");

            if (timeFrom == null || timeTo == null || setType == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnRecordMeterSet query = new FnRecordMeterSet();
            query.setOperateType(setType);
            query.setSpecialOperation("createTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("createTime", SpecialOperator.$lt, standard.parse(timeTo));
            int count = fnRecordMeterSetService.count(query);
            if (isDownload.intValue() == 0) {
                if (pageIndex == null || pageSize == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
                query.setSkip(Long.valueOf(pageIndex) * pageSize);
                query.setLimit(Long.valueOf(pageSize));
            }
            query.setSort("createTime", EMSOrder.Desc);
            List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
            List<FnRecordMeterSet> quetyList = fnRecordMeterSetService.query(query);
            if (quetyList != null && quetyList.size() > 0) {
                Map<String, FnTenant> tenantMap = fnTenantService.queryMap();
                for (FnRecordMeterSet fnRecordMeterSet : quetyList) {
                    HashMap<String, Object> map = new HashMap<String, Object>();
                    map.put("meterId", fnRecordMeterSet.getMeterId());
                    map.put("roomCode", fnRecordMeterSet.getRoomCode());
                    map.put("tenantId", fnRecordMeterSet.getTenantId());
                    FnTenant fnTenant = tenantMap.get(fnRecordMeterSet.getTenantId());
                    if (fnTenant!=null){
                        map.put("tenantName", fnTenant.getName());
                    }
                    map.put("setType", fnRecordMeterSet.getOperateType());
                    map.put("energyTypeName", EnergyTypeUtil.queryEnergyTypeNameById(fnRecordMeterSet.getEnergyTypeId()));
                    String str = fnRecordMeterSet.getExtend();
                    StringBuffer stringBuffer = new StringBuffer();
                    if (fnRecordMeterSet.getOperateType() == EnumMeterSetType.UPDATEPRICE.getValue().intValue()) {
                        Map valueList = SystemConstant.jsonMapper.readValue(str, Map.class);
                        stringBuffer.append(valueList.get("value"));

                    } else {
                        Map valueMap = SystemConstant.jsonMapper.readValue(str, Map.class);
                        stringBuffer.append(valueMap.get("value"));
                        if (valueMap.get("unit") != null) {
                            stringBuffer.append(valueMap.get("unit"));
                        }
                    }
                    map.put("value", stringBuffer.toString());
                    map.put("operateTime", fnRecordMeterSet.getCreateTime());
                    map.put("userName", fnRecordMeterSet.getUserName());
                    resultList.add(map);
                }
            }

            if (isDownload == 0) {
                HashMap<String, Object> contentMap = new HashMap<>();
                contentMap.put("count", count);
                contentMap.put("dataList", resultList);
                content.add(contentMap);
            } else {
                String subdirectory = pathUtil.getTempDownloadSubDir();
                String path = pathUtil.getPath();
                String fileDir = Paths.get(path, subdirectory).toString();
                File file = new File(fileDir);
                if (!file.exists()) {
                    file.mkdirs();
                }
                FileResource fileResource = new FileResource();
                String id = UUID.randomUUID().toString();
                fileResource.setId(id);
                StringBuffer stringBuffer = new StringBuffer();

                stringBuffer.append("仪表操作记录").append("-")
                        .append(standard.format(standard.parse(timeFrom)).substring(0, 10));
                Date date = DateUtils.addDays(standard.parse(timeTo), -1);
                stringBuffer.append("_").append(standard.format(date).substring(0, 10));
                fileResource.setName(stringBuffer.toString());
                fileResource.setSubdirectory(subdirectory);
                fileResource.setSuffix("xls");
                this.meterSetBuildExcel(path, subdirectory, id, resultList);
                fnFileResourceService.save(fileResource);
                Map<String, Object> newContentObj = new HashMap<>();
                newContentObj.put("id", id);
                content.add(newContentObj);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCRecordMeterSetService");
        }
    }

    /**
     * 操作记录 —— 仪表设置记录 —— 下载
     */
    @SuppressWarnings("deprecation")
    private void meterSetBuildExcel(String path, String subdirectory, String id, List<Map<String, Object>> result) {
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        // 创建标题
        HSSFRow row0 = sheet.createRow((short) 0);

        {
            HSSFCell cell = row0.createCell((short) 0);
            cell.setCellStyle(style);
            cell.setCellValue("仪表ID");
        }

        {
            HSSFCell cell = row0.createCell((short) 1);
            cell.setCellStyle(style);
            cell.setCellValue("租户ID");
        }
        {
            HSSFCell cell = row0.createCell((short) 2);
            cell.setCellStyle(style);
            cell.setCellValue("租户名称");
        }
        {
            HSSFCell cell = row0.createCell((short) 3);
            cell.setCellStyle(style);
            cell.setCellValue("能耗類型");
        }
        {
            HSSFCell cell = row0.createCell((short) 4);
            cell.setCellStyle(style);
            cell.setCellValue("操作类型");
        }

        {
            HSSFCell cell = row0.createCell((short) 5);
            cell.setCellStyle(style);
            cell.setCellValue("操作值");
        }

        {
            HSSFCell cell = row0.createCell((short) 6);
            cell.setCellStyle(style);
            cell.setCellValue("操作时间");
        }

        {
            HSSFCell cell = row0.createCell((short) 7);
            cell.setCellStyle(style);
            cell.setCellValue("操作人");
        }

        if (result != null) {
            int currentRow = 1;
            for (int i = 0; i < result.size(); i++) {
                Map<String, Object> object = result.get(i);
                String tenantId = (String) object.get("tenantId");
                String tenantName = (String) object.get("tenantName");
                String energyTypeName = (String) object.get("energyTypeName");
                String meterId = (String) object.get("meterId");
                Integer operateType = (Integer) object.get("setType");
                String value = (String) object.get("value");
                Date operateTime = (Date) object.get("createTime");
                String userName = (String) object.get("userName");
                HSSFRow row = sheet.createRow((short) currentRow);
                {
                    HSSFCell cell = row.createCell((short) (0));
                    cell.setCellValue(meterId == null ? " " : meterId);
                }
                {
                    HSSFCell cell = row.createCell((short) (1));
                    cell.setCellValue(tenantId == null ? " " : tenantId);
                }
                {
                    HSSFCell cell = row.createCell((short) (2));
                    cell.setCellValue(tenantName == null ? " " : tenantName);
                }
                {
                    HSSFCell cell = row.createCell((short) (3));
                    cell.setCellValue(energyTypeName == null ? " " : energyTypeName);
                }
                {
                    HSSFCell cell = row.createCell((short) (4));
                    cell.setCellValue(operateType == null ? " " : EnumMeterSetType.valueOf(operateType).getView());
                }
                {
                    HSSFCell cell = row.createCell((short) (5));
                    cell.setCellValue(value == null ? " " : value);
                }

                {
                    HSSFCell cell = row.createCell((short) (6));
                    cell.setCellValue(operateTime == null ? " " : standard.format(operateTime));
                }
                {
                    HSSFCell cell = row.createCell((short) (7));
                    cell.setCellValue(userName == null ? " " : userName);
                }
                currentRow++;
            }
        }
        String newFilePath = Paths.get(path, subdirectory, id).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fout != null) {
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }

    }

    /**
     * 操作记录 —— 结算记录
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCRecordPostClearingPayService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult recordPostClearingPay(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String) dto.get("energyTypeId");
            String buildingId = (String) dto.get("buildingId");
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            String tenantName = (String) dto.get("tenantName");
            Integer isDownload = (Integer) dto.get("isDownload");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");

            if (timeFrom == null || timeTo == null || isDownload == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            if(isDownload.intValue() == 0){
                if (pageIndex == null || pageSize == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
            }
            String currentSumEnergyUnit ="";
            if(energyTypeId!=null&&!"".equals(energyTypeId)){
                currentSumEnergyUnit= UnitUtil.getCumulantUnit(energyTypeId);
            }
            FnRecordPostClearingPay query = new FnRecordPostClearingPay();
            query.setEnergyTypeId(energyTypeId);
            query.setBuildingId(buildingId);
            query.setSpecialOperation("createTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("createTime", SpecialOperator.$lt, standard.parse(timeTo));
            if(tenantName != null && !"".equals(tenantName.trim())){
                query.setSpecialOperation("tenantName", SpecialOperator.$like, "%"+tenantName.trim()+"%");
            }
            int count = fnRecordPostClearingPayService.count(query);
            if(isDownload.intValue() == 0){
                query.setSkip(Long.valueOf(pageIndex) * pageSize);
                query.setLimit(Long.valueOf(pageSize));
            }
            query.setSort("buildingId", EMSOrder.Asc);
            query.setSort("createTime", EMSOrder.Desc);

            List<FnRecordPostClearingPay> dataList = fnRecordPostClearingPayService.query(query);

            List<Map<String,Object>> resultList = new ArrayList<Map<String,Object>>();
            if(dataList != null){
                for(FnRecordPostClearingPay fnRecordPostClearingPay : dataList){
                    Map<String,Object> dataMap = new HashMap<String,Object>();
                    dataMap.put("createTime", standard.format(fnRecordPostClearingPay.getCreateTime()));
                    dataMap.put("tenantId", fnRecordPostClearingPay.getTenantId());
                    dataMap.put("tenantName", fnRecordPostClearingPay.getTenantName());
                    dataMap.put("orderId", fnRecordPostClearingPay.getOrderId());
                    dataMap.put("lastClearingTime", fnRecordPostClearingPay.getLastClearingTime() == null ? "--" : standard.format(fnRecordPostClearingPay.getLastClearingTime()));
                    dataMap.put("currentClearingTime", fnRecordPostClearingPay.getCurrentClearingTime() == null ? "--" : standard.format(fnRecordPostClearingPay.getCurrentClearingTime()));
                    dataMap.put("currentSumEnergy", fnRecordPostClearingPay.getCurrentSumEnergy() == null ? 0.0 : DoubleFormatUtil.Instance().getDoubleData_00(fnRecordPostClearingPay.getCurrentSumEnergy()));
//				dataMap.put("currentSumEnergyUnit", fnRecordPostClearingPay.getCurrentSumEnergyUnit());
                    dataMap.put("money", fnRecordPostClearingPay.getMoney() == null ? 0.0 : DoubleFormatUtil.Instance().getDoubleData_00(fnRecordPostClearingPay.getMoney()));
                    dataMap.put("userName", fnRecordPostClearingPay.getUserName());
                    dataMap.put("energyTypeId", fnRecordPostClearingPay.getEnergyTypeId());
                    dataMap.put("roomIds", fnRecordPostClearingPay.getRoomIds());
                    resultList.add(dataMap);
                }
            }
            HashMap<String, Object> contentMap = new HashMap<>();
            contentMap.put("count", count);
            contentMap.put("currentSumEnergyUnit", currentSumEnergyUnit);
            contentMap.put("dataList", resultList);

            if(isDownload.intValue() == 0){
                content.add(contentMap);
            }else{
                Map<String,String> result = new HashMap<String,String>();
                result.put("id", this.recordPostClearingPayBuildExcel(energyTypeId,resultList,timeFrom,timeTo));
                content.add(result);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCRecordPostClearingPayService");
        }
    }

    /**
     * 操作记录 —— 结算记录 —— 下载
     * @throws Exception
     */
    @SuppressWarnings("deprecation")
    private String recordPostClearingPayBuildExcel(String energyTypeId,List<Map<String,Object>> dataList,String timeFrom,String timeTo) throws Exception{
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");

        HSSFCellStyle style = wb.createCellStyle();
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        {//创建标题
            HSSFRow row = sheet.createRow((short) 0);
            String unit = UnitUtil.getDataUnit(energyTypeId, "LeiJiLiang", null);
            String[] titleArray = {"操作时间","账单号","租户名称","上次结算日期","本次结算日期","本期结算能耗("+unit+")","本期结算金额","操作人","租户编号","房间编号"};
            for(int i=0;i<titleArray.length;i++)
            {
                HSSFCell cell = row.createCell((short)i);
                cell.setCellStyle(style);
                cell.setCellValue(titleArray[i]);
            }
        }
        //创建内容
        if(dataList != null){
            for(int i=0;i<dataList.size();i++){
                HSSFRow row = sheet.createRow((short) i+1);
                Map<String,Object> obj = dataList.get(i);
                {
                    HSSFCell cell = row.createCell((short)0);
                    cell.setCellValue((String)obj.get("createTime"));
                }
                {
                    HSSFCell cell = row.createCell((short)1);
                    cell.setCellValue((String)obj.get("orderId"));
                }
                {
                    HSSFCell cell = row.createCell((short)2);
                    cell.setCellValue((String)obj.get("tenantName"));
                }
                {
                    HSSFCell cell = row.createCell((short)3);
                    cell.setCellValue((String)obj.get("lastClearingTime"));
                }
                {
                    HSSFCell cell = row.createCell((short)4);
                    cell.setCellValue((String)obj.get("currentClearingTime"));
                }
                {
                    HSSFCell cell = row.createCell((short)5);
                    cell.setCellValue((Double)obj.get("currentSumEnergy"));
                }
                {
                    HSSFCell cell = row.createCell((short)6);
                    cell.setCellValue((Double)obj.get("money"));
                }
                {
                    HSSFCell cell = row.createCell((short)7);
                    cell.setCellValue((String)obj.get("userName"));
                }
                {
                    HSSFCell cell = row.createCell((short)8);
                    cell.setCellValue((String)obj.get("tenantId"));
                }
                {
                    HSSFCell cell = row.createCell((short)9);
                    cell.setCellValue((String)obj.get("roomIds"));
                }
            }
        }
        String path = getPath();
        String subdirectory = getRecordPostClearingPaySubdirectory();
        String resourceId = UUID.randomUUID().toString();

        String fileDir = Paths.get(path, subdirectory).toString();
        File file = new File(fileDir);
        if(!file.exists()){
            file.mkdirs();
        }

        String newFilePath = Paths.get(path, subdirectory,resourceId).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            if(fout!=null){
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if(wb!=null){
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
        //保存文件
        StringBuilder name = new StringBuilder("结算记录");
        if(energyTypeId!=null){
            for(FnEnergyType fnEnergyType: ConstantDBBaseData.EnergyTypeList){
                if(fnEnergyType.getId().equals(energyTypeId)){
                    name.append("-").append(fnEnergyType.getName());
                }
            }
        }
        Date to = DateUtils.addDays(standard.parse(timeTo), -1);
        Date from = standard.parse(timeFrom);
        timeFrom = standard.format(from);
        name.append("-").append(timeFrom.substring(0, 10)).append("~").append(standard.format(to).substring(0, 10));
        FileResource resource = new FileResource();
        resource.setId(resourceId);
        resource.setName(name.toString());
        resource.setSuffix("xls");
        resource.setSubdirectory(subdirectory);
        fnFileResourceService.save(resource);
        //返回资源ID
        return resourceId;
    }

    private String getRecordPostClearingPaySubdirectory(){
        return File.separator + "finein" + File.separator + "tenant_manage"+File.separator + "back_config"+File.separator + "record";
    }


    /**
     * 操作记录 —— 缴费记录
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCRecordPostPayService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult recordPostPay(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            String tenantName = (String) dto.get("tenantName");
            Integer isDownload = (Integer) dto.get("isDownload");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");

            if (timeFrom == null || timeTo == null || isDownload == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            if(isDownload.intValue() == 0){
                if (pageIndex == null || pageSize == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
            }

            FnRecordPostPay query = new FnRecordPostPay();
            query.setBuildingId(buildingId);
            query.setSpecialOperation("operateTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("operateTime", SpecialOperator.$lt, standard.parse(timeTo));

            if(tenantName != null && !"".equals(tenantName.trim())){
                query.setSpecialOperation("tenantName", SpecialOperator.$like, "%"+tenantName.trim()+"%");
            }
            int count = fnRecordPostPayService.count(query);
            query.setSort("buildingId", EMSOrder.Asc);
            query.setSort("operateTime", EMSOrder.Desc);
            if(isDownload.intValue() == 0){
                query.setSkip(Long.valueOf(pageIndex) * pageSize);
                query.setLimit(Long.valueOf(pageSize));
            }
            List<FnRecordPostPay> dataList = fnRecordPostPayService.query(query);
            HashMap<String, Object> contentMap = new HashMap<>();
            contentMap.put("count", count);
            contentMap.put("dataList", dataList);
            if(isDownload.intValue() == 0){
                content.add(contentMap);
            }else{
                Map<String,String> result = new HashMap<String,String>();
                result.put("id", this.recordPostPayBuildExcel(dataList,timeFrom,timeTo));
                content.add(result);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCRecordPostPayService");
        }
    }

    /**
     * 操作记录 —— 缴费记录 —— 下载
     * @param dataList
     * @param timeFrom
     * @param timeTo
     * @return
     * @throws Exception
     */
    @SuppressWarnings("deprecation")
    private String recordPostPayBuildExcel(List<FnRecordPostPay> dataList,String timeFrom,String timeTo) throws Exception{
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");

        HSSFCellStyle style = wb.createCellStyle();
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        {//创建标题
            HSSFRow row = sheet.createRow((short) 0);
            String[] titleArray = {"操作时间","租户名称","账单","账单号","缴费金额（元）","操作人","租户编号","房间编号"};
            for(int i=0;i<titleArray.length;i++)
            {
                HSSFCell cell = row.createCell((short)i);
                cell.setCellStyle(style);
                cell.setCellValue(titleArray[i]);
            }
        }
        //创建内容
        if(dataList != null){
            for(int i=0;i<dataList.size();i++){
                HSSFRow row = sheet.createRow((short) i+1);
                FnRecordPostPay obj = dataList.get(i);
                {
                    HSSFCell cell = row.createCell((short)0);
                    cell.setCellValue(standard.format(obj.getOperateTime()));
                }
                {
                    HSSFCell cell = row.createCell((short)1);
                    cell.setCellValue(obj.getTenantName());
                }
                {
                    HSSFCell cell = row.createCell((short)2);
                    cell.setCellValue(obj.getOrderTime());
                }
                {
                    HSSFCell cell = row.createCell((short)3);
                    cell.setCellValue(obj.getOrderId());
                }
                {
                    HSSFCell cell = row.createCell((short)4);
                    cell.setCellValue(DoubleFormatUtil.Instance().getDoubleData_00(obj.getMoney()));
                }
                {
                    HSSFCell cell = row.createCell((short)5);
                    cell.setCellValue(obj.getUserName() == null ? " " : obj.getUserName());
                }
                {
                    HSSFCell cell = row.createCell((short)6);
                    cell.setCellValue(obj.getTenantId()== null ? " " : obj.getTenantId());
                }
                {
                    HSSFCell cell = row.createCell((short)7);
                    cell.setCellValue(obj.getRoomIds() == null ? " " : obj.getRoomIds());
                }
            }
        }
        String path = getPath();
        String subdirectory = getRecordPostPaySubdirectory();
        String resourceId = UUID.randomUUID().toString();

        String fileDir = Paths.get(path, subdirectory).toString();
        File file = new File(fileDir);
        if(!file.exists()){
            file.mkdirs();
        }

        String newFilePath = Paths.get(path, subdirectory,resourceId).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            if(fout!=null){
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if(wb!=null){
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
        //保存文件
        FileResource resource = new FileResource();
        resource.setId(resourceId);
        StringBuilder name = new StringBuilder("缴费记录");
        Date to = DateUtils.addDays(standard.parse(timeTo), -1);
        Date from = standard.parse(timeFrom);
        timeFrom = standard.format(from);
        name.append("-").append(timeFrom.substring(0, 10)).append("~").append(standard.format(to).substring(0,10));
        resource.setName(name.toString());
        resource.setSuffix("xls");
        resource.setSubdirectory(subdirectory);
        fnFileResourceService.save(resource);
        //返回资源ID
        return resourceId;
    }


    private String getRecordPostPaySubdirectory(){
        return File.separator + "finein" + File.separator + "tenant_manage"+File.separator + "back_config"+File.separator + "record";
    }

    /**
     * 操作记录 —— 充值记录
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCRecordPrePayService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult recordPrePay(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String) dto.get("energyTypeId");
            String buildingId = (String) dto.get("buildingId");
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            String name = (String) dto.get("name");
            Integer isDownload = (Integer) dto.get("isDownload");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");

            if (timeFrom == null || timeTo == null || isDownload == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            if(isDownload.intValue() == 0){
                if (pageIndex == null || pageSize == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
            }
            String amountUnit="";
            if(energyTypeId!=null){
                amountUnit = UnitUtil.getCumulantUnit(energyTypeId);
            }
            FnRecordPrePay query = new FnRecordPrePay();
            query.setEnergyTypeId(energyTypeId);
            query.setBuildingId(buildingId);
            query.setSpecialOperation("operateTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("operateTime", SpecialOperator.$lt, standard.parse(timeTo));

            if(name != null && !"".equals(name.trim())){
                query.setSpecialOperation("name", SpecialOperator.$like, "%"+name.trim()+"%");
            }
            int count = fnRecordPrePayService.count(query);
            if(isDownload.intValue() == 0){
                query.setSkip(Long.valueOf(pageIndex) * pageSize);
                query.setLimit(Long.valueOf(pageSize));
            }
            query.setSort("buildingId", EMSOrder.Asc);
            query.setSort("operateTime", EMSOrder.Desc);

            List<FnRecordPrePay> dataList = fnRecordPrePayService.query(query);
            List<String> perPayIds = new ArrayList<>();
            for (FnRecordPrePay pays:dataList){
                perPayIds.add(pays.getId());
            }
            List<FnRecordPayChannel> fnRecordPayChannels = new ArrayList<>();
            if (dataList.size()>0) {
                FnRecordPayChannel query1 = new FnRecordPayChannel();
                query.setSpecialOperation("recordPayID", SpecialOperator.$in, perPayIds);
                fnRecordPayChannels = fnRecordPayChannelService.query(query1);
            }
            Map<String,Object> RecordPayChannelsMap = new HashMap<>();
            for (FnRecordPayChannel channel :fnRecordPayChannels){
                RecordPayChannelsMap.put(channel.getRecordPayID(),channel.getChannelType());
            }
            List<Map<String,Object>> resultList = new ArrayList<Map<String,Object>>();
            if(dataList != null){
                for(FnRecordPrePay fnRecordPrePay : dataList){
                    Map<String,Object> dataMap = new HashMap<String,Object>();
                    dataMap.put("createTime", standard.format(fnRecordPrePay.getOperateTime()));
                    if(fnRecordPrePay.getType().intValue() == EnumPayBodyType.TENANT.getValue().intValue()){
                        dataMap.put("tenantName", fnRecordPrePay.getName() == null ? " " : fnRecordPrePay.getName()+"("+fnRecordPrePay.getTenantId()+")");
                        dataMap.put("meterId", "--");
                    }else{
                        dataMap.put("meterId", fnRecordPrePay.getCode() == null ? " " : fnRecordPrePay.getCode());
                        dataMap.put("tenantName", fnRecordPrePay.getTenantName() == null ? " " : fnRecordPrePay.getTenantName() +"("+fnRecordPrePay.getTenantId()+")");
                    }
                    dataMap.put("orderId", fnRecordPrePay.getOrderId());
                    dataMap.put("money", fnRecordPrePay.getMoney() == null ? 0.0 : DoubleFormatUtil.Instance().getDoubleData_00(fnRecordPrePay.getMoney()));
                    dataMap.put("amount", fnRecordPrePay.getAmount() == null ? 0.0 : DoubleFormatUtil.Instance().getDoubleData_00(fnRecordPrePay.getAmount()));
//				dataMap.put("amountUnit", fnRecordPrePay.getAmountUnit());
                    dataMap.put("userName", fnRecordPrePay.getUserName());
                    dataMap.put("energyTypeId", fnRecordPrePay.getEnergyTypeId());
                    String s = "";
                    if (RecordPayChannelsMap.get(fnRecordPrePay.getId()) != null && RecordPayChannelsMap.get(fnRecordPrePay.getId()).equals(EnumPayChannelType.WECHATNORMALPAY.getValue())){
                        s = EnumPayChannelType.WECHATNORMALPAY.getView();
                    }else if (RecordPayChannelsMap.get(fnRecordPrePay.getId()) != null && RecordPayChannelsMap.get(fnRecordPrePay.getId()).equals(EnumPayChannelType.ERRORBILLFILL.getValue())){
                        s = EnumPayChannelType.ERRORBILLFILL.getView();
                    }
                    dataMap.put("channelType",s);
                    resultList.add(dataMap);
                }
            }
            HashMap<String, Object> contentMap = new HashMap<>();
            contentMap.put("count", count);
            contentMap.put("amountUnit", amountUnit);
            contentMap.put("dataList", resultList);
            if(isDownload.intValue() == 0){
                content.add(contentMap);
            }else{
                Map<String,String> result = new HashMap<String,String>();
                result.put("id", this.recordPrePayBuildExcel(resultList,energyTypeId,timeFrom,timeTo));
                content.add(result);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCRecordPrePayService");
        }
    }

    /**
     * 操作记录 —— 充值记录 —— 下载
     * @throws Exception
     */
    @SuppressWarnings("deprecation")
    private String recordPrePayBuildExcel(List<Map<String,Object>> dataList,String energyTypeId,String timeFrom ,String timeTo) throws Exception{
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");

        HSSFCellStyle style = wb.createCellStyle();
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        {//创建标题
            HSSFRow row = sheet.createRow((short) 0);
            String[] titleArray = {"操作时间","充值单号","租户名称(编号)","仪表ID","充值类型","充值金额(元)","充值量","操作人"};
            for(int i=0;i<titleArray.length;i++)
            {
                HSSFCell cell = row.createCell((short)i);
                cell.setCellStyle(style);
                cell.setCellValue(titleArray[i]);
            }
        }
        //创建内容
        if(dataList != null){
            for(int i=0;i<dataList.size();i++){
                HSSFRow row = sheet.createRow((short) i+1);
                Map<String,Object> obj = dataList.get(i);
                {
                    HSSFCell cell = row.createCell((short)0);
                    cell.setCellValue((String)obj.get("createTime"));
                }
                {
                    HSSFCell cell = row.createCell((short)1);
                    cell.setCellValue((String)obj.get("orderId"));
                }
                {
                    HSSFCell cell = row.createCell((short)2);
                    cell.setCellValue((String)obj.get("tenantName"));
                }
                {
                    HSSFCell cell = row.createCell((short)3);
                    cell.setCellValue((String)obj.get("meterId"));
                }
                {
                    HSSFCell cell = row.createCell((short)4);
                    cell.setCellValue((String) obj.get("channelType"));
                }
                {
                    HSSFCell cell = row.createCell((short)5);
                    cell.setCellValue((Double)obj.get("money"));
                }
                {
                    HSSFCell cell = row.createCell((short)6);
                    cell.setCellValue((Double)obj.get("amount"));
                }
                {
                    HSSFCell cell = row.createCell((short)7);
                    cell.setCellValue((String)obj.get("userName"));
                }
            }
        }
        String path = getPath();
        String subdirectory = getRecordPrePaySubdirectory();
        String resourceId = UUID.randomUUID().toString();

        String fileDir = Paths.get(path, subdirectory).toString();
        File file = new File(fileDir);
        if(!file.exists()){
            file.mkdirs();
        }

        String newFilePath = Paths.get(path, subdirectory,resourceId).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            if(fout!=null){
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if(wb!=null){
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
        //保存文件
        StringBuilder name = new StringBuilder("充值记录");
        if(energyTypeId!=null){
            for(FnEnergyType fnEnergyType: ConstantDBBaseData.EnergyTypeList){
                if(fnEnergyType.getId().equals(energyTypeId)){
                    name.append("-").append(fnEnergyType.getName());
                }
            }
        }
        Date to = DateUtils.addDays(standard.parse(timeTo), -1);
        Date from = standard.parse(timeFrom);
        timeFrom = standard.format(from);
        name.append("-").append(timeFrom.substring(0, 10)).append("~").append(standard.format(to).substring(0,10));
        FileResource resource = new FileResource();
        resource.setId(resourceId);
        resource.setName(name.toString());
        resource.setSuffix("xls");
        resource.setSubdirectory(subdirectory);
        fnFileResourceService.save(resource);
        //返回资源ID
        return resourceId;
    }


    private String getRecordPrePaySubdirectory(){
        return File.separator + "finein" + File.separator + "tenant_manage"+File.separator + "back_config"+File.separator + "record";
    }

    /**
     * 操作记录 —— 变更价格方案记录
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCRecordPriceChangeService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult recordPriceChange(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String) dto.get("energyTypeId");
            String buildingId = (String) dto.get("buildingId");
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            String tenantName = (String) dto.get("tenantName");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");

            if (timeFrom == null || timeTo == null ) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnRecordPriceChange query = new FnRecordPriceChange();
            query.setEnergyTypeId(energyTypeId);
            query.setBuildingId(buildingId);
            query.setSpecialOperation("changeTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("changeTime", SpecialOperator.$lt, standard.parse(timeTo));
            if(tenantName != null && !"".equals(tenantName.trim())){
                query.setSpecialOperation("tenantName", SpecialOperator.$like, "%"+tenantName.trim()+"%");
            }
            int count = fnRecordPriceChangeService.count(query);
            query.setSkip(Long.valueOf(pageIndex) * pageSize);
            query.setLimit(Long.valueOf(pageSize));

            query.setSort("buildingId", EMSOrder.Desc);
            query.setSort("energyTypeId", EMSOrder.Desc);
            query.setSort("changeTime", EMSOrder.Desc);
            query.setSort("createTime", EMSOrder.Desc);

            List<FnRecordPriceChange> dataList = fnRecordPriceChangeService.query(query);
            HashMap<String, Object> contentMap = new HashMap<>();
            contentMap.put("count", count);
            contentMap.put("dataList", dataList);
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCRecordPriceChangeService");
        }
    }

    /**
     * 操作记录 —— 变更价格方案记录 —— 下载
     * @throws Exception
     */
    @SuppressWarnings({ "deprecation", "unused" })
    private String recordPriceChangeBuildExcel(List<FnRecordPriceChange> dataList) throws Exception{
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");

        HSSFCellStyle style = wb.createCellStyle();
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        {//创建标题
            HSSFRow row = sheet.createRow((short) 0);
            String[] titleArray = {"操作时间","租户名称","修改前价格方案","修改后价格方案","操作人","租户编号","所在建筑","房间编号"};
            for(int i=0;i<titleArray.length;i++)
            {
                HSSFCell cell = row.createCell((short)i);
                cell.setCellStyle(style);
                cell.setCellValue(titleArray[i]);
            }
        }
        //创建内容
        if(dataList != null){
            for(int i=0;i<dataList.size();i++){
                HSSFRow row = sheet.createRow((short) i+1);
                FnRecordPriceChange obj = dataList.get(i);
                {
                    HSSFCell cell = row.createCell((short)0);
                    cell.setCellValue(standard.format(obj.getChangeTime()));
                }
                {
                    HSSFCell cell = row.createCell((short)1);
                    cell.setCellValue(obj.getTenantName());
                }
                {
                    HSSFCell cell = row.createCell((short)2);
                    cell.setCellValue(obj.getBeforePriceName());
                }
                {
                    HSSFCell cell = row.createCell((short)3);
                    cell.setCellValue(obj.getAfterPriceName());
                }
                {
                    HSSFCell cell = row.createCell((short)4);
                    cell.setCellValue(obj.getUserName() == null ? " " : obj.getUserName());
                }
                {
                    HSSFCell cell = row.createCell((short)5);
                    cell.setCellValue(obj.getTenantId() == null ? " " : obj.getTenantId());
                }
                {
                    HSSFCell cell = row.createCell((short)6);
                    cell.setCellValue(obj.getBuildingName() == null ? " " : obj.getBuildingName());
                }
                {
                    HSSFCell cell = row.createCell((short)7);
                    cell.setCellValue(obj.getRoomIds() == null ? " " : obj.getRoomIds());
                }
            }
        }
        String path = getPath();
        String subdirectory = getRecordPriceChangeSubdirectory();
        String resourceId = UUID.randomUUID().toString();

        String fileDir = Paths.get(path, subdirectory).toString();
        File file = new File(fileDir);
        if(!file.exists()){
            file.mkdirs();
        }

        String newFilePath = Paths.get(path, subdirectory,resourceId).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            if(fout!=null){
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if(wb!=null){
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
        //保存文件
        FileResource resource = new FileResource();
        resource.setId(resourceId);
        resource.setName("变更价格方案");
        resource.setSuffix("xls");
        resource.setSubdirectory(subdirectory);
        fnFileResourceService.save(resource);
        //返回资源ID
        return resourceId;
    }


    private String getRecordPriceChangeSubdirectory(){
        return File.separator + "finein" + File.separator + "tenant_manage"+File.separator + "back_config"+File.separator + "record";
    }


    /**
     * 操作记录 —— 退费记录
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCRecordReturnService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult recordReturn(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String) dto.get("energyTypeId");
            String buildingId = (String) dto.get("buildingId");
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            String name = (String) dto.get("name");
            Integer isDownload = (Integer) dto.get("isDownload");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");

            if (timeFrom == null || timeTo == null || isDownload == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            if(isDownload.intValue() == 0){
                if (pageIndex == null || pageSize == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
            }
            String amountUnit="";
            if(energyTypeId!=null){
                amountUnit = UnitUtil.getCumulantUnit(energyTypeId);
            }
            FnRecordReturn query = new FnRecordReturn();
            query.setEnergyTypeId(energyTypeId);
            query.setBuildingId(buildingId);
            query.setSpecialOperation("operateTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("operateTime", SpecialOperator.$lt, standard.parse(timeTo));

            if(name != null && !"".equals(name.trim())){
                query.setSpecialOperation("name", SpecialOperator.$like, "%"+name.trim()+"%");
            }
            int count = fnRecordReturnService.count(query);
            if(isDownload.intValue() == 0){
                query.setSkip(Long.valueOf(pageIndex) * pageSize);
                query.setLimit(Long.valueOf(pageSize));
            }
            query.setSort("buildingId", EMSOrder.Asc);
            query.setSort("operateTime", EMSOrder.Desc);

            List<FnRecordReturn> dataList = fnRecordReturnService.query(query);

            List<Map<String,Object>> resultList = new ArrayList<Map<String,Object>>();
            if(dataList != null){
                for(FnRecordReturn recordReturn : dataList){
                    Map<String,Object> dataMap = new HashMap<String,Object>();
                    dataMap.put("createTime", standard.format(recordReturn.getOperateTime()));
                    if(recordReturn.getType().intValue() == EnumPayBodyType.TENANT.getValue().intValue()){
                        dataMap.put("tenantName", recordReturn.getName() == null ? " " : recordReturn.getName()+"("+recordReturn.getTenantId()+")");
                        dataMap.put("meterId", "--");
                    }else{
                        dataMap.put("meterId", recordReturn.getCode() == null ? " " : recordReturn.getCode());
                        dataMap.put("tenantName", recordReturn.getTenantName() == null ? " " : recordReturn.getTenantName() +"("+recordReturn.getTenantId()+")");
                    }
                    dataMap.put("orderId", recordReturn.getOrderId());
                    dataMap.put("money", recordReturn.getMoney() == null ? "--" : DoubleFormatUtil.Instance().getDoubleData_00(recordReturn.getMoney())+"");
                    dataMap.put("amount", recordReturn.getAmount() == null ? "--" : DoubleFormatUtil.Instance().getDoubleData_00(recordReturn.getAmount())+"");
//				dataMap.put("amountUnit", fnRecordPrePay.getAmountUnit());
                    dataMap.put("userName", recordReturn.getUserName());
                    dataMap.put("energyTypeId", recordReturn.getEnergyTypeId());
                    resultList.add(dataMap);
                }
            }
            HashMap<String, Object> contentMap = new HashMap<>();
            contentMap.put("count", count);
            contentMap.put("amountUnit", amountUnit);
            contentMap.put("dataList", resultList);
            if(isDownload.intValue() == 0){
                content.add(contentMap);
            }else{
                Map<String,String> result = new HashMap<String,String>();
                result.put("id", this.recordReturnBuildExcel(resultList,energyTypeId,timeFrom,timeTo));
                content.add(result);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCRecordReturnService");
        }
    }

    /**
     * 操作记录 —— 退费记录 —— 下载
     * @throws Exception
     */
    @SuppressWarnings("deprecation")
    private String recordReturnBuildExcel(List<Map<String,Object>> dataList,String energyTypeId,String timeFrom ,String timeTo) throws Exception{
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");

        HSSFCellStyle style = wb.createCellStyle();
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        {//创建标题
            HSSFRow row = sheet.createRow((short) 0);
            String[] titleArray = {"操作时间","退费单号","租户名称(编号)","仪表ID","退费金额(元)","退费量","操作人"};
            for(int i=0;i<titleArray.length;i++)
            {
                HSSFCell cell = row.createCell((short)i);
                cell.setCellStyle(style);
                cell.setCellValue(titleArray[i]);
            }
        }
        //创建内容
        if(dataList != null){
            for(int i=0;i<dataList.size();i++){
                HSSFRow row = sheet.createRow((short) i+1);
                Map<String,Object> obj = dataList.get(i);
                {
                    HSSFCell cell = row.createCell((short)0);
                    cell.setCellValue((String)obj.get("createTime"));
                }
                {
                    HSSFCell cell = row.createCell((short)1);
                    cell.setCellValue((String)obj.get("orderId"));
                }
                {
                    HSSFCell cell = row.createCell((short)2);
                    cell.setCellValue((String)obj.get("tenantName"));
                }
                {
                    HSSFCell cell = row.createCell((short)3);
                    cell.setCellValue((String)obj.get("meterId"));
                }

                {
                    HSSFCell cell = row.createCell((short)4);
                    cell.setCellValue((String)obj.get("money"));
                }
                {
                    HSSFCell cell = row.createCell((short)5);
                    cell.setCellValue((String)obj.get("amount"));
                }
                {
                    HSSFCell cell = row.createCell((short)6);
                    cell.setCellValue((String)obj.get("userName"));
                }
            }
        }
        String path = getPath();
        String subdirectory = getRecordReturnSubdirectory();
        String resourceId = UUID.randomUUID().toString();

        String fileDir = Paths.get(path, subdirectory).toString();
        File file = new File(fileDir);
        if(!file.exists()){
            file.mkdirs();
        }

        String newFilePath = Paths.get(path, subdirectory,resourceId).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            if(fout!=null){
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if(wb!=null){
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
        //保存文件
        StringBuilder name = new StringBuilder("退费记录");
        if(energyTypeId!=null){
            for(FnEnergyType fnEnergyType: ConstantDBBaseData.EnergyTypeList){
                if(fnEnergyType.getId().equals(energyTypeId)){
                    name.append("-").append(fnEnergyType.getName());
                }
            }
        }
        Date to = DateUtils.addDays(standard.parse(timeTo), -1);
        Date from = standard.parse(timeFrom);
        timeFrom = standard.format(from);
        name.append("-").append(timeFrom.substring(0, 10)).append("~").append(standard.format(to).substring(0,10));
        FileResource resource = new FileResource();
        resource.setId(resourceId);
        resource.setName(name.toString());
        resource.setSuffix("xls");
        resource.setSubdirectory(subdirectory);
        fnFileResourceService.save(resource);
        //返回资源ID
        return resourceId;
    }


    private String getRecordReturnSubdirectory(){
        return File.separator + "finein" + File.separator + "tenant_manage"+File.separator + "back_config"+File.separator + "record";
    }

    /**
     * 操作记录 —— 激活退租记录
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCRecordTenantOperateService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult recordTenantOperate(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer type = (Integer) dto.get("type");//0 激活 1 退租
            String buildingId = (String) dto.get("buildingId");
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            String tenantName = (String) dto.get("tenantName");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");

            if (timeFrom == null || timeTo == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            if (pageIndex == null || pageSize == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnRecordTenantOperate query = new FnRecordTenantOperate();
            query.setBuildingId(buildingId);
            query.setSpecialOperation("operteTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("operteTime", SpecialOperator.$lt, standard.parse(timeTo));
            query.setOperateType(type);
            int count = fnRecordTenantOperateService.count(query);
            if(tenantName != null && !"".equals(tenantName.trim())){
                query.setSpecialOperation("tenantName", SpecialOperator.$like, "%"+tenantName.trim()+"%");
            }

            query.setSkip(Long.valueOf(pageIndex) * pageSize);
            query.setLimit(Long.valueOf(pageSize));
            query.setSort("buildingId", EMSOrder.Asc);
            query.setSort("operateTime", EMSOrder.Desc);
            List<FnRecordTenantOperate> dataList = fnRecordTenantOperateService.query(query);
            HashMap<String, Object> contentMap = new HashMap<>();
            contentMap.put("count", count);
            contentMap.put("dataList", dataList);
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCRecordTenantOperateService");
        }
    }

}
