package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnRoom;
import com.persagy.finein.fnconfig.handler.FNCConfigDownloadBaseRoomHandler;
import com.persagy.finein.service.FNRoomMeterService;
import com.persagy.finein.service.FNRoomService;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:基础处理
*/
@Component("FNCConfigDownloadBaseRoomHandler")
public class FNCConfigDownloadBaseRoomHandlerImpl extends CoreServiceImpl implements FNCConfigDownloadBaseRoomHandler {
	
	@Resource(name="FNRoomService")
	private FNRoomService FNRoomService;

	@Resource(name="FNRoomMeterService")
	private FNRoomMeterService FNRoomMeterService;
	
	@Override
	public void process(File srcFile,File destFile,String buildingId) throws Exception {
		FileInputStream is = new FileInputStream(srcFile);// 创建文件流  
        XSSFWorkbook wb = new XSSFWorkbook(is);// 加载文件流  
        XSSFCellStyle cellStyle = wb.createCellStyle();
        XSSFDataFormat format = wb.createDataFormat();
        cellStyle.setDataFormat(format.getFormat("@"));
        XSSFSheet sheet = wb.getSheetAt(0);
        FileOutputStream fout = null;
        List<FnRoom> roomList = FNRoomService.queryListByBuildingId(buildingId);
        
        Map<String, List<DTOMeter>> roomMeterMap = FNRoomMeterService.queryRoomMeter(buildingId);
        
		if(roomList != null && roomList.size() > 0){
			for(int i=0;i<roomList.size();i++){
				XSSFRow row = sheet.getRow(3+i);
				if(row == null){
					row = sheet.createRow(3+i);
				}
                row.createCell(1).setCellValue(roomList.get(i).getCode());  
                row.createCell(2).setCellValue(roomList.get(i).getFloorId());   
                row.createCell(3).setCellValue(roomList.get(i).getArea());  
                
                String dianMeters = "";
                String shuiMeters = "";
                String reShuiMeters = "";
                String ranQiMeters = "";
                
                if(roomMeterMap.get(roomList.get(i).getCode()) != null){
                	List<DTOMeter> meterList = roomMeterMap.get(roomList.get(i).getCode());
                	if(meterList != null && meterList.size() > 0){
                		for(DTOMeter meter : meterList){
                			switch (meter.getEnergyTypeId()) {
							case FineinConstant.EnergyType.Dian:
								dianMeters = dianMeters.equals("") ? meter.getMeterId() : dianMeters + "," + meter.getMeterId();
								break;
							case FineinConstant.EnergyType.Shui:
								shuiMeters = shuiMeters.equals("") ? meter.getMeterId() : shuiMeters + "," + meter.getMeterId();
								break;
							case FineinConstant.EnergyType.ReShui:
								reShuiMeters = reShuiMeters.equals("") ? meter.getMeterId() : reShuiMeters + "," + meter.getMeterId();
								break;
							case FineinConstant.EnergyType.RanQi:
								ranQiMeters = ranQiMeters.equals("") ? meter.getMeterId() : ranQiMeters + "," + meter.getMeterId();
								break;
							default:
								break;
							}
                		}
                	}
                }
                
                XSSFCell cell;
                {//电表
                	cell = row.createCell(4);
                	cell.setCellStyle(cellStyle);
                	cell.setCellValue(dianMeters);  
                }
                {//水表
                	cell = row.createCell(5);
                	cell.setCellStyle(cellStyle);
                	cell.setCellValue(shuiMeters);   
                }
                {//热水
                	cell = row.createCell(6);
                	cell.setCellStyle(cellStyle);
                	row.createCell(6).setCellValue(reShuiMeters);  
                }
                {//燃气
                	cell = row.createCell(7);
                	cell.setCellStyle(cellStyle);
                	row.createCell(7).setCellValue(ranQiMeters);  
                }
			}
		}
        
		try {
			fout = new FileOutputStream(destFile);  
			wb.write(fout);  
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			if(fout!=null){
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if(wb!=null){
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
			if(is!=null){
				try {
					is.close();
				} catch (IOException e1) {
				}
			}
		}
	}
}

