package com.persagy.finein.fnconfig.handler;

import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.exception.ConfigUploadException;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:基础处理
*/
public interface FNCConfigUploadMeterRanQiHandler{
	
	public void process(Map<String, Object> contentObj, Integer type, String logicCode, Workbook workbook, FileResource fileResource, String userId) throws Exception,ConfigUploadException;
	
}

