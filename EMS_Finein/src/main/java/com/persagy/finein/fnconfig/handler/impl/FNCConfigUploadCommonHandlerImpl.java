package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.exception.ConfigUploadException;
import com.persagy.finein.enumeration.EnumConfigUploadResult;
import com.persagy.finein.fnconfig.handler.FNCConfigUploadCommonHandler;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:仪表处理
*/
@Component("FNCConfigUploadCommonHandler")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNCConfigUploadCommonHandlerImpl  extends CoreServiceImpl implements FNCConfigUploadCommonHandler {

	@Resource(name = "FNCConfigUploadCommonPriceHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigUploadCommonPriceHandler FNCConfigUploadCommonPriceHandler;
	
	@Resource(name = "FNCConfigUploadMeterDianHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigUploadMeterDianHandler FNCConfigUploadMeterDianHandler;
	
	@Resource(name = "FNCConfigUploadMeterShuiHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigUploadMeterShuiHandler FNCConfigUploadMeterShuiHandler;
	
	@Resource(name = "FNCConfigUploadMeterReShuiHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigUploadMeterReShuiHandler FNCConfigUploadMeterReShuiHandler;
	
	@Resource(name = "FNCConfigUploadMeterRanQiHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigUploadMeterRanQiHandler FNCConfigUploadMeterRanQiHandler;
	
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void process(Map<String,Object> contentObj,Integer type,String logicCode,Workbook workbook,FileResource fileResource,String userId) throws Exception,ConfigUploadException{
		switch (logicCode) {
		case FineinConstant.BackConfigType.Common_Price_Config:
			FNCConfigUploadCommonPriceHandler.process(contentObj, type, logicCode, workbook,fileResource,userId);
			break;
		case FineinConstant.BackConfigType.Meter_Dian_Config:
			FNCConfigUploadMeterDianHandler.process(contentObj, type, logicCode, workbook,fileResource,userId);
			break;
		case FineinConstant.BackConfigType.Meter_Shui_Config:
			FNCConfigUploadMeterShuiHandler.process(contentObj, type, logicCode, workbook,fileResource,userId);
			break;
		case FineinConstant.BackConfigType.Meter_ReShui_Config:
			FNCConfigUploadMeterReShuiHandler.process(contentObj, type, logicCode, workbook,fileResource,userId);
			break;
		case FineinConstant.BackConfigType.Meter_RanQi_Config:
			FNCConfigUploadMeterRanQiHandler.process(contentObj, type, logicCode, workbook,fileResource,userId);
			break;
		default:
			contentObj.put("result", EnumConfigUploadResult.Code_2.getValue());
			contentObj.put("message", EnumConfigUploadResult.Code_2.getMessage());
			break;
		}
	}
}

