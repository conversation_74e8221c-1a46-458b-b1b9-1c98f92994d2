package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.pojo.finein.FnPriceTemplate;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.enumeration.EnumPriceType;
import com.persagy.finein.fnconfig.handler.FNCConfigDownloadCommonPriceHandler;
import com.persagy.finein.service.FNPriceTemplateService;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:基础处理
*/
@Component("FNCConfigDownloadCommonPriceHandler")
public class FNCConfigDownloadCommonPriceHandlerImpl extends CoreServiceImpl implements FNCConfigDownloadCommonPriceHandler {
	
	@Resource(name="FNPriceTemplateService")
	private FNPriceTemplateService FNPriceTemplateService;
	
	@Override
	public void process(File srcFile,File destFile) throws Exception {
		FileInputStream is = new FileInputStream(srcFile);// 创建文件流  
        XSSFWorkbook wb = new XSSFWorkbook(is);// 加载文件流  
        XSSFSheet sheet = wb.getSheetAt(0);
        FileOutputStream fout = null;
		List<FnPriceTemplate> priceList = FNPriceTemplateService.queryList();
		if(priceList != null && priceList.size() > 0){
			for(int i=0;i<priceList.size();i++){
				XSSFRow row = sheet.getRow(3+i);
				if(row == null){
					row = sheet.createRow(3+i);
				}
                row.createCell(1).setCellValue(priceList.get(i).getName());  
                row.createCell(2).setCellValue(EnergyTypeUtil.queryEnergyTypeNameById(priceList.get(i).getEnergyTypeId()));  
                if(priceList.get(i).getType().intValue() == EnumPriceType.AVG.getValue().intValue()){
                	 row.createCell(3).setCellValue(EnumPriceType.AVG.getView());
                	 row.createCell(4).setCellValue(DoubleFormatUtil.Instance().getDoubleData(priceList.get(i).getContent()));
                }else{
                	row.createCell(3).setCellValue(EnumPriceType.TOU.getView());  
                	//
                	JSONArray contentValueArray = (JSONArray)JSONValue.parse(priceList.get(i).getContent());
        			if(contentValueArray != null){
        				for(int j=0;j<contentValueArray.size();j++){
        					JSONObject contentValueObj = (JSONObject)contentValueArray.get(j);
        					if("J".equals(contentValueObj.get("type"))){
        						row.createCell(5).setCellValue(DoubleFormatUtil.Instance().getDoubleData(contentValueObj.get("value")));
        					}else if("F".equals(contentValueObj.get("type"))){
        						row.createCell(6).setCellValue(DoubleFormatUtil.Instance().getDoubleData(contentValueObj.get("value")));
        					}else if("G".equals(contentValueObj.get("type"))){
        						row.createCell(7).setCellValue(DoubleFormatUtil.Instance().getDoubleData(contentValueObj.get("value")));
        					}else if("P".equals(contentValueObj.get("type"))){
        						row.createCell(8).setCellValue(DoubleFormatUtil.Instance().getDoubleData(contentValueObj.get("value")));
        					}
        				}
        			}
                }
			}
		}
        
		try {
			fout = new FileOutputStream(destFile);  
			wb.write(fout);  
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			if(fout!=null){
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if(wb!=null){
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
			if(is!=null){
				try {
					is.close();
				} catch (IOException e1) {
				}
			}
		}
	}
}

