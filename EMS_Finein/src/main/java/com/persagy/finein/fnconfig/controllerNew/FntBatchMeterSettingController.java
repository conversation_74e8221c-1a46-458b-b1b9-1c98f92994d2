package com.persagy.finein.fnconfig.controllerNew;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.ems.dto.DTOMeterSetting;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnPriceTemplate;
import com.persagy.ems.pojo.finein.FnRecordMeterSet;
import com.persagy.finein.communication.exception.MeterSetException;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.enumeration.EnumMeterSetType;
import com.persagy.finein.enumeration.EnumPriceDetail;
import com.persagy.finein.enumeration.EnumPriceType;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNPriceTemplateService;
import com.persagy.finein.service.FNRecordMeterSetService;
import com.persagy.finein.service.FNUserService;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 仪表批量操作
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntBatchMeterSettingController extends BaseController {

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @Resource(name = "FNPriceTemplateService")
    private FNPriceTemplateService fnPriceTemplateService;

    @Resource(name = "FNRecordMeterSetService")
    private FNRecordMeterSetService fnRecordMeterSetService;

    /**
     * 仪表批量操作 -- 可批量操作电表列表
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTBatchMeterSettingListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchMeterSettingList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");
            Map<String, Object> contentMap = new HashMap<>();
            List<Map<String, Object>> maps = fnMeterService.queryMeterSetting(pageIndex,pageSize);
            List<DTOMeterSetting>  dtoMeterSettings = new ArrayList<>();
            for (Map<String,Object> map : maps){
                String extend = (String) map.get("extend");
                Map functionMap = SystemConstant.jsonMapper.readValue(extend, Map.class);
                int remainClearNum = (int) functionMap.get("remainClear");
                //int updatePriceNum = (int) functionMap.get("updatePrice");
                if ( remainClearNum == 0 ){
                    continue;
                }
                DTOMeterSetting dtoMeterSetting = new DTOMeterSetting();
                dtoMeterSetting.setMeterId((String) map.get("meterId"));
                dtoMeterSetting.setInstallAddress((String) map.get("installAddress"));
                dtoMeterSetting.setBuildingId((String) map.get("buildingId"));
                dtoMeterSetting.setArea(DoubleFormatUtil.Instance().getDoubleData(map.get("area")));
                dtoMeterSetting.setFloorId((String) map.get("floorId"));
                dtoMeterSettings.add(dtoMeterSetting);
            }
            contentMap.put("count", fnMeterService.queryMeterSettingCount());
            contentMap.put("data",dtoMeterSettings);
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTBatchMeterSettingListService");
        }
    }

    /**
     * 仪表批量操作 -- 价格方案
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTBatchMeterSettingPriceListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchMeterSettingPriceList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer type = (Integer) dto.get("type");
            String energyTypeId = (String)dto.get("energyTypeId");
            if(energyTypeId == null || type == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            FnPriceTemplate query = new FnPriceTemplate();
            if (type.equals(EnumPriceType.AVG.getValue())){
                query.setType(EnumPriceType.AVG.getValue());
            }else if (type.equals(EnumPriceType.TOU.getValue())){
                query.setType(EnumPriceType.TOU.getValue());
            }
            List<Map<String,Object>> map = new ArrayList<>();
            List<FnPriceTemplate> priceTemplates = fnPriceTemplateService.query(query);
            for (FnPriceTemplate template:priceTemplates){
                Map<String,Object> m = new HashMap<>();
                m.put("priceId",template.getId());
                m.put("priceName",template.getName());
                m.put("unit", "元/"+ UnitUtil.getCumulantUnit(energyTypeId));
                if(template.getType().intValue() == EnumPriceType.AVG.getValue().intValue()){
                    m.put("content", DoubleFormatUtil.Instance().doubleFormat(template.getContent(), 4L));
                }else{
                    JSONArray contentValueArray = (JSONArray) JSONValue.parse(template.getContent());
                    List<Object> contentValueList = new ArrayList<Object>();
                    if(contentValueArray != null){
                        for(int i=0;i<contentValueArray.size();i++){
                            JSONObject contentValueObj = (JSONObject)contentValueArray.get(i);
                            Map<String,Object> contentObjMap = new HashMap<String,Object>();
                            contentObjMap.put("type", contentValueObj.get("type"));
                            contentObjMap.put("value", DoubleFormatUtil.Instance().doubleFormat(contentValueObj.get("value"), 4L));
                            contentValueList.add(contentObjMap);
                        }
                    }
                    m.put("content", contentValueList);
                }
                map.add(m);
            }
            content.add(map);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTBatchMeterSettingPriceListService");
        }
    }

    /**
     * 仪表批量操作 -- 批量更改电价或清零
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTBatchMeterSettingService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchMeterSetting(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer type = (Integer) dto.get("type");

            String userId = this.getParamUserId(dto);
            List<String> meterIds = (List<String>) dto.get("meterIds");
            if (type==null || meterIds==null || userId==null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            String priceId = (String) dto.get("priceId");
            if (type.equals(EnumMeterSetType.UPDATEPRICE.getValue()) && (priceId == null || priceId.equals(""))){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            List<FnMeter> fnMeters = fnMeterService.queryMeterByIds(meterIds);
            Map<String,Object> map = new HashMap<>();
            DTOUser user = fnUserService.queryUserByUserId(userId);
            map.put("user", user);
            Map<String,Object> result= new HashMap<>();
            int success = 0;
            int error = 0;
            List<FnRecordMeterSet> saveList = new ArrayList<>();
            for (FnMeter meter : fnMeters){
                try {
                    String extend = null;
                    ICommunication communication = (ICommunication) SystemConstant.context.getBean(meter.getProtocolId());
                    if (communication==null){
                        continue;
                    }
                    boolean falg = false;
                    Map<String,Object> map1 = new HashMap<>();
                    if (type.equals(EnumMeterSetType.REMAINCLEAR.getValue())){
                        falg= communication.clearRemain(meter,map);
                        if (falg){
                            success++;
                            map1.put("value",EnumMeterSetType.REMAINCLEAR.getView());
                            extend = SystemConstant.jsonMapper.writeValueAsString(map1);
                        }else {
                            error++;
                        }
                    }else if (type.equals(EnumMeterSetType.UPDATEPRICE.getValue())){
                        FnPriceTemplate template = fnPriceTemplateService.query(priceId);
                        Double price = DoubleFormatUtil.Instance().getDoubleData(template.getContent());
                        falg =communication.settingPrice(meter, EnumPriceDetail.L, price,map);
                        if (falg){
                            success++;
                            extend = SystemConstant.jsonMapper.writeValueAsString(priceId);
                        }else {
                            error++;
                        }
                    }
                    if (falg){
                        //保存仪表设置记录
                        FnRecordMeterSet save = new FnRecordMeterSet();
                        save.setId(UUID.randomUUID().toString());
                        save.setTenantId("");
                        save.setMeterId(meter.getId());
                        save.setRoomCode(meter.getInstallAddress());
                        save.setEnergyTypeId(meter.getEnergyTypeId());
                        save.setOperateType(type);
                        save.setCreateTime(new Date());
                        save.setUserId(user.getUserId());
                        save.setUserName(user.getUserName());
                        save.setExtend(extend);
                        saveList.add(save);

                    }
                }catch (MeterSetException e){
                    e.printStackTrace();
                }
            }
            if (saveList.size() > 0){
                fnRecordMeterSetService.save(saveList);
            }
            result.put("success",success);
            result.put("error",error);
            content.add(result);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTBatchMeterSettingService");
        }
    }
}
