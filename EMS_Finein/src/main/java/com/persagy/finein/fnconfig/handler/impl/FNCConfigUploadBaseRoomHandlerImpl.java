package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.exception.ConfigUploadException;
import com.persagy.finein.enumeration.EnumConfigUploadResult;
import com.persagy.finein.enumeration.EnumExsistStatus;
import com.persagy.finein.enumeration.EnumUseStatus;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.fnconfig.handler.FNCConfigUploadBaseRoomHandler;
import com.persagy.finein.service.*;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月17日 下午3:14:12
 * 
 * 说明:基础处理
 */
@Component("FNCConfigUploadBaseRoomHandler")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNCConfigUploadBaseRoomHandlerImpl extends FNCConfigUploadHandlerImpl
		implements FNCConfigUploadBaseRoomHandler {

	@Resource(name = "FNRoomService")
	private FNRoomService FNRoomService;

	@Resource(name = "FNRoomMeterService")
	private FNRoomMeterService FNRoomMeterService;

	@Resource(name = "FNFloorService")
	private FNFloorService FNFloorService;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNBackConfigBaseService")
	private FNBackConfigBaseService FNBackConfigBaseService;

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void process(Map<String, Object> contentObj, String buildingId, Integer type, String logicCode,
			Workbook workbook, FileResource fileResource, String userId) throws Exception, ConfigUploadException {
		Sheet sheet = workbook.getSheetAt(0);
		int rowMaxIndex = sheet.getLastRowNum();
		Map<String, List<String>> meterIdMap= new HashMap<String, List<String>>();
		List<FnFloor> dbFloorList = FNFloorService.queryList(buildingId, EMSOrder.Asc);
		Map<String, FnFloor> floorMap = new HashMap<String, FnFloor>();
		if (dbFloorList != null) {
			for (FnFloor floor : dbFloorList) {
				floorMap.put(floor.getId(), floor);
			}
		}
		List<FnRoom> roomList = new ArrayList<FnRoom>();
		Map<String, FnRoom> roomMap = new HashMap<String, FnRoom>();
		// 查询建筑所有房间
		List<FnRoom> dbfnRooms = FNRoomService.queryListByBuildingId(buildingId);
		Map<String, List<String>> dianRoomMeterMap = new HashMap<String, List<String>>();
		Map<String, List<String>> shuiRoomMeterMap = new HashMap<String, List<String>>();
		Map<String, List<String>> reShuiRoomMeterMap = new HashMap<String, List<String>>();
		Map<String, List<String>> ranQiRoomMeterMap = new HashMap<String, List<String>>();
		for (int rowIndex = 3; rowIndex <= rowMaxIndex; rowIndex++) {
			Row row = sheet.getRow(rowIndex);
			if(row==null){
				break;
			}
			FnRoom room = new FnRoom();
			// 编号
			Cell cell = row.getCell(1);
			String value = getCellStringValue(cell, false);

			if (value == null || "".equals(value.trim())) {
				cell = row.getCell(2);
				value = getCellStringValue(cell, false);
				if (value == null || "".equals(value.trim())) {
					break;
				} else {
					throw new ConfigUploadException((rowIndex + 1) + "行2列房间编号不能为空");
				}

			}
			if(value.contains(",")){
				throw new ConfigUploadException((rowIndex + 1) + "行2列房间编号不能包含逗号"+value);
			}
			if (roomMap.containsKey(value)) {
				throw new ConfigUploadException((rowIndex + 1) + "行第2列房间编码不能重复：" + value);
			}

			room.setId(UUID.randomUUID().toString());
			room.setBuildingId(buildingId.trim());
			room.setCode(value.trim());

			// 楼层id
			cell = row.getCell(2);
			value = getCellStringValue(cell, false);
			checkNull(rowIndex, 2, value);
			room.setFloorId(value.trim());

			if (!floorMap.containsKey(room.getFloorId())) {
				throw new ConfigUploadException((rowIndex + 1) + "行3列楼层不存在" + value);
			}

			// 面积
			cell = row.getCell(3);
			value = getCellStringValue(cell, true);
			checkNull(rowIndex, 3, value);
			room.setArea(DoubleFormatUtil.Instance().getDoubleData(value));
			if (room.getArea() == null) {
				throw new ConfigUploadException((rowIndex + 1) + "行4列解析面积异常" + value);
			}

			String dianMeters = null;
			String shuiMeters = null;
			String reShuiMeters = null;
			String ranQiMeters = null;

			List<String> dianMeterList = new ArrayList<String>();
			List<String> shuiMeterList = new ArrayList<String>();
			List<String> reShuiMeterList = new ArrayList<String>();
			List<String> ranQiMeterList = new ArrayList<String>();
			// 电表
			cell = row.getCell(4);
			value = getCellStringValue(cell, false);
			dianMeters = value;

			// 水表
			cell = row.getCell(5);
			value = getCellStringValue(cell, false);
			shuiMeters = value;

			// 热水表
			cell = row.getCell(6);
			value = getCellStringValue(cell, false);
			reShuiMeters = value;

			// 燃气表
			cell = row.getCell(7);
			value = getCellStringValue(cell, false);
			ranQiMeters = value;

			this.stringToList(dianMeters, dianMeterList);
			this.stringToList(shuiMeters, shuiMeterList);
			this.stringToList(reShuiMeters, reShuiMeterList);
			this.stringToList(ranQiMeters, ranQiMeterList);
			List<String> list = new ArrayList<>();
			list.addAll(dianMeterList);
			list.addAll(shuiMeterList);
			list.addAll(reShuiMeterList);
			list.addAll(ranQiMeterList);
			meterIdMap.put(room.getCode(), list);
			String energyTypeIds = "";
			if (dianMeterList.size() > 0) {
				// 查询数据库所有电表Id
				for (String meterId : dianMeterList) {
					FnMeter fnMeter = FNMeterService.queryMeterById(meterId);

					if (fnMeter == null || !FineinConstant.EnergyType.Dian.equals(fnMeter.getEnergyTypeId())) {
						throw new ConfigUploadException((rowIndex + 1) + "行5列解析电表号不存在:" + meterId);
					}

				}
				energyTypeIds = FineinConstant.EnergyType.Dian;
			}
			if (shuiMeterList.size() > 0) {
				// 查询数据库所有水表Id
				for (String meterId : shuiMeterList) {
					FnMeter fnMeter = FNMeterService.queryMeterById(meterId);
					if (fnMeter == null || !FineinConstant.EnergyType.Shui.equals(fnMeter.getEnergyTypeId())) {
						throw new ConfigUploadException((rowIndex + 1) + "行6列解析水表号" + meterId + "不存在");
					}
				}
				if ("".equals(energyTypeIds)) {
					energyTypeIds = FineinConstant.EnergyType.Shui;
				} else {
					energyTypeIds = energyTypeIds + "," + FineinConstant.EnergyType.Shui;
				}
			}
			if (reShuiMeterList.size() > 0) {
				// 查询数据库所有热水表Id
				for (String meterId : reShuiMeterList) {
					FnMeter fnMeter = FNMeterService.queryMeterById(meterId);
					if (fnMeter == null || !FineinConstant.EnergyType.ReShui.equals(fnMeter.getEnergyTypeId())) {
						throw new ConfigUploadException((rowIndex + 1) + "行7列解析热水表号" + meterId + "不存在");
					}
				}
				if ("".equals(energyTypeIds)) {
					energyTypeIds = FineinConstant.EnergyType.ReShui;
				} else {
					energyTypeIds = energyTypeIds + "," + FineinConstant.EnergyType.ReShui;
				}
			}
			if (ranQiMeterList.size() > 0) {
				for (String meterId : ranQiMeterList) {
					FnMeter fnMeter = FNMeterService.queryMeterById(meterId);
					if (fnMeter == null || !FineinConstant.EnergyType.RanQi.equals(fnMeter.getEnergyTypeId())) {
						throw new ConfigUploadException((rowIndex + 1) + "行8列解析燃气表号" + meterId + "不存在");
					}
				}
				if ("".equals(energyTypeIds)) {
					energyTypeIds = FineinConstant.EnergyType.RanQi;
				} else {
					energyTypeIds = energyTypeIds + "," + FineinConstant.EnergyType.RanQi;
				}
			}
			room.setEnergyTypeIds(energyTypeIds);
			room.setStatus(EnumUseStatus.Not_In_Use.getValue());
			room.setCreateTime(new Date());
			room.setIsValid(EnumValidStatus.VALID.getValue());
			room.setCreateUserId(userId);
			roomList.add(room);
			roomMap.put(room.getCode(), room);

			dianRoomMeterMap.put(room.getCode(), dianMeterList);
			shuiRoomMeterMap.put(room.getCode(), shuiMeterList);
			reShuiRoomMeterMap.put(room.getCode(), reShuiMeterList);
			ranQiRoomMeterMap.put(room.getCode(), ranQiMeterList);
		}
		List<FnRoomMeter> roomMeterList = new ArrayList<FnRoomMeter>();
		for (FnRoom room : roomList) {
			List<String> dianMeterList = dianRoomMeterMap.get(room.getCode());
			if (dianMeterList != null) {
				for (String meterId : dianMeterList) {
					FnRoomMeter saveObj = new FnRoomMeter();
					saveObj.setId(UUID.randomUUID().toString());
					saveObj.setBuildingId(buildingId.trim());
					saveObj.setRoomId(room.getId());
					saveObj.setRoomCode(room.getCode());
					saveObj.setEnergyTypeId(FineinConstant.EnergyType.Dian);
					saveObj.setMeterId(meterId);
					roomMeterList.add(saveObj);

				}

			}

			List<String> shuiMeterList = shuiRoomMeterMap.get(room.getCode());
			if (shuiMeterList != null) {
				for (String meterId : shuiMeterList) {
					FnRoomMeter saveObj = new FnRoomMeter();
					saveObj.setId(UUID.randomUUID().toString());
					saveObj.setBuildingId(buildingId.trim());
					saveObj.setRoomId(room.getId());
					saveObj.setRoomCode(room.getCode());
					saveObj.setEnergyTypeId(FineinConstant.EnergyType.Shui);
					saveObj.setMeterId(meterId);
					roomMeterList.add(saveObj);
				}

			}

			List<String> reShuiMeterList = reShuiRoomMeterMap.get(room.getCode());
			if (reShuiMeterList != null) {
				for (String meterId : reShuiMeterList) {
					FnRoomMeter saveObj = new FnRoomMeter();
					saveObj.setId(UUID.randomUUID().toString());
					saveObj.setBuildingId(buildingId.trim());
					saveObj.setRoomId(room.getId());
					saveObj.setRoomCode(room.getCode());
					saveObj.setEnergyTypeId(FineinConstant.EnergyType.ReShui);
					saveObj.setMeterId(meterId);
					roomMeterList.add(saveObj);

				}
			}

			List<String> ranQiMeterList = ranQiRoomMeterMap.get(room.getCode());
			if (ranQiMeterList != null) {
				for (String meterId : ranQiMeterList) {
					FnRoomMeter saveObj = new FnRoomMeter();
					saveObj.setId(UUID.randomUUID().toString());
					saveObj.setBuildingId(buildingId.trim());
					saveObj.setRoomId(room.getId());
					saveObj.setRoomCode(room.getCode());
					saveObj.setEnergyTypeId(FineinConstant.EnergyType.RanQi);
					saveObj.setMeterId(meterId);
					roomMeterList.add(saveObj);
				}
			}
		}

		// 查询所有房间
		Map<String, FnRoom> dbroomMap = new HashMap<String, FnRoom>();
		if (dbfnRooms != null) {
			for (FnRoom fnRoom : dbfnRooms) {
				dbroomMap.put(fnRoom.getCode(), fnRoom);
			}
		}

		List<FnRoom> newRoomList = new ArrayList<FnRoom>();
		FnRoom query = new FnRoom();
		List<FnRoomMeter> removeRoomMeters = new ArrayList<>();
		List<String> meterIds = new ArrayList<String>();
		for (FnRoom room : roomList) {
			if (dbroomMap.containsKey(room.getCode())) {
				FnRoom dbRoom = dbroomMap.get(room.getCode());
				if(dbRoom.getStatus()==EnumUseStatus.In_Use.getValue()){
					//查询数据库中房间已存在的表ID
					List<String> dbMeterIdList = FNRoomMeterService.queryMeterIds(buildingId, room.getCode());
					//上传的房间表ID
					List<String> meterIdList =meterIdMap.get(room.getCode());
					if(dbMeterIdList!=null){
						if(dbMeterIdList.size()!=meterIdList.size()){
							throw new ConfigUploadException("房间：" + room.getCode()+"已被引用，不能修改房间仪表关系"); 
						}
						for (String meterId : meterIdList) {
							FnRoomMeter queryRoomMeter = new FnRoomMeter();
							queryRoomMeter.setBuildingId(buildingId);
							queryRoomMeter.setRoomCode(room.getCode());
							queryRoomMeter.setMeterId(meterId);
							List<FnRoomMeter> list = FNRoomMeterService.query(queryRoomMeter);
							if(list==null||list.size()==0){
								throw new ConfigUploadException("房间：" + room.getCode()+"已被引用，不能修改房间仪表关系"); 
							}
						}
					}else{
						if(meterIdList.size()!=0){
							throw new ConfigUploadException("房间：" + room.getCode()+"已被引用，不能修改房间仪表关系"); 
						}
					}
				}
				FnRoomMeter remove = new FnRoomMeter();
				remove.setRoomId(dbroomMap.get(room.getCode()).getId());
				removeRoomMeters.add(remove);
				// FNRoomMeterService.remove(fnRoomMeter);
				query.setCode(room.getCode());
				query.setBuildingId(buildingId);
				room.setId(null);
				room.setCreateTime(null);
				FNRoomService.update(query, room);
				if(roomMeterList.size()>0){
					for (FnRoomMeter roomMeter : roomMeterList) {
						if (roomMeter.getRoomCode().equals(room.getCode())) {
							roomMeter.setRoomId(dbroomMap.get(room.getCode()).getId());
						}
					}
				}
			} else {
				newRoomList.add(room);
			}
		}
		for (FnRoomMeter roomMeter : roomMeterList) {
			meterIds.add(roomMeter.getMeterId());
		}

		FNRoomMeterService.remove(removeRoomMeters);
		FNRoomMeterService.save(roomMeterList);
		// 更新房间仪表
		FNMeterService.changeMeterStatus(meterIds, EnumUseStatus.In_Use.getValue());
		if (newRoomList.size() > 0) {
			FNRoomService.save(newRoomList);
		}
		FnBackConfigBase queryBase = new FnBackConfigBase();
		queryBase.setLogicCode(logicCode);
		queryBase.setBuildingId(buildingId);

		FnBackConfigBase upload = new FnBackConfigBase();
		upload.setFileId(fileResource.getId());
		upload.setFileName(fileResource.getName());
		upload.setIsExsit(EnumExsistStatus.Exsist.getValue());
		upload.setUploadTime(new Date());
		upload.setUserId(userId);

		FNBackConfigBaseService.update(queryBase, upload);
		contentObj.put("result", EnumConfigUploadResult.Code_0.getValue());
		contentObj.put("message", "");

		/*
		 * if (!"".equals(exsitRoomSb.toString())) { contentObj.put("数据库已存在编码",
		 * exsitRoomSb.toString()); }
		 */

	}
}
