package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.*;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnconfig.handler.FNCConfigDownloadBaseTenantHandler;
import com.persagy.finein.service.*;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:基础处理
*/
@Component("FNCConfigDownloadBaseTenantHandler")
public class FNCConfigDownloadBaseTenantHandlerImpl extends CoreServiceImpl implements FNCConfigDownloadBaseTenantHandler {
	
	@Resource(name="FNRoomService")
	private FNRoomService FNRoomService;

	@Resource(name="FNTenantService")
	private FNTenantService FNTenantService;
	
	@Resource(name="FNTenantPriceService")
	private FNTenantPriceService FNTenantPriceService;
	
	@Resource(name="FNTenantPayTypeService")
	private FNTenantPayTypeService FNTenantPayTypeService;
	
	@Resource(name="FNTenantEnergySplitService")
	private FNTenantEnergySplitService FNTenantEnergySplitService;
	
	@Resource(name="FNPriceTemplateService")
	private FNPriceTemplateService FNPriceTemplateService;
	
	@Override
	public void process(File srcFile,File destFile,String buildingId) throws Exception {
		FileInputStream is = new FileInputStream(srcFile);// 创建文件流  
        XSSFWorkbook wb = new XSSFWorkbook(is);// 加载文件流  
        XSSFSheet sheet = wb.getSheetAt(0);
        FileOutputStream fout = null;
        
        List<FnTenant> tenantList = FNTenantService.queryListByValidStatus(buildingId, EnumValidStatus.VALID);
        
		if(tenantList != null && tenantList.size() > 0){
			
			List<FnTenantType> tenantTypeList = ConstantDBBaseData.TenantTypeList;
			Map<String,String> tenantTypeMap = new HashMap<>();
			for(FnTenantType type : tenantTypeList){
				tenantTypeMap.put(type.getId(), type.getName());
			}
			
			List<FnPriceTemplate> priceList = FNPriceTemplateService.queryList();
			Map<String,FnPriceTemplate> priceMap = new HashMap<>();
			if(priceList != null){
				for(FnPriceTemplate price : priceList){
					priceMap.put(price.getId(), price);
				}
			}
			Map<String,Map<String,FnTenantPayType>> payTypeMap = FNTenantPayTypeService.queryByBuildingId(buildingId);
			
			Map<String, Map<String, FnTenantPrice>> tenantPriceMap = FNTenantPriceService.queryMap();
			Map<String, Map<String, FnTenantEnergySplit>> tenantEnergySplitMap = FNTenantEnergySplitService.queryMap();
			
			for(int i=0;i<tenantList.size();i++){
				XSSFRow row = sheet.getRow(4+i);
				if(row == null){
					row = sheet.createRow(4+i);
				}
				String tenantId = tenantList.get(i).getId();
                row.createCell(1).setCellValue(tenantList.get(i).getId());  
                row.createCell(2).setCellValue(tenantList.get(i).getName());   
                row.createCell(3).setCellValue(tenantList.get(i).getRoomCodes());  
                row.createCell(4).setCellValue(tenantTypeMap.containsKey(tenantList.get(i).getTenantTypeId()) ? tenantTypeMap.get(tenantList.get(i).getTenantTypeId()) : ""); 
                row.createCell(5).setCellValue(tenantList.get(i).getArea()); 
                row.createCell(6).setCellValue(tenantList.get(i).getContactName()); 
                row.createCell(7).setCellValue(tenantList.get(i).getContactMobile()); 
                {//电
                	String energyTypeId = FineinConstant.EnergyType.Dian;
                	if(payTypeMap.containsKey(tenantId) && payTypeMap.get(tenantId).get(energyTypeId) != null){
                		FnTenantPayType payType = payTypeMap.get(tenantId).get(energyTypeId);
                		row.createCell(8).setCellValue("Y"); 
                		//付费类型
                		String view = EnumPayType.valueOf(payType.getPayType()).getView();
                		String chargeView = "充"+EnumPrepayChargeType.valueOf(payType.getPrepayChargeType()).getView();
                		if(view.equals("预付费")){
                			String preView = EnumPrePayType.valueOf(payType.getPrePayType()).getView();
                			row.createCell(9).setCellValue(preView); 
                			row.createCell(10).setCellValue(chargeView); 
                		}else{//后
                			row.createCell(9).setCellValue(view); 
                			row.createCell(10).setCellValue(chargeView); 
                		}
                		{//价格
                			String priceId = tenantPriceMap.get(tenantId).get(energyTypeId).getPriceTemplateId();
                			FnPriceTemplate priceTemplate = priceMap.get(priceId);
                			Integer priceType = EnumPriceType.valueOf(priceTemplate.getType()).getValue();
                			row.createCell(11).setCellValue(priceType==0?"N":"Y"); 
                			row.createCell(12).setCellValue(priceTemplate.getName()); 
                		}
                		{//拆分
                			if(tenantEnergySplitMap.containsKey(tenantId) && tenantEnergySplitMap.get(tenantId).get(energyTypeId) != null){
                				FnTenantEnergySplit split = tenantEnergySplitMap.get(tenantId).get(energyTypeId);
                				row.createCell(13).setCellValue("Y"); 
                				row.createCell(14).setCellValue(split.getExpression()); 
                			}else{
                				row.createCell(13).setCellValue("N"); 
                			}
                		}
                	}else{
                		row.createCell(8).setCellValue("N"); 
                	}
                }
                {//水
                	String energyTypeId = FineinConstant.EnergyType.Shui;
                	if(payTypeMap.containsKey(tenantId) && payTypeMap.get(tenantId).get(energyTypeId) != null){
                		FnTenantPayType payType = payTypeMap.get(tenantId).get(energyTypeId);
                		row.createCell(15).setCellValue("Y"); 
                		//付费类型
                		String view = EnumPayType.valueOf(payType.getPayType()).getView();
                		String chargeView = "充"+EnumPrepayChargeType.valueOf(payType.getPrepayChargeType()).getView();
                		if(view.equals("预付费")){
                			String preView = EnumPrePayType.valueOf(payType.getPrePayType()).getView();
                			row.createCell(16).setCellValue(preView); 
                			row.createCell(17).setCellValue(chargeView); 
                		}else{//后
                			row.createCell(16).setCellValue(view); 
                			row.createCell(17).setCellValue(chargeView); 
                		}
                		{//价格
                			String priceId = tenantPriceMap.get(tenantId).get(energyTypeId).getPriceTemplateId();
                			FnPriceTemplate priceTemplate = priceMap.get(priceId);
                			row.createCell(18).setCellValue(priceTemplate.getName()); 
                		}
                		{//拆分
                			if(tenantEnergySplitMap.containsKey(tenantId) && tenantEnergySplitMap.get(tenantId).get(energyTypeId) != null){
                				FnTenantEnergySplit split = tenantEnergySplitMap.get(tenantId).get(energyTypeId);
                				row.createCell(19).setCellValue("Y"); 
                				row.createCell(20).setCellValue(split.getExpression()); 
                			}else{
                				row.createCell(19).setCellValue("N"); 
                			}
                		}
                	}else{
                		row.createCell(15).setCellValue("N"); 
                	}
                }
                {//热水
                	String energyTypeId = FineinConstant.EnergyType.ReShui;
                	if(payTypeMap.containsKey(tenantId) && payTypeMap.get(tenantId).get(energyTypeId) != null){
                		FnTenantPayType payType = payTypeMap.get(tenantId).get(energyTypeId);
                		row.createCell(21).setCellValue("Y"); 
                		//付费类型
                		String chargeView = "充"+EnumPrepayChargeType.valueOf(payType.getPrepayChargeType()).getView();
                		String view = EnumPayType.valueOf(payType.getPayType()).getView();
                		if(view.equals("预付费")){
                			String preView = EnumPrePayType.valueOf(payType.getPrePayType()).getView();
                			row.createCell(22).setCellValue(preView); 
                			row.createCell(23).setCellValue(chargeView); 
                		}else{//后
                			row.createCell(22).setCellValue(view); 
                			row.createCell(23).setCellValue(chargeView); 
                		}
                		{//价格
                			String priceId = tenantPriceMap.get(tenantId).get(energyTypeId).getPriceTemplateId();
                			FnPriceTemplate priceTemplate = priceMap.get(priceId);
                			row.createCell(24).setCellValue(priceTemplate.getName()); 
                		}
                		{//拆分
                			if(tenantEnergySplitMap.containsKey(tenantId) && tenantEnergySplitMap.get(tenantId).get(energyTypeId) != null){
                				FnTenantEnergySplit split = tenantEnergySplitMap.get(tenantId).get(energyTypeId);
                				row.createCell(25).setCellValue("Y"); 
                				row.createCell(26).setCellValue(split.getExpression()); 
                			}else{
                				row.createCell(25).setCellValue("N"); 
                			}
                		}
                	}else{
                		row.createCell(21).setCellValue("N"); 
                	}
                }
                {//燃气
                	String energyTypeId = FineinConstant.EnergyType.RanQi;
                	if(payTypeMap.containsKey(tenantId) && payTypeMap.get(tenantId).get(energyTypeId) != null){
                		FnTenantPayType payType = payTypeMap.get(tenantId).get(energyTypeId);
                		row.createCell(27).setCellValue("Y"); 
                		//付费类型
                		String view = EnumPayType.valueOf(payType.getPayType()).getView();
                		String chargeView = "充"+EnumPrepayChargeType.valueOf(payType.getPrepayChargeType()).getView();
                		if(view.equals("预付费")){
                			String preView = EnumPrePayType.valueOf(payType.getPrePayType()).getView();
                			row.createCell(28).setCellValue(preView); 
                			row.createCell(29).setCellValue(chargeView); 
                		}else{//后
                			row.createCell(28).setCellValue(view); 
                			row.createCell(29).setCellValue(chargeView); 
                		}
                		{//价格
                			String priceId = tenantPriceMap.get(tenantId).get(energyTypeId).getPriceTemplateId();
                			FnPriceTemplate priceTemplate = priceMap.get(priceId);
                			row.createCell(30).setCellValue(priceTemplate.getName()); 
                		}
                		{//拆分
                			if(tenantEnergySplitMap.containsKey(tenantId) && tenantEnergySplitMap.get(tenantId).get(energyTypeId) != null){
                				FnTenantEnergySplit split = tenantEnergySplitMap.get(tenantId).get(energyTypeId);
                				row.createCell(31).setCellValue("Y"); 
                				row.createCell(32).setCellValue(split.getExpression()); 
                			}else{
                				row.createCell(31).setCellValue("N"); 
                			}
                		}
                	}else{
                		row.createCell(27).setCellValue("N"); 
                	}
                }  
			}
		}
        
		try {
			fout = new FileOutputStream(destFile);  
			wb.write(fout);  
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			if(fout!=null){
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if(wb!=null){
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
			if(is!=null){
				try {
					is.close();
				} catch (IOException e1) {
				}
			}
		}
	}
}

