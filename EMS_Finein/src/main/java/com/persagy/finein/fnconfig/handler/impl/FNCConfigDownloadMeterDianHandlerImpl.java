package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnProtocol;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumCommunicationType;
import com.persagy.finein.fnconfig.handler.FNCConfigDownloadMeterDianHandler;
import com.persagy.finein.service.FNMeterService;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:基础处理
*/
@Component("FNCConfigDownloadMeterDianHandler")
public class FNCConfigDownloadMeterDianHandlerImpl extends CoreServiceImpl implements FNCConfigDownloadMeterDianHandler {
	
	@Resource(name="FNMeterService")
	private FNMeterService FNMeterService;
	
	@Override
	public void process(File srcFile,File destFile) throws Exception {
		FileInputStream is = new FileInputStream(srcFile);// 创建文件流  
		XSSFWorkbook wb = new XSSFWorkbook(is);// 加载文件流  
		XSSFSheet sheet = wb.getSheetAt(0);
        FileOutputStream fout = null;
        List<FnMeter> meterList = FNMeterService.queryMeterByEnergyTypeId(FineinConstant.EnergyType.Dian);
		if(meterList != null && meterList.size() > 0){
			for(int i=0;i<meterList.size();i++){
				XSSFRow row = sheet.getRow(3+i);
				if(row == null){
					row = sheet.createRow(3+i);
				}
                row.createCell(1).setCellValue(meterList.get(i).getId()); 
                String serviceId = null;
                Double kkValue = null;
                Long ljlIsCt = null;
                Long syjIsCt = null;
                Long glIsCt = null;
                String address = null;
                String config_user_id = null;
                Long isSanXiang = null;
                try {
                	JSONObject extendObj = (JSONObject)JSONValue.parse(meterList.get(i).getExtend());
                    serviceId = (String)extendObj.get("serviceId");
                    kkValue = DoubleFormatUtil.Instance().getDoubleData(extendObj.get("kkValue"));
                    ljlIsCt = (Long)extendObj.get("ljlIsCt");
                    syjIsCt = (Long)extendObj.get("syjIsCt");
                    glIsCt = (Long)extendObj.get("glIsCt");
                    address = (String)extendObj.get("address");
                    config_user_id = (String)extendObj.get("config_user_id");
                    try {
						isSanXiang = (Long)extendObj.get("isSanXiang");
					} catch (Exception e) {
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
                row.createCell(2).setCellValue(serviceId == null ? "" : serviceId); 
                row.createCell(3).setCellValue(kkValue == null ? "" : kkValue+""); 
                row.createCell(4).setCellValue(meterList.get(i).getRadio()); 
                row.createCell(5).setCellValue(glIsCt == null || glIsCt.intValue() == 0? "N" : "Y"); 
                row.createCell(6).setCellValue(ljlIsCt == null || ljlIsCt.intValue() == 0? "N" : "Y"); 
                row.createCell(7).setCellValue(syjIsCt == null || syjIsCt.intValue() == 0? "N" : "Y"); 
                row.createCell(8).setCellValue(EnumCommunicationType.valueOf(meterList.get(i).getCommunicationType()).getView()); 
                row.createCell(9).setCellValue(meterList.get(i).getClientIp()); 
                row.createCell(10).setCellValue(meterList.get(i).getClientPort()==null?"":meterList.get(i).getClientPort()+""); 
                row.createCell(11).setCellValue(meterList.get(i).getServerIp()); 
                row.createCell(12).setCellValue(meterList.get(i).getServerPort()==null?"":meterList.get(i).getServerPort()+""); 
                row.createCell(13).setCellValue(address); 
                row.createCell(14).setCellValue(config_user_id); 
                FnProtocol protocol = ConstantDBBaseData.ProtocolMap.get(meterList.get(i).getProtocolId());
                row.createCell(15).setCellValue(protocol.getId()+"#"+protocol.getName()); 
                row.createCell(16).setCellValue(meterList.get(i).getInstallAddress()); 
                row.createCell(17).setCellValue(isSanXiang == null || isSanXiang.intValue() == 1? "Y" : "N"); 
			}
		}
        
		try {
			fout = new FileOutputStream(destFile);  
			wb.write(fout);  
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			if(fout!=null){
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if(wb!=null){
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
			if(is!=null){
				try {
					is.close();
				} catch (IOException e1) {
				}
			}
		}
	}
}

