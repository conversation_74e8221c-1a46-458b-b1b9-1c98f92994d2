package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.finein.enumeration.EnumConfigType;
import com.persagy.finein.fnconfig.handler.FNCConfigDownloadCommonHandler;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:仪表处理
*/
@Component("FNCConfigDownloadCommonHandler")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNCConfigDownloadCommonHandlerImpl  extends CoreServiceImpl implements FNCConfigDownloadCommonHandler {

	@Resource(name = "FNCConfigDownloadCommonPriceHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigDownloadCommonPriceHandler FNCConfigDownloadCommonPriceHandler;
	
	@Resource(name = "FNCConfigDownloadMeterDianHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigDownloadMeterDianHandler FNCConfigDownloadMeterDianHandler;
	
	@Resource(name = "FNCConfigDownloadMeterShuiHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigDownloadMeterShuiHandler FNCConfigDownloadMeterShuiHandler;
	
	@Resource(name = "FNCConfigDownloadMeterReShuiHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigDownloadMeterReShuiHandler FNCConfigDownloadMeterReShuiHandler;
	
	@Resource(name = "FNCConfigDownloadMeterRanQiHandler")
	private com.persagy.finein.fnconfig.handler.FNCConfigDownloadMeterRanQiHandler FNCConfigDownloadMeterRanQiHandler;
	
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void process(EnumConfigType configType,File srcFile,File destFile) throws Exception{
		switch (configType) {
		case Common_Price_Config:
			FNCConfigDownloadCommonPriceHandler.process(srcFile,destFile);
			break;
		case Meter_Dian_Config:
			FNCConfigDownloadMeterDianHandler.process(srcFile,destFile);
			break;
		case Meter_Shui_Config:
			FNCConfigDownloadMeterShuiHandler.process(srcFile,destFile);
			break;
		case Meter_ReShui_Config:
			FNCConfigDownloadMeterReShuiHandler.process(srcFile,destFile);
			break;
		case Meter_RanQi_Config:
			FNCConfigDownloadMeterRanQiHandler.process(srcFile,destFile);
			break;
		default:
			throw new Exception("配置类型不存在");
		}
	}
}

