package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.mvc.service.CoreService;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.finein.FnProtocol;
import com.persagy.ems.pojo.finein.FnRoom;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.exception.ConfigUploadException;
import com.persagy.finein.enumeration.*;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:
*/
public class FNCConfigUploadHandlerImpl extends CoreServiceImpl implements CoreService{
	
	
	public String getCellStringValue(Cell cell, boolean isDouble) throws ConfigUploadException {
		String value = null;
		if (cell != null) {
			switch (cell.getCellType()) {
			case Cell.CELL_TYPE_STRING:
				value = cell.getStringCellValue();
				break;
			case Cell.CELL_TYPE_NUMERIC:
				if (isDouble) {
					value = new BigDecimal(new Double(cell.getNumericCellValue())).toString();
				} else {
					if (DateUtil.isCellDateFormatted(cell)) {
						value = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(cell.getDateCellValue());
					} else {
						value = new BigDecimal(new Double(cell.getNumericCellValue()).toString()).intValue() + "";
					}
				}

				break;
			case Cell.CELL_TYPE_BOOLEAN:
				value = "" + cell.getBooleanCellValue();
				break;
			case Cell.CELL_TYPE_BLANK:
				value = "";
				break;
			case Cell.CELL_TYPE_ERROR:
				throw new ConfigUploadException("不能辨析CELL_TYPE_ERROR");
			case Cell.CELL_TYPE_FORMULA:
				value = cell.getCellFormula();
				break;
			}
		} else {
			value = "";
		}
		return value.trim();
	}
	
	public void checkNull(int row,int column,String value) throws ConfigUploadException {
		if (value == null || "".equals(value)) {
			throw new ConfigUploadException((row+1)+"行"+(column+1)+"列不可为空");
		}
	}
	
	public Integer getYorNIntValue(int row,int column,String value) throws ConfigUploadException{
		try {
			if("Y".equals(value.trim())){
				return 1;
			}else if("N".equals(value.trim())){
				return 0;
			}else{
				throw new ConfigUploadException();
			}
		} catch (ConfigUploadException e) {
			throw new ConfigUploadException((row+1)+"行"+(column+1)+"列解析Y or N异常"+value);
		}
	}
	
	public Integer getCommunicationTypeValue(int row,int column,String value) throws ConfigUploadException{
		try {
			if(EnumCommunicationType.Serial.getView().equals(value.trim())){
				return EnumCommunicationType.Serial.getValue();
			}else if(EnumCommunicationType.Collector.getView().equals(value.trim())){
				return EnumCommunicationType.Collector.getValue();
			}else{
				throw new ConfigUploadException();
			}
		} catch (Exception e) {
			throw new ConfigUploadException((row+1)+"行"+(column+1)+"列解析采集类型异常"+value);
		}
	}
	
	public Integer getPriceType(int row,int column,String value) throws ConfigUploadException{
		try {
			if("分时".equals(value.trim()) || "Y".equals(value.trim())){
				return EnumPriceType.TOU.getValue();
			}else if("平均".equals(value.trim()) || "N".equals(value.trim())){
				return EnumPriceType.AVG.getValue();
			}else{
				throw new ConfigUploadException();
			}
		} catch (ConfigUploadException e) {
			throw new ConfigUploadException((row+1)+"行"+(column+1)+"列解析分时 or 平均异常" + value);
		}
	}
	
	public FnProtocol getProtocol(int row,int column,String value) throws ConfigUploadException{
		FnProtocol result = null;
		try {
			result = ConstantDBBaseData.ProtocolMap.get(value.trim());
			if(result == null){
				throw new ConfigUploadException((row+1)+"行"+(column+1)+"列解析仪表协议不存在" + value);
			}
			return result;
		} catch (ConfigUploadException e) {
			throw new ConfigUploadException((row+1)+"行"+(column+1)+"列解析仪表协议异常" + value);
		}
	}
	
	public void stringToList(String str,List<String> list){
		if(str != null){
			String[] array = str.split(",");
			for(String obj : array){
				if(!"".equals(obj.trim())){
					list.add(obj.trim());
				}
			}
		}
	}
	

	public EnumPayType getPayType(int row,int column,String str) throws ConfigUploadException{
		try {
			if(str.contains(EnumPayType.PREPAY.getView())){
				return EnumPayType.PREPAY;
			}else if(str.contains(EnumPayType.POSTPAY.getView())){
				return EnumPayType.POSTPAY;
			}else{
				throw new ConfigUploadException();
			}
		} catch (ConfigUploadException e) {
			throw new ConfigUploadException((row+1)+"行"+(column+1)+"列解析付费类型异常" + str);
		}
	}
	
	public EnumPrePayType getPrePayType(int row,int column,String str) throws ConfigUploadException{
		try {
			if(str.equals(EnumPrePayType.OFFLINE_METERPAY.getView())){
				return EnumPrePayType.OFFLINE_METERPAY;
			}else if(str.equals(EnumPrePayType.ONLINE_METERPAY.getView())){
				return EnumPrePayType.ONLINE_METERPAY;
			}else if(str.equals(EnumPrePayType.ONLINE_TENANTPAY.getView())){
				return EnumPrePayType.ONLINE_TENANTPAY;
			}else{
				return EnumPrePayType.None;
			}
		} catch (Exception e) {
			throw new ConfigUploadException((row+1)+"行"+(column+1)+"列解析预付费类型异常" + str);
		}
	}
	
	public EnumPrepayChargeType getBillingMode(int row,int column,String str) throws ConfigUploadException{
		try {
			if(str.equals("充钱")){
				return EnumPrepayChargeType.Qian;
			}else if(str.equals("充量")){
				return EnumPrepayChargeType.Liang;
			}else{
				return EnumPrepayChargeType.None;
			}
		} catch (Exception e) {
			throw new ConfigUploadException((row+1)+"行"+(column+1)+"列解析充值类型异常" + str);
		}
	}
	public void checkEnergy (Map<String, FnRoom> roomMap,List<String> tenantRoomList,String energyType) throws ConfigUploadException{
		try {
			List<String> energyTypes = new ArrayList<String>();
			for (String roomCode : tenantRoomList) {
				FnRoom fnRoom = roomMap.get(roomCode);
				String energyTypeIds = fnRoom.getEnergyTypeIds();
				if(energyTypeIds.contains(energyType)){
					energyTypes.add(energyType);
				}
			}
			if(energyTypes.size()==0){
				throw new ConfigUploadException();
			}
			
		} catch (ConfigUploadException e) {
			throw  new ConfigUploadException("租户所使用房间不包括此能源消耗："+energyType);
		}
		
	}
	public void check (Integer commType,FnProtocol protocol,int rowIndex,int cell,String value) throws ConfigUploadException{
	if(commType == EnumCommunicationType.Serial.getValue().intValue() 
			|| protocol.getPrePayType() == EnumPrePayType.ONLINE_METERPAY.getValue().intValue()){ 
		this.checkNull(rowIndex,cell,value);
	}
	}
}

