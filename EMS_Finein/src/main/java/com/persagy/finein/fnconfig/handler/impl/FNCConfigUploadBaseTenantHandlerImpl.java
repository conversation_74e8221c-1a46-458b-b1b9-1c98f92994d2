package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ToSpellUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.exception.ConfigUploadException;
import com.persagy.finein.core.util.ExpressionUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnconfig.handler.FNCConfigUploadBaseTenantHandler;
import com.persagy.finein.service.*;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.NumberFormat;
import java.util.*;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月17日 下午3:14:12
 * 
 * 说明:基础处理
 */
@Component("FNCConfigUploadBaseTenantHandler")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNCConfigUploadBaseTenantHandlerImpl extends FNCConfigUploadHandlerImpl
		implements FNCConfigUploadBaseTenantHandler {

	@Resource(name = "FNRoomService")
	private FNRoomService FNRoomService;

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;

	@Resource(name = "FNPriceTemplateService")
	private FNPriceTemplateService FNPriceTemplateService;

	@Resource(name = "FNTenantRoomService")
	private FNTenantRoomService FNTenantRoomService;

	@Resource(name = "FNTenantPriceService")
	private FNTenantPriceService FNTenantPriceService;

	@Resource(name = "FNTenantEnergySplitService")
	private FNTenantEnergySplitService FNTenantEnergySplitService;

	@Resource(name = "FNTenantPayTypeService")
	private FNTenantPayTypeService FNTenantPayTypeService;

	@Resource(name = "FNBackConfigBaseService")
	private FNBackConfigBaseService FNBackConfigBaseService;

	@Resource(name = "ExpressionUtil")
	private ExpressionUtil ExpressionUtil;

	@Resource(name = "FNTenantSpellNameService")
	private FNTenantSpellNameService FNTenantSpellNameService;

	@Resource(name = "FNTenantFlagService")
	private FNTenantFlagService FNTenantFlagService;

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void process(Map<String, Object> contentObj, String buildingId, Integer type, String logicCode,
			Workbook workbook, FileResource fileResource, String userId) throws Exception, ConfigUploadException {
		Sheet sheet = workbook.getSheetAt(0);
		int rowMaxIndex = sheet.getLastRowNum();
		Row secondRow = sheet.getRow(1);
		Cell secondCell = secondRow.getCell(1);
		String cellValue = secondCell.getStringCellValue();
		if (cellValue == null || "".equals(cellValue) || !"租户编号".equals(cellValue)) {
			throw new ConfigUploadException("上传模板不是租户模板");
		}
		List<FnRoom> dbRoomList = FNRoomService.queryListByBuildingId(buildingId);
		Map<String, FnRoom> roomMap = new HashMap<String, FnRoom>();
		if (dbRoomList != null) {
			for (FnRoom room : dbRoomList) {
				roomMap.put(room.getCode(), room);
			}
		}
		// 查询数据库所有租户
		FnTenant tenantQuery = new FnTenant();
		List<FnTenant> dbTenantList = FNTenantService.query(tenantQuery);
		Map<String, FnTenant> tenantMap = new HashMap<String, FnTenant>();
		if (dbTenantList != null) {
			for (FnTenant tenant : dbTenantList) {
				tenantMap.put(tenant.getId(), tenant);
			}
		}

		Map<String, FnTenantType> tenantTypeMap = new HashMap<String, FnTenantType>();
		for (FnTenantType tenantType : ConstantDBBaseData.TenantTypeList) {
			tenantTypeMap.put(tenantType.getName(), tenantType);
		}

		List<FnPriceTemplate> priceTemplateList = FNPriceTemplateService.queryList();
		Map<String, FnPriceTemplate> priceTemplateMap = new HashMap<String, FnPriceTemplate>();
		if (priceTemplateList != null) {
			for (FnPriceTemplate priceTemplate : priceTemplateList) {
				priceTemplateMap.put(priceTemplate.getName(), priceTemplate);
			}
		}

		Map<String, List<FnTenantRoom>> tenantRoomListMap = new HashMap<String, List<FnTenantRoom>>();
		Map<String, List<FnTenantPrice>> tenantPriceListMap = new HashMap<String, List<FnTenantPrice>>();
		Map<String, List<FnTenantEnergySplit>> tenantEnergySplitListMap = new HashMap<String, List<FnTenantEnergySplit>>();
		Map<String, List<FnTenantPayType>> tenantPayTypeListMap = new HashMap<String, List<FnTenantPayType>>();
		List<String> tenants = new ArrayList<String>();
		List<FnTenant> tenantList = new ArrayList<FnTenant>();

		for (int rowIndex = 4; rowIndex < rowMaxIndex + 1; rowIndex++) {
			Row row = sheet.getRow(rowIndex);
			if (row == null) {
				break;
			}
			FnTenant tenant = new FnTenant();
			// 租户编号
			Cell cell = row.getCell(1);
			String value = getCellStringValue(cell, false);
			if (value == null || "".equals(value.trim())) {
				cell = row.getCell(2);
				value = getCellStringValue(cell, false);
				if (value == null || "".equals(value.trim())) {
					break;
				} else {
					throw new ConfigUploadException((rowIndex + 1) + "行2列租户编号不能为空");
				}
			}
			// 判断租户编号在模板中是否存在
			if (tenants.contains(value.trim())) {
				throw new ConfigUploadException((rowIndex + 1) + "行2列租户编号已存在:" + value);
			}
			tenants.add(value.trim());

			tenant.setId(value.trim());
			tenant.setBuildingId(buildingId);
			tenant.setStatus(EnumTenantStatus.NOT_ACTIVE.getValue());
			tenant.setIsValid(EnumValidStatus.VALID.getValue());
			tenant.setCreateTime(new Date());

			// 租户名称
			cell = row.getCell(2);
			value = getCellStringValue(cell, false);
			checkNull(rowIndex, 2, value);
			tenant.setName(value.trim());

			// 房间编号
			cell = row.getCell(3);
			value = getCellStringValue(cell, false);
			checkNull(rowIndex, 3, value);
			tenant.setRoomCodes(value);
			List<String> roomList = new ArrayList<String>();
			this.stringToList(value, roomList);

			List<FnTenantRoom> tenantRoomList = new ArrayList<FnTenantRoom>();
			Double areaSum = 0.0;
			for (String roomCode : roomList) {
				if (!roomMap.containsKey(roomCode)) {
					throw new ConfigUploadException((rowIndex + 1) + "行4列解析租户房间不存在:" + value);
				}
				Double area = roomMap.get(roomCode).getArea();
				areaSum += area;
				FnTenantRoom tenantRoom = new FnTenantRoom();
				tenantRoom.setId(UUID.randomUUID().toString());
				tenantRoom.setBuildingId(buildingId);
				tenantRoom.setTenantId(tenant.getId());
				tenantRoom.setRoomId(roomMap.get(roomCode).getId());
				tenantRoom.setRoomCode(roomMap.get(roomCode).getCode());
				tenantRoomList.add(tenantRoom);

			}
			tenantRoomListMap.put(tenant.getId(), tenantRoomList);
			// 所属业态
			cell = row.getCell(4);
			value = getCellStringValue(cell, false);
			checkNull(rowIndex, 4, value);
			FnTenantType tenantType = tenantTypeMap.get(value.trim());
			if (tenantType == null) {
				throw new ConfigUploadException((rowIndex + 1) + "行5列解析租户类型不存在:" + value);
			}
			tenant.setTenantTypeId(tenantType.getId());

			// 面积
			cell = row.getCell(5);
			value = getCellStringValue(cell, true);
			// checkNull(rowIndex, 5, value);
			Double area = null;
			if (value == null || "".equals(value.trim())) {
				area = DoubleFormatUtil.Instance().getDoubleData(areaSum);
			} else {
				area = DoubleFormatUtil.Instance().getDoubleData(value);
			}
			if (area == null) {
				throw new ConfigUploadException((rowIndex + 1) + "行6列解析面积解析异常:" + value);
			}
			tenant.setArea(area);

			// 联系人
			cell = row.getCell(6);
			value = getCellStringValue(cell, false);
			checkNull(rowIndex, 6, value);
			tenant.setContactName(value.trim());

			// 联系电话
			cell = row.getCell(7);
			if (cell != null){
				if (cell.getCellType() == Cell.CELL_TYPE_NUMERIC) {
					NumberFormat nf = NumberFormat.getInstance();
					value = nf.format(cell.getNumericCellValue());
					if (value.indexOf(",") >= 0) {
						value = value.replace(",", "");
					}
				} else {
					value = getCellStringValue(cell, false);
				}
				tenant.setContactMobile(value.trim());
			}else {
				tenant.setContactMobile("");
			}

//			checkNull(rowIndex, 7, value);
			StringBuffer energyTypeIds = new StringBuffer();
			{// 电系统
				cell = row.getCell(8);
				value = getCellStringValue(cell, false);
				checkNull(rowIndex, 8, value);
				Integer isExsit = getYorNIntValue(rowIndex, 8, value);
				if (isExsit.intValue() == EnumExsistStatus.Exsist.getValue().intValue()) {
					this.checkEnergy(roomMap, roomList, FineinConstant.EnergyType.Dian);
					energyTypeIds.append(FineinConstant.EnergyType.Dian);
					cell = row.getCell(9);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 9, value);
					EnumPayType payType = getPayType(rowIndex, 9, value);
					EnumPrePayType prePayType = getPrePayType(rowIndex, 9, value);

					cell = row.getCell(10);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 10, value);
					EnumPrepayChargeType billingMode = getBillingMode(rowIndex, 10, value);

					cell = row.getCell(11);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 11, value);
					int priceType = getPriceType(rowIndex, 11, value);
					{// 封装租户付费方式
						FnTenantPayType tenantPayType = new FnTenantPayType();
						tenantPayType.setId(UUID.randomUUID().toString());
						tenantPayType.setBuildingId(buildingId);
						tenantPayType.setEnergyTypeId(FineinConstant.EnergyType.Dian);
						tenantPayType.setTenantId(tenant.getId());
						tenantPayType.setPayType(payType.getValue());
						if (prePayType != null) {
							tenantPayType.setPrePayType(prePayType.getValue());
						} else {
							tenantPayType.setPrePayType(EnumPrepayChargeType.None.getValue());
						}
						if (billingMode != null) {
							tenantPayType.setPrepayChargeType(billingMode.getValue());
						}

						if (!tenantPayTypeListMap.containsKey(tenant.getId())) {
							tenantPayTypeListMap.put(tenant.getId(), new ArrayList<FnTenantPayType>());
						}
						tenantPayTypeListMap.get(tenant.getId()).add(tenantPayType);
					}

					// 价格方案
					cell = row.getCell(12);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 12, value);
					FnPriceTemplate priceTemplate = priceTemplateMap.get(value.trim());

					if (priceTemplate == null) {
						throw new ConfigUploadException((rowIndex + 1) + "行13列解析价格模板不存在:" + value);
					}
					if (priceType != priceTemplate.getType().intValue()) {
						throw new ConfigUploadException((rowIndex + 1) + "行13列解析价格模板与12列类型不匹配:" + value);
					}
					// 判断所输入的价格模板是不是属于电能耗的价格方案
					String energyTypeId = priceTemplate.getEnergyTypeId();
					if (!energyTypeId.equalsIgnoreCase(FineinConstant.EnergyType.Dian)) {
						throw new ConfigUploadException((rowIndex + 1) + "行13列解析价格模板不属于电能耗的价格方案" + value);
					}

					{// 封装租户价格
						FnTenantPrice tenantPrice = new FnTenantPrice();
						tenantPrice.setId(UUID.randomUUID().toString());
						tenantPrice.setTenantId(tenant.getId());
						tenantPrice.setPriceTemplateId(priceTemplate.getId());
						tenantPrice.setEnergyTypeId(FineinConstant.EnergyType.Dian);
						tenantPrice.setLastUpdateTime(new Date());

						if (!tenantPriceListMap.containsKey(tenant.getId())) {
							tenantPriceListMap.put(tenant.getId(), new ArrayList<FnTenantPrice>());
						}
						tenantPriceListMap.get(tenant.getId()).add(tenantPrice);
					}

					cell = row.getCell(13);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 13, value);
					Integer isSplit = getYorNIntValue(rowIndex, 13, value);
					if (isSplit.intValue() == 1) {// 拆分
						cell = row.getCell(14);
						value = getCellStringValue(cell, false);
						checkNull(rowIndex, 14, value);

						{// 封装租户拆分公式
							FnTenantEnergySplit tenantEnergySplit = new FnTenantEnergySplit();
							tenantEnergySplit.setId(UUID.randomUUID().toString());
							tenantEnergySplit.setTenantId(tenant.getId());
							if (!ExpressionUtil.verifyExpression(FineinConstant.EnergyType.Dian, value.trim())) {
								throw new ConfigUploadException((rowIndex + 1) + "行15列解析拆分公式异常" + value);
							}
							tenantEnergySplit.setExpression(value.trim());
							tenantEnergySplit.setEnergyTypeId(FineinConstant.EnergyType.Dian);
							tenantEnergySplit.setLastUpdateTime(new Date());
							tenantEnergySplit.setElements("");

							if (!tenantEnergySplitListMap.containsKey(tenant.getId())) {
								tenantEnergySplitListMap.put(tenant.getId(), new ArrayList<FnTenantEnergySplit>());
							}
							tenantEnergySplitListMap.get(tenant.getId()).add(tenantEnergySplit);
						}
					}
				}
			}

			{// 水系统
				cell = row.getCell(15);
				value = getCellStringValue(cell, false);
				checkNull(rowIndex, 15, value);
				Integer isExsit = getYorNIntValue(rowIndex, 15, value);
				if (isExsit.intValue() == EnumExsistStatus.Exsist.getValue().intValue()) {
					this.checkEnergy(roomMap, roomList, FineinConstant.EnergyType.Shui);
					if (!"".equals(energyTypeIds.toString())) {
						energyTypeIds.append(",").append(FineinConstant.EnergyType.Shui);
					} else {
						energyTypeIds.append(FineinConstant.EnergyType.Shui);
					}
					cell = row.getCell(16);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 16, value);
					EnumPayType payType = getPayType(rowIndex, 16, value);
					EnumPrePayType prePayType = getPrePayType(rowIndex, 16, value);

					cell = row.getCell(17);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 17, value);
					EnumPrepayChargeType billingMode = getBillingMode(rowIndex, 17, value);

					{// 封装租户付费方式
						FnTenantPayType tenantPayType = new FnTenantPayType();
						tenantPayType.setId(UUID.randomUUID().toString());
						tenantPayType.setBuildingId(buildingId);
						tenantPayType.setEnergyTypeId(FineinConstant.EnergyType.Shui);
						tenantPayType.setTenantId(tenant.getId());
						tenantPayType.setPayType(payType.getValue());
						if (prePayType != null) {
							tenantPayType.setPrePayType(prePayType.getValue());
						} else {
							tenantPayType.setPrePayType(EnumPrepayChargeType.None.getValue());
						}
						if (billingMode != null) {
							tenantPayType.setPrepayChargeType(billingMode.getValue());
						}

						if (!tenantPayTypeListMap.containsKey(tenant.getId())) {
							tenantPayTypeListMap.put(tenant.getId(), new ArrayList<FnTenantPayType>());
						}
						tenantPayTypeListMap.get(tenant.getId()).add(tenantPayType);
					}

					// 价格方案
					cell = row.getCell(18);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 18, value);
					FnPriceTemplate priceTemplate = priceTemplateMap.get(value.trim());
					if (priceTemplate == null) {
						throw new ConfigUploadException((rowIndex + 1) + "行19列解析价格模板不存在:" + value);
					}
					if (priceTemplate.getType().intValue() != EnumPriceType.AVG.getValue().intValue()) {
						throw new ConfigUploadException((rowIndex + 1) + "行19列解析价格模板不是平均价格类型:" + value);
					}
					// 判断所输入的价格模板是不是属于水能耗的价格方案
					String energyTypeId = priceTemplate.getEnergyTypeId();
					if (!energyTypeId.equalsIgnoreCase(FineinConstant.EnergyType.Shui)) {
						throw new ConfigUploadException((rowIndex + 1) + "行19列解析价格模板不属于水能耗的价格方案" + value);
					}

					{// 封装租户价格
						FnTenantPrice tenantPrice = new FnTenantPrice();
						tenantPrice.setId(UUID.randomUUID().toString());
						tenantPrice.setTenantId(tenant.getId());
						tenantPrice.setPriceTemplateId(priceTemplate.getId());
						tenantPrice.setEnergyTypeId(FineinConstant.EnergyType.Shui);
						tenantPrice.setLastUpdateTime(new Date());

						if (!tenantPriceListMap.containsKey(tenant.getId())) {
							tenantPriceListMap.put(tenant.getId(), new ArrayList<FnTenantPrice>());
						}
						tenantPriceListMap.get(tenant.getId()).add(tenantPrice);
					}

					cell = row.getCell(19);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 19, value);
					Integer isSplit = getYorNIntValue(rowIndex, 19, value);
					if (isSplit.intValue() == 1) {// 拆分
						cell = row.getCell(20);
						value = getCellStringValue(cell, false);
						checkNull(rowIndex, 20, value);
						{// 封装租户拆分公式
							FnTenantEnergySplit tenantEnergySplit = new FnTenantEnergySplit();
							tenantEnergySplit.setId(UUID.randomUUID().toString());
							tenantEnergySplit.setTenantId(tenant.getId());
							tenantEnergySplit.setExpression(value.trim());
							if (!ExpressionUtil.verifyExpression(FineinConstant.EnergyType.Shui, value.trim())) {
								throw new ConfigUploadException((rowIndex + 1) + "行21列解析拆分公式异常" + value);
							}
							tenantEnergySplit.setEnergyTypeId(FineinConstant.EnergyType.Shui);
							tenantEnergySplit.setLastUpdateTime(new Date());
							tenantEnergySplit.setElements("");
							if (!tenantEnergySplitListMap.containsKey(tenant.getId())) {
								tenantEnergySplitListMap.put(tenant.getId(), new ArrayList<FnTenantEnergySplit>());
							}
							tenantEnergySplitListMap.get(tenant.getId()).add(tenantEnergySplit);
						}
					}
				}
			}

			{// 热水系统
				cell = row.getCell(21);
				value = getCellStringValue(cell, false);
				checkNull(rowIndex, 21, value);
				Integer isExsit = getYorNIntValue(rowIndex, 21, value);
				if (isExsit.intValue() == EnumExsistStatus.Exsist.getValue().intValue()) {
					this.checkEnergy(roomMap, roomList, FineinConstant.EnergyType.ReShui);
					if (!"".equals(energyTypeIds.toString())) {
						energyTypeIds.append(",").append(FineinConstant.EnergyType.ReShui);
					} else {
						energyTypeIds.append(FineinConstant.EnergyType.ReShui);
					}
					cell = row.getCell(22);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 22, value);
					EnumPayType payType = getPayType(rowIndex, 22, value);
					EnumPrePayType prePayType = getPrePayType(rowIndex, 22, value);

					cell = row.getCell(23);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 23, value);
					EnumPrepayChargeType billingMode = getBillingMode(rowIndex, 23, value);

					{// 封装租户付费方式
						FnTenantPayType tenantPayType = new FnTenantPayType();
						tenantPayType.setId(UUID.randomUUID().toString());
						tenantPayType.setBuildingId(buildingId);
						tenantPayType.setEnergyTypeId(FineinConstant.EnergyType.ReShui);
						tenantPayType.setTenantId(tenant.getId());
						tenantPayType.setPayType(payType.getValue());
						if (prePayType != null) {
							tenantPayType.setPrePayType(prePayType.getValue());
						} else {
							tenantPayType.setPrePayType(EnumPrepayChargeType.None.getValue());
						}
						if (billingMode != null) {
							tenantPayType.setPrepayChargeType(billingMode.getValue());
						}

						if (!tenantPayTypeListMap.containsKey(tenant.getId())) {
							tenantPayTypeListMap.put(tenant.getId(), new ArrayList<FnTenantPayType>());
						}
						tenantPayTypeListMap.get(tenant.getId()).add(tenantPayType);
					}

					// 价格方案
					cell = row.getCell(24);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 24, value);
					FnPriceTemplate priceTemplate = priceTemplateMap.get(value.trim());
					if (priceTemplate == null) {
						throw new ConfigUploadException((rowIndex + 1) + "行25列解析价格模板不存在:" + value);
					}
					if (priceTemplate.getType().intValue() != EnumPriceType.AVG.getValue().intValue()) {
						throw new ConfigUploadException((rowIndex + 1) + "行25列解析价格模板不是平均价格类型:" + value);
					}
					// 判断所输入的价格模板是不是属于热水能耗的价格方案
					String energyTypeId = priceTemplate.getEnergyTypeId();
					if (!energyTypeId.equalsIgnoreCase(FineinConstant.EnergyType.ReShui)) {
						throw new ConfigUploadException((rowIndex + 1) + "行25列解析价格模板不属于热水能耗的价格方案" + value);
					}

					{// 封装租户价格
						FnTenantPrice tenantPrice = new FnTenantPrice();
						tenantPrice.setId(UUID.randomUUID().toString());
						tenantPrice.setTenantId(tenant.getId());
						tenantPrice.setPriceTemplateId(priceTemplate.getId());
						tenantPrice.setEnergyTypeId(FineinConstant.EnergyType.ReShui);
						tenantPrice.setLastUpdateTime(new Date());

						if (!tenantPriceListMap.containsKey(tenant.getId())) {
							tenantPriceListMap.put(tenant.getId(), new ArrayList<FnTenantPrice>());
						}
						tenantPriceListMap.get(tenant.getId()).add(tenantPrice);
					}

					cell = row.getCell(25);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 25, value);
					Integer isSplit = getYorNIntValue(rowIndex, 25, value);
					if (isSplit.intValue() == 1) {// 拆分
						cell = row.getCell(26);
						value = getCellStringValue(cell, false);
						checkNull(rowIndex, 26, value);
						{// 封装租户拆分公式
							FnTenantEnergySplit tenantEnergySplit = new FnTenantEnergySplit();
							tenantEnergySplit.setId(UUID.randomUUID().toString());
							tenantEnergySplit.setTenantId(tenant.getId());
							tenantEnergySplit.setExpression(value.trim());
							if (!ExpressionUtil.verifyExpression(FineinConstant.EnergyType.ReShui, value.trim())) {
								throw new ConfigUploadException((rowIndex + 1) + "行27列解析拆分公式异常" + value);
							}
							tenantEnergySplit.setEnergyTypeId(FineinConstant.EnergyType.ReShui);
							tenantEnergySplit.setLastUpdateTime(new Date());
							tenantEnergySplit.setElements("");

							if (!tenantEnergySplitListMap.containsKey(tenant.getId())) {
								tenantEnergySplitListMap.put(tenant.getId(), new ArrayList<FnTenantEnergySplit>());
							}
							tenantEnergySplitListMap.get(tenant.getId()).add(tenantEnergySplit);
						}
					}
				}
			}

			{// 燃气系统
				cell = row.getCell(27);
				value = getCellStringValue(cell, false);
				checkNull(rowIndex, 27, value);
				Integer isExsit = getYorNIntValue(rowIndex, 27, value);
				if (isExsit.intValue() == EnumExsistStatus.Exsist.getValue().intValue()) {
					this.checkEnergy(roomMap, roomList, FineinConstant.EnergyType.RanQi);
					if (!"".equals(energyTypeIds.toString())) {
						energyTypeIds.append(",").append(FineinConstant.EnergyType.RanQi);
					} else {
						energyTypeIds.append(FineinConstant.EnergyType.RanQi);
					}
					cell = row.getCell(28);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 28, value);
					EnumPayType payType = getPayType(rowIndex, 28, value);
					EnumPrePayType prePayType = getPrePayType(rowIndex, 28, value);

					cell = row.getCell(29);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 29, value);
					EnumPrepayChargeType billingMode = getBillingMode(rowIndex, 29, value);

					{// 封装租户付费方式
						FnTenantPayType tenantPayType = new FnTenantPayType();
						tenantPayType.setId(UUID.randomUUID().toString());
						tenantPayType.setBuildingId(buildingId);
						tenantPayType.setEnergyTypeId(FineinConstant.EnergyType.RanQi);
						tenantPayType.setTenantId(tenant.getId());
						tenantPayType.setPayType(payType.getValue());
						if (prePayType != null) {
							tenantPayType.setPrePayType(prePayType.getValue());
						} else {
							tenantPayType.setPrePayType(EnumPrepayChargeType.None.getValue());
						}
						if (billingMode != null) {
							tenantPayType.setPrepayChargeType(billingMode.getValue());
						}

						if (!tenantPayTypeListMap.containsKey(tenant.getId())) {
							tenantPayTypeListMap.put(tenant.getId(), new ArrayList<FnTenantPayType>());
						}
						tenantPayTypeListMap.get(tenant.getId()).add(tenantPayType);
					}

					// 价格方案
					cell = row.getCell(30);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 30, value);
					FnPriceTemplate priceTemplate = priceTemplateMap.get(value.trim());
					if (priceTemplate == null) {
						throw new ConfigUploadException((rowIndex + 1) + "行31列解析价格模板不存在:" + value);
					}
					if (priceTemplate.getType().intValue() != EnumPriceType.AVG.getValue().intValue()) {
						throw new ConfigUploadException((rowIndex + 1) + "行31列解析价格模板不是平均价格类型:" + value);
					}
					// 判断所输入的价格模板是不是属于燃气能耗的价格方案
					String energyTypeId = priceTemplate.getEnergyTypeId();
					if (!energyTypeId.equalsIgnoreCase(FineinConstant.EnergyType.RanQi)) {
						throw new ConfigUploadException((rowIndex + 1) + "行31列解析价格模板不属于燃气能耗的价格方案" + value);
					}

					{// 封装租户价格
						FnTenantPrice tenantPrice = new FnTenantPrice();
						tenantPrice.setId(UUID.randomUUID().toString());
						tenantPrice.setTenantId(tenant.getId());
						tenantPrice.setPriceTemplateId(priceTemplate.getId());
						tenantPrice.setEnergyTypeId(FineinConstant.EnergyType.RanQi);
						tenantPrice.setLastUpdateTime(new Date());

						if (!tenantPriceListMap.containsKey(tenant.getId())) {
							tenantPriceListMap.put(tenant.getId(), new ArrayList<FnTenantPrice>());
						}
						tenantPriceListMap.get(tenant.getId()).add(tenantPrice);
					}

					cell = row.getCell(31);
					value = getCellStringValue(cell, false);
					checkNull(rowIndex, 31, value);
					Integer isSplit = getYorNIntValue(rowIndex, 31, value);
					if (isSplit.intValue() == 1) {// 拆分
						cell = row.getCell(32);
						value = getCellStringValue(cell, false);
						checkNull(rowIndex, 32, value);
						{// 封装租户拆分公式
							FnTenantEnergySplit tenantEnergySplit = new FnTenantEnergySplit();
							tenantEnergySplit.setId(UUID.randomUUID().toString());
							tenantEnergySplit.setTenantId(tenant.getId());
							tenantEnergySplit.setExpression(value.trim());
							if (!ExpressionUtil.verifyExpression(FineinConstant.EnergyType.RanQi, value.trim())) {
								throw new ConfigUploadException((rowIndex + 1) + "行33列解析拆分公式异常" + value);
							}
							tenantEnergySplit.setEnergyTypeId(FineinConstant.EnergyType.RanQi);
							tenantEnergySplit.setLastUpdateTime(new Date());
							tenantEnergySplit.setElements("");
							if (!tenantEnergySplitListMap.containsKey(tenant.getId())) {
								tenantEnergySplitListMap.put(tenant.getId(), new ArrayList<FnTenantEnergySplit>());
							}
							tenantEnergySplitListMap.get(tenant.getId()).add(tenantEnergySplit);
						}
					}
				}
			}
			tenant.setEnergyTypeIds(energyTypeIds.toString());
			tenant.setCreateUserId(userId);
			tenantList.add(tenant);
		}
		List<FnTenant> newFnTenantList = new ArrayList<FnTenant>();
		List<FnTenant> exsitFnTenantSb = new ArrayList<FnTenant>();
		for (FnTenant tenant : tenantList) {
			if (tenantMap.containsKey(tenant.getId())) {
				exsitFnTenantSb.add(tenant);
			} else {
				newFnTenantList.add(tenant);
			}
		}
		if (newFnTenantList.size() > 0) {
			List<FnTenantRoom> saveTenantRoomList = new ArrayList<FnTenantRoom>();
			List<FnTenantPrice> saveTenantPriceList = new ArrayList<FnTenantPrice>();
			List<FnTenantEnergySplit> saveTenantEnergySplitList = new ArrayList<FnTenantEnergySplit>();
			List<FnTenantPayType> saveTenantPayTypeList = new ArrayList<FnTenantPayType>();
			List<FnTenantSpellName> saveTenantSpellNameList = new ArrayList<FnTenantSpellName>();
			List<FnTenantFlag> saveTenantFlagList = new ArrayList<FnTenantFlag>();
			for (FnTenant fnTenant : newFnTenantList) {
				if (tenantRoomListMap.containsKey(fnTenant.getId())) {
					saveTenantRoomList.addAll(tenantRoomListMap.get(fnTenant.getId()));
				}
				if (tenantPriceListMap.containsKey(fnTenant.getId())) {
					saveTenantPriceList.addAll(tenantPriceListMap.get(fnTenant.getId()));
				}
				if (tenantEnergySplitListMap.containsKey(fnTenant.getId())) {
					saveTenantEnergySplitList.addAll(tenantEnergySplitListMap.get(fnTenant.getId()));
				}
				if (tenantPayTypeListMap.containsKey(fnTenant.getId())) {
					saveTenantPayTypeList.addAll(tenantPayTypeListMap.get(fnTenant.getId()));
				}
				FnTenantSpellName save = new FnTenantSpellName();
				save.setId(fnTenant.getId());
				String name = fnTenant.getName();
				String pinYin = ToSpellUtil.Instance().getStringPinYin(name);
				save.setTenantName(name);
				save.setSpellName(pinYin);
				saveTenantSpellNameList.add(save);

				StringBuffer sb = new StringBuffer();
				sb.append(fnTenant.getBuildingId()).append("-").append(fnTenant.getId()).append("-")
						.append((int) (Math.random() * 90 + 10));
				FnTenantFlag tenantFlag = new FnTenantFlag();
				tenantFlag.setId(UUID.randomUUID().toString());
				tenantFlag.setBuildingId(fnTenant.getBuildingId());
				tenantFlag.setTenantId(fnTenant.getId());
				tenantFlag.setTenantFlag(sb.toString());
				saveTenantFlagList.add(tenantFlag);
			}
			// 更新房间状态
			List<String> roomIds = new ArrayList<String>();
			for (FnTenantRoom tenantRoom : saveTenantRoomList) {
				roomIds.add(tenantRoom.getRoomId());
			}
			FNRoomService.updateRoomStatus(buildingId, roomIds, EnumUseStatus.In_Use);
			FNTenantService.save(newFnTenantList);
			FNTenantRoomService.save(saveTenantRoomList);
			FNTenantPriceService.save(saveTenantPriceList);
			FNTenantEnergySplitService.save(saveTenantEnergySplitList);
			FNTenantPayTypeService.save(saveTenantPayTypeList);
			// 更新租户拼音名表
			FNTenantSpellNameService.save(saveTenantSpellNameList);
			FNTenantFlagService.save(saveTenantFlagList);
		}

		if (exsitFnTenantSb.size() > 0) {
			List<FnTenantPrice> updateTenantPriceList = new ArrayList<FnTenantPrice>();
			List<FnTenant> updateTenantList = new ArrayList<FnTenant>();
			List<FnTenantSpellName> updateTenantSpellNameList = new ArrayList<FnTenantSpellName>();
			for (FnTenant fnTenant : exsitFnTenantSb) {
				if (tenantPriceListMap.containsKey(fnTenant.getId())) {
					updateTenantPriceList.addAll(tenantPriceListMap.get(fnTenant.getId()));
				}
				{
					FnTenantSpellName update = new FnTenantSpellName();
					update.setId(fnTenant.getId());
					String name = fnTenant.getName();
					String pinYin = ToSpellUtil.Instance().getStringPinYin(name);
					update.setTenantName(name);
					update.setSpellName(pinYin);
					updateTenantSpellNameList.add(update);
				}
				FnTenant update = new FnTenant();
				update.setId(fnTenant.getId());
				update.setName(fnTenant.getName());
				update.setContactName(fnTenant.getContactName());
				update.setContactMobile(fnTenant.getContactMobile());
				update.setTenantTypeId(fnTenant.getTenantTypeId());
				update.setArea(fnTenant.getArea());
				updateTenantList.add(update);
			}
			for (FnTenant updateTenant : updateTenantList) {
				FnTenant query = new FnTenant();
				query.setId(updateTenant.getId());
				FNTenantService.update(query, updateTenant);
			}
			for (FnTenantPrice updateTenantPrice : updateTenantPriceList) {
				FnTenantPrice query = new FnTenantPrice();
				query.setTenantId(updateTenantPrice.getTenantId());
				query.setEnergyTypeId(updateTenantPrice.getEnergyTypeId());
				FNTenantPriceService.update(query, updateTenantPrice);
			}
			// 更新租户拼音名表
			for (FnTenantSpellName updateTenantSpellName : updateTenantSpellNameList) {
				FnTenantSpellName query = new FnTenantSpellName();
				query.setId(updateTenantSpellName.getId());
				FNTenantSpellNameService.update(query, updateTenantSpellName);
			}
		}

		FnBackConfigBase query = new FnBackConfigBase();
		query.setLogicCode(logicCode);
		query.setBuildingId(buildingId);

		FnBackConfigBase upload = new FnBackConfigBase();
		upload.setFileId(fileResource.getId());
		upload.setFileName(fileResource.getName());
		upload.setIsExsit(EnumExsistStatus.Exsist.getValue());
		upload.setUploadTime(new Date());
		upload.setUserId(userId);

		FNBackConfigBaseService.update(query, upload);

		contentObj.put("result", EnumConfigUploadResult.Code_0.getValue());
		contentObj.put("message", "");

	}
}
