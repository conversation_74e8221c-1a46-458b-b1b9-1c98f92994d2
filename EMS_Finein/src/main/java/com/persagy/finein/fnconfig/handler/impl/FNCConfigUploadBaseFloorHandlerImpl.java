package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.FnBackConfigBase;
import com.persagy.ems.pojo.finein.FnFloor;
import com.persagy.ems.pojo.finein.FnRoom;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.exception.ConfigUploadException;
import com.persagy.finein.enumeration.EnumConfigUploadResult;
import com.persagy.finein.enumeration.EnumExsistStatus;
import com.persagy.finein.fnconfig.handler.FNCConfigUploadBaseFloorHandler;
import com.persagy.finein.service.FNBackConfigBaseService;
import com.persagy.finein.service.FNFloorService;
import com.persagy.finein.service.FNRoomService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.Map.Entry;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月17日 下午3:14:12
 * 
 * 说明:基础处理
 */
@Component("FNCConfigUploadBaseFloorHandler")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNCConfigUploadBaseFloorHandlerImpl extends FNCConfigUploadHandlerImpl
		implements FNCConfigUploadBaseFloorHandler {

	@Resource(name = "FNFloorService")
	private FNFloorService FNFloorService;

	@Resource(name = "FNBackConfigBaseService")
	private FNBackConfigBaseService FNBackConfigBaseService;
	@Resource(name = "FNRoomService")
	private FNRoomService FNRoomService;

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void process(Map<String, Object> contentObj, String buildingId, Integer type, String logicCode,
			Workbook workbook, FileResource fileResource, String userId) throws Exception, ConfigUploadException {
		Sheet sheet = workbook.getSheetAt(0);
		int rowMaxIndex = sheet.getLastRowNum();
		List<FnFloor> floorList = new ArrayList<FnFloor>();
		Map<String, FnFloor> floorMap = new HashMap<String, FnFloor>();
		ArrayList<String> orderBys = new ArrayList<String>();
		for (int rowIndex = 3; rowIndex <= rowMaxIndex; rowIndex++) {
			Row row = sheet.getRow(rowIndex);
			if(row==null){
				break;
			}
			FnFloor floor = new FnFloor();
			// 序号
			System.out.println("第"+rowIndex+"行");
			Cell cell = row.getCell(1);
			String value = getCellStringValue(cell, false);
			if (value == null || "".equals(value.trim())) {
				cell = row.getCell(2);
				value = getCellStringValue(cell, false);
				if (value == null || "".equals(value.trim())) {
					break;
				} else {
					throw new ConfigUploadException((rowIndex + 1) + "行2列楼层序号不能为空:");
				}
			}
			if (orderBys.contains(value + "")) {
				throw new ConfigUploadException((rowIndex + 1) + "行第2列序号不能重复");
			}
			Integer orderBy = null;
			try {
				orderBy = Integer.parseInt(value);
			} catch (Exception e) {
				throw new ConfigUploadException((rowIndex + 1) + "行2列解析序号异常：" + value);
			}
			floor.setOrderBy(orderBy);
			// id
			cell = row.getCell(2);
			value = getCellStringValue(cell, false);
			checkNull(rowIndex, 2, value);
			if (floorMap.containsKey(value)) {
				throw new ConfigUploadException((rowIndex + 1) + "行第3列楼层id不能重复：" + value);
			}
			floor.setId(value.trim());

			// 名称
			cell = row.getCell(3);
			value = getCellStringValue(cell, false);
			checkNull(rowIndex, 3, value);
			floor.setName(value.trim());

			floor.setBuildingId(buildingId);

			orderBys.add(floor.getOrderBy() + "");
			floorList.add(floor);
			floorMap.put(floor.getId(), floor);
		}
		List<FnFloor> dbFloorList = FNFloorService.queryList(buildingId, EMSOrder.Asc);
		Map<String, FnFloor> dbFloorMap = new HashMap<String, FnFloor>();
		List<FnFloor> deleteList = new ArrayList<FnFloor>();
		if (dbFloorList != null && dbFloorList.size() > 0) {
			for (FnFloor fnFloor : dbFloorList) {
				dbFloorMap.put(fnFloor.getId(), fnFloor);
			}
		}
		for (FnFloor fnFloor : floorList) {
			if (dbFloorMap.containsKey(fnFloor.getId())) {
				FnFloor floor = new FnFloor();
				floor.setId(fnFloor.getId());
				deleteList.add(floor);
				dbFloorMap.remove(fnFloor.getId());
			}

		}
		for (Entry<String, FnFloor> entry : dbFloorMap.entrySet()) {
			List<FnRoom> fnRoom = FNRoomService.queryListByFloorId(entry.getKey());
			if (fnRoom == null || fnRoom.size() == 0) {
				deleteList.add(entry.getValue());
			} else {
				throw new ConfigUploadException("楼层更新失败，数据库中楼层已经与房间关联");
			}
		}
		FNFloorService.remove(deleteList);

		FNFloorService.save(floorList);

		contentObj.put("result", EnumConfigUploadResult.Code_0.getValue());

		FnBackConfigBase query = new FnBackConfigBase();
		query.setLogicCode(logicCode);
		query.setBuildingId(buildingId);

		FnBackConfigBase upload = new FnBackConfigBase();
		upload.setFileId(fileResource.getId());
		upload.setFileName(fileResource.getName());
		upload.setIsExsit(EnumExsistStatus.Exsist.getValue());
		upload.setUploadTime(new Date());
		upload.setUserId(userId);

		FNBackConfigBaseService.update(query, upload);

		contentObj.put("result", EnumConfigUploadResult.Code_0.getValue());
		contentObj.put("message", "");

	}
}
