package com.persagy.finein.fnconfig.handler.impl;

import com.persagy.core.component.JsonObjectMapper;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.pojo.finein.FnBackConfigMeter;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnProtocol;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.exception.ConfigUploadException;
import com.persagy.finein.enumeration.EnumConfigUploadResult;
import com.persagy.finein.enumeration.EnumExsistStatus;
import com.persagy.finein.fnconfig.handler.FNCConfigUploadMeterDianHandler;
import com.persagy.finein.service.FNBackConfigMeterService;
import com.persagy.finein.service.FNMeterService;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.ss.usermodel.*;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:电表处理
*/
@Component("FNCConfigUploadMeterDianHandler")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNCConfigUploadMeterDianHandlerImpl extends FNCConfigUploadHandlerImpl implements FNCConfigUploadMeterDianHandler {
	
	@Resource(name="objectMapper")
	private JsonObjectMapper objectMapper;
	
	@Resource(name="FNMeterService")
	private FNMeterService FNMeterService;
	
	@Resource(name="FNBackConfigMeterService")
	private FNBackConfigMeterService FNBackConfigMeterService;
	
//	@Resource(name = "DictionaryTopologyRelationService")
//	private DictionaryTopologyRelationService dictionaryTopologyRelationService;
	
	
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void process(Map<String,Object> contentObj,Integer type,String logicCode,Workbook workbook, FileResource fileResource,String userId) throws Exception,ConfigUploadException{
		Sheet sheet = workbook.getSheetAt(0);
		//检查数据准确性
		CellStyle textStyle = workbook.createCellStyle();
		
//		String dataCode = "01";
//		String dataToplogyType = "TY0101";
//		String dataToplogyId = dataToplogyType + dataCode;
		List<String> ids = new ArrayList<String>();
		List<FnMeter> meterList = new ArrayList<FnMeter>();
		int rowMaxIndex = sheet.getLastRowNum();
		for (int rowIndex = 3; rowIndex <= rowMaxIndex; rowIndex++) {
			Row row = sheet.getRow(rowIndex);
			if(row==null){
				break;
			}
			FnMeter fnMeter = new FnMeter();
			String serviceId = null;
			Double kkValue = null;
			Integer glIsCt = null;
			Integer ljlIsCt = null;
			Integer syjIsCt = null;
			String address = null;
			String config_user_id = null;
			Integer isSanXiang=null;
			// 仪表id
			Cell cell = row.getCell(1);
			String value = this.getCellStringValue(cell, false);
			if(value == null || "".equals(value.trim())){
				cell = row.getCell(3);
				 value = getCellStringValue(cell, false);
				 if(value == null || "".equals(value.trim())){
					 break;
				 }else{
					 throw new ConfigUploadException((rowIndex+1)+"行2列仪表id不能为空:");
				 }
			}
			if(ids.contains(value.trim())){
				 throw new ConfigUploadException((rowIndex+1)+"行2列仪表id重复:"+value);
			}
			
			fnMeter.setId(value.trim());
			ids.add(value.trim());
			fnMeter.setEnergyTypeId(FineinConstant.EnergyType.Dian);

			// 支路id
			cell = row.getCell(2);
			value = this.getCellStringValue(cell, false);
			serviceId = value == null ? null : value.trim();

			// 空开容量
			cell = row.getCell(3);
			value = this.getCellStringValue(cell, true);
			this.checkNull(rowIndex,3,value);
			kkValue = DoubleFormatUtil.Instance().getDoubleData(value);
			if(kkValue == null){
				throw new ConfigUploadException((rowIndex+1)+"行4列解析空开量异常"+value);
			}

			// ct值
			cell = row.getCell(4);
			value = this.getCellStringValue(cell, true);
			this.checkNull(rowIndex,4,value);
			Double radio = null;
			try {
				radio = DoubleFormatUtil.Instance().getDoubleData(value);
			} catch (Exception e) {
			}
			if(radio == null){
				throw new ConfigUploadException((rowIndex+1)+"行5列解析异常"+value);
			}
			fnMeter.setRadio(radio);
			
			// 功率乘ct
			cell = row.getCell(5);
			value = this.getCellStringValue(cell, true);
			this.checkNull(rowIndex,5,value);
			glIsCt = getYorNIntValue(rowIndex, 5, value);

			// 正向有功乘ct
			cell = row.getCell(6);
			value = this.getCellStringValue(cell, true);
			this.checkNull(rowIndex,6,value);
			ljlIsCt = getYorNIntValue(rowIndex, 6, value);

			// 剩余量乘ct
			cell = row.getCell(7);
			value = this.getCellStringValue(cell, true);
			this.checkNull(rowIndex,7,value);
			syjIsCt = getYorNIntValue(rowIndex, 7, value);
			
			//通讯类型
			cell = row.getCell(8);
			value = this.getCellStringValue(cell, true);
			this.checkNull(rowIndex,8,value);
			Integer commType = getCommunicationTypeValue(rowIndex, 8, value);
			fnMeter.setCommunicationType(commType);
			

			// 通讯地址
			
			cell = row.getCell(13);
			cell.setCellStyle(textStyle);//设置单元格格式为"文本"
			cell.setCellType(HSSFCell.CELL_TYPE_STRING); 
			value = this.getCellStringValue(cell, false);
			address = value;

			// userId
			cell = row.getCell(14);
			value = this.getCellStringValue(cell, false);
			config_user_id = value;

			// 协议
			cell = row.getCell(15);
			value = this.getCellStringValue(cell, false);
			String protocolId = value.split("#")[0].trim();
			FnProtocol fnProtocol = ConstantDBBaseData.ProtocolMap.get(protocolId);
			if(fnProtocol==null){
				throw new ConfigUploadException((rowIndex+1)+"行16列电表协议不存在" );
			}
			if(!FineinConstant.EnergyType.Dian.equals(fnProtocol.getEnergyTypeId())){
				throw new ConfigUploadException((rowIndex+1)+"行16列使用非电表类型协议" );
			}                                 
			fnMeter.setProtocolId(protocolId);
			//查询协议
			FnProtocol protocol = getProtocol(rowIndex, 15, protocolId);
			
			// 服务端ip
			cell = row.getCell(9);
			value = this.getCellStringValue(cell, false);
			this.check(commType, protocol, rowIndex, 9, value);
			fnMeter.setClientIp(value.trim());
			
			//服务端口
			cell = row.getCell(10);
			value = this.getCellStringValue(cell, false);
			this.check(commType, protocol, rowIndex, 10, value);
			try {
				if(value==null||"".equals(value.trim())){
					fnMeter.setClientPort(null);
				}else{
					int post = Integer.parseInt(value.trim());
					if(post > 0 && post < 65535){
						fnMeter.setClientPort(post);
					}else{
						throw new Exception();
					}
				}
				
			} catch (Exception e) {
			throw new ConfigUploadException((rowIndex+1)+"行11列客户端端口号格式不正确：" +value);
			}
			
			//客户端Ip
			cell = row.getCell(11);
			value = this.getCellStringValue(cell, false);
			this.check(commType, protocol, rowIndex, 11, value);
			fnMeter.setServerIp(value.trim());
			// 客户端口
			cell = row.getCell(12);
			value = this.getCellStringValue(cell, false);
			this.check(commType, protocol, rowIndex, 12, value);
			try {
				if(value==null||"".equals(value.trim())){
					fnMeter.setServerPort(null);
				}else{
					int post = Integer.parseInt(value.trim());
					if(post > 0 && post < 65535){
						fnMeter.setServerPort(post);
					}else{
						throw new Exception();
					}
				}
				
			} catch (Exception e) {
			throw new ConfigUploadException((rowIndex+1)+"行13列服务端端口号格式不正确：" +value);
			}
			
			fnMeter.setBillingMode(protocol.getBillingMode());
			fnMeter.setIsUse(0);
			fnMeter.setPayType(protocol.getPayType());
			fnMeter.setMeterType(protocol.getMeterType());
			
			// 安装位置
			cell = row.getCell(16);
			value = this.getCellStringValue(cell, false);
			this.checkNull(rowIndex,16,value);
			fnMeter.setInstallAddress(value);  
            //三相
			cell = row.getCell(17);
			value = this.getCellStringValue(cell, false);
			this.checkNull(rowIndex,17,value);
			isSanXiang=this.getYorNIntValue(rowIndex, 17, value);
			Map<String, Object> extendMap = new HashMap<>();
			extendMap.put("serviceId", serviceId);
			extendMap.put("kkValue", kkValue);
			extendMap.put("glIsCt", glIsCt);
			extendMap.put("ljlIsCt", ljlIsCt);
			extendMap.put("syjIsCt", syjIsCt);
			extendMap.put("address", address);
			extendMap.put("config_user_id", config_user_id);
			extendMap.put("isSanXiang", isSanXiang);
			fnMeter.setExtend(objectMapper.writeValueAsString(extendMap));
			meterList.add(fnMeter);
			
//			{//数据字典
//				Meter meter = new Meter();
//				meter.setId(fnMeter.getId());
//
//				if(dictionaryLogicService.staticSelect(meter).size()==0){
//					meter.setName(fnMeter.getInstallAddress());
//					meter.setType(FineinConstant.MeterType.Dian);
//					meter.setCt(fnMeter.getRadio());
//					dictionaryLogicService.staticInsert(meter);
//				}else{
//					Meter meterUpdate = new Meter();
//					meterUpdate.setName(fnMeter.getInstallAddress());
//					meterUpdate.setType(FineinConstant.MeterType.Dian);
//					meterUpdate.setCt(fnMeter.getRadio());
//					dictionaryLogicService.staticUpate(meter, meterUpdate);
//				}
//				if(serviceId != null && "".equals(serviceId.trim())){
//					DTOMeterRealtion relation = new DTOMeterRealtion();
//					relation.setMeterId(meter.getId());
//					relation.setMeterType(FineinConstant.MeterType.Dian);
//					relation.setOrder(1);
//					DictionaryTopologyRelation dictionaryTopologyRelation = new DictionaryTopologyRelation();
//					dictionaryTopologyRelation.setId(dictionaryTopologyRelation.buildId());
//					dictionaryTopologyRelation.setDictionaryTopologyId(dataToplogyId);
//					dictionaryTopologyRelation.setCategoryCode(dataToplogyType);
//					dictionaryTopologyRelation.setElementStart(serviceId.trim());
//					dictionaryTopologyRelation.setElementStartCategoryCode("22");
//					dictionaryTopologyRelation.setElementEnd(fnMeter.getId());
//					dictionaryTopologyRelation.setElementEndCategoryCode("MT");
//					dictionaryTopologyRelation.setTopologyType(1);
//					dictionaryTopologyRelation.setRemark(objectMapper.writeValueAsString(meter));
//					dictionaryTopologyRelationService.saveDictionaryTopologyRelation(dictionaryTopologyRelation);
//				}
//			}
		}
		if(meterList.size() == 0){
			throw new ConfigUploadException("上传仪表数据行数为0");
		}
		
		//查询所有仪表
		List<FnMeter> dbMeterList = FNMeterService.query(new FnMeter());
		Map<String,FnMeter> meterMap = new HashMap<String,FnMeter>();
		if(dbMeterList != null){
			for(FnMeter meter : dbMeterList){
				meterMap.put(meter.getId(), meter);
			}
		}
//		StringBuffer exsitMeterSb = new StringBuffer();
		
		List<FnMeter> exsitMeterList = new ArrayList<FnMeter>();
		List<FnMeter> newMeterList = new ArrayList<FnMeter>();
		for(FnMeter meter : meterList){
			if(meterMap.containsKey(meter.getId())){
				FnMeter dbMeter = meterMap.get(meter.getId());
				if(!FineinConstant.EnergyType.Dian.equals(dbMeter.getEnergyTypeId())){
					throw new ConfigUploadException("数据已存在相同Id的非电类型仪表，请核实修改后上传:"+meter.getId());
				}
				meter.setIsUse(dbMeter.getIsUse());
				exsitMeterList.add(meter);
			}else{
				newMeterList.add(meter);
			}
		}
		
		if(newMeterList.size() > 0){
			FNMeterService.save(newMeterList);
		}
		//更新数据库已有的数据
		for (FnMeter updata : exsitMeterList) {
			FnMeter query = new FnMeter();
			query.setId(updata.getId());
			
			FNMeterService.update(query, updata);
		}
		FnBackConfigMeter query = new FnBackConfigMeter();
		query.setLogicCode(logicCode);
		
		FnBackConfigMeter upload = new FnBackConfigMeter();
		upload.setFileId(fileResource.getId());
		upload.setFileName(fileResource.getName());
		upload.setIsExsit(EnumExsistStatus.Exsist.getValue());
		upload.setUploadTime(new Date());
		upload.setUserId(userId);
		
		FNBackConfigMeterService.update(query, upload);
		
		contentObj.put("result", EnumConfigUploadResult.Code_0.getValue());
		contentObj.put("message", "");
		
		/*if(!"".equals(exsitMeterSb.toString())){
			contentObj.put("数据库已存在编码", exsitMeterSb.toString());
		}*/
		
	}
}

