package com.persagy.finein.fnconfig.meterfault.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.dictionary.meter.DictionaryFunction;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnProtocolFunction;
import com.persagy.ems.pojo.finein.FnRecordFault;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.ems.pojo.originaldata.ElectricCurrentData;
import com.persagy.ems.pojo.originaldata.MonthData;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumFaultBodyType;
import com.persagy.finein.enumeration.EnumFaultType;
import com.persagy.finein.enumeration.EnumOriginalDataType;
import com.persagy.finein.fnconfig.meterfault.handler.FNCMeterFaultHandler;
import com.persagy.finein.service.FNOriginalDataService;
import com.persagy.finein.service.FNProjectService;
import com.persagy.finein.service.FNRecordFaultService;
import org.apache.log4j.Logger;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月17日 下午3:14:12

* 说明:基础处理
*/
@Component("FNCMeterFaultHandler")
public class FNCMeterFaultHandlerImpl extends CoreServiceImpl implements FNCMeterFaultHandler{
	
	@Resource(name="FNRecordFaultService")
	private FNRecordFaultService FNRecordFaultService;
	
	@Resource(name="FNOriginalDataService")
	private FNOriginalDataService FNOriginalDataService;
	
	@Resource(name="FNProjectService")
	private FNProjectService FNProjectService;
	
	private static Logger log = Logger.getLogger(FNCMeterFaultHandlerImpl.class);
	
	public void handle(FnMeter meter,long instantToleraterSecond,long cumulantToleraterSecond) throws Exception{
		//查询仪表对应协议
		if(meter.getProtocolId() == null || ConstantDBBaseData.ProtocolMap.get(meter.getProtocolId()) == null){//未找到仪表协议
			//删除该仪表现有中断记录
			FNRecordFaultService.removeRecord(meter.getId(), EnumFaultType.TXYC, meter.getEnergyTypeId());
			return;
		}
		List<FnProtocolFunction> functionList = ConstantDBBaseData.ProtocolFunctionMap.get(meter.getProtocolId());
		if(functionList == null || functionList.size() == 0){
			//删除该仪表现有中断记录
			FNRecordFaultService.removeRecord(meter.getId(), EnumFaultType.TXYC, meter.getEnergyTypeId());
			return;
		}
		Project project = FNProjectService.queryProject();
		if(project == null){
			return;
		}
		
		boolean isFault = false;
		JSONArray faultFunctionArray = new JSONArray();
		for(FnProtocolFunction function : functionList){
			Integer functionId = function.getFunctionId();
			DictionaryFunction dictionaryFunction = ConstantDBBaseData.DictionaryFunctionMap.get(functionId+"");
			if(dictionaryFunction == null){
				log.error("【租户管理】仪表通讯故障检测,数据字典未找到功能号:"+functionId);
				continue;
			}
			String tableType = null;
			try {
				JSONObject remarkObj = (JSONObject)JSONValue.parse(dictionaryFunction.getRemark());
				tableType = (String)remarkObj.get("entityName");
			} catch (Exception e) {
			}
			
			if(tableType == null){
				if("00".equals(dictionaryFunction.getType())){
					tableType = EnumOriginalDataType.MonthData.getValue();
				}else{
					tableType = EnumOriginalDataType.ElectriccurrentData.getValue();
				}
			}
			Date timeTo = new Date();
			if(tableType.equals(EnumOriginalDataType.MonthData.getValue())){
				Date timeFrom = new Date(timeTo.getTime() - cumulantToleraterSecond * 1000);
				//查询数据
				MonthData lastData = FNOriginalDataService.queryLastMonthDataGteLte(project.getId(), meter.getId(), functionId, timeFrom, timeTo);
				if(lastData == null || lastData.getData() == null){
					isFault = true;
					JSONObject faultObj = new JSONObject();
					faultObj.put("id", Long.valueOf(functionId));
					faultObj.put("name", dictionaryFunction.getName());
					faultFunctionArray.add(faultObj);
				}
			}else{
				Date timeFrom = new Date(timeTo.getTime() - instantToleraterSecond * 1000);
				//查询数据
				ElectricCurrentData lastData = FNOriginalDataService.queryLastEleDataGteLte(project.getId(), meter.getId(), functionId, timeFrom, timeTo);
				if(lastData == null || lastData.getData() == null){
					isFault = true;
					JSONObject faultObj = new JSONObject();
					faultObj.put("id", Long.valueOf(functionId));
					faultObj.put("name", dictionaryFunction.getName());
					faultFunctionArray.add(faultObj);
				}
			}
		}
		if(isFault){//查看是否已经存在，不存在插入
			FnRecordFault query = new FnRecordFault();
			query.setEnergyTypeId(meter.getEnergyTypeId());
			query.setFaultSrcId(meter.getId());
			query.setFaultType(EnumFaultType.TXYC.getValue());
			
			FnRecordFault old = (FnRecordFault)FNRecordFaultService.queryObject(query);
			if(old == null){
				query.setId(UUID.randomUUID().toString());
				query.setFaultTime(new Date());
				query.setFaultSrcType(EnumFaultBodyType.METER.getValue());
				query.setFaultSrcName(meter.getInstallAddress());
				query.setCreateTime(new Date());
				query.setExtend(faultFunctionArray.toString());
				FNRecordFaultService.save(query);
			}
		}else{//删除
			this.FNRecordFaultService.removeRecord(meter.getId(), EnumFaultType.TXYC, meter.getEnergyTypeId());
		}
	}
}

