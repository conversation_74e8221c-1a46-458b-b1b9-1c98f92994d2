package com.persagy.finein.fnconfig.controller;

import com.persagy.ac.service.AcSystemFunctionService;
import com.persagy.ac.service.AcSystemUserFunctionService;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.ems.pojo.ac.AcSystemFunction;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.enumeration.EnumYesNo;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：通用-查询当前用户权限
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FncUserPermissionController extends BaseController {


    @Resource(name = "AcSystemUserFunctionService")
    private AcSystemUserFunctionService acSystemUserFunctionService;

    @Resource(name = "AcSystemFunctionService")
    private AcSystemFunctionService acSystemFunctionService;

    @RequestMapping("FNCUserPermissionService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult userPermission(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            AcSystemFunction query = new AcSystemFunction();
            query.setIsParent(EnumYesNo.NO.getValue());
            query.setParentId("TenantConfig");
            List<AcSystemFunction> list = acSystemFunctionService.query(query);
            Map<String, Object> resultMap = new HashMap<String, Object>();
            try {
                Map user = (Map) dto.get("puser");
                if ("persagyAdmin".equals((String) user.get("name"))) {// 最高权限
                    for (AcSystemFunction acSystemFunction : list) {
                        resultMap.put(acSystemFunction.getId(), 1);
                    }
                    content.add(resultMap);
                    return Result.SUCCESS(content);
                }
                Map<String, AcSystemFunction> functionMap = acSystemUserFunctionService
                        .queryByUserId((String) user.get("id"));
                for (AcSystemFunction acSystemFunction : list) {
                    if (acSystemFunction.getIsParent().equals(EnumYesNo.NO.getValue())
                            && "Finein".equals(acSystemFunction.getProductId())) {
                        if (functionMap.containsKey(acSystemFunction.getId())) {
                            resultMap.put(acSystemFunction.getId(), 1);
                        } else {
                            resultMap.put(acSystemFunction.getId(), 0);
                        }
                    }
                }

            } catch (Exception e) {

            }
            content.add(resultMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCUserPermissionService");
        }
    }
}
