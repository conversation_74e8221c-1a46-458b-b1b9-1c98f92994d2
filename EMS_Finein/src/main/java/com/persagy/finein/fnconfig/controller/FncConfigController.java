package com.persagy.finein.fnconfig.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.DictionaryUtil;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.SystemPropertiesUtil;
import com.persagy.ems.pojo.finein.FnBackConfigBase;
import com.persagy.ems.pojo.finein.FnBackConfigMeter;
import com.persagy.ems.pojo.system.ErrorLog;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.exception.ConfigUploadException;
import com.persagy.finein.enumeration.EnumConfigType;
import com.persagy.finein.enumeration.EnumConfigUploadResult;
import com.persagy.finein.enumeration.EnumTenantConfigType;
import com.persagy.finein.fnconfig.handler.FNCConfigDownloadBaseHandler;
import com.persagy.finein.fnconfig.handler.FNCConfigDownloadCommonHandler;
import com.persagy.finein.fnconfig.handler.FNCConfigUploadBaseHandler;
import com.persagy.finein.fnconfig.handler.FNCConfigUploadCommonHandler;
import com.persagy.finein.service.FNBackConfigBaseService;
import com.persagy.finein.service.FNBackConfigMeterService;
import com.persagy.finein.service.FNFileResourceService;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.file.Paths;
import java.util.*;

/**
 * 后台配置
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FncConfigController extends BaseController {

    @Resource(name = "FNBackConfigBaseService")
    private FNBackConfigBaseService fnBackConfigBaseService;

    @Resource(name = "SystemPropertiesUtil")
    private SystemPropertiesUtil systemPropertiesUtil;

    @Resource(name = "FNFileResourceService")
    private FNFileResourceService fnFileResourceService;

    @Resource(name = "FNCConfigDownloadBaseHandler")
    private FNCConfigDownloadBaseHandler fncConfigDownloadBaseHandler;

    @Resource(name = "FNCConfigDownloadCommonHandler")
    private FNCConfigDownloadCommonHandler fncConfigDownloadCommonHandler;

    @Resource(name = "FNCConfigUploadBaseHandler")
    private FNCConfigUploadBaseHandler fncConfigUploadBaseHandler;

    @Resource(name = "FNCConfigUploadCommonHandler")
    private FNCConfigUploadCommonHandler fncConfigUploadCommonHandler;


    private static final List<EnumConfigType> baseTypeList = new ArrayList<EnumConfigType>();

    static {
        baseTypeList.add(EnumConfigType.Base_Floor_Config);
        baseTypeList.add(EnumConfigType.Base_Room_Config);
        baseTypeList.add(EnumConfigType.Base_Tenant_Config);
    }

    @Resource(name = "FNBackConfigMeterService")
    private FNBackConfigMeterService FNBackConfigMeterService;

    private static final List<EnumConfigType> commonTypeList = new ArrayList<EnumConfigType>();

    static {
        commonTypeList.add(EnumConfigType.Meter_Dian_Config);
        commonTypeList.add(EnumConfigType.Meter_Shui_Config);
        commonTypeList.add(EnumConfigType.Meter_ReShui_Config);
        commonTypeList.add(EnumConfigType.Meter_RanQi_Config);
        commonTypeList.add(EnumConfigType.Common_Price_Config);
    }

    /**
     * 后台配置 —— 基础配置查询
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCConfigBaseQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult configBaseQuery(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            FnBackConfigBase fnBackConfigBase = new FnBackConfigBase();
            fnBackConfigBase.setBuildingId(buildingId);

            List<FnBackConfigBase> result = fnBackConfigBaseService.query(fnBackConfigBase);
            if(result == null || result.size() == 0){
                List<FnBackConfigBase> saveList = new ArrayList<FnBackConfigBase>();
                for (int i = 0;i < baseTypeList.size();i++) {
                    FnBackConfigBase saveObj = new FnBackConfigBase();
                    saveObj.setId(UUID.randomUUID().toString());
                    saveObj.setBuildingId(buildingId);
                    saveObj.setLogicCode(baseTypeList.get(i).getKey());
                    saveObj.setName(baseTypeList.get(i).getValue());
                    saveObj.setTemplateResourceId(this.getResourceId(baseTypeList.get(i)));
                    saveObj.setIsExsit(0);
                    saveObj.setOrderBy(i+1);
                    saveList.add(saveObj);
                }
                fnBackConfigBaseService.save(saveList);
                content.addAll(saveList);
            }else{
                content.addAll(result);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCConfigBaseQueryService");
        }
    }


    /**
     * 后台配置 —— 通用配置查询
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCConfigMeterQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult configCommonQuery(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            List<FnBackConfigMeter> result = FNBackConfigMeterService.query(new FnBackConfigMeter());
            if(result == null || result.size() == 0){
                List<FnBackConfigMeter> saveList = new ArrayList<FnBackConfigMeter>();
                for (int i=0;i < commonTypeList.size();i++) {
                    FnBackConfigMeter saveObj = new FnBackConfigMeter();
                    saveObj.setId(UUID.randomUUID().toString());
                    saveObj.setLogicCode(commonTypeList.get(i).getKey());
                    saveObj.setName(commonTypeList.get(i).getValue());
                    saveObj.setTemplateResourceId(this.getResourceId(commonTypeList.get(i)));
                    saveObj.setIsExsit(0);
                    saveObj.setOrderBy(i+1);
                    saveList.add(saveObj);
                }
                FNBackConfigMeterService.save(saveList);
                content.addAll(saveList);
            }else{
                content.addAll(result);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCConfigCommonQueryService");
        }
    }


    /**
     * 后台配置 —— 删除
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCConfigDeleteService")
    @ResponseBody
    public InterfaceResult configDelete(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer type = (Integer) dto.get("type");
            String buildingId = (String) dto.get("buildingId");
            String logicCode = (String) dto.get("logicCode");

            if (type == null || logicCode == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            if (type == EnumTenantConfigType.CommonConfig.getValue().intValue()) {
                FnBackConfigMeter query = new FnBackConfigMeter();
                query.setLogicCode(logicCode);

                Set<String> set = new HashSet<>();
                set.add("fileId");
                set.add("fileName");
                set.add("uploadTime");
                FnBackConfigMeter update = new FnBackConfigMeter();
                update.setNull(set);
                update.setIsExsit(0);
                FNBackConfigMeterService.update(query, update);
            } else {
                if (buildingId == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull("buildingId"));
                }
                FnBackConfigBase query = new FnBackConfigBase();
                query.setBuildingId(buildingId);
                query.setLogicCode(logicCode);

                Set<String> set = new HashSet<>();
                set.add("fileId");
                set.add("fileName");
                set.add("uploadTime");
                FnBackConfigBase update = new FnBackConfigBase();
                update.setNull(set);
                update.setIsExsit(0);

                fnBackConfigBaseService.update(query, update);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCConfigDeleteService");
        }
    }

    private String getResourceId(EnumConfigType configType) throws Exception{
        String subdirectory = getSubdirectory();
        String resouceId = UUID.randomUUID().toString();
        FileResource resource = new FileResource();
        resource.setId(resouceId);
        resource.setName(configType.getValue());
        resource.setSuffix("xlsx");
        resource.setSubdirectory(subdirectory);
        String path = this.getPath();

        String fileDir = Paths.get(path, subdirectory).toString();
        File file = new File(fileDir);
        if(!file.exists()){
            file.mkdirs();
        }
        String filePath = this.getClass().getResource("/").getPath() + configType.getTemplatePath();
        File srcFile = new File(URLDecoder.decode(filePath, "UTF-8"));
        File destFile = new File(Paths.get(path, subdirectory).toString()+File.separator + resouceId);

        FileUtils.copyFile(srcFile, destFile);

        fnFileResourceService.save(resource);
        return resouceId;
    }


    /**
     * 后台配置 —— 下载
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCConfigDownloadService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation= Propagation.REQUIRED,rollbackFor=Exception.class)
    public InterfaceResult configDownload(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer type = (Integer) dto.get("type");
            String logicCode = (String) dto.get("logicCode");
            String buildingId = (String) dto.get("buildingId");

            if (type == null || logicCode == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            if(type.intValue() == 1){
                if (buildingId == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
            }

            String subdirectory = this.getDownloadSubdirectory();

            String resouceId = UUID.randomUUID().toString();
            FileResource resource = new FileResource();
            resource.setId(resouceId);
            resource.setSuffix("xlsx");
            resource.setSubdirectory(subdirectory);

            String path = this.getDownloadPath();

            String fileDir = Paths.get(path, subdirectory).toString();
            File file = new File(fileDir);
            if(!file.exists()){
                file.mkdirs();
            }
            EnumConfigType configType = EnumConfigType.getConfigType(logicCode);
            File srcFile = this.getSrcFile(configType, resouceId);
            File destFile = this.getDestFile(configType, resouceId);
            if(type.intValue() == 0){
                fncConfigDownloadCommonHandler.process(configType, srcFile,destFile);
            }else{
                fncConfigDownloadBaseHandler.process(configType, srcFile,destFile, buildingId);
            }

            resource.setName(configType.getDownloadName()+(buildingId == null ? "" : "-"+buildingId));
            fnFileResourceService.save(resource);

            Map<String,Object> contentObj = new HashMap<String,Object>();
            contentObj.put("id", resouceId);

            content.add(contentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCConfigDownloadService");
        }
    }

    private File getSrcFile(EnumConfigType configType, String resouceId) throws Exception{
        String subdirectory = getDownloadSubdirectory();

        String path = this.getDownloadPath();

        String fileDir = Paths.get(path, subdirectory).toString();
        File file = new File(fileDir);
        if(!file.exists()){
            file.mkdirs();
        }
        String filePath = this.getClass().getResource("/").getPath() + configType.getTemplatePath();
        return new File(URLDecoder.decode(filePath, "UTF-8"));
    }

    private File getDestFile(EnumConfigType configType, String resouceId) throws Exception{
        String subdirectory = getDownloadSubdirectory();

        String path = this.getDownloadPath();

        String fileDir = Paths.get(path, subdirectory).toString();
        File file = new File(fileDir);
        if(!file.exists()){
            file.mkdirs();
        }
        String destFilePath = Paths.get(path, subdirectory).toString()+File.separator + resouceId;
        return new File(destFilePath);
    }


    /**
     * 后台配置 —— 模板下载
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCConfigTemplateDownloadService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
    public InterfaceResult configTemplateDownload(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String logicCode = (String) dto.get("logicCode");

            if (logicCode == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            EnumConfigType configType = EnumConfigType.valueOf(logicCode);

            String resouceId = getTemplateDownloadResourceId(configType);

            Map<String,Object> contentObj = new HashMap<String,Object>();
            contentObj.put("id", resouceId);

            content.add(contentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCConfigTemplateDownloadService");
        }
    }

    private String getTemplateDownloadResourceId(EnumConfigType configType) throws Exception{
        String subdirectory = getTemplateDownloadSubdirectory();
        String resouceId = UUID.randomUUID().toString();
        FileResource resource = new FileResource();
        resource.setId(resouceId);
        resource.setName(configType.getValue());
        resource.setSuffix("xlsx");
        resource.setSubdirectory(subdirectory);
        String path = this.getTemplateDownloadPath();

        String fileDir = Paths.get(path, subdirectory).toString();
        File file = new File(fileDir);
        if(!file.exists()){
            file.mkdirs();
        }

        String filePath = this.getClass().getResource("/").getPath() + configType.getTemplatePath();
        File srcFile = new File(URLDecoder.decode(filePath, "UTF-8"));
        File destFile = new File(Paths.get(path, subdirectory).toString()+File.separator + resouceId);

        FileUtils.copyFile(srcFile, destFile);

        fnFileResourceService.save(resource);
        return resouceId;
    }


    /**
     * 后台配置 —— 模板上传
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCConfigTemplateUploadService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
    public InterfaceResult configTemplateUpload(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer type = (Integer) dto.get("type");
            String logicCode = (String) dto.get("logicCode");
            List<String> resourceId = (List<String>) dto.get("resourceId");
            String userId = getParamUserId(dto);

            String buildingId = (String) dto.get("buildingId");
            Map<String,Object> contentObj = new HashMap<String,Object>();
            Workbook wb = null;
            try {
                if (type == null || logicCode == null || resourceId == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }

                FileResource fileResource = new FileResource();
                fileResource.setId(resourceId.get(0));
                fileResource = fnFileResourceService.query(fileResource).get(0);

                String fileStorageDictionary = DictionaryUtil.getFileStorageDictionary();

                String filePath = fileStorageDictionary + fileResource.getSubdirectory() + "/" + resourceId.get(0);
                File file = new File(filePath);
                wb = WorkbookFactory.create(file);

                if(type.intValue() == EnumTenantConfigType.CommonConfig.getValue().intValue()){
                    fncConfigUploadCommonHandler.process(contentObj, type, logicCode, wb,fileResource,userId);
                }else if(type.intValue() == EnumTenantConfigType.BaseConfig.getValue().intValue()){
                    if(buildingId == null){
                        throw new Exception(ExceptionUtil.ParamIsNull("buildingId"));
                    }
                    fncConfigUploadBaseHandler.process(contentObj,buildingId, type, logicCode, wb,fileResource,userId);
                }else{
                    contentObj.put("result", EnumConfigUploadResult.Code_1.getValue());
                    contentObj.put("message", EnumConfigUploadResult.Code_1.getMessage());
                }
                content.add(contentObj);
            } catch (ConfigUploadException e) {
                e.printStackTrace();

                ErrorLog errorLog = new ErrorLog();
                errorLog.setId(UUID.randomUUID().toString());
                errorLog.setParameter(objectMapper.writeValueAsString(dto));
                errorLog.setType("FNCConfigTemplateUploadService");
                errorLog.setError(e.getMessage());
                errorLog.setTime(new Date());
                coreDao.save(errorLog);

                contentObj.put("result", EnumConfigUploadResult.Code_3.getValue());
                contentObj.put("message", EnumConfigUploadResult.Code_3.getMessage()+":"+e.getMessage());
                content.add(contentObj);
            } finally{
                try {
                    if(wb != null){
                        wb.close();
                    }
                } catch (Exception e) {
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCConfigTemplateUploadService");
        }
    }


    private String getPath() throws IOException {
        return systemPropertiesUtil.getFileStorageDirectory();
    }

    private String getSubdirectory(){
        return File.separator + "finein" + File.separator + "tenant_manage"+File.separator + "back_config"+File.separator + "config";
    }

    private String getDownloadPath() throws IOException {
        return systemPropertiesUtil.getFileStorageDirectory();
    }

    private String getDownloadSubdirectory(){
        return File.separator + "temp" + File.separator + "download";
    }

    private String getTemplateDownloadPath() throws IOException {
        return systemPropertiesUtil.getFileStorageDirectory();
    }

    private String getTemplateDownloadSubdirectory(){
        return File.separator + "temp" + File.separator + "download";
    }
}
