package com.persagy.finein.fnmanage.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.finein.communication.exception.MeterSetException;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNTenantService;
import com.persagy.finein.service.FNUserService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：租户管理-仪表设置
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FntMeterSetController extends BaseController {

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @RequestMapping("FNTMeterSetService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult meterSet(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String meterId = (String) dto.get("meterId");
            String tenantId = (String) dto.get("tenantId");
            String userId = this.getParamUserId(dto);
            Integer type = (Integer) dto.get("type");
            Integer setType = (Integer) dto.get("setType");
            if (meterId == null || setType == null || userId == null||tenantId==null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            FnTenant tenant = fnTenantService.queryOne(tenantId);
            //设置仪表设置参数
            Map<String,Object> map = new HashMap<String,Object>();
            map.put("tenantId", tenantId);
            map.put("tenantName", tenant.getName());
            DTOUser user = fnUserService.queryUserByUserId(userId);
            map.put("user", user);

            Map<String, Object> contentObj = new HashMap<>();
            content.add(contentObj);
            FnMeter meter = fnMeterService.queryMeterById(meterId);
            ICommunication iCommunication = (ICommunication) SystemConstant.context.getBean(meter.getProtocolId());
            if (iCommunication == null) {
                contentObj.put("result", EnumMeterSetStaus.Other);// 不支持此功能
                return Result.SUCCESS(content);
            }
            boolean flag = false;
            try {
                if (setType == EnumMeterSetType.PAULELE.getValue().intValue()) {// 保电
                    flag = iCommunication.settingPaulEle(meter,type==0? EnumPaulEleStatus.UnPaulEle:EnumPaulEleStatus.PaulEle,map);
                }else if (setType == EnumMeterSetType.REMAINCLEAR.getValue().intValue()) {// 剩余清零
                    flag = iCommunication.clearRemain(meter,map);
                } else if (setType == EnumMeterSetType.GATE.getValue().intValue()) {//闸
                    flag = iCommunication.settingGate(meter,type==0? EnumGateStatus.Fen:EnumGateStatus.He,map);
                } else if (setType == EnumMeterSetType.OVERDRAFT.getValue().intValue()) {// 透支金额
                    Double overDraft =DoubleFormatUtil.Instance().getDoubleData(dto.get("value"));
                    if (overDraft == null) {
                        throw new Exception(ExceptionUtil.ParamIsNull());
                    }
                    flag = iCommunication.overdraft(meter, overDraft,map);
                }
                else if (setType == EnumMeterSetType.UPDATEPRICE.getValue().intValue()) {// 设置价格
                    List<Map<String, Object>> priceList = (List<Map<String, Object>>) dto.get("price");
                    if (priceList == null) {
                        throw new Exception(ExceptionUtil.ParamIsNull());
                    }
                    //租户下的所有电表
                    List<FnMeter> fnMeters = fnMeterService.queryMeterListByTenantId(tenantId);
                    for (FnMeter fnMeter:fnMeters) {
                        Integer meterType = fnMeter.getMeterType();
                        if (meterType == EnumMeterType.Common.getValue().intValue()) {// 普通仪表
                            Double price = null;
                            for (Map<String, Object> priceMap : priceList) {
                                if (EnumPriceDetail.L.getValue().equals(priceMap.get("type"))) {
                                    price= DoubleFormatUtil.Instance().getDoubleData(priceMap.get("value"));
                                }
                            }
                            flag = iCommunication.settingPrice(meter, EnumPriceDetail.L, price,map);
                        } else {// 多费率
                            Map<String, Double> price = new HashMap<>();
                            for (Map<String, Object> priceMap : priceList) {
                                price.put((String)priceMap.get("type"), DoubleFormatUtil.Instance().getDoubleData(priceMap.get("value")));
                            }
                            flag = iCommunication.settingBatchPrice(meter, price, map);
                        }
                    }
                }
            } catch (MeterSetException e) {
                // 不支持此功能
                contentObj.put("result", EnumMeterSetStaus.Other.getValue());
                return Result.SUCCESS(content);
            }
            if (flag) {
                contentObj.put("result", EnumMeterSetStaus.Sucess.getValue());
            } else {
                contentObj.put("result", EnumMeterSetStaus.Fail.getValue());
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTMeterSetService");
        }
    }


}
