package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnRecordPrePayErrorOrder;
import com.persagy.finein.enumeration.EnumErrorOrderStatus;
import com.persagy.finein.service.FNRecordPrePayErrorOrderService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：异常账单查询
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntErrorOrderQueryController extends BaseController {


    @Resource(name = "FNRecordPrePayErrorOrderService")
    private FNRecordPrePayErrorOrderService fnRecordPrePayErrorOrderService;

    @RequestMapping("FNTErrorOrderQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult errorOrderQuery(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");
            String energyTypeId = (String) dto.get("energyTypeId");
            Integer status = (Integer) dto.get("status");
            if (timeFrom == null || timeTo == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnRecordPrePayErrorOrder query = new FnRecordPrePayErrorOrder();
            query.setEnergyTypeId(energyTypeId);
            if (status != null) {
                if (status == 1) {
                    query.setSpecialOperation("status", SpecialOperator.$ne,
                            EnumErrorOrderStatus.NoProcess.getValue().intValue());
                }else{
                    query.setStatus(EnumErrorOrderStatus.NoProcess.getValue());
                }
            }
            query.setSpecialOperation("orderTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("orderTime", SpecialOperator.$lt, standard.parse(timeTo));
            int count = fnRecordPrePayErrorOrderService.count(query);
            query.setSkip(Long.valueOf(pageIndex) * pageSize);
            query.setLimit(Long.valueOf(pageSize));
            query.setSort("orderTime", EMSOrder.Desc);
            List<FnRecordPrePayErrorOrder> quetyList = fnRecordPrePayErrorOrderService.query(query);
            Map<Object, Object> map = new HashMap<>();
            map.put("list", quetyList);
            map.put("count", count);
            content.add(map);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTErrorOrderQueryService");
        }
    }


}
