package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.finein.FnRemoteRechargeStatus;
import com.persagy.ems.pojo.finein.FnTenantFlag;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNRemoteRechargeStatusService;
import com.persagy.finein.service.FNTenantFlagService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理 接口名：自动发送短信设置
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntPrePayBlacklistSaveController extends BaseController {

    @Resource(name = "FNRemoteRechargeStatusService")
    private FNRemoteRechargeStatusService fnRemoteRechargeStatusService;

    @Resource(name = "FNTenantFlagService")
    private FNTenantFlagService fnTenantFlagService;

    @RequestMapping("FNTPrePayBlacklistSaveService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult prePayBlacklistSave(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            List<Map<String, Object>> list = (List<Map<String, Object>>) dto.get("list");
            List<FnRemoteRechargeStatus> saveList = new ArrayList<FnRemoteRechargeStatus>();
            List<String> updateYesList = new ArrayList<String>();
            if (list != null) {
                Map<String, FnTenantFlag> flagMap = fnTenantFlagService.queryFlag();
                Map<String, FnRemoteRechargeStatus> remoteRechargeStatusMap = fnRemoteRechargeStatusService.queryMap(null,
                        EnumYesNo.NO);
                Map puser = (Map) dto.get("puser");
                String id = (String) puser.get("id");
                String name = (String) puser.get("showName");
                Date now = new Date();
                Map<String, String> noMap = new HashMap<String, String>();
                for (Map<String, Object> map : list) {
                    String tenantId = (String) map.get("tenantId");
                    String buildingId = (String) map.get("buildingId");
                    FnTenantFlag fnTenantFlag = flagMap.get(tenantId);
                    if (fnTenantFlag == null) {
                        throw new Exception("住户全码不存在:" + (String) map.get("tenantId"));
                    }
                    noMap.put(tenantId, tenantId);
                    if (remoteRechargeStatusMap.containsKey(tenantId)) {
                        continue;
                    } else {
                        FnRemoteRechargeStatus save = new FnRemoteRechargeStatus();
                        save.setTenantFlag(fnTenantFlag.getTenantFlag());
                        save.setSystemCode(1 + "");// 微信
                        save.setTenantId(tenantId);
                        save.setBuildingId(buildingId);
                        fnRemoteRechargeStatusService.remove(save);
                        save.setUserId(id);
                        save.setUserName(name);
                        save.setLastTime(now);
                        save.setRemoteRechargeStatus(EnumYesNo.NO.getValue());
                        saveList.add(save);
                    }
                }
                for (Map.Entry<String, FnRemoteRechargeStatus> entry : remoteRechargeStatusMap.entrySet()) {
                    if (!noMap.containsKey(entry.getKey())) {
                        updateYesList.add(entry.getValue().getTenantFlag());
                    }
                }
            }
            if (updateYesList.size() > 0) {
                fnRemoteRechargeStatusService.updateStatus(updateYesList, EnumYesNo.YES);
            }
            fnRemoteRechargeStatusService.save(saveList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPrePayBlacklistSaveService");
        }
    }


}
