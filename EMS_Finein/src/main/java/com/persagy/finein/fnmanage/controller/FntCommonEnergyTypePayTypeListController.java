package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.finein.FnEnergyType;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.service.FNEnergyTypeService;
import com.persagy.finein.service.FNTenantPayTypeService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理
 * 接口名：通用-查询能源付费类型
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntCommonEnergyTypePayTypeListController extends BaseController {

    @Resource(name = "FNEnergyTypeService")
    private FNEnergyTypeService fnEnergyTypeService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;


    @RequestMapping("FNTCommonEnergyTypePayTypeListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonEnergyTypePayTypeList(@RequestParam(value="jsonString") String jsonString) {
        try {
            List content = new ArrayList();
            List<FnEnergyType> energyTypeList = fnEnergyTypeService.queryList(EnumValidStatus.VALID, EMSOrder.Asc);
            if(energyTypeList != null){
                List<String> energyTypePayTypeList = fnTenantPayTypeService.queryEnergyTypePayTypeList();
                EnumPayType[] payTypeArray = EnumPayType.values();
                for(FnEnergyType energyType : energyTypeList){
                    for(EnumPayType payType : payTypeArray){
                        String key = energyType.getId()+"_"+payType.getValue();
                        if(energyTypePayTypeList.contains(key)){
                            Map<String,Object> map = new HashMap<String,Object>();
                            map.put("id", key);
                            map.put("name", energyType.getName()+"-"+payType.getView());
                            content.add(map);
                        }
                    }
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTCommonEnergyTypePayTypeListService");
        }
    }


}
