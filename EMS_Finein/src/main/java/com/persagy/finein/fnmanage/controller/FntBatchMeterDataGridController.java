package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.dto.DTORoomMeter;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnProtocolFunction;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.meterdata.MeterData;
import com.persagy.ems.pojo.originaldata.MonthData;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.core.util.PathUtil;
import com.persagy.finein.enumeration.EnumMeterType;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.*;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;

/**
 * 项目名：租户管理 接口名：批量查询下载表底读数
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "unchecked", "rawtypes", "deprecation" })
public class FntBatchMeterDataGridController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNMeterDataService")
    private FNMeterDataService fnMeterDataService;

    @Resource(name = "PathUtil")
    private PathUtil pathUtil;

    @Resource(name = "FNFileResourceService")
    private FNFileResourceService fnFileResourceService;

    @Resource(name = "FNRoomService")
    private FNRoomService fnRoomService;

    @Resource(name = "FNOriginalDataService")
    private FNOriginalDataService fnOriginalDataService;


    @RequestMapping("FNTBatchMeterDataGridService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchMeterDataGrid(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String time = (String) dto.get("time");
            List<String> ids = (List<String>) dto.get("tenantList");
            String energyTypeId = (String) dto.get("energyTypeId");
            if (ids == null || ids.size() == 0) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            List<FnTenant> tenantList = fnTenantService.queryListByIds(ids);
            if (tenantList == null) {
                throw new Exception("查不到所选租户");
            }
            List<Map<String, Object>> result = new ArrayList<>();
            if (time != null && !"".equals(time)) {
                Date timeFrom = standard.parse(time);
                for (FnTenant fnTenant : tenantList) {
                    List<DTORoomMeter> roomMeterList = fnRoomService.queryRoomListByTenantId(fnTenant.getId(),
                            energyTypeId);
                    processTenant(energyTypeId, result, roomMeterList, fnTenant, timeFrom);
                }
            } else {// 查询租户激活时间仪表读数
                for (FnTenant fnTenant : tenantList) {
                    List<DTORoomMeter> roomMeterList = fnRoomService.queryRoomListByTenantId(fnTenant.getId(),
                            energyTypeId);
                    Date timeFrom = fnTenant.getActiveTime();
                    processTenant(energyTypeId, result, roomMeterList, fnTenant, timeFrom);
                }
            }
            Integer isDownload = (Integer) dto.get("isDownload");
            if (isDownload == 0) {
                content.addAll(result);
            } else {
                String subdirectory = pathUtil.getTempDownloadSubDir();
                String resouceId = UUID.randomUUID().toString();
                FileResource resource = new FileResource();
                resource.setId(resouceId);
                StringBuffer fileNameSb = new StringBuffer();
                // 仪表读数记录-日期 时间
                fileNameSb.append("仪表读数记录").append("-");
                if (time != null && !"".equals(time)) {
                    fileNameSb.append(time.replaceAll(":", "："));
                } else {
                    fileNameSb.append("激活时间");
                }
                resource.setName(fileNameSb.toString());
                resource.setSuffix("xls");
                resource.setSubdirectory(subdirectory);

                String path = pathUtil.getPath();

                String fileDir = Paths.get(path, subdirectory).toString();
                File file = new File(fileDir);
                if (!file.exists()) {
                    file.mkdirs();
                }
                this.buildExcel(time, path, subdirectory, resouceId, result,energyTypeId);

                fnFileResourceService.save(resource);
                Map<String, Object> newContentObj = new HashMap<>();
                newContentObj.put("id", resouceId);
                content.add(newContentObj);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTBatchMeterDataGridService");
        }
    }

    private void processTenant(String energyTypeId, List<Map<String, Object>> result, List<DTORoomMeter> roomMeterList,
                               FnTenant fnTenant, Date timeFrom) throws Exception {
        if (roomMeterList != null&&roomMeterList.size()>0) {
            Map<String, Object> contentObject = new HashMap<>();
            contentObject.put("tenantId", fnTenant.getId());
            contentObject.put("tenantName", fnTenant.getName());
            contentObject.put("time", timeFrom);
            //租户下所有仪表的读数总条数
            Integer count=0;
            // contentObject.put("roomCodes", fnTenant.getRoomCodes());
            List<Map<String, Object>> roomList = new ArrayList<>();
            contentObject.put("roomList", roomList);
            for (DTORoomMeter roomMeter : roomMeterList) {
                //租户房间下所有仪表的读数总条数
                int orderCount=0;
                Map<String, Object> roomMap = new HashMap<String, Object>();
                List<Map<String, Object>> meterList = new ArrayList<>();
                roomList.add(roomMap);
                roomMap.put("roomCode", roomMeter.getRoomCode());
                roomMap.put("meterList", meterList);
                //房间下所有的仪表
                List<DTOMeter> dTOMeterList = roomMeter.getMeterList();
                if (dTOMeterList != null) {
                    for (DTOMeter dtoMeter : dTOMeterList) {

                        Map<String, Object> meterMap = new HashMap<String, Object>();
                        Integer meterType = dtoMeter.getMeterType();
                        meterMap.put("meterId", dtoMeter.getMeterId());
                        meterMap.put("energyTypeId", dtoMeter.getEnergyTypeId());
                        meterMap.put("energyTypeName",
                                EnergyTypeUtil.queryEnergyTypeNameById(dtoMeter.getEnergyTypeId()));
                        meterMap.put("meterType", dtoMeter.getMeterType());
                        meterMap.put("energyUnit", UnitUtil.getCumulantUnit(dtoMeter.getEnergyTypeId()));
                        meterMap.put("ct", dtoMeter.getRadio());
                        //仪表读数是否乘以倍率
                        JSONObject extendObj = (JSONObject) JSONValue.parse(dtoMeter.getExtend());
                        Long ljlIsCt = (Long)extendObj.get("ljlIsCt");
                        Double radio = 1.0;
                        if(ljlIsCt != null && ljlIsCt.intValue() == 1){
                            radio = dtoMeter.getRadio();
                        }
                        List<Map<String, Object>> list = new ArrayList<>();
                        if (meterType == EnumMeterType.Common.getValue().intValue()) {// 普通表
                            Map<String, Object> dataMap = new HashMap<>();
                            String protocolId = dtoMeter.getProtocolId();
                            Integer cumulantFunctionId = FunctionTypeUtil
                                    .getCumulantFunctionId(dtoMeter.getEnergyTypeId());
                            Integer functionId = this.queryCumulantFunctionId(protocolId, energyTypeId, cumulantFunctionId);
                            if(functionId!=null){
                                dataMap.put("type", "L");
                                Double data = this.queryMeterData(fnTenant,dtoMeter,functionId,timeFrom);
                                dataMap.put("value", data == null ? null :data*radio);
                                dataMap.put("isCt",ljlIsCt==null?null: ljlIsCt.intValue());
                                if (dtoMeter.getEnergyTypeId().equals(FineinConstant.EnergyType.Dian)) {
                                    dataMap.put("name", "正向有功电能");
                                } else {
                                    dataMap.put("name", "");
                                }
                            }
                            list.add(dataMap);
                        } else {// 多费率
                            Map<String, Integer> functionMap = FunctionTypeUtil.getCumulantDianMultiple();
                            for (Map.Entry<String, Integer> entry : functionMap.entrySet()) {
                                Integer functionId = this.queryCumulantFunctionId(dtoMeter.getProtocolId(), energyTypeId, entry.getValue());
                                if(functionId==null){
                                    continue;
                                }
                                Double data = this.queryMeterData(fnTenant, dtoMeter, functionId, timeFrom);
                                Map<String, Object> hashMap = new HashMap<>();
                                hashMap.put("type", entry.getKey());
                                String name = getFunctionName(entry.getKey());
                                hashMap.put("name", name);
                                hashMap.put("value", data == null ? null : data*radio);
                                hashMap.put("isCt", ljlIsCt.intValue());
                                list.add(hashMap);
                            }
                        }
                        meterMap.put("list", list);
                        //仪表所对应的读数总条数
                        meterMap.put("meterOrderCount", list.size());
                        count+=list.size();
                        orderCount+=list.size();
                        meterList.add(meterMap);
                    }
                }
                roomMap.put("orderCount", orderCount);
            }
            contentObject.put("count", count);
            result.add(contentObject);
        }
    }

    private Double queryMeterData(FnTenant fnTenant, DTOMeter dtoMeter, Integer functionId, Date timeFrom) throws Exception {
        MeterData data = fnMeterDataService.queryMeterDataEqual(fnTenant.getBuildingId(),
                dtoMeter.getMeterId(), functionId,
                EnumTimeType.T1.getValue().intValue(), timeFrom);
        if(data!=null&&data.getData()!=null){
            return data.getData();
        }else{//查询原始数据
            Date from  = new Date(timeFrom.getTime() - FineinConstant.Time.Minute_15 * 2);
            Date to  = new Date(timeFrom.getTime() + FineinConstant.Time.Minute_15 * 2);
            MonthData monthData = fnOriginalDataService.queryLastMonthDataGteLte(fnTenant.getBuildingId(), dtoMeter.getMeterId(), functionId, from, to);
            if(monthData != null && monthData.getData() != null){
                return monthData.getData();
            }

        }
        return null;
    }


    private String getFunctionName(String str) {
        String name = null;
        switch (str) {
            case "J":
                name = "尖段";
                break;
            case "F":
                name = "峰段";
                break;
            case "G":
                name = "谷段";
                break;
            case "P":
                name = "平段";
                break;
            default:
                break;
        }
        return name;
    }

    public Integer queryCumulantFunctionId(String protocolId,String energyTypeId,int functionId){
        List<FnProtocolFunction> functionList = ConstantDBBaseData.ProtocolFunctionMap.get(protocolId);
        if(functionList != null){
            for(FnProtocolFunction protocolFunction : functionList){
                if(protocolFunction.getFunctionId().intValue() == functionId){
                    return functionId;
                }
            }
        }
        return null;
    }

    private void buildExcel(String time, String path, String subPath, String fileName,
                            List<Map<String, Object>> result,String energyTypeId) {
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);

        // 创建标题
        HSSFRow row0 = sheet.createRow((short) 0);
        {
            HSSFCell cell = row0.createCell((short) 0);
            cell.setCellStyle(style);
            cell.setCellValue("租户编号");
        }
        {
            HSSFCell cell = row0.createCell((short) 1);
            cell.setCellStyle(style);
            cell.setCellValue("租户名称");
        }

        {
            HSSFCell cell = row0.createCell((short) 2);
            cell.setCellStyle(style);
            if (time == null||"".equals(time)) {
                cell.setCellValue("租户激活时间");
            } else {
                cell.setCellValue("时间");
            }
        }
        {
            HSSFCell cell = row0.createCell((short) 3);
            cell.setCellStyle(style);
            cell.setCellValue("房间编号");
        }
        {
            HSSFCell cell = row0.createCell((short) 4);
            cell.setCellStyle(style);
            cell.setCellValue("仪表ID");
        }
        {
            HSSFCell cell = row0.createCell((short) 5);
            cell.setCellStyle(style);
            cell.setCellValue("仪表能源类型");
        }
        {
            HSSFCell cell = row0.createCell((short) 6);
            cell.setCellStyle(style);
            if(energyTypeId==null){
                cell.setCellValue("CT/倍率");
            }else if(FineinConstant.EnergyType.Dian.equals(energyTypeId)){
                cell.setCellValue("CT");
            }else{
                cell.setCellValue("倍率");
            }
        }
        {
            HSSFCell cell = row0.createCell((short) 7);
            cell.setCellStyle(style);
            cell.setCellValue("所选时间的仪表读数");
        }
        {
            HSSFCell cell = row0.createCell((short) 8);
            cell.setCellStyle(style);
            if(energyTypeId==null){
                cell.setCellValue("是否乘以CT/倍率");
            }else if(FineinConstant.EnergyType.Dian.equals(energyTypeId)){
                cell.setCellValue("是否乘以CT");
            }else{
                cell.setCellValue("是否乘以倍率");
            }
        }

        if (result != null) {
            int currentRow = 1;
            for (int i = 0; i < result.size(); i++) {
                Map<String, Object> tenant = (Map<String, Object>) result.get(i);
                Integer count = (Integer) tenant.get("count");
                String tenantId = (String) tenant.get("tenantId");
                String tenantName = (String) tenant.get("tenantName");
                Date time2 = (Date) tenant.get("time");
                List<Map<String, Object>> roomList = (List<Map<String, Object>>) tenant.get("roomList");
                HSSFRow row = sheet.createRow((short) currentRow);
                {
                    HSSFCell cell = row.createCell((short) (0));
                    cell.setCellValue(tenantId == null ? " " : tenantId);
                }
                {
                    HSSFCell cell = row.createCell((short) (1));
                    cell.setCellValue(tenantName == null ? " " : tenantName);
                }
                {
                    HSSFCell cell = row.createCell((short) (2));
                    cell.setCellValue(time2 == null ? " " : standard.format(time2));
                }

                if (roomList != null && roomList.size() > 0) {
                    if(count>0){
                        sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + count - 1, 0, 0));
                        sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + count - 1, 1, 1));
                        sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + count - 1, 2, 2));
                    }
                    for (int j = 0; j < roomList.size(); j++) {
                        if(j!=0){
                            row = sheet.createRow((short) currentRow);
                        }
                        Map<String, Object> roomMap = (Map<String, Object>) roomList.get(j);
                        String roomCode = (String) roomMap.get("roomCode");
                        int orderCount = (int) roomMap.get("orderCount");
                        if(orderCount>0){
                            sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + orderCount - 1, 3, 3));
                        }
                        List<Map<String, Object>> meterList = (List<Map<String, Object>>) roomMap.get("meterList");

                        if (meterList != null && meterList.size() > 0) {
                            for (int k = 0; k < meterList.size(); k++) {
                                Map<String, Object> meterMap = meterList.get(k);
                                String meterId = (String) meterMap.get("meterId");
                                String energyTypeName = (String) meterMap.get("energyTypeName");
                                String energyUnit = (String) meterMap.get("energyUnit");
                                int meterOrderCount = (int) meterMap.get("meterOrderCount");
                                if(meterOrderCount>0){
                                    sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + meterOrderCount - 1, 4, 4));
                                }
                                Double ct = (Double) meterMap.get("ct");
                                List<Map<String, Object>> list = (List<Map<String, Object>>) meterMap.get("list");
                                if(k!=0){
                                    row = sheet.createRow((short) currentRow);
                                }
                                for (int z = 0; z < list.size(); z++) {
                                    if (z != 0) {
                                        row = sheet.createRow((short) currentRow);
                                    }
                                    {
                                        HSSFCell cell = row.createCell((short) (3));
                                        cell.setCellValue(roomCode == null ? "--" : roomCode);
                                    }
                                    {
                                        HSSFCell cell = row.createCell((short) (4));
                                        cell.setCellValue(meterId == null ? "--" : meterId);
                                    }
                                    {
                                        HSSFCell cell = row.createCell((short) (5));
                                        cell.setCellValue(energyTypeName == null ? "--" : energyTypeName);
                                    }

                                    {
                                        HSSFCell cell = row.createCell((short) (6));
                                        cell.setCellValue(ct == null ? "--" : ct + "");
                                    }
                                    {
                                        HSSFCell cell = row.createCell((short) (7));
                                        Double data = DoubleFormatUtil.Instance().getDoubleData_00(list.get(z).get("value"));
                                        switch ((String) list.get(z).get("type")) {
                                            case "L":
                                                cell.setCellValue(data==null?"--":data + "(" + energyUnit + ")");
                                                break;
                                            case "J":
                                                cell.setCellValue(data==null?"尖段:":"尖段:" + data + "(" + energyUnit + ")");
                                                break;
                                            case "F":
                                                cell.setCellValue(data==null?"峰段:":"峰段:" + data + "(" + energyUnit + ")");
                                                break;
                                            case "G":
                                                cell.setCellValue(data==null?"谷段:":"谷段:" + data + "(" + energyUnit + ")");
                                                break;
                                            case "P":
                                                cell.setCellValue(data==null?"平段:":"平段:" + data + "(" + energyUnit + ")");
                                                break;
                                            default:
                                                break;
                                        }
                                    }
                                    {
                                        HSSFCell cell = row.createCell((short) (8));
                                        Integer isCt = (Integer) list.get(z).get("isCt");
                                        cell.setCellValue(isCt == null ? "--" : EnumYesNo.valueOf(isCt).getView());
                                    }
                                    currentRow++;
                                }
                            }
                        } else {
                            {
                                HSSFCell cell = row.createCell((short) (3));
                                cell.setCellValue("--");
                            }
                            {
                                HSSFCell cell = row.createCell((short) (4));
                                cell.setCellValue("--");
                            }
                            {
                                HSSFCell cell = row.createCell((short) (5));
                                cell.setCellValue("--");
                            }
                            {
                                HSSFCell cell = row.createCell((short) (6));
                                cell.setCellValue("--");
                            }
                            {
                                HSSFCell cell = row.createCell((short) (7));
                                cell.setCellValue("--");
                            }
                            {
                                HSSFCell cell = row.createCell((short) (8));
                                cell.setCellValue("--");
                            }
                            currentRow++;
                        }
                    }
                }
            }

            String newFilePath = Paths.get(path, subPath, fileName).toString();
            try {
                fout = new FileOutputStream(newFilePath);
                wb.write(fout);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (fout != null) {
                    try {
                        fout.close();
                    } catch (IOException e1) {
                    }
                }
                if (wb != null) {
                    try {
                        wb.close();
                    } catch (IOException e1) {
                    }
                }
            }
        }
    }
}
