package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.TimeDataUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantData;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumTenantStatus;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.service.FNTenantDataService;
import com.persagy.finein.service.FNTenantFlagService;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;


/**
 * 项目名：租户管理
 * 接口名：能耗费用报表-能耗费用报表查询（单租户）
 * <AUTHOR>
 */

@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntReportDataController extends BaseController {

    @Resource(name = "FNTenantDataService")
    private FNTenantDataService fnTenantDataService;

    @Resource(name = "FNTenantFlagService")
    private FNTenantFlagService fnTenantFlagService;

    @RequestMapping("FNTReportDataService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult reportData(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantFlag = (String) dto.get("tenantFlag");
            String energyTypeId = (String) dto.get("energyTypeId");
            String time = (String) dto.get("time");
            Integer timeType = (Integer) dto.get("timeType");

            if (tenantFlag == null || energyTypeId == null || time == null || timeType == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            Date timeFrom = standard.parse(time);
            String energyUnit = UnitUtil.getCumulantUnit(energyTypeId);
            Map<String, Object> contentMap = new HashMap<String, Object>();
            contentMap.put("energyUnit", energyUnit);

            FnTenant tenant = fnTenantFlagService.queryTenantByFlag(tenantFlag);
            if (tenant == null) {
                throw new Exception("租户不存在:" + tenantFlag);
            }
            Double sumData = 0.0;
            Date timeTo = null;
            Map<String, Double> timeDataMap = null;
            Date now = new Date();
            switch (timeType) {
                case 0:// 日
                    timeTo = DateUtils.addDays(timeFrom, 1);
                    if (timeTo.getTime() > now.getTime()) {
                        timeTo = DateUtils.addHours(now, -1);
                    }
                    timeDataMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.T1);
                    break;
                case 1:// 月
                    timeTo = DateUtils.addMonths(timeFrom, 1);
                    if (timeTo.getTime() > now.getTime()) {
                        timeTo = DateUtils.truncate(now, Calendar.DATE);
                    }
                    timeDataMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.T2);
                    break;
                case 2:// 年
                    now = new Date();
                    timeTo = DateUtils.addYears(timeFrom, 1);
                    if (timeTo.getTime() > now.getTime()) {
                        timeTo = DateUtils.truncate(now, Calendar.MONTH);
                    }
                    timeDataMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.T4);
                    break;
                default:
                    break;
            }
            if (tenant.getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue()) {
                if (tenant.getLeaveTime() != null) {
                    timeTo = new Date(tenant.getLeaveTime().getTime());
                }
            }
            List<Object> dataList = new ArrayList<Object>();
            contentMap.put("dataList", dataList);
            List<FnTenantData> tenantDataList = fnTenantDataService.queryListGteLt(tenant.getBuildingId(), tenant.getId(),
                    EnumTimeType.T1, energyTypeId, timeFrom, timeTo, EnumEnergyMoney.Energy);
            if (dataList != null) {
                for (FnTenantData tenantData : tenantDataList) {
                    if (tenantData.getData() != null) {
                        if (tenantData.getTimeFrom().getTime() < tenant.getActiveTime().getTime()) {// 小于激活时间，不统计
                            continue;
                        }
                        sumData += tenantData.getData();
                        if (timeType == 2) {
                            Date date = DateUtils.truncate(tenantData.getTimeFrom(), Calendar.MONTH);
                            if (timeDataMap.get(standard.format(date)) == null) {
                                timeDataMap.put(standard.format(date), tenantData.getData());

                            } else {
                                timeDataMap.put(standard.format(date),
                                        timeDataMap.get(standard.format(date)) + tenantData.getData());
                            }
                        } else if (timeType == 1) {// 月
                            Date date = DateUtils.truncate(tenantData.getTimeFrom(), Calendar.DATE);
                            if (timeDataMap.get(standard.format(date)) == null) {
                                timeDataMap.put(standard.format(date), tenantData.getData());
                            } else {
                                timeDataMap.put(standard.format(date),
                                        timeDataMap.get(standard.format(date)) + tenantData.getData());
                            }
                        } else {
                            timeDataMap.put(standard.format(tenantData.getTimeFrom()), tenantData.getData());
                        }
                    }
                }
            }
            for (Map.Entry<String, Double> tenantData : timeDataMap.entrySet()) {
                Map<String, Object> dataMap = new HashMap<String, Object>();
                dataMap.put("time", standard.parse(tenantData.getKey()).getTime());
                dataMap.put("data", tenantData.getValue() == null ? 0 : tenantData.getValue());
                dataList.add(dataMap);
            }
            contentMap.put("sumData", sumData);
            content.add(contentMap);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTReportDataService");
        }
    }


}
