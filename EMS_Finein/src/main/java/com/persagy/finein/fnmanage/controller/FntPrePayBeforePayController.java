package com.persagy.finein.fnmanage.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.finein.communication.exception.MeterSetException;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.finein.enumeration.EnumPrePayType;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.*;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理 接口名：预付费充值-充值前查询
 *
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntPrePayBeforePayController extends BaseController {

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNTenantPrePayParamService")
    private FNTenantPrePayParamService fnTenantPrePayParamService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNTenantBackPayRecordService")
    private FNTenantBackPayRecordService fnTenantBackPayRecordService;

    @Resource(name = "FNBeiDianReturnRecordService")
    private FNBeiDianReturnRecordService fnBeiDianReturnRecordService;


    @RequestMapping("FNTPrePayBeforePayService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult prePayBeforePay(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");
            String meterId = (String) dto.get("meterId");
            String energyTypeId = (String) dto.get("energyTypeId");
            Integer prePayType = (Integer) dto.get("prePayType");

            if (tenantId == null || prePayType == null || energyTypeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            Map<String, Object> contentObj = new HashMap<String, Object>();
            contentObj.put("isBeiDian", EnumYesNo.NO.getValue());
            if (prePayType.intValue() == EnumPrePayType.ONLINE_METERPAY.getValue().intValue()) {
                if (meterId == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull("meterId"));
                }
                try {
                    this.processMeterPay(contentObj, tenantId, meterId, energyTypeId);
                } catch (MeterSetException e) {
                    e.printStackTrace();
                }
            } else if (prePayType.intValue() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
                this.processTenantPay(contentObj, tenantId, energyTypeId);
            } else {
                throw new Exception("不知道的prePayType:" + prePayType);
            }
            content.add(contentObj);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTPrePayBeforePayService");
        }
    }

    private void processTenantPay(Map<String, Object> contentObj, String tenantId, String energyTypeId)
            throws Exception {
        FnTenantPayType tenantPayType = fnTenantPayTypeService.query(tenantId, energyTypeId);
        if (tenantPayType == null) {
            throw new Exception("未找到租户的付费类型");
        }
        Integer billingType = tenantPayType.getPrepayChargeType();
        String billingTypeUnit = UnitUtil.getBillModeUnit(energyTypeId,
                EnumPrepayChargeType.valueOf(tenantPayType.getPrepayChargeType()));

        FnTenantPrePayParam preParam = fnTenantPrePayParamService.queryTenantPreParam(tenantId, energyTypeId);

        if (preParam != null) {
            contentObj.put("remainData", preParam.getRemainData());
            contentObj.put("lastUpdateTime", preParam.getLastUpdateTime());
        } else {
            contentObj.put("remainData", null);
            contentObj.put("lastUpdateTime", new Date());
        }
        FnTenantBackPayRecord query = new FnTenantBackPayRecord();
        query.setTenantId(tenantId);
        query.setEnergyTypeId(energyTypeId);
        query.setIsProcessed(EnumYesNo.NO.getValue());
        query.setSpecialOperation("data", SpecialOperator.$gte, 0);
        int count = fnTenantBackPayRecordService.count(query);
        contentObj.put("tenantId", tenantId);
        contentObj.put("meterId", null);
        contentObj.put("billingType", billingType);
        contentObj.put("billingTypeUnit", billingTypeUnit);
        contentObj.put("weiDaoZhangCount", count);
    }

    private void processMeterPay(Map<String, Object> contentObj, String tenantId, String meterId, String energyTypeId)
            throws Exception, MeterSetException {
        FnTenantPayType tenantPayType = fnTenantPayTypeService.query(tenantId, energyTypeId);
        if (tenantPayType == null) {
            throw new Exception("未找到租户的付费类型");
        }
        FnMeter fnMeter = fnMeterService.queryMeterById(meterId);
        Integer billingType = tenantPayType.getPrepayChargeType();
        String billingTypeUnit = UnitUtil.getBillModeUnit(energyTypeId,
                EnumPrepayChargeType.valueOf(tenantPayType.getPrepayChargeType()));

        // FnMeter fnMeter = FNMeterService.queryMeterById(meterId);

        contentObj.put("tenantId", tenantId);
        contentObj.put("meterId", meterId);
        contentObj.put("billingType", billingType);
        contentObj.put("billingTypeUnit", billingTypeUnit);
        contentObj.put("billingTypeUnit", billingTypeUnit);

        Double remainData = null;
        try {
            ICommunication communication = (ICommunication) SystemConstant.context.getBean(fnMeter.getProtocolId());
            if (communication != null) {
                remainData = communication.queryRemainData(fnMeter);
                if (remainData == null) {
                    throw new Exception("查询剩余量失败");
                }
                System.out.println("剩余量查询:" + remainData);
                if (!"DI_C_07_Y_Q_001".equals(fnMeter.getProtocolId())) {
                    Double overdraft = communication.queryOverdraft(fnMeter);//透支金额
                    if (overdraft == null) {
                        throw new Exception("查询透支金额失败");
                    }
                    remainData = remainData - overdraft;
                }
                if (fnMeter.getProtocolId().equals("DI_C_01_Y_Q_001")) {// 北电仪表
                    contentObj.put("isBeiDian", EnumYesNo.YES.getValue());
                    Double returnData;
                    FnBeiDianReturnRecord query = new FnBeiDianReturnRecord();
                    query.setMeterId(meterId);
                    List<FnBeiDianReturnRecord> list = fnBeiDianReturnRecordService.query(query);
                    if (list != null && list.size() > 0) {
                        returnData = list.get(0).getMoney() == null ? 0.0 : list.get(0).getMoney();
                    } else {
                        returnData = 0.0;
                    }
                    contentObj.put("returnData", returnData);
                    Integer saleCount = null;
                    try {
                        saleCount = communication.querySaleCount(fnMeter);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    contentObj.put("saleCount", saleCount == null ? "--" : saleCount);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
//			throw new Exception("不支持此功能");
        }

        if (remainData != null) {
            // 剩余量是否乘变比
            Double radio = 1.0;
            try {
                Long syjIsCt = (Long) ((JSONObject) JSONValue.parse(fnMeter.getExtend())).get("syjIsCt");
                if (syjIsCt != null && syjIsCt.intValue() == 1) {
                    radio = fnMeter.getRadio();
                }
            } catch (Exception e) {
            }
            contentObj.put("remainData", remainData * radio);
            contentObj.put("lastUpdateTime", new Date());
        } else {
            contentObj.put("remainData", null);
            contentObj.put("lastUpdateTime", new Date());
        }
    }

}
