package com.persagy.finein.fnmanage.compute.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.ems.pojo.originaldata.MonthData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.compute.handler.FNTComputeRemainDaysHandler;
import com.persagy.finein.service.*;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月22日 下午12:58:25
 * 
 * 说明:计算租户仪表剩余使用天数线程
 */
@Component("FNTComputeRemainDaysHandler")
public class FNTComputeRemainDaysHandlerImpl extends CoreServiceImpl implements FNTComputeRemainDaysHandler {

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;

	@Resource(name = "FNTenantPayTypeService")
	private FNTenantPayTypeService FNTenantPayTypeService;

	@Resource(name = "FNTenantBackPayDataService")
	private FNTenantBackPayDataService FNTenantBackPayDataService;

	@Resource(name = "FNTenantStatService")
	private FNTenantStatService FNTenantStatService;

	@Resource(name = "FNTenantMeterAvgService")
	private FNTenantMeterAvgService FNTenantMeterAvgService;

	@Resource(name = "FNTenantMeterRemainDaysService")
	private FNTenantMeterRemainDaysService FNTenantMeterRemainDaysService;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNOriginalDataService")
	private FNOriginalDataService FNOriginalDataService;

	@Resource(name = "FNTenantMeterStatService")
	private FNTenantMeterStatService FNTenantMeterStatService;


	@Override
	public void processBuilding(Building building, long toleraterSecond) throws Exception {
		// 查询建筑下所有租户
		List<FnTenant> tenantList = FNTenantService.queryListByValidStatus(building.getId(), EnumValidStatus.VALID);
		if (tenantList != null) {
			for (FnTenant tenant : tenantList) {
				if (tenant.getStatus().intValue() == EnumTenantStatus.ACTIVATED.getValue().intValue()) {// 只计算已激活
					this.processTenant(building, tenant, toleraterSecond);
				}
			}
		}
	}

	@Override
	public void processTenant(Building building, FnTenant tenant, long toleraterSecond) throws Exception {
		Map<String, FnTenantPayType> payTypeMap = FNTenantPayTypeService.queryPayTypeMap(tenant.getId());
		for (Map.Entry<String, FnTenantPayType> entry : payTypeMap.entrySet()) {
			if (entry.getValue().getPayType().intValue() == EnumPayType.POSTPAY.getValue().intValue()) {
				continue;// 后付费不计算
			}
			// if("20180629".equals(tenant.getId()) &&
			// "Dian".equals(entry.getValue().getEnergyTypeId())){
			// System.out.println("--");
			// }
			if (entry.getValue().getPrePayType().intValue() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {// 租户
				if (entry.getValue().getPrepayChargeType().intValue() == EnumPrepayChargeType.Qian.getValue()
						.intValue()) {

					FnTenantBackPayData lastData = FNTenantBackPayDataService.queryLastData(building.getId(),
							tenant.getId(), entry.getKey(), EnumTimeType.T0, EnumPrepayChargeType.Qian, null);
					if (lastData != null && lastData.getData() != null) {
						Integer minDays = null;
						Integer maxDays = null;
						// 历史最大费用
						FnTenantStat tenantStat = FNTenantStatService.queryData(building.getId(), tenant.getId(),
								entry.getKey(), EnumTimeType.T2, EnumStatType.Max, EnumEnergyMoney.Money);
						if (tenantStat != null && tenantStat.getData() != null
								&& tenantStat.getData().doubleValue() != 0.0) {
							minDays = Long
									.valueOf(Math.round(
											lastData.getData().doubleValue() / tenantStat.getData().doubleValue()))
									.intValue();
							if (minDays != null && minDays < 0) {
								minDays = 0;
							}
						}
						// 近*日平均值费用
						FnTenantMeterAvg tenantMeterAvg = FNTenantMeterAvgService.queryData(building.getId(),
								tenant.getId(), EnumPayBodyType.TENANT, tenant.getId(), entry.getKey(),
								EnumEnergyMoney.Money);
						if (tenantMeterAvg != null && tenantMeterAvg.getData() != null
								&& tenantMeterAvg.getData().doubleValue() != 0.0) {
							maxDays = Long
									.valueOf(Math.round(
											lastData.getData().doubleValue() / tenantMeterAvg.getData().doubleValue()))
									.intValue();
							if (maxDays != null && maxDays < 0) {
								maxDays = 0;
							}
						}
						if (minDays != null && maxDays != null && minDays > maxDays) {
							int temp = 0;
							temp = minDays;
							minDays = maxDays;
							maxDays = temp;
						}
						if (maxDays == null && minDays != null) {
							maxDays = minDays;
						} else if (maxDays != null && minDays == null) {
							minDays = maxDays;
						}

						FNTenantMeterRemainDaysService.saveData(building.getId(), tenant.getId(), entry.getKey(),
								EnumPayBodyType.TENANT, tenant.getId(), maxDays, minDays, lastData.getData());
					}
				} else {
					FnTenantBackPayData lastData = FNTenantBackPayDataService.queryLastData(building.getId(),
							tenant.getId(), entry.getKey(), EnumTimeType.T0, EnumPrepayChargeType.Liang, null);
					if (lastData != null && lastData.getData() != null) {
						Integer minDays = null;
						Integer maxDays = null;
						// 历史最大费用
						FnTenantStat tenantStat = FNTenantStatService.queryData(building.getId(), tenant.getId(),
								entry.getKey(), EnumTimeType.T2, EnumStatType.Max, EnumEnergyMoney.Energy);
						if (tenantStat != null && tenantStat.getData() != null
								&& tenantStat.getData().doubleValue() != 0.0) {
							minDays = Long
									.valueOf(Math.round(
											lastData.getData().doubleValue() / tenantStat.getData().doubleValue()))
									.intValue();
							if (minDays != null && minDays < 0) {
								minDays = 0;
							}
						}
						// 近*日平均值费用
						FnTenantMeterAvg tenantMeterAvg = FNTenantMeterAvgService.queryData(building.getId(),
								tenant.getId(), EnumPayBodyType.TENANT, tenant.getId(), entry.getKey(),
								EnumEnergyMoney.Energy);
						if (tenantMeterAvg != null && tenantMeterAvg.getData() != null
								&& tenantMeterAvg.getData().doubleValue() != 0.0) {
							maxDays = Long
									.valueOf(Math.round(
											lastData.getData().doubleValue() / tenantMeterAvg.getData().doubleValue()))
									.intValue();
							if (maxDays != null && maxDays < 0) {
								maxDays = 0;
							}
						}
						if (minDays != null && maxDays != null && minDays > maxDays) {
							int temp = 0;
							temp = minDays;
							minDays = maxDays;
							maxDays = temp;
						}
						if (maxDays == null && minDays != null) {
							maxDays = minDays;
						} else if (maxDays != null && minDays == null) {
							minDays = maxDays;
						}
						FNTenantMeterRemainDaysService.saveData(building.getId(), tenant.getId(), entry.getKey(),
								EnumPayBodyType.TENANT, tenant.getId(), maxDays, minDays, lastData.getData());
					}
				}
			} else {
				// 查询租户下的仪表
				Integer tenantMaxDays = null;
				Integer tenantMinDays = null;
				Double remainData = null;
				List<DTOMeter> meterList = FNMeterService.queryMeterList(tenant.getId(), entry.getKey());
				// System.out.println("***********查询仪表:" +
				// meterList.size()+"_"+meterList.get(0).getMeterId());
				// 查询最后数据
				Integer functionId = null;
				Integer touZhiFunctionId = null;
				if (entry.getValue().getPrepayChargeType() != null && entry.getValue().getPrepayChargeType()
						.intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
					functionId = FunctionTypeUtil.getShengYuJinEFunctionId(entry.getKey());
					touZhiFunctionId = FunctionTypeUtil.getTouZhiJEFunctionId(entry.getKey());
				} else if (entry.getValue().getPrepayChargeType() != null && entry.getValue().getPrepayChargeType()
						.intValue() == EnumPrepayChargeType.Liang.getValue().intValue()) {
					functionId = FunctionTypeUtil.getShengYuLiangFunctionId(entry.getKey());
					touZhiFunctionId = FunctionTypeUtil.getTouZhiLiangFunctionId(entry.getKey());
				}
				if (functionId == null || touZhiFunctionId == null) {
					return;
				}

				// if(tenant.getId().equals("ZHBH_1053")){
				// System.out.println("--");
				// }

				Date timeTo = new Date();
				Date timeFrom = new Date(timeTo.getTime() - toleraterSecond * 1000);
				if (meterList != null && meterList.size() > 0) {
					for (DTOMeter meter : meterList) {
						Double radio = 1.0;
						try {
							Long syjIsCt = (Long) ((JSONObject) JSONValue.parse(meter.getExtend())).get("syjIsCt");
							if (syjIsCt != null && syjIsCt.intValue() == 1) {
								radio = meter.getRadio();
							}
						} catch (Exception e) {

						}
						Integer meterMaxDays = null;
						Integer meterMinDays = null;
						// 查询仪表最后数据
//						System.out.println("meterId:" + meter.getMeterId() + ",functionId:" + functionId + ",timeFrom:"
//								+ standard.format(timeFrom));
						MonthData monthData = FNOriginalDataService.queryLastMonthDataGteLte(meter.getMeterId(),
								functionId, timeFrom, timeTo);
						// SimpleDateFormat format = new
						// SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
						// System.out.println("****************查询原始数据开始时间:" +
						// format.format(timeFrom));
						// System.out.println("****************查询原始数据结束时间:" +
						// format.format(timeTo));
						// System.out.println("****************查询原始数据表号:"+meter.getMeterId()+"_"+functionId
						// );
						// if(monthData!=null){
						// System.out.println("************计算剩余查询值:" +
						// meter.getMeterId() + "_" + functionId + "_" +
						// monthData.toString());
						// }
						// MonthData touZhimonthData =
						// FNOriginalDataService.queryLastMonthDataGteLte(meter.getMeterId(),
						// touZhiFunctionId, timeFrom, timeTo);
						Double lastData = null;

						if (monthData != null && monthData.getData() != null) {
							lastData = monthData.getData() * radio;
//							System.out.println("meterId:" + meter.getMeterId() + ",lastMonthData:" + lastData);
							{
								FnTenantMeterAvg tenantMeterAvg = null;
								if (entry.getValue().getPrepayChargeType().intValue() == EnumPrepayChargeType.Qian
										.getValue().intValue()) {
									tenantMeterAvg = FNTenantMeterAvgService.queryData(building.getId(), tenant.getId(),
											EnumPayBodyType.METER, meter.getMeterId(), entry.getKey(),
											EnumEnergyMoney.Money);
								} else {
									tenantMeterAvg = FNTenantMeterAvgService.queryData(building.getId(), tenant.getId(),
											EnumPayBodyType.METER, meter.getMeterId(), entry.getKey(),
											EnumEnergyMoney.Energy);
								}
								if (tenantMeterAvg != null && tenantMeterAvg.getData() != null
										&& tenantMeterAvg.getData().doubleValue() != 0.0) {
									meterMaxDays = Long
											.valueOf(Math.round(
													lastData.doubleValue() / tenantMeterAvg.getData().doubleValue()))
											.intValue();
								}
							}
							{
								FnTenantMeterStat tenantMeterStat = null;
								if (entry.getValue().getPrepayChargeType().intValue() == EnumPrepayChargeType.Qian
										.getValue().intValue()) {
									tenantMeterStat = FNTenantMeterStatService.queryData(building.getId(),
											tenant.getId(), meter.getMeterId(),
											FunctionTypeUtil.getCumulantFunctionId(entry.getKey()), entry.getKey(),
											EnumTimeType.T2, EnumStatType.Max, EnumEnergyMoney.Money);
								} else {
									tenantMeterStat = FNTenantMeterStatService.queryData(building.getId(),
											tenant.getId(), meter.getMeterId(),
											FunctionTypeUtil.getCumulantFunctionId(entry.getKey()), entry.getKey(),
											EnumTimeType.T2, EnumStatType.Max, EnumEnergyMoney.Energy);
								}
								if (tenantMeterStat != null && tenantMeterStat.getData() != null
										&& tenantMeterStat.getData().doubleValue() != 0.0) {
									meterMinDays = Long
											.valueOf(Math.round(
													lastData.doubleValue() / tenantMeterStat.getData().doubleValue()))
											.intValue();
								}
							}
							// if(touZhimonthData!=null&&touZhimonthData.getData()!=null){
							// lastData=lastData-touZhimonthData.getData()*radio;
							// }
							remainData = remainData == null ? lastData : remainData + lastData;
						}
						if (meterMaxDays != null && meterMaxDays < 0) {
							meterMaxDays = 0;
						}
						if (meterMinDays != null && meterMinDays < 0) {
							meterMinDays = 0;
						}
						if (meterMaxDays != null) {
							if (tenantMaxDays == null || meterMaxDays > tenantMaxDays) {
								tenantMaxDays = meterMaxDays;
							}
						}
						if (meterMinDays != null) {
							if (tenantMinDays == null || meterMinDays < tenantMinDays) {
								tenantMinDays = meterMinDays;
							}
						}
						if (meterMinDays != null && meterMaxDays != null && meterMinDays > meterMaxDays) {
							int temp = 0;
							temp = meterMinDays;
							meterMinDays = meterMaxDays;
							meterMaxDays = temp;
						}
						if (meterMaxDays == null && meterMinDays != null) {
							meterMaxDays = meterMinDays;
						} else if (meterMaxDays != null && meterMinDays == null) {
							meterMinDays = meterMaxDays;
						}
						FNTenantMeterRemainDaysService.saveData(building.getId(), tenant.getId(), entry.getKey(),
								EnumPayBodyType.METER, meter.getMeterId(), meterMaxDays, meterMinDays,
								lastData == null ? null : lastData.doubleValue());
					}
				}
				if (tenantMaxDays != null && tenantMaxDays < 0) {
					tenantMaxDays = 0;
				}
				if (tenantMinDays != null && tenantMinDays < 0) {
					tenantMinDays = 0;
				}
				if (tenantMinDays != null && tenantMaxDays != null && tenantMinDays > tenantMaxDays) {
					int temp = 0;
					temp = tenantMinDays;
					tenantMinDays = tenantMaxDays;
					tenantMaxDays = temp;
				}
				if (tenantMaxDays == null && tenantMinDays != null) {
					tenantMaxDays = tenantMinDays;
				} else if (tenantMaxDays != null && tenantMinDays == null) {
					tenantMinDays = tenantMaxDays;
				}
				FNTenantMeterRemainDaysService.saveData(building.getId(), tenant.getId(), entry.getKey(),
						EnumPayBodyType.TENANT, tenant.getId(), tenantMaxDays, tenantMinDays, remainData);
			}
		}
	}
}
