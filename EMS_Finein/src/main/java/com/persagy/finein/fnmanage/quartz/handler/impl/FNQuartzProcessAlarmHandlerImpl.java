package com.persagy.finein.fnmanage.quartz.handler.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.quartz.handler.FNQuartzProcessJob;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;

@Service("FNQuartzProcessAlarmHandler")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNQuartzProcessAlarmHandlerImpl implements FNQuartzProcessJob {

	@Resource(name = "FNAlarmService")
	private FNAlarmService FNAlarmService;

	@Resource(name = "FNChargeMessageService")
	private FNChargeMessageService FNChargeMessageService;

	@Resource(name = "FNBuildingService")
	private FNBuildingService FNBuildingService;

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;

	@Resource(name = "FNTenantPayTypeService")
	private FNTenantPayTypeService FNTenantPayTypeService;

	@Resource(name = "FNRecordScheduleJobService")
	private FNRecordScheduleJobService FNRecordScheduleJobService;

	@Resource(name = "FNTenantPrePayParamService")
	private FNTenantPrePayParamService FNTenantPrePayParamService;

	@Resource(name = "FNTenantPrePayMeterParamService")
	private FNTenantPrePayMeterParamService FNTenantPrePayMeterParamService;

	@Resource(name = "FNMessageBlackListService")
	private FNMessageBlackListService FNMessageBlackListService;

	private final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH-mm");

	private final String JOB_Name = "定时发送短信";
	private final String JOB_Group = "FNQuartzProcessAlarmHandler";

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void handle() throws Exception {
		// 一天最多发两条短信
		FnRecordScheduleJob queryR = new FnRecordScheduleJob();
		queryR.setJobGroup(JOB_Group);
		queryR.setJobName(JOB_Name);
		Date date = new Date();
		Date day = DateUtils.truncate(date, Calendar.DATE);
		queryR.setExecuteTime(day);
		List<FnRecordScheduleJob> recordList = FNRecordScheduleJobService.query(queryR);
		if (recordList != null && recordList.size() > 0) {
			FnRecordScheduleJob recordScheduleJob = recordList.get(0);
			Integer times = recordScheduleJob.getTimes();
			if (times == 2) {
				return;
			} else {
				times++;
				FnRecordScheduleJob update = new FnRecordScheduleJob();
				update.setTimes(times);
				update.setUpdateTime(new Date());
				FNRecordScheduleJobService.update(recordScheduleJob, update);
			}
		} else {
			queryR.setId(UUID.randomUUID().toString());
			queryR.setTimes(1);
			queryR.setUpdateTime(new Date());
			FNRecordScheduleJobService.save(queryR);
		}
		List<FnChargeMessage> messageList = new ArrayList<FnChargeMessage>();

		FnAlarm query = new FnAlarm();
		query.setParentAlarmTypeId("ZHBJ_01");// 预付费不足
		query.setStatus(EnumAlarmStatus.WeiHuiHu.getValue().intValue());
		query.setSort("lastUpdateTime", EMSOrder.Asc);
		List<FnAlarm> list = FNAlarmService.query(query);
		Map<String, ArrayList> map = new HashMap<String, ArrayList>();
		if (list != null && list.size() > 0) {
			for (FnAlarm fnAlarm : list) {
				if (map.get(fnAlarm.getEnergyTypeId()) == null) {
					map.put(fnAlarm.getEnergyTypeId(), new ArrayList<FnAlarm>());
				}
				map.get(fnAlarm.getEnergyTypeId()).add(fnAlarm);
			}
		}
		for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
			HashMap<String, FnAlarm> AlarmMap = new HashMap<String, FnAlarm>();
			ArrayList<FnAlarm> arrayList = map.get(energyType.getId());
			if (arrayList != null && arrayList.size() > 0) {// 只处理租户最后更新的报警
				for (FnAlarm fnAlarm : arrayList) {
					AlarmMap.put(fnAlarm.getTenantId(), fnAlarm);
				}
			}
			if (AlarmMap.size() > 0) {
				Map<String, FnMessageBlackList> blackList = FNMessageBlackListService.queryMap(null);
				for (Entry<String, FnAlarm> entry : AlarmMap.entrySet()) {
					if (blackList.containsKey(entry.getValue().getTenantId())) {// 短信黑名单
						continue;
					}
					processAlarm(entry.getValue(), messageList);
				}
			}
		}
		FNChargeMessageService.save(messageList);
	}

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void processAlarm(FnAlarm fnAlarm, List<FnChargeMessage> messageList) throws Exception {
		String buildingId = fnAlarm.getBuildingId();
		Building building = FNBuildingService.query(buildingId);
		if (building == null) {
			throw new Exception("建筑不存在" + buildingId);
		}
		String tenantId = fnAlarm.getTenantId();
		FnTenant tenant = FNTenantService.queryOne(tenantId);
		if (tenant == null) {
			throw new Exception("租户不存在" + tenantId);
		}
		if (tenant.getContactMobile() == null || tenant.getContactMobile().equals("")){
			throw new Exception("租户手机号为空" + tenantId);
		}

		FnTenantPayType payType = FNTenantPayTypeService.query(tenantId, fnAlarm.getEnergyTypeId());
		if (payType.getPrePayType() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
			processTenantPay(fnAlarm, messageList, building, tenantId, tenant, payType);
		} else {
			processMeterPay(fnAlarm, messageList, buildingId, building, tenantId, tenant, payType);
		}
	}

	private void processMeterPay(FnAlarm fnAlarm, List<FnChargeMessage> messageList, String buildingId,
			Building building, String tenantId, FnTenant tenant, FnTenantPayType payType) throws Exception {
		// 表扣
		FnTenantPrePayMeterParam query = new FnTenantPrePayMeterParam();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(fnAlarm.getEnergyTypeId());
		query.setIsAlarm(EnumYesNo.YES.getValue());
		List<FnTenantPrePayMeterParam> list = FNTenantPrePayMeterParamService.query(query);
		if (list != null) {
			for (FnTenantPrePayMeterParam prePayParam : list) {
				FnChargeMessage message = new FnChargeMessage();
				message.setId(UUID.randomUUID().toString());
				message.setTenantId(tenantId);
				message.setTenantName(tenant.getName());
				message.setContactName(tenant.getContactName());
				message.setContactMobile(tenant.getContactMobile());

				StringBuffer contentSb = new StringBuffer();
				contentSb.append(
						ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_MessageSignature));

				String template = (String) ConstantDBBaseData.SysParamValueMap
						.get(FineinConstant.SysParamValueKey.Id_MessagePrePayTemplate);
				String energyTypeName = EnergyTypeUtil.queryEnergyTypeNameById(fnAlarm.getEnergyTypeId());
				template = template.replace("${buildingName}", building.getName());
				template = template.replace("${tenantName}", tenant.getName());
				template = template.replace("${tenantID}", tenantId);
				String time = standard.format(prePayParam.getLastUpdateTime());
				String replace = time.replace(" ", "-");
				String[] split = replace.trim().split("-");
				template = template.replace("${year}", split[0]);
				template = template.replace("${month}", split[1]);
				template = template.replace("${day}", split[2]);
				template = template.replace("${hour}", split[3]);

				StringBuffer stringBuffer = new StringBuffer();
				stringBuffer.append("仪表(").append(prePayParam.getMeterId()).append(")");
				stringBuffer.append(energyTypeName);
				template = template.replace("${energyTypeName}", stringBuffer.toString());
				template = template.replace("${remainData}", prePayParam.getRemainData() == null ? ""
						: DoubleFormatUtil.Instance().getDoubleData_00(prePayParam.getRemainData()) + "");

				// 租户充值类型
				String billModeUnit = UnitUtil.getBillModeUnit(fnAlarm.getEnergyTypeId(),
						EnumPrepayChargeType.valueOf(payType.getPrepayChargeType()));
				if ("m³".equals(billModeUnit)) {
					billModeUnit = "立方米";
				}
				template = template.replace("${unit}", billModeUnit);
				String minDays = null;
				String maxDays = null;
				String remainDays = prePayParam.getRemainDays();
				if (remainDays != null && !remainDays.equals("")) {
					String[] split2 = remainDays.split("~");
					minDays = split2[0];
					maxDays = split2[1];
				}
				template = template.replace("${minDay}", minDays == null ? "" : minDays);
				template = template.replace("${maxDay}", maxDays == null ? "" : maxDays);

				contentSb.append(template);

				message.setContent(contentSb.toString());
				message.setCreateTime(new Date());
				message.setLastUpdateTime(new Date());
				message.setStatus(EnumMessageSendStatus.WAIT_SEND.getValue());
				message.setTryTimes(0);
				messageList.add(message);
			}
		}
	}

	private void processTenantPay(FnAlarm fnAlarm, List<FnChargeMessage> messageList, Building building,
			String tenantId, FnTenant tenant, FnTenantPayType payType) throws Exception {
		FnChargeMessage message = new FnChargeMessage();
		message.setId(UUID.randomUUID().toString());
		message.setTenantId(tenantId);
		message.setTenantName(tenant.getName());
		message.setContactName(tenant.getContactName());
		message.setContactMobile(tenant.getContactMobile());
		FnTenantPrePayParam prePayParam = FNTenantPrePayParamService.queryTenantPreParam(tenantId,
				fnAlarm.getEnergyTypeId());
		if (prePayParam != null) {

			StringBuffer contentSb = new StringBuffer();
			contentSb.append(
					ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_MessageSignature));

			String template = (String) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_MessagePrePayTemplate);
			String energyTypeName = EnergyTypeUtil.queryEnergyTypeNameById(fnAlarm.getEnergyTypeId());
			template = template.replace("${buildingName}", building.getName());
			template = template.replace("${tenantName}", tenant.getName());
			template = template.replace("${tenantID}", tenantId);
			String time = standard.format(prePayParam.getLastUpdateTime());
			String replace = time.replace(" ", "-");
			String[] split = replace.trim().split("-");
			template = template.replace("${year}", split[0]);
			template = template.replace("${month}", split[1]);
			template = template.replace("${day}", split[2]);
			template = template.replace("${hour}", split[3]);
			template = template.replace("${energyTypeName}", energyTypeName == null ? "" : energyTypeName);
			template = template.replace("${remainData}", prePayParam.getRemainData() == null ? ""
					: DoubleFormatUtil.Instance().getDoubleData_00(prePayParam.getRemainData()) + "");

			// 租户充值类型
			String billModeUnit = UnitUtil.getBillModeUnit(fnAlarm.getEnergyTypeId(),
					EnumPrepayChargeType.valueOf(payType.getPrepayChargeType()));
			if ("m³".equals(billModeUnit)) {
				billModeUnit = "立方米";
			}
			template = template.replace("${unit}", billModeUnit);
			String minDays = null;
			String maxDays = null;
			String remainDays = prePayParam.getRemainDays();
			if (remainDays != null && !remainDays.equals("")) {
				String[] split2 = remainDays.split("~");
				minDays = split2[0];
				maxDays = split2[1];
			}
			template = template.replace("${minDay}", minDays == null ? "" : minDays);
			template = template.replace("${maxDay}", maxDays == null ? "" : maxDays);

			contentSb.append(template);

			message.setContent(contentSb.toString());
			message.setCreateTime(new Date());
			message.setLastUpdateTime(new Date());
			message.setStatus(EnumMessageSendStatus.WAIT_SEND.getValue());
			message.setTryTimes(0);

			messageList.add(message);

		}
	}
}
