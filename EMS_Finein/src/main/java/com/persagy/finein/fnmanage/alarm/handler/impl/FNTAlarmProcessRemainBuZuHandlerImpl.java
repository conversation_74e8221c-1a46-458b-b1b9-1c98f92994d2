package com.persagy.finein.fnmanage.alarm.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.core.util.AlarmTypeUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.alarm.handler.FNTAlarmProcessRemainBuZuHandler;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.json.simple.JSONObject;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * 说明:剩余不足
 */
@Component("FNTAlarmProcessRemainBuZuHandler")
public class FNTAlarmProcessRemainBuZuHandlerImpl extends CoreServiceImpl implements FNTAlarmProcessRemainBuZuHandler {

	@Resource(name = "FNAlarmService")
	private FNAlarmService FNAlarmService;
	
	@Resource(name = "FNAlarmPushStatusService")
	private FNAlarmPushStatusService FNAlarmPushStatusService;

	@Resource(name = "FNAlarmLimitCustomSettingService")
	private FNAlarmLimitCustomSettingService FNAlarmLimitCustomSettingService;

	@Resource(name = "FNAlarmLimitCustomObjService")
	private FNAlarmLimitCustomObjService FNAlarmLimitCustomObjService;

	@Resource(name = "FNTenantPrePayParamService")
	private FNTenantPrePayParamService FNTenantPrePayParamService;

	@Resource(name = "FNTenantDataService")
	private FNTenantDataService FNTenantDataService;

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void handle(Building building, FnTenant tenant, String energyTypeId, String alarmTypeId,
			FnTenantPayType tenantPayType, Map<String, FnAlarmLimitGlobal> globalAlarmLimitMap) throws Exception {
		// 查询当前租户的报警门限
		Double limitValue = null;
		FnAlarmLimitCustomSetting alarmLimitCustomSetting = FNAlarmLimitCustomSettingService.query(building.getId(),
				tenant.getId());
		if (alarmLimitCustomSetting == null
				|| alarmLimitCustomSetting.getIsFollowGlobal().intValue() == EnumYesNo.YES.getValue().intValue()) {// 跟随全局报警
			FnAlarmLimitGlobal global = globalAlarmLimitMap.get(alarmTypeId);
			if (global == null || global.getIsOpen().intValue() == EnumYesNo.NO.getValue().intValue()) {
				FNAlarmService.updateAlarmStatus(tenant.getId(), alarmTypeId, EnumAlarmStatus.YiGuoQi, null);
			} else {
				limitValue = global.getLimitValue();
			}
		} else {
			FnAlarmLimitCustomObj customObj = FNAlarmLimitCustomObjService.queryList(building.getId(), tenant.getId(),
					alarmTypeId);
			if (customObj == null || customObj.getIsOpen().intValue() == EnumYesNo.NO.getValue().intValue()) {// 将历史未恢复报警转为过期
				FNAlarmService.updateAlarmStatus(tenant.getId(), alarmTypeId, EnumAlarmStatus.YiGuoQi, null);
			} else {// 检查租户是否报警
				limitValue = customObj.getLimitValue();
			}
		}
		if (limitValue == null) {
			FNAlarmService.updateAlarmStatus(tenant.getId(), alarmTypeId, EnumAlarmStatus.YiGuoQi, null);
			return;
		}

		// 查询当前剩余
		FnTenantPrePayParam param = FNTenantPrePayParamService.queryTenantPreParam(tenant.getId(), energyTypeId);
		if (param == null || param.getRemainData() == null) {
			FNAlarmService.updateAlarmStatus(tenant.getId(), alarmTypeId, EnumAlarmStatus.YiGuoQi, null);
		} else {
			try {
				// 查询报警
				FnAlarm alarm = this.FNAlarmService.queryAlarmWeiHuiFu(tenant.getId(), alarmTypeId);
				if (param.getRemainData() < limitValue) {// 报警
					if (alarm == null) {// 新增报警
						this.alarm(building, tenant, energyTypeId, alarmTypeId, limitValue, param.getRemainData());
					} else {
						FnAlarm update = new FnAlarm();
						update.setLimitValue(limitValue);
						update.setCurrentValue(param.getRemainData());
						update.setLastUpdateTime(new Date());
						FNAlarmService.update(alarm, update);
					}
				} else {
					if (alarm != null) {
						FNAlarmService.updateAlarmStatus(tenant.getId(), alarmTypeId, EnumAlarmStatus.YiHuiFu, null);
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
	}

	
	public void alarm(Building building, FnTenant tenant, String energyTypeId, String alarmTypeId, Double limitValue,
			Double currentValue) throws Exception {
		FnAlarm saveObj = new FnAlarm();
		String id = UUID.randomUUID().toString();
		saveObj.setId(id);
		saveObj.setBuildingId(building.getId());
		saveObj.setTenantId(tenant.getId());
		saveObj.setAlarmTypeId(alarmTypeId);
		FnAlarmType alarmType = AlarmTypeUtil.queryAlarmTypeById(alarmTypeId);
		saveObj.setParentAlarmTypeId(alarmType.getParentId());
		saveObj.setTreeId(alarmType.getTreeId());
		saveObj.setEnergyTypeId(energyTypeId);
		Date now = new Date();
		saveObj.setAlarmTime(now);
		saveObj.setAlarmPositionType(EnumAlarmPositionType.Tenant.getValue());
		saveObj.setAlarmPositionId(tenant.getId());
		saveObj.setAlarmPositionName(tenant.getName());
		saveObj.setStatus(EnumAlarmStatus.WeiHuiHu.getValue());
		saveObj.setLimitValue(limitValue);
		saveObj.setCurrentValue(currentValue);
		saveObj.setCreateTime(now);
		saveObj.setLastUpdateTime(now);
		saveObj.setUnit(alarmType.getUnit());
		saveObj.setIsRead(EnumYesNo.NO.getValue());
		saveObj.setPushStatus(EnumAlarmPushStatus.WaitPush.getValue());
		// 历史能耗最大值，历史能耗平均值
		Date timeTo = DateUtils.truncate(now, Calendar.DATE);
		Double historyAvgValue = null;
		Double historyMaxValue = null;
		Double historySumValue = null;

		int count = 0;
		List<FnTenantData> dataList = FNTenantDataService.queryListGteLt(tenant.getBuildingId(), tenant.getId(),
				EnumTimeType.T2, energyTypeId, tenant.getActiveTime(), timeTo, EnumEnergyMoney.Energy);
		if (dataList != null) {
			for (FnTenantData entity : dataList) {
				if (entity.getData() != null) {
					if (historyMaxValue == null || entity.getData() > historyAvgValue) {
						historyAvgValue = entity.getData();
					}
					historySumValue = historySumValue == null ? entity.getData() : entity.getData() + historySumValue;
					count++;
				}
			}
		}
		if (historySumValue != null && count > 0) {
			historyAvgValue = historySumValue.doubleValue() / count;
		}
		JSONObject extendObj = new JSONObject();
		extendObj.put("historyAvgValue", historyAvgValue);
		extendObj.put("historyMaxValue", historyMaxValue);
		saveObj.setExtend(extendObj.toString());
		FNAlarmService.save(saveObj);
		
		
		FnAlarmPushStatus save = new FnAlarmPushStatus();
		save.setBuildingId(building.getId());
		save.setTenantId(tenant.getId());
		save.setAlarmPositionType(EnumAlarmPositionType.Tenant.getValue());
		save.setAlarmPositionId(tenant.getId());
		save.setStatus(EnumAlarmStatus.WeiHuiHu.getValue());
		save.setAlarmTypeId(alarmTypeId);
		save.setEnergyTypeId(energyTypeId);
		save.setId(id);
		save.setLastUpdateTime(now);
		save.setPushStatus(EnumAlarmPushStatus.WaitPush.getValue());
		FNAlarmPushStatusService.save(save);
	}
}
