package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.ems.pojo.meterdata.MeterData;
import com.persagy.ems.pojo.originaldata.MonthData;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.core.util.FunctionUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 租户状态
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntTenantStatusController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNRoomService")
    private FNRoomService fnRoomService;

    @Resource(name = "FNTenantRoomService")
    private FNTenantRoomService fnTenantRoomService;

    @Resource(name = "FNTenantPriceService")
    private FNTenantPriceService fnTenantPriceService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNPriceTemplateService")
    private FNPriceTemplateService fnPriceTemplateService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @Resource(name = "FNRecordTenantOperateService")
    private FNRecordTenantOperateService fnRecordTenantOperateService;

    @Resource(name = "FNRecordPostClearingPayService")
    private FNRecordPostClearingPayService fnRecordPostClearingPayService;

    @Resource(name = "FNRecordPostClearingMeterService")
    private FNRecordPostClearingMeterService fnRecordPostClearingMeterService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNMeterDataService")
    private FNMeterDataService fnMeterDataService;

    @Resource(name = "FNOriginalDataService")
    private FNOriginalDataService fnOriginalDataService;

    @Resource(name = "FNProjectService")
    private FNProjectService fnProjectService;

    @Resource(name = "FNTenantBackPayDataService")
    private FNTenantBackPayDataService fnTenantBackPayDataService;

    @Resource(name = "FNTenantMeterComputeService")
    private FNTenantMeterComputeService fnTenantMeterComputeService;

    @Resource(name = "FNOrderIdService")
    private FNOrderIdService fnOrderIdService;

    @Resource(name = "FNRecordPostPayService")
    private FNRecordPostPayService fnRecordPostPayService;


    /**
     * 租户状态 -- 激活
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantActiveService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantActive(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");
            String userId = this.getParamUserId(dto);
            String activeTime = (String) dto.get("activeTime");

            if (tenantId == null || activeTime == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            //查询租户
            FnTenant query = new FnTenant();//未激活
            query.setId(tenantId);

            FnTenant fnTenant = (FnTenant) fnTenantService.queryObject(query);
            if (fnTenant.getStatus().intValue() != EnumTenantStatus.NOT_ACTIVE.getValue().intValue()) {
                throw new Exception("租户状态不是未激活状态:" + tenantId);
            }

            Map<String, Integer> result = new HashMap<String, Integer>();
            FnTenant update = new FnTenant();
            update.setStatus(EnumTenantStatus.ACTIVATED.getValue());
            Date activeDate = standard.parse(activeTime);
            update.setActiveTime(activeDate);
            Date now = new Date();
            update.setLastUpdateTime(now);
            update.setLastUpdateUserId(userId);

            {//插入激活记录
                FnRecordTenantOperate saveObj = new FnRecordTenantOperate();
                saveObj.setId(UUID.randomUUID().toString());
                saveObj.setTenantId(tenantId);
                saveObj.setBuildingId(fnTenant.getBuildingId());
                String buildingName = null;
                try {
                    buildingName = fnBuildingService.query(fnTenant.getBuildingId()).getName();
                } catch (Exception e) {
                }
                saveObj.setBuildingName(buildingName);
                saveObj.setOperateType(EnumTenantOperateType.ACTIVATE.getValue());
                saveObj.setOperteTime(activeDate);
                saveObj.setTenantName(fnTenant.getName());
                saveObj.setUserId(userId);
                String userName = null;
                try {
                    userName = fnUserService.queryUserByUserId(userId).getUserName();
                } catch (Exception e) {
                }
                saveObj.setUserName(userName);
                saveObj.setRoomIds(fnTenant.getRoomCodes());
                saveObj.setCreateTime(now);
                fnRecordTenantOperateService.save(saveObj);
            }

            fnTenantService.update(query, update);
            result.put("result", 0);
            content.add(result);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTTenantActiveService");
        }
    }

    /**
     * 租户状态 -- 退租
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantLeaveService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantLeave(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Map<String, Object> contentMap = new HashMap<String, Object>();
            try {
                String tenantId = (String) dto.get("tenantId");
                String leaveTime = (String) dto.get("leaveTime");
                String userId = this.getParamUserId(dto);
                List<Object> energyTypeList = (List<Object>) dto.get("energyTypeList");


                if (tenantId == null || leaveTime == null || userId == null || energyTypeList == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }

                Date leaveDate = standard.parse(leaveTime);
                FnTenant fnTenant = fnTenantService.queryOne(tenantId);
                DTOUser dtoUser = fnUserService.queryUserByUserId(userId);
                if (energyTypeList.size() > 0) {
                    Building building = fnBuildingService.query(fnTenant.getBuildingId());
                    List<FnRecordPostClearingMeter> meterClearingRecordList = new ArrayList<>();
                    List<FnRecordPostClearingPay> tenantClearingRecordList = new ArrayList<>();
                    for (Object energyTypeObj : energyTypeList) {
                        Map<String, Object> energyTypeMap = (Map<String, Object>) energyTypeObj;
                        String energyTypeId = (String) energyTypeMap.get("energyTypeId");
                        String priceTemplateId = (String) energyTypeMap.get("priceTemplateId");

                        FnPriceTemplate priceTemplate = fnPriceTemplateService.query(priceTemplateId);

                        Integer payType = (Integer) energyTypeMap.get("payType");
                        if (payType.intValue() != EnumPayType.POSTPAY.getValue().intValue()) {
                            continue;
                        }
                        Map<String, Object> postPayMap = (Map<String, Object>) energyTypeMap.get("postPay");
                        List<Object> meterList = (List<Object>) postPayMap.get("meterList");
                        if (meterList != null && meterList.size() > 0) {
                            //解析仪表
                            Date now = new Date();
                            String orderId = fnOrderIdService.queryOrderId(payType, DateUtils.truncate(now, Calendar.DATE));
                            Double tenantEnergy = null;
                            Double tenantMoney = null;
                            String tenantLastClearingTime = null;
                            {
                                Double meterEnergy = null;
                                Double meterMoney = null;
                                for (Object meterObj : meterList) {
                                    Map<String, Object> meterMap = (Map<String, Object>) meterObj;
                                    String meterId = (String) meterMap.get("meterId");
                                    Integer meterType = (Integer) meterMap.get("meterType");
                                    String lastClearingTime = (String) meterMap.get("lastClearingTime");
                                    if (tenantLastClearingTime == null) {
                                        tenantLastClearingTime = lastClearingTime;
                                    }
                                    List<Object> functionList = (List<Object>) meterMap.get("functionList");

                                    JSONObject extendObj = new JSONObject();
                                    JSONArray functionArray = new JSONArray();
                                    extendObj.put("functionList", functionArray);
                                    if (functionList != null && functionList.size() > 0) {
                                        for (Object functionObj : functionList) {
                                            JSONObject functionExtendObj = new JSONObject();
                                            Map<String, Object> functionMap = (Map<String, Object>) functionObj;
                                            String type = (String) functionMap.get("type");
                                            Integer functionId = (Integer) functionMap.get("functionId");
                                            Double lastBillingData = DoubleFormatUtil.Instance().getDoubleData(functionMap.get("lastBillingData"));
                                            Double currentBillingData = DoubleFormatUtil.Instance().getDoubleData(functionMap.get("currentBillingData"));
                                            Double diffData = DoubleFormatUtil.Instance().getDoubleData(functionMap.get("diffData"));
                                            Double money = DoubleFormatUtil.Instance().getDoubleData(functionMap.get("money"));
                                            if (diffData != null) {
                                                meterEnergy = meterEnergy == null ? diffData : meterEnergy + diffData;
                                                tenantEnergy = tenantEnergy == null ? diffData : tenantEnergy + diffData;
                                            }
                                            if (money != null) {
                                                meterMoney = meterMoney == null ? money : meterMoney + money;
                                                tenantMoney = tenantMoney == null ? money : tenantMoney + money;
                                            }
                                            functionExtendObj.put("type", type);
                                            functionExtendObj.put("functionId", Long.valueOf(functionId));
                                            functionExtendObj.put("lastData", lastBillingData);
                                            functionExtendObj.put("currentData", currentBillingData);
                                            functionExtendObj.put("diffData", diffData);
                                            functionExtendObj.put("money", money);
                                            functionArray.add(functionExtendObj);
                                        }
                                    }

                                    FnRecordPostClearingMeter meterRecord = new FnRecordPostClearingMeter();
                                    meterRecord.setId(UUID.randomUUID().toString());
                                    meterRecord.setBuildingId(fnTenant.getBuildingId());
                                    meterRecord.setEnergyTypeId(energyTypeId);
                                    meterRecord.setBuildingName(building.getName());
                                    meterRecord.setTenantId(fnTenant.getId());
                                    meterRecord.setTenantName(fnTenant.getName());
                                    meterRecord.setOrderId(orderId);
                                    meterRecord.setMeterId(meterId);
                                    meterRecord.setMeterType(meterType);
                                    meterRecord.setPriceTemplateId(priceTemplate.getId());
                                    meterRecord.setPriceTemplateName(priceTemplate.getName());
                                    meterRecord.setPriceTemplateContent(priceTemplate.getContent());
                                    meterRecord.setPriceTemplateType(priceTemplate.getType());
                                    meterRecord.setExtend(extendObj.toString());
                                    String lastTime = lastClearingTime.substring(0, 10);
                                    String endTime = leaveTime.substring(0, 10);
                                    meterRecord.setLastClearingTime(standard.parse(lastClearingTime));
                                    meterRecord.setCurrentClearingTime(standard.parse(leaveTime));
                                    meterRecord.setOrderTime(lastTime + "~" + endTime);
                                    meterRecord.setCurrentSumEnergy(meterEnergy);
                                    meterRecord.setCurrentSumEnergyUnit(UnitUtil.getCumulantUnit(energyTypeId));
                                    meterRecord.setMoney(meterMoney);
                                    meterRecord.setRoomIds(fnTenant.getRoomCodes());
                                    meterRecord.setCreateTime(now);
                                    meterClearingRecordList.add(meterRecord);
                                }
                            }
                            //处理租户
                            FnRecordPostClearingPay tenantRecord = new FnRecordPostClearingPay();
                            tenantRecord.setId(UUID.randomUUID().toString());
                            tenantRecord.setBuildingId(fnTenant.getBuildingId());
                            tenantRecord.setBuildingName(building.getName());
                            tenantRecord.setTenantId(tenantId);
                            tenantRecord.setEnergyTypeId(energyTypeId);
                            tenantRecord.setTenantName(fnTenant.getName());
                            tenantRecord.setOrderId(orderId);
                            String lastTime = tenantLastClearingTime.substring(0, 10);
                            String endTime = leaveTime.substring(0, 10);
                            tenantRecord.setLastClearingTime(standard.parse(lastTime + " 00:00:00"));
                            tenantRecord.setCurrentClearingTime(standard.parse(endTime + " 00:00:00"));
                            tenantRecord.setOrderTime(lastTime + "~" + endTime);
                            tenantRecord.setCurrentSumEnergy(tenantEnergy);
                            tenantRecord.setCurrentSumEnergyUnit(UnitUtil.getCumulantUnit(energyTypeId));
                            tenantRecord.setMoney(tenantMoney);
                            tenantRecord.setPayStatus(EnumPayStatus.NO.getValue());
                            tenantRecord.setUserId(userId);
                            tenantRecord.setUserName(dtoUser.getUserName());
                            tenantRecord.setRoomIds(fnTenant.getRoomCodes());
                            tenantRecord.setCreateTime(now);
                            tenantClearingRecordList.add(tenantRecord);
                        }
                    }
                    if (tenantClearingRecordList.size() > 0) {
                        fnRecordPostClearingPayService.save(tenantClearingRecordList);
                    }
                    if (meterClearingRecordList.size() > 0) {
                        fnRecordPostClearingMeterService.save(meterClearingRecordList);
                    }

                    {//插入缴费记录
                        List<FnRecordPostClearingPay> recordList = fnRecordPostClearingPayService.queryNotPayRecord(fnTenant.getBuildingId(), fnTenant.getId(), null);
                        if (recordList != null && recordList.size() > 0) {
                            List<FnRecordPostPay> saveList = new ArrayList<FnRecordPostPay>();
                            Date now = new Date();

                            for (FnRecordPostClearingPay record : recordList) {
                                //保存缴费记录
                                FnRecordPostPay saveObj = new FnRecordPostPay();
                                saveObj.setId(UUID.randomUUID().toString());
                                saveObj.setBuildingId(record.getBuildingId());
                                saveObj.setBuildingName(record.getBuildingName());
                                saveObj.setTenantId(record.getTenantId());
                                saveObj.setTenantName(record.getTenantName());
                                saveObj.setEnergyTypeId(record.getEnergyTypeId());
                                saveObj.setOperateTime(now);
                                saveObj.setOrderId(record.getOrderId());
                                saveObj.setOrderTime(record.getOrderTime());
                                saveObj.setMoney(record.getMoney());
                                saveObj.setRoomIds(record.getRoomIds());
                                saveObj.setUserId(userId);
                                saveObj.setUserName(userId == null ? userId : dtoUser.getUserName());
                                saveObj.setCreateTime(now);

                                saveList.add(saveObj);
                            }
                            if (saveList.size() > 0) {
                                fnRecordPostPayService.save(saveList);
                            }
                        }
                    }
                    {
                        fnRecordPostClearingPayService.payAllRecord(fnTenant.getBuildingId(), fnTenant.getId());
                    }
                }
                {//修改租户状态
                    FnTenant query = new FnTenant();
                    query.setId(tenantId);

                    FnTenant update = new FnTenant();
                    update.setStatus(EnumTenantStatus.RETURNED_LEASE.getValue());
                    update.setLeaveTime(leaveDate);
                    Date now = new Date();
                    update.setLastUpdateTime(now);
                    update.setLastUpdateUserId(userId);
                    fnTenantService.update(query, update);
                    {//修改租户房间状态
                        List<FnTenantRoom> tenantToomList = fnTenantRoomService.queryRoomByTenantId(tenantId);
                        List<String> idList = new ArrayList<>();
                        if (tenantToomList != null && tenantToomList.size() > 0) {
                            for (FnTenantRoom fnTenantRoom : tenantToomList) {
                                idList.add(fnTenantRoom.getRoomId());
                            }
                        }
                        fnRoomService.updateRoomStatus(fnTenant.getBuildingId(), idList, EnumUseStatus.Not_In_Use);
//					FNTenantRoomService.remove(tenantToomList);
                    }

                    {//插入激活记录
                        FnRecordTenantOperate saveObj = new FnRecordTenantOperate();
                        saveObj.setId(UUID.randomUUID().toString());
                        saveObj.setTenantId(tenantId);
                        saveObj.setBuildingId(fnTenant.getBuildingId());
                        String buildingName = null;
                        try {
                            buildingName = fnBuildingService.query(fnTenant.getBuildingId()).getName();
                        } catch (Exception e) {
                        }
                        saveObj.setBuildingName(buildingName);
                        saveObj.setOperateType(EnumTenantOperateType.LEAVE.getValue());
                        saveObj.setOperteTime(leaveDate);
                        saveObj.setTenantName(fnTenant.getName());
                        saveObj.setUserId(userId);
                        String userName = null;
                        try {
                            userName = fnUserService.queryUserByUserId(userId).getUserName();
                        } catch (Exception e) {
                        }
                        saveObj.setUserName(userName);
                        saveObj.setRoomIds(fnTenant.getRoomCodes());
                        saveObj.setCreateTime(now);
                        fnRecordTenantOperateService.save(saveObj);
                    }
                }
                contentMap.put("result", 0);
            } catch (Exception e) {
                e.printStackTrace();
                contentMap.put("result", 1);
                contentMap.put("message", "退租异常");
            }
            content.add(contentMap);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTTenantLeaveService");
        }
    }

    /**
     * 租户状态 -- 退租详情查询
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantLeaveQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantLeaveQuery(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");
            String leaveTime = (String) dto.get("leaveTime");


            if (tenantId == null || leaveTime == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Date leaveDate = standard.parse(leaveTime);

            Map<String, Object> contentMap = new HashMap<String, Object>();
            contentMap.put("tenantId", tenantId);
            contentMap.put("canClearing", 1);
            try {
                FnTenant fnTenant = fnTenantService.queryOne(tenantId);

                List<Object> energyTypeList = new ArrayList<>();
                Map<String, FnTenantPayType> payTypeMap = fnTenantPayTypeService.queryPayTypeMap(tenantId);
                Map<String, FnPriceTemplate> priceTemplateMap = fnTenantPriceService.queryPriceTemplateMap(tenantId);
                if (payTypeMap != null) {
                    for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                        for (Map.Entry<String, FnTenantPayType> payTypeEntry : payTypeMap.entrySet()) {
                            if (!energyType.getId().equals(payTypeEntry.getValue().getEnergyTypeId())) {
                                continue;
                            }
                            Map<String, Object> energyTypeMap = new HashMap<String, Object>();
                            energyTypeMap.put("energyTypeId", payTypeEntry.getKey());
                            energyTypeMap.put("energyTypeName", EnergyTypeUtil.queryEnergyTypeById(payTypeEntry.getKey()).getName());
                            energyTypeList.add(energyTypeMap);

                            FnPriceTemplate priceTemplate = priceTemplateMap.get(payTypeEntry.getKey());
                            energyTypeMap.put("priceTemplateId", priceTemplate.getId());
                            energyTypeMap.put("priceType", priceTemplate.getType());
                            energyTypeMap.put("priceTemplateName", priceTemplate.getName());

                            Map<String, Double> priceMap = new HashMap<>();

                            if (priceTemplate.getType().intValue() == EnumPriceType.AVG.getValue().intValue()) {
                                Double price = DoubleFormatUtil.Instance().doubleFormat(priceTemplate.getContent(), 2L);
                                energyTypeMap.put("priceTemplateContent", price);
                                priceMap.put("L", price);
                            } else {
                                JSONArray contentValueArray = (JSONArray) JSONValue.parse(priceTemplate.getContent());
                                List<Object> contentValueList = new ArrayList<Object>();
                                if (contentValueArray != null) {
                                    for (int i = 0; i < contentValueArray.size(); i++) {
                                        JSONObject contentValueObj = (JSONObject) contentValueArray.get(i);
                                        Map<String, Object> contentObjMap = new HashMap<String, Object>();
                                        contentObjMap.put("type", contentValueObj.get("type"));
                                        Double price = DoubleFormatUtil.Instance().doubleFormat(contentValueObj.get("value"), 2L);
                                        priceMap.put((String) contentValueObj.get("type"), price);
                                        contentObjMap.put("value", price);
                                        contentValueList.add(contentObjMap);
                                    }
                                }
                                energyTypeMap.put("priceTemplateContent", contentValueList);
                            }
                            energyTypeMap.put("payType", payTypeEntry.getValue().getPayType());
                            if (payTypeEntry.getValue().getPayType().intValue() == EnumPayType.POSTPAY.getValue().intValue()) {
                                Map<String, Object> postPay = new HashMap<String, Object>();
                                energyTypeMap.put("postPay", postPay);
                                this.processPostPay(fnTenant, payTypeEntry.getValue().getEnergyTypeId(), postPay, leaveDate, priceMap);
                            } else {//预付费
                                Map<String, Object> prePay = new HashMap<String, Object>();
                                energyTypeMap.put("prePay", prePay);
                                this.processPrePay(fnTenant, payTypeEntry.getValue(), prePay, leaveDate, priceMap);
                            }
                        }
                    }

                }

                contentMap.put("energyTypeList", energyTypeList);
            } catch (Exception e) {
                if (e instanceof ClearingException) {
                    contentMap.put("canClearing", 0);
                    contentMap.put("reason", e.getMessage());
                } else {
                    contentMap.put("canClearing", 0);
                    contentMap.put("reason", "查询异常");
                }
            }
            content.add(contentMap);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTTenantLeaveQueryService");
        }
    }

    private void processPostPay(FnTenant fnTenant, String energyTypeId, Map<String, Object> postPay, Date leaveDate, Map<String, Double> priceMap) throws Exception {
        {//账单
            List<Object> orderList = new ArrayList<>();
            postPay.put("orderList", orderList);

            List<FnRecordPostClearingPay> recordList = fnRecordPostClearingPayService.queryNotPayRecord(fnTenant.getBuildingId(), fnTenant.getId(), energyTypeId);
            if (recordList != null && recordList.size() > 0) {
                for (FnRecordPostClearingPay record : recordList) {
                    Map<String, Object> orderMap = new HashMap<>();
                    orderMap.put("orderId", record.getOrderId());
                    orderMap.put("orderTime", record.getOrderTime());
                    orderMap.put("amount", record.getCurrentSumEnergy());
                    orderMap.put("money", record.getMoney());
                    orderList.add(orderMap);
                }
            }
        }
        {//仪表
            List<Object> meterList = new ArrayList<>();
            postPay.put("meterList", meterList);

            List<DTOMeter> dtoMeterList = fnMeterService.queryMeterList(fnTenant.getId(), energyTypeId);
            if (dtoMeterList != null) {
                Project project = fnProjectService.queryProject();

                for (DTOMeter meter : dtoMeterList) {
                    Map<String, Object> meterMap = new HashMap<>();
                    meterMap.put("meterId", meter.getMeterId());
                    meterMap.put("meterType", meter.getMeterType());

                    List<Object> functionList = new ArrayList<>();
                    meterMap.put("functionList", functionList);
                    meterList.add(meterMap);
                    FnRecordPostClearingMeter record = fnRecordPostClearingMeterService.queryLastClearingMeter(fnTenant.getBuildingId(), fnTenant.getId(), energyTypeId, meter.getMeterId());

                    if (meter.getMeterType().intValue() == EnumMeterType.Common.getValue().intValue()) {//单累计量
                        Integer functionId = FunctionTypeUtil.getCumulantFunctionId(energyTypeId);
                        if (FunctionUtil.queryFunctionIdExsit(meter.getProtocolId(), energyTypeId, functionId)) {
                            this.procePostPayFunction(project, fnTenant, meter, functionId, energyTypeId, meterMap, functionList, leaveDate, priceMap, EnumPriceDetail.L.getValue(), record);
                        } else {
                            throw new Exception("协议必须包含此功能号:" + meter.getProtocolId());
                        }
                    } else {
                        Map<String, Integer> functionIdMap = FunctionTypeUtil.getCumulantDianMultiple();
                        for (Map.Entry<String, Integer> functionEntry : functionIdMap.entrySet()) {
                            if (FunctionUtil.queryFunctionIdExsit(meter.getProtocolId(), energyTypeId, functionEntry.getValue())) {
                                this.procePostPayFunction(project, fnTenant, meter, functionEntry.getValue(), energyTypeId, meterMap, functionList, leaveDate, priceMap, functionEntry.getKey(), record);
                            }
                        }
                    }
                }
            }
        }
    }

    private void procePostPayFunction(Project project, FnTenant fnTenant, DTOMeter meter, int functionId, String energyTypeId, Map<String, Object> meterMap, List<Object> functionList, Date leaveDate, Map<String, Double> priceMap, String type, FnRecordPostClearingMeter record) throws Exception {
        Double lastBillingData = null;
        Double currentBillingData = null;
        Date lastClearingTime = null;
        Double diffData = null;
        Double money = null;
        if (record == null) {
            //查询激活时间，查询激活时间点的读数
            MeterData meterData = fnMeterDataService.queryMeterDataEqual(project.getId(), meter.getMeterId(), functionId, EnumTimeType.T2.getValue(), fnTenant.getActiveTime());
            if (meterData != null) {
                Double radio = 1.0;
                try {
                    JSONObject extendObj = (JSONObject) JSONValue.parse(meter.getExtend());
                    Long ljlIsCt = (Long) extendObj.get("ljlIsCt");
                    if (ljlIsCt != null && ljlIsCt.intValue() == 1) {
                        radio = meter.getRadio();
                    }
                } catch (Exception e) {
                }
                lastClearingTime = fnTenant.getActiveTime();
                lastBillingData = meterData.getData() * radio;
            } else {
                lastClearingTime = fnTenant.getActiveTime();

                //查找前后原始读数
                Date from = new Date(lastClearingTime.getTime() - FineinConstant.Time.Minute_15 * 2);
                Date to = new Date(lastClearingTime.getTime() + FineinConstant.Time.Minute_15 * 2);
                MonthData monthData = fnOriginalDataService.queryLastMonthDataGteLte(project.getId(), meter.getMeterId(), functionId, from, to);
                if (monthData != null && monthData.getData() != null) {
                    Double ct = 1.0;
                    JSONObject extendObj = null;
                    try {
                        extendObj = (JSONObject) JSONValue.parse(meter.getExtend());
                        Double tempCt = DoubleFormatUtil.Instance().getDoubleData(extendObj.get("ljlIsCt"));
                        if (tempCt != null && tempCt.intValue() == 1) {
                            ct = meter.getRadio();
                        }
                    } catch (Exception e) {
                    }
                    lastBillingData = monthData.getData() * ct;
                } else {
                    throw new ClearingException("租户【" + fnTenant.getName() + "】仪表【" + meter.getMeterId() + "】未找到上次结算读数");
                }
            }
        } else {
            lastClearingTime = record.getCurrentClearingTime();
            try {
                JSONObject extendObj = (JSONObject) JSONValue.parse(record.getExtend());
                JSONArray fList = (JSONArray) extendObj.get("functionList");
                JSONObject functionObj = null;
                for (int i = 0; i < fList.size(); i++) {
                    functionObj = (JSONObject) fList.get(i);
                    if (type.equals(functionObj.get("type"))) {
                        lastBillingData = DoubleFormatUtil.Instance().getDoubleData(functionObj.get("currentData"));
                        break;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (lastClearingTime == null) {
            throw new ClearingException("租户【" + fnTenant.getName() + "】仪表【" + meter.getMeterId() + "】未找到上次结算时间");
        }
        if (lastBillingData == null) {
            throw new ClearingException("租户【" + fnTenant.getName() + "】仪表【" + meter.getMeterId() + "】未找到上次结算读数");
        }

        {
            MeterData meterData = fnMeterDataService.queryMeterDataEqual(project.getId(), meter.getMeterId(), functionId, EnumTimeType.T2.getValue(), leaveDate);
            if (meterData != null) {
                Double radio = 1.0;
                try {
                    JSONObject extendObj = (JSONObject) JSONValue.parse(meter.getExtend());
                    Long ljlIsCt = (Long) extendObj.get("ljlIsCt");
                    if (ljlIsCt != null && ljlIsCt.intValue() == 1) {
                        radio = meter.getRadio();
                    }
                } catch (Exception e) {
                }
                currentBillingData = meterData.getData() * radio;
            } else {
                Date from = new Date(leaveDate.getTime() - FineinConstant.Time.Minute_15 * 2);
                Date to = new Date(leaveDate.getTime() + FineinConstant.Time.Minute_15 * 2);
                MonthData monthData = fnOriginalDataService.queryLastMonthDataGteLte(project.getId(), meter.getMeterId(), functionId, from, to);
                if (monthData != null && monthData.getData() != null) {
                    Double ct = 1.0;
                    JSONObject extendObj = null;
                    try {
                        extendObj = (JSONObject) JSONValue.parse(meter.getExtend());
                        Double tempCt = DoubleFormatUtil.Instance().getDoubleData(extendObj.get("ljlIsCt"));
                        if (tempCt != null && tempCt.intValue() == 1) {
                            ct = meter.getRadio();
                        }
                    } catch (Exception e) {
                    }
                    currentBillingData = monthData.getData() * ct;
                } else {
                    throw new ClearingException("租户【" + fnTenant.getName() + "】仪表【" + meter.getMeterId() + "】未找到本次结算读数");
                }
            }
        }

        Date lastComputeDate = fnTenantMeterComputeService.queryLastComputeTime(fnTenant.getBuildingId(), fnTenant.getId(), meter.getMeterId(), functionId);
        if (lastComputeDate == null || lastComputeDate.getTime() <= leaveDate.getTime()) {
            throw new ClearingException("租户【" + fnTenant.getName() + "】仪表【" + meter.getMeterId() + "】计算未到达结算时间");
        }

        if (lastBillingData != null && currentBillingData != null && currentBillingData >= lastBillingData) {
            diffData = currentBillingData - lastBillingData;
        }
        if (diffData != null && priceMap.get(type) != null) {
            Double price = priceMap.get(type);
            money = diffData * price;
        }

        Map<String, Object> functionMap = new HashMap<>();
        functionMap.put("type", type);
        functionMap.put("functionId", functionId);
        functionMap.put("lastBillingData", lastBillingData);
        functionMap.put("currentBillingData", currentBillingData);
        functionMap.put("diffData", diffData);
        functionMap.put("money", money);

        functionList.add(functionMap);
        meterMap.put("lastClearingTime", lastClearingTime);
    }

    private void processPrePay(FnTenant fnTenant, FnTenantPayType tenantPayType, Map<String, Object> prePay, Date leaveDate, Map<String, Double> priceMap) throws Exception {
        prePay.put("prePayType", tenantPayType.getPrePayType());
        prePay.put("prepayChargeType", tenantPayType.getPrepayChargeType());
        String unit = UnitUtil.getBillModeUnit(tenantPayType.getEnergyTypeId(), EnumPrepayChargeType.valueOf(tenantPayType.getPrepayChargeType()));
        prePay.put("unit", unit);

        if (tenantPayType.getPrePayType().intValue() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {//租户
            FnTenantBackPayData tenantBackPayData = fnTenantBackPayDataService.queryByDate(fnTenant.getBuildingId(), fnTenant.getId(), tenantPayType.getEnergyTypeId(), EnumTimeType.T0, EnumPrepayChargeType.valueOf(tenantPayType.getPrepayChargeType()), leaveDate);
            if (tenantBackPayData != null) {
                prePay.put("tenantRemainData", tenantBackPayData.getData());
                prePay.put("tenantRemainDataTime", tenantBackPayData.getTimeFrom());
            } else {
                prePay.put("tenantRemainData", null);
                prePay.put("tenantRemainDataTime", null);
            }
        } else {//仪表
            List<Object> meterList = new ArrayList<>();
            prePay.put("meterList", meterList);

            List<DTOMeter> dtoMeterList = fnMeterService.queryMeterList(fnTenant.getId(), tenantPayType.getEnergyTypeId());
            if (dtoMeterList != null) {
                Project project = fnProjectService.queryProject();

                for (DTOMeter meter : dtoMeterList) {
                    Map<String, Object> meterMap = new HashMap<>();
                    meterMap.put("meterId", meter.getMeterId());
                    int functionId = -1;
                    if (tenantPayType.getPrepayChargeType() != null) {
                        if (tenantPayType.getPrepayChargeType().intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
                            functionId = FunctionTypeUtil.getShengYuJinEFunctionId(tenantPayType.getEnergyTypeId());
                        } else {
                            functionId = FunctionTypeUtil.getShengYuLiangFunctionId(tenantPayType.getEnergyTypeId());
                        }
                    } else {
                        if (queryShengYuJinEFunctionId(meter.getMeterId(), tenantPayType.getEnergyTypeId())) {
                            functionId = FunctionTypeUtil.getShengYuJinEFunctionId(tenantPayType.getEnergyTypeId());
                        } else if (queryShengYuLiangFunctionId(meter.getMeterId(), tenantPayType.getEnergyTypeId())) {
                            functionId = FunctionTypeUtil.getShengYuLiangFunctionId(tenantPayType.getEnergyTypeId());
                        }
                    }
                    Date from = new Date(leaveDate.getTime() - FineinConstant.Time.Minute_15 * 2);
                    Date to = new Date(leaveDate.getTime() + FineinConstant.Time.Minute_15 * 2);
                    MonthData monthData = fnOriginalDataService.queryLastMonthDataGteLte(project.getId(), meter.getMeterId(), functionId, from, to);
                    if (monthData != null && monthData.getData() != null) {
                        Double ct = 1.0;
                        JSONObject extendObj = null;
                        try {
                            extendObj = (JSONObject) JSONValue.parse(meter.getExtend());
                            Double tempCt = DoubleFormatUtil.Instance().getDoubleData(extendObj.get("syjIsCt"));
                            if (tempCt != null && tempCt.intValue() == 1) {
                                ct = meter.getRadio();
                            }
                        } catch (Exception e) {
                        }

                        meterMap.put("remainData", monthData.getData() * ct);
                        meterMap.put("remainDataTime", monthData.getReceivetime());
                    } else {
                        meterMap.put("remainData", null);
                        meterMap.put("remainDataTime", null);
                    }
                    meterList.add(meterMap);
                }
            }
        }
    }


    private boolean queryShengYuLiangFunctionId(String protocolId, String energyTypeId) {
        int cumulantFunctionId = FunctionTypeUtil.getShengYuLiangFunctionId(energyTypeId);
        List<FnProtocolFunction> functionList = ConstantDBBaseData.ProtocolFunctionMap.get(protocolId);
        if (functionList != null) {
            for (FnProtocolFunction protocolFunction : functionList) {
                if (protocolFunction.getFunctionId().intValue() == cumulantFunctionId) {
                    return true;
                }
            }
        }
        return false;
    }

    private boolean queryShengYuJinEFunctionId(String protocolId, String energyTypeId) {
        int cumulantFunctionId = FunctionTypeUtil.getShengYuJinEFunctionId(energyTypeId);
        List<FnProtocolFunction> functionList = ConstantDBBaseData.ProtocolFunctionMap.get(protocolId);
        if (functionList != null) {
            for (FnProtocolFunction protocolFunction : functionList) {
                if (protocolFunction.getFunctionId().intValue() == cumulantFunctionId) {
                    return true;
                }
            }
        }
        return false;
    }

    static class ClearingException extends Exception {
        private static final long serialVersionUID = -5771269677894177322L;

        ClearingException(String message) {
            super(message);
        }
    }
}
