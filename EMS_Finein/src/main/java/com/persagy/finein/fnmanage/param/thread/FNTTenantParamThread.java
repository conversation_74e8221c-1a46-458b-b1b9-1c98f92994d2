package com.persagy.finein.fnmanage.param.thread;

import com.persagy.core.thread.BaseThread;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.fnmanage.param.handler.FNTTenantParamBuildingHandler;
import com.persagy.finein.service.FNBuildingService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月21日 下午12:58:25

* 说明:租户参数计算线程
*/
@Component("FNTTenantParamThread")
public class FNTTenantParamThread extends BaseThread{

	private static boolean CONSTANT_THREAD_IS_OPEN = true;
	private static int CONSTANT_SLEEP = 30;
	
	private static Logger log = Logger.getLogger(FNTTenantParamThread.class);
	
	@Resource(name = "FNBuildingService")
	private FNBuildingService FNBuildingService;
	
	@Resource(name = "FNTTenantParamBuildingHandler")
	private FNTTenantParamBuildingHandler FNTTenantParamBuildingHandler;
	
	private static boolean IsSysParamValueInited = false;
	
	@Override
	protected void business() throws Exception {
		try {
			this.initSysParamValue();
			
			if(!CONSTANT_THREAD_IS_OPEN){
				this.setStop(true);
				log.error("【租户管理】租户参数计算线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}
			
			this.process();
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}
	}
	
	private void initSysParamValue(){
		if(IsSysParamValueInited){
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_AlarmThreadIsOpen);
			if(threadIsOpen != null){
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Integer sleep = (Integer)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_AlarmThreadSleepSecond);
			if(sleep != null){
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}
		
		IsSysParamValueInited = true;
		log.error("【租户管理】租户参数计算线程开始运行。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}
	
	private void process(){
		try {
			//1.查询所有建筑
			List<Building> buildingList = FNBuildingService.queryList(new Building());
			if(buildingList != null && buildingList.size() > 0){
				for(Building building : buildingList){
					this.FNTTenantParamBuildingHandler.handle(building);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}

