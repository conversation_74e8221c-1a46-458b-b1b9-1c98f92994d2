package com.persagy.finein.fnmanage.compute.handler;

import com.persagy.ems.pojo.finein.dictionary.Building;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月22日 下午12:58:25

* 说明:租户计算线程
*/
public interface FNTComputeBuildingEnergyCostHandler{
	
	public void process();

	public void processBuilding(Building building) throws Exception;
	
//	public void processTenantMeterStat(String buildingId,String tenantId,String meterId,String energyTypeId,EnumTimeType timeType,int functionId,EnumEnergyMoney energyMoney,Date timeFrom,Double data) throws Exception;
//	
//	public void processTenantStat(String buildingId,String tenantId,String energyTypeId,EnumTimeType timeType,EnumEnergyMoney energyMoney,Date timeFrom,Double data) throws Exception;
	
}

