package com.persagy.finein.fnmanage.thread;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.thread.BaseThread;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.dictionary.meter.DictionaryFunction;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnProtocolFunction;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumCommunicationType;
import com.persagy.finein.enumeration.EnumOriginalDataType;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNProjectService;
import com.persagy.finein.service.FNSendDataService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月18日 下午7:56:04
 * 
 * 说明: 采集数据线程
 */
@Component("FNTCollectDataThread")
public class FNTCollectDataThread extends BaseThread {

	private static boolean CONSTANT_THREAD_IS_OPEN = true;
	private static int CONSTANT_SLEEP = 30;

	private static Logger log = Logger.getLogger(FNTCollectDataThread.class);

	private static boolean IsSysParamValueInited = false;

	private static final long RefreshMeterTimeSpan = FineinConstant.Time.Minute_5;

	private Date lastRefreshMeterTime = null;

	private List<FnMeter> meterList = null;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNSendDataService")
	private FNSendDataService FNSendDataService;

	@Resource(name = "FNProjectService")
	private FNProjectService FNProjectService;

	@Override
	protected void business() throws Exception {
		try {
			this.initSysParamValue();

			if (!CONSTANT_THREAD_IS_OPEN) {
				this.setStop(true);
				log.error("【租户管理】采集数据线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}

			this.initMeter();

			this.process();
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}
	}

	private void initSysParamValue() {
		if (IsSysParamValueInited) {
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_CollectDataThreadIsOpen);
			if (threadIsOpen != null) {
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Integer sleep = (Integer) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_CollectDataThreadSleepSecond);
			if (sleep != null) {
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}

		IsSysParamValueInited = true;
		log.error("【租户管理】租户采集数据线程开始。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}

	private void initMeter() throws Exception {
		Date now = new Date();
		if (lastRefreshMeterTime == null || now.getTime() - lastRefreshMeterTime.getTime() > RefreshMeterTimeSpan) {
			meterList = FNMeterService.query(new FnMeter());
			lastRefreshMeterTime = now;
			log.info("【租户管理】租户采集数据线程刷新仪表完成。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
		}
	}

	private void process() {
		if (meterList == null || meterList.size() == 0) {
			return;
		}
		try {
			Project project = FNProjectService.queryProject();
			if (project == null) {
				log.info("【租户管理】租户采集数据未找到项目编号");
				return;
			}
			long start = System.currentTimeMillis();
			for (FnMeter meter : meterList) {
				if (meter.getCommunicationType().intValue() == EnumCommunicationType.Serial.getValue().intValue()) {// 只采集串口
					List<FnProtocolFunction> functionList = ConstantDBBaseData.ProtocolFunctionMap
							.get(meter.getProtocolId());
					if (functionList == null || functionList.size() == 0) {
						log.info("【租户管理】租户采集数据未找仪表:" + meter.getId() + "对应协议:" + meter.getProtocolId() + "的功能号");
						continue;
					}
					for (FnProtocolFunction function : functionList) {
						try {
							ICommunication communication = (ICommunication) SystemConstant.context
									.getBean(function.getProtocolId());
							if (communication != null) {
								DictionaryFunction dictionaryFunction = ConstantDBBaseData.DictionaryFunctionMap
										.get(function.getFunctionId() + "");
								if (dictionaryFunction == null) {
									log.info("【租户管理】租户采集数据未找到数据字典功能号:" + function.getFunctionId());
									continue;
								}
								Double data = null;
								try {
									Integer functionId = function.getFunctionId();
									if (functionId == 10711 || functionId == 10712 || functionId == 10713
											|| functionId == 10714 || functionId == 10715 || functionId == 10716
											|| functionId == 10709) {
										continue;
									}
//									if (!FineinConstant.SerialCollect) {
//										continue;
//									}
									data = communication.collectQuery(meter, function.getFunctionId());
									Thread.sleep(30);
								} catch (Exception e) {
								}
								if (data != null) {
									if (dictionaryFunction.getRemark() != null && dictionaryFunction.getRemark()
											.contains(EnumOriginalDataType.MonthData.getValue())) {
										FNSendDataService.saveData(project.getId(), meter.getId(),
												function.getFunctionId(), EnumOriginalDataType.MonthData.getDataType(),
												new Date(), data);
									} else {
										FNSendDataService.saveData(project.getId(), meter.getId(),
												function.getFunctionId(),
												EnumOriginalDataType.ElectriccurrentData.getDataType(), new Date(),
												data);
									}
								}

							}
						} catch (Exception e) {
							// TODO
							// log.info("【租户管理】租户采集数据未找到协议:"+meter.getProtocolId()+"的采集实现");
						}
					}
				}
			}
			long end = System.currentTimeMillis();
			log.info("【租户管理】租户采集数据" + meterList.size() + "个仪表用时:" + (end - start) + "毫秒");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
