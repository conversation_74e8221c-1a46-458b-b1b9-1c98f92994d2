package com.persagy.finein.fnmanage.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.finein.communication.exception.MeterSetException;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumPriceDetail;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNMeterService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理
 * 接口名：租户-批量设置仪表价格
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntMeterPriceSettingMultipleController extends BaseController {

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @RequestMapping("FNTMeterPriceSettingMultipleService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult meterPriceSettingMultiple(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String)dto.get("energyTypeId");
            List<Map<String,Object>> price = (List<Map<String,Object>>)dto.get("price");

            if(price == null || price.size() == 0){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            List<FnMeter> meterList = fnMeterService.queryMeterByEnergyTypeId(energyTypeId);
            if(meterList != null && meterList.size() > 0){
                for(FnMeter meter : meterList){
                    if(meter.getPayType().intValue() == EnumPayType.POSTPAY.getValue().intValue()){
                        continue;
                    }
                    Map<String,Object> meterMap = new HashMap<>();
                    meterMap.put("meterId", meter.getId());
                    List<Object> result = new ArrayList<>();
                    meterMap.put("result", result);

                    for(Map<String,Object> priceObj : price){
                        Map<String,Object> priceResult = new HashMap<>();
                        String type = (String)priceObj.get("type");
                        priceResult.put("type", type);
                        try {
                            Double value = DoubleFormatUtil.Instance().getDoubleData(priceObj.get("value"));
                            EnumPriceDetail priceDetail = EnumPriceDetail.parse(type);
                            if(priceDetail == null || value == null){
                                priceResult.put("status", EnumYesNo.NO.getValue());
                                priceResult.put("reason", "type 解析失败或value为空");
                            }
                            String protocolId = meter.getProtocolId();
                            ICommunication communication = (ICommunication) SystemConstant.context.getBean(protocolId);
                            boolean singleResult = communication.settingPrice(meter, priceDetail, value,new HashMap<String,Object>());
                            if(singleResult){
                                priceResult.put("status", EnumYesNo.YES.getValue());
                            }else{
                                priceResult.put("status", EnumYesNo.NO.getValue());
                                priceResult.put("reason", "设置失败");
                            }
                        } catch (MeterSetException e) {
                            meterMap.put("status", EnumYesNo.NO.getValue());
                            meterMap.put("reason", "不支持此功能");
                        } catch (Exception e) {
                            priceResult.put("status", EnumYesNo.NO.getValue());
                            priceResult.put("reason", "设置失败:"+e.getMessage());
                        }
                        result.add(priceResult);
                    }

                    content.add(meterMap);
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTMeterPriceSettingMultipleService");
        }
    }


}
