package com.persagy.finein.fnmanage.param.handler;

import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.dictionary.Building;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月21日 下午1:45:10

* 说明:
*/
public interface FNTTenantParamPrePayHandler {
	
	public void handle(Building building, FnTenant tenant, FnTenantPayType tenantPayType) throws Exception;
	
}

