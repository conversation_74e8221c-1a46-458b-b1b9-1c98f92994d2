package com.persagy.finein.fnmanage.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnRecordMeterSet;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.core.util.PathUtil;
import com.persagy.finein.enumeration.EnumMeterSetType;
import com.persagy.finein.enumeration.EnumPriceDetail;
import com.persagy.finein.service.FNFileResourceService;
import com.persagy.finein.service.FNRecordMeterSetService;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;

/**
 * 项目名：租户管理 接口名：仪表设置-仪表设置记录查询下载
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes", "deprecation" })
public class FntMeterSetRecordController extends BaseController {

    @Resource(name = "FNRecordMeterSetService")
    private FNRecordMeterSetService fnRecordMeterSetService;

    @Resource(name = "FNFileResourceService")
    private FNFileResourceService fnFileResourceService;

    @Resource(name = "PathUtil")
    private PathUtil pathUtil;

    @RequestMapping("FNTMeterSetRecordService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult meterSetRecord(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");
            String energyTypeId = (String) dto.get("energyTypeId");
            String tenantName = (String) dto.get("tenantName");
            Integer setType = (Integer) dto.get("setType");
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            Integer isDownload = (Integer) dto.get("isDownload");

            if (tenantId == null || timeFrom == null || timeTo == null || tenantName == null || setType == null || isDownload == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            List<Map<String, Object>> result = new ArrayList<>();
            // if (setType == EnumMeterSetType.METEROPERATE.getValue().intValue())
            // {// 仪表操作记录
            // List<FnRecordMeterOperate> queryList =
            // FNRecordMeterOperateService.queryList(tenantId,
            // standard.parse(timeFrom), standard.parse(timeTo));
            // if (queryList != null) {
            // for (FnRecordMeterOperate record : queryList) {
            // Map<String, Object> map = new HashMap<>();
            // map.put("meterId", record.getMeterId());
            // map.put("protocolId", record.getProtocolId());
            // map.put("operateType",
            // EnumMeterSetType.valueOf(record.getOperateType()).getView());
            // map.put("value", record.getValue());
            // map.put("operateTime", record.getOperateTime());
            // map.put("value", record.getValue());
            // map.put("request", record.getRequest());
            // map.put("respond", record.getRespond());
            // map.put("userName", record.getUserName());
            // result.add(map);
            // }
            // }
            //
            // } else {
            FnRecordMeterSet query = new FnRecordMeterSet();
            query.setTenantId(tenantId);
            query.setEnergyTypeId(energyTypeId);
            query.setOperateType(setType);
            query.setSpecialOperation("createTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("createTime", SpecialOperator.$lte, standard.parse(timeTo));
            query.setSort("createTime", EMSOrder.Desc);
            int count = fnRecordMeterSetService.count(query);
            if (isDownload == 0) {
                Integer pageIndex = (Integer) dto.get("pageIndex");
                Integer pageSize = (Integer) dto.get("pageSize");
                if (pageIndex == null || pageSize == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
                query.setSkip(Long.valueOf(pageIndex) * pageSize);
                query.setLimit(Long.valueOf(pageSize));
            }
            List<FnRecordMeterSet> queryList = fnRecordMeterSetService.query(query);
            if (queryList != null) {
                for (FnRecordMeterSet fnRecordMeterSet : queryList) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("roomCode", fnRecordMeterSet.getRoomCode());
                    map.put("meterId", fnRecordMeterSet.getMeterId());
                    map.put("energyTypeId", fnRecordMeterSet.getEnergyTypeId());
                    map.put("energyTypeName", EnergyTypeUtil.queryEnergyTypeNameById(fnRecordMeterSet.getEnergyTypeId()));
                    map.put("operateTime", fnRecordMeterSet.getCreateTime());
                    map.put("userName", fnRecordMeterSet.getUserName());
                    List<Map<String, Object>> list = new ArrayList<>();
                    if (setType == EnumMeterSetType.REMAINCLEAR.getValue().intValue()
                            || setType == EnumMeterSetType.GATE.getValue().intValue()
                            || setType == EnumMeterSetType.PAULELE.getValue().intValue()) {
                        Map readValue = SystemConstant.jsonMapper.readValue(fnRecordMeterSet.getExtend(), Map.class);
                        list.add(readValue);
                        map.put("data", list);
                    } else if (setType == EnumMeterSetType.UPDATEPRICE.getValue().intValue()) {
//					map.put("unit", "元/" + UnitUtil.getCumulantUnit(fnRecordMeterSet.getEnergyTypeId()));
//					list = SystemConstant.jsonMapper.readValue(fnRecordMeterSet.getExtend(), List.class);
                        Map valueList = SystemConstant.jsonMapper.readValue(fnRecordMeterSet.getExtend(), Map.class);
                        list.add(valueList);
                        map.put("data", list);

                    } else if (setType == EnumMeterSetType.OVERDRAFT.getValue().intValue()) {
                        Map readValue = SystemConstant.jsonMapper.readValue(fnRecordMeterSet.getExtend(), Map.class);
                        map.put("unit", "元");
                        list.add(readValue);
                        map.put("data", list);
                    }
                    result.add(map);
                }
            }
            // }
            if ((Integer) dto.get("isDownload") == 0) {
                Map<String, Object> resultMap = new HashMap<String, Object>();
                resultMap.put("count", count);
                resultMap.put("list", result);
                content.add(resultMap);
            } else {
                String subdirectory = pathUtil.getTempDownloadSubDir();
                String path = pathUtil.getPath();
                String fileDir = Paths.get(path, subdirectory).toString();
                File file = new File(fileDir);
                if (!file.exists()) {
                    file.mkdirs();
                }
                FileResource fileResource = new FileResource();
                String id = UUID.randomUUID().toString();
                fileResource.setId(id);
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append(tenantName).append("-").append(EnumMeterSetType.valueOf(setType).getView()).append("-").append(timeFrom.substring(0, 10));
                Date to = DateUtils.addDays(standard.parse(timeTo), -1);
                stringBuffer.append("~").append(standard.format(to).substring(0, 10));
                fileResource.setName(stringBuffer.toString());
                fileResource.setSubdirectory(subdirectory);
                fileResource.setSuffix("xls");
                // if (setType ==
                // EnumMeterSetType.METEROPERATE.getValue().intValue()) {
                // this.buildMeterOperateExcel(path, subdirectory, id, result);
                // }
                this.buildExcel(setType, path, subdirectory, id, result);
                fnFileResourceService.save(fileResource);
                Map<String, Object> newContentObj = new HashMap<>();
                newContentObj.put("id", id);
                content.add(newContentObj);
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTMeterSetRecordService");
        }
    }


    @SuppressWarnings("unchecked")
    private void buildExcel(Integer type, String path, String subdirectory, String id,
                            List<Map<String, Object>> result) {
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        // 创建标题
        HSSFRow row0 = sheet.createRow((short) 0);
        {
            HSSFCell cell = row0.createCell((short) 0);
            cell.setCellStyle(style);
            cell.setCellValue("房间ID");
        }
        {
            HSSFCell cell = row0.createCell((short) 1);
            cell.setCellStyle(style);
            cell.setCellValue("仪表ID");
        }

        {
            HSSFCell cell = row0.createCell((short) 2);
            cell.setCellStyle(style);
            cell.setCellValue("仪表能源类型");
        }
        {
            HSSFCell cell = row0.createCell((short) 3);
            cell.setCellStyle(style);
            cell.setCellValue("操作时间");
        }
        {
            HSSFCell cell = row0.createCell((short) 4);
            cell.setCellStyle(style);
            cell.setCellValue("操作人");
        }
        {
            HSSFCell cell = row0.createCell((short) 5);
            cell.setCellStyle(style);
            if (type == EnumMeterSetType.REMAINCLEAR.getValue().intValue()
                    || type == EnumMeterSetType.GATE.getValue().intValue()
                    || type == EnumMeterSetType.PAULELE.getValue().intValue()) {
                cell.setCellValue("操作");
            }
            if (type == EnumMeterSetType.OVERDRAFT.getValue().intValue()) {
                cell.setCellValue("透支金额");
            }
            if (type == EnumMeterSetType.UPDATEPRICE.getValue().intValue()) {
                cell.setCellValue("更新后价格");
            }
        }
        if (result != null) {
            int currentRow = 1;
            for (int i = 0; i < result.size(); i++) {
                Map<String, Object> tenant = (Map<String, Object>) result.get(i);
                String roomCode = (String) tenant.get("roomCode");
                String meterId = (String) tenant.get("meterId");
                String energyTypeName = (String) tenant.get("energyTypeName");
                Date operateTime = (Date) tenant.get("operateTime");
                String userName = (String) tenant.get("userName");
                List<Map<String, Object>> list = (List<Map<String, Object>>) tenant.get("data");

                HSSFRow row = sheet.createRow((short) currentRow);
                {
                    HSSFCell cell = row.createCell((short) (0));
                    cell.setCellValue(roomCode == null ? " " : roomCode);
                }
                {
                    HSSFCell cell = row.createCell((short) (1));
                    cell.setCellValue(meterId == null ? " " : meterId);
                }
                {
                    HSSFCell cell = row.createCell((short) (2));
                    cell.setCellValue(energyTypeName == null ? " " : energyTypeName);
                }
                {
                    HSSFCell cell = row.createCell((short) (3));
                    cell.setCellValue(operateTime == null ? " " : standard.format(operateTime));
                }
                {
                    HSSFCell cell = row.createCell((short) (4));
                    cell.setCellValue(userName == null ? " " : userName);
                }
                if (list != null && list.size() > 0) {
                    sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + list.size() - 1, 0, 0));
                    sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + list.size() - 1, 1, 1));
                    sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + list.size() - 1, 2, 2));
                    sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + list.size() - 1, 3, 3));
                    sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + list.size() - 1, 4, 4));
                    for (int j = 0; j < list.size(); j++) {
                        Map<String, Object> map = (Map<String, Object>) list.get(j);
                        String priceType = (String) map.get("type");
                        Object value = map.get("value");
                        String data;
                        if (priceType != null) {
                            EnumPriceDetail priceDetail = EnumPriceDetail.parse(priceType);
                            data = priceDetail.getView() + ":" + String.valueOf(value) + map.get("unit");
                        } else {
                            data = String.valueOf(value);
                        }
                        if (j != 0) {
                            row = sheet.createRow((short) currentRow);
                        }
                        {
                            HSSFCell cell = row.createCell((short) (5));
                            cell.setCellValue(data == null ? "--" : data);
                        }

                        currentRow++;
                    }
                } else {

                    {
                        HSSFCell cell = row.createCell((short) (5));
                        cell.setCellValue("--");
                    }
                    currentRow++;
                }
            }
        }
        String newFilePath = Paths.get(path, subdirectory, id).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fout != null) {
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
    }

}
