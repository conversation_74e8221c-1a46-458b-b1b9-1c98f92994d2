package com.persagy.finein.fnmanage.compute.thread;

import com.persagy.core.thread.BaseThread;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.fnmanage.compute.handler.FNTComputeTenantHandler;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月22日 下午12:58:25

* 说明:租户计算线程
*/
@Component("FNTComputeTenantThread")
public class FNTComputeTenantThread extends BaseThread{

	private static boolean CONSTANT_THREAD_IS_OPEN = true;
	private static long CONSTANT_SLEEP = 60;
	
	private static Logger log = Logger.getLogger(FNTComputeTenantThread.class);
	
	private static boolean IsSysParamValueInited = false;
	
	@Resource(name = "FNTComputeTenantHandler")
	private FNTComputeTenantHandler FNTComputeTenantHandler;
	
	@Override
	protected void business() throws Exception {
		try {
			this.initSysParamValue();
			
			if(!CONSTANT_THREAD_IS_OPEN){
				this.setStop(true);
				log.error("【租户管理】租户计算线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}
			
			this.FNTComputeTenantHandler.process();
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}
	}
	
	private void initSysParamValue(){
		if(IsSysParamValueInited){
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_ComputeTenantThreadIsOpen);
			if(threadIsOpen != null){
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Long sleep = (Long)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_ComputeTenantThreadSleepSecond);
			if(sleep != null){
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}
		
		IsSysParamValueInited = true;
		log.error("【租户管理】租户计算线程开始运行。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}
	
}

