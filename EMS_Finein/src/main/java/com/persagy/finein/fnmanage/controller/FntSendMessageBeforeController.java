package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.finein.FnScheduleJob;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNScheduleJobService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：自动发送短信设置前查询
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntSendMessageBeforeController extends BaseController {

    @Resource(name = "FNScheduleJobService")
    private FNScheduleJobService fnScheduleJobService;

    private final String jobGroup ="FNQuartzProcessAlarmHandler";


    @RequestMapping("FNTSendMessageBeforeService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult sendMessageBefore(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Map<String, Object> map = new HashMap<String, Object>();
            List<Integer> timeList = new ArrayList<Integer>();
            map.put("timeList", timeList);
            FnScheduleJob query = new FnScheduleJob();
            query.setJobGroup(jobGroup);
            List<FnScheduleJob> list = fnScheduleJobService.query(query);
            if (list != null && list.size() > 0) {
                FnScheduleJob scheduleJob = list.get(0);
                map.put("sendStuats", scheduleJob.getJobStatus());
                String time = scheduleJob.getTimeList();
                String[] split = time.split(",");
                for (String hour : split) {
                    timeList.add(Integer.valueOf(hour));
                }

            } else {
                map.put("sendStuats", EnumYesNo.NO.getValue());
            }
            content.add(map);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTSendMessageBeforeService");
        }
    }


}
