package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.originaldata.MonthData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.apache.log4j.Logger;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 万科定制
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FnWkCollectController extends BaseController {

    @Resource(name = "FNFloorService")
    private FNFloorService fnFloorService;

    @Resource(name = "FNRoomService")
    private FNRoomService fnRoomService;

    @Resource(name = "FNRoomMeterService")
    private FNRoomMeterService fnRoomMeterService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantRoomService")
    private FNTenantRoomService fnTenantRoomService;

    @Resource(name = "FNPriceTemplateService")
    private FNPriceTemplateService fnPriceTemplateService;

    @Resource(name = "FNTenantPriceService")
    private FNTenantPriceService fnTenantPriceService;

    @Resource(name = "FNTenantPrePayParamService")
    private FNTenantPrePayParamService fnTenantPrePayParamService;

    @Resource(name = "FNTenantPrePayMeterParamService")
    private FNTenantPrePayMeterParamService fnTenantPrePayMeterParamService;


    private static Logger log = Logger.getLogger(FnWkCollectController.class);

    @RequestMapping("FNWkCollectBuildingStaticService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult buildingStatic(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            List<Map> floorList = (List<Map>) dto.get("floorList");
            if (buildingId == null || floorList==null||floorList.size() == 0 ) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            log.info("****************采集建筑静态信息参数:"+floorList.toString());
            Map<String,FnRoom> dbRoomMap = fnRoomService.queryMap(buildingId);
            log.info("****************采集建筑静态信息数据库房间信息:"+dbRoomMap.toString());
            List<FnFloor> saveFloorList = new ArrayList<FnFloor>();
            List<FnRoom> saveRoomList = new ArrayList<FnRoom>();
            List<FnRoom> updateRoomList = new ArrayList<FnRoom>();
            List<FnRoomMeter> saveRoomMeterList = new ArrayList<FnRoomMeter>();
            for (Map floor : floorList) {
                String floorId=(String) floor.get("floorId");
                System.out.println("****************采集建筑静态信息楼层参数:"+floorId);
                FnFloor fnFloor = new FnFloor();
                fnFloor.setId(floorId);
                fnFloor.setBuildingId(buildingId);
                fnFloorService.remove(fnFloor);
                List<Map> roomList = (List<Map>) floor.get("roomList");
                System.out.println("****************采集建筑静态信息房间参数:"+roomList.toString());
                if(roomList!=null){
                    for (Map roomMap : roomList) {
                        String id = (String) roomMap.get("id");
                        String code = (String) roomMap.get("code");
                        Double area = (Double) roomMap.get("area");
                        String energyTypeIds = (String) roomMap.get("energyTypeIds");
                        Integer status = (Integer) roomMap.get("status");
                        FnRoom saveRoom = new FnRoom();
                        saveRoom.setBuildingId(buildingId);
                        saveRoom.setId(id);
                        saveRoom.setCode(code);
                        saveRoom.setArea(area);
                        saveRoom.setEnergyTypeIds(energyTypeIds);
                        saveRoom.setStatus(status);
                        saveRoom.setFloorId(floorId);
                        saveRoom.setCreateTime(new Date());
                        saveRoom.setCreateUserId("DJ");//对接
                        saveRoom.setIsValid(EnumValidStatus.VALID.getValue());
                        String meterList = (String) roomMap.get("meterList");
                        if(meterList!=null){
//		            	System.out.println("****************采集建筑静态信息房间参数:");
                            String[] split = meterList.split(",");
                            FnRoomMeter roomMeter = new FnRoomMeter();
                            roomMeter.setBuildingId(buildingId);
                            roomMeter.setRoomId(id);
                            fnRoomMeterService.remove(roomMeter);
                            for (String meterId : split) {
                                FnRoomMeter fnRoomMeter = new FnRoomMeter();
                                fnRoomMeter.setMeterId(meterId);
                                fnRoomMeter.setBuildingId(buildingId);
                                fnRoomMeter.setRoomId(id);
                                fnRoomMeter.setRoomCode(code);
                                fnRoomMeter.setId(UUID.randomUUID().toString());
                                fnRoomMeter.setEnergyTypeId("Dian");
                                saveRoomMeterList.add(fnRoomMeter);
                            }
                        }
                        if(dbRoomMap.containsKey(id)){
                            updateRoomList.add(saveRoom);
                            continue;
                        }
                        saveRoomList.add(saveRoom);
                    }
                }

                fnFloor.setName((String) floor.get("name"));
                fnFloor.setOrderBy((Integer) floor.get("orderBy"));
                saveFloorList.add(fnFloor);
            }
            {//保存
                fnFloorService.save(saveFloorList);
                log.info("****************采集建筑静态信息楼层信息:"+saveFloorList.toString());
                fnRoomService.save(saveRoomList);
                log.info("****************采集建筑静态信息房间信息:"+saveRoomList.toString());
                if(updateRoomList.size()>0){
                    for (FnRoom room : updateRoomList) {
                        FnRoom fnRoom = new FnRoom();
                        fnRoom.setId(room.getId());
                        fnRoomService.update(fnRoom, room);
                    }
                }
                fnRoomMeterService.save(saveRoomMeterList);
                log.info("****************采集建筑静态信息房间仪表信息:"+saveRoomMeterList.toString());
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNWkCollectBuildingStaticService");
        }
    }

    @RequestMapping("FNWkCollectMeterService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult collectMeter(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            List<Map> meterList = (List<Map>) dto.get("meterList");

            if (buildingId == null || meterList==null||meterList.size() == 0 ) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            List<FnMeter> list = new ArrayList<FnMeter>();
            for (Map meter : meterList) {
                FnMeter save = new FnMeter();
                save.setId((String)meter.get("meterId"));
                save.setProtocolId((String)meter.get("protocolId"));
                fnMeterService.remove(save);
                save.setEnergyTypeId((String)meter.get("energyTypeId"));
                save.setPayType((Integer)meter.get("payType"));
                save.setRadio((Double)meter.get("radio"));
                save.setMeterType((Integer)meter.get("meterType"));
                Map<String, Object> extendMap = new HashMap<>();
                extendMap.put("kkValue", (Double)meter.get("kkValue"));
                extendMap.put("ljlIsCt", (Integer)meter.get("ljlIsCt"));
                extendMap.put("syjIsCt", (Integer)meter.get("syjIsCt"));
                extendMap.put("isSanXiang",(Integer)meter.get("isSanXiang"));
                save.setExtend(objectMapper.writeValueAsString(extendMap));

                save.setClientIp("DJWK");
                save.setClientPort(0);
                save.setServerIp("DJWK");
                save.setServerPort(0);
                save.setCommunicationType(EnumCommunicationType.Collector.getValue());
                save.setIsUse(EnumUseStatus.In_Use.getValue());
                list.add(save);

            }
            fnMeterService.save(list);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNWkCollectMeterService");
        }
    }

    @RequestMapping("FNWkCollectOrignalDataService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult collectOrignalData(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            List<Map> contentList = (List<Map>) dto.get("contentList");

            if (buildingId == null || contentList == null || contentList.size() == 0) {
                log.info("************参数为空:buildingId"+buildingId+",contentList"+contentList+",contentList长"+contentList.size());
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            List<MonthData> monthDataList = new ArrayList<MonthData>();
            for (Map map : contentList) {
                String sign = (String) map.get("sign");
                Integer funcid = (Integer) map.get("funcid");
                String receiveTime = (String) map.get("receiveTime");
                Double data = (Double) map.get("data");
                MonthData monthData = new MonthData();
                monthData.setFuncid(funcid);
                monthData.setSign(sign);
                monthData.setBuildingForContainer(buildingId);
                monthData.setReceivetime(standard.parse(receiveTime));
                coreDao.remove(monthData);
                monthData.setData(data);
                monthDataList.add(monthData);
            }
            coreDao.save(monthDataList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNWkCollectOrignalDataService");
        }
    }

    @RequestMapping("FNWkCollectRealTimeDataService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult collectRealTimeData(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            List<Map> contentList = (List<Map>) dto.get("contentList");

            if (buildingId == null || contentList == null || contentList.size() == 0) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
//		FNTenantPrePayParamService.remove(new FnTenantPrePayParam());
//		FNTenantPrePayMeterParamService.remove(new FnTenantPrePayMeterParam());

            List<FnTenantPrePayParam> tenantPrePayList = new ArrayList<FnTenantPrePayParam>();
            List<FnTenantPrePayMeterParam> meterPrePayList = new ArrayList<FnTenantPrePayMeterParam>();
            for (Map tenant : contentList) {
                String tenantId = (String) tenant.get("tenantId");
                Integer objectType = (Integer) tenant.get("objectType");
                String objectId = (String) tenant.get("objectId");
                String energyTypeId = (String) tenant.get("energyTypeId");
                Integer payType = (Integer) tenant.get("payType");
                String lastUpdateFrom = (String) tenant.get("lastUpdateFrom");
                if (payType != 4) {// 预付费
                    if (objectType == 0) {// 租户
                        FnTenantPrePayParam tenantPrePayParam = new FnTenantPrePayParam();
                        tenantPrePayParam.setBuildingId(buildingId);
                        tenantPrePayParam.setEnergyTypeId(energyTypeId);
                        tenantPrePayParam.setTenantId(tenantId);
                        fnTenantPrePayParamService.remove(tenantPrePayParam);
                        tenantPrePayParam.setIsAlarm((Integer) tenant.get("isAlarm"));
                        tenantPrePayParam.setLastUpdateTime(lastUpdateFrom==null?null:standard.parse(lastUpdateFrom));
                        tenantPrePayParam.setPrepayChargeType((Integer) tenant.get("remainType"));
                        tenantPrePayParam.setPrePayType(payType);
                        tenantPrePayParam.setRemainData((Double) tenant.get("remainData"));
                        tenantPrePayParam.setRemainDays(tenant.get("remainMinDay") + "~" + tenant.get("remainMaxDay"));
                        tenantPrePayList.add(tenantPrePayParam);
                    } else {
                        FnTenantPrePayMeterParam tenantPrePayMeterParam = new FnTenantPrePayMeterParam();
                        tenantPrePayMeterParam.setBuildingId(buildingId);
                        tenantPrePayMeterParam.setMeterId((String) tenant.get("objectId"));
                        tenantPrePayMeterParam.setEnergyTypeId(energyTypeId);
                        tenantPrePayMeterParam.setTenantId(tenantId);
                        fnTenantPrePayMeterParamService.remove(tenantPrePayMeterParam);
                        tenantPrePayMeterParam.setIsAlarm((Integer) tenant.get("isAlarm"));
                        tenantPrePayMeterParam.setLastUpdateTime(standard.parse((String) tenant.get("lastUpdateTime")));
                        tenantPrePayMeterParam.setPrepayChargeType((Integer) tenant.get("remainType"));
                        tenantPrePayMeterParam.setPrePayType(payType);
                        tenantPrePayMeterParam.setRemainData((Double) tenant.get("remainData"));
                        tenantPrePayMeterParam.setRemainDays(tenant.get("remainMinDay") + "~" + tenant.get("remainMaxDay"));
                        meterPrePayList.add(tenantPrePayMeterParam);
                    }
                }
            }
            fnTenantPrePayParamService.save(tenantPrePayList);
            fnTenantPrePayMeterParamService.save(meterPrePayList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNWkCollectRealTimeDataService");
        }
    }

    @RequestMapping("FNWkCollectStaticDataService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult collectStaticData(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            List<Map> tenantList = (List<Map>) dto.get("tenantList");


            if (buildingId == null || tenantList == null || tenantList.size() == 0) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            log.info("*************采集租户静态信息参数:"+tenantList.toString());
            Map<String, FnPriceTemplate> dbPriceTemplateMap=new HashMap<String,FnPriceTemplate>();
            List<FnTenant> fnTenantSaveList = new ArrayList<FnTenant>();
            List<FnTenantPayType> fnTenantPayTypeList = new ArrayList<FnTenantPayType>();
            List<FnTenantRoom> fnTenantRoomList = new ArrayList<FnTenantRoom>();
            List<FnPriceTemplate> fnPriceTemplateList = new ArrayList<FnPriceTemplate>();
            List<FnTenantPrice> fnTenantPriceList = new ArrayList<FnTenantPrice>();
            for (Map tenant : tenantList) {
                String leaveTime = (String) tenant.get("leaveTime");
                String createTime = (String) tenant.get("createTime");
                String activeTime = (String) tenant.get("activeTime");
                FnTenant fnTenant = new FnTenant();
                String tenantId = (String) tenant.get("tenantId");
                fnTenant.setId(tenantId);
                fnTenant.setActiveUserId("DJ");
                fnTenantService.remove(fnTenant);

                fnTenant.setBuildingId(buildingId);
                Double area = (Double) tenant.get("area");
                fnTenant.setActiveTime(activeTime == null ? null : standard.parse(activeTime));
                fnTenant.setArea(area);
                fnTenant.setContactMobile((String) tenant.get("contactMobile"));
                fnTenant.setCreateTime(createTime == null ? null : standard.parse(createTime));
                fnTenant.setContactName((String) tenant.get("contactName"));
                fnTenant.setLeaveTime(leaveTime == null ? null : standard.parse(leaveTime));
                fnTenant.setName((String) tenant.get("tenantName"));
                fnTenant.setStatus((Integer) tenant.get("status"));
                fnTenant.setTenantTypeId((Integer) tenant.get("tenantType") + "");

                fnTenant.setLeaveUserId("DJ");
                fnTenant.setCreateTime(currentTime);
                fnTenant.setCreateUserId("DJ");
                fnTenant.setEnergyTypeIds("");
                fnTenant.setInvalidTime(currentTime);
                fnTenant.setIsValid(EnumValidStatus.VALID.getValue());
                fnTenant.setLastUpdateTime(new Date());
                fnTenant.setLastUpdateUserId("DJ");
                StringBuffer roomCodeBuffer = new StringBuffer();
                StringBuffer energyTypeBuffer = new StringBuffer();
                List<Map> roomList = (List<Map>) tenant.get("roomList");
                if (roomList != null) {
                    {
                        FnTenantRoom room = new FnTenantRoom();
                        room.setBuildingId(buildingId);
                        room.setTenantId(tenantId);
                        fnTenantRoomService.remove(room);
                    }
                    for (Map map : roomList) {
                        if(roomCodeBuffer.length()!=0){
                            roomCodeBuffer.append(",");
                        }
                        roomCodeBuffer.append((String) map.get("code"));
                        FnTenantRoom fnTenantRoom = new FnTenantRoom();
                        fnTenantRoom.setRoomId((String) map.get("id"));
                        fnTenantRoom.setTenantId(tenantId);
                        fnTenantRoom.setBuildingId(buildingId);
                        fnTenantRoom.setRoomCode((String) map.get("code"));
                        fnTenantRoom.setId(UUID.randomUUID().toString());
                        fnTenantRoomList.add(fnTenantRoom);
                    }
                }

                List<Map> energyTypeList = (List<Map>) tenant.get("energyTypeList");
                for (Map energyType : energyTypeList) {
                    String energyTypeId = (String) energyType.get("energyTypeId");
                    if(energyTypeBuffer.length()!=0){
                        energyTypeBuffer.append(",");
                    }
                    energyTypeBuffer.append(energyTypeId);
                    FnTenantPayType fnTenantPayType = new FnTenantPayType();
                    fnTenantPayType.setBuildingId(buildingId);
                    fnTenantPayType.setTenantId(tenantId);
                    fnTenantPayTypeService.remove(fnTenantPayType);
                    fnTenantPayType.setId(UUID.randomUUID().toString());
                    fnTenantPayType.setEnergyTypeId(energyTypeId);
                    fnTenantPayType.setPayType((Integer) energyType.get("payType"));
                    if ((Integer) energyType.get("payType") == 4) {// 后付费
                        fnTenantPayType.setPayType(EnumPayType.POSTPAY.getValue());
                        fnTenantPayType.setPrePayType(EnumPrePayType.None.getValue());
                        fnTenantPayType.setPrepayChargeType(EnumPrepayChargeType.Liang.getValue());
                    } else {
                        fnTenantPayType.setPayType(EnumPayType.PREPAY.getValue());
                        fnTenantPayType.setPrePayType((Integer) energyType.get("payType"));
                        fnTenantPayType.setPrepayChargeType((Integer) energyType.get("chargeType"));
                    }
                    fnTenantPayTypeList.add(fnTenantPayType);
                    {// 价格模板
                        String priceTemplateId = (String) energyType.get("priceTemplateId");
                        Integer priceTemplateType = (Integer) energyType.get("priceTemplateType");
                        String priceTemplateName = (String) energyType.get("priceTemplateName");
                        String priceTemplateContent = (String) energyType.get("priceTemplateContent");
                        FnPriceTemplate priceTemplate = new FnPriceTemplate();
                        priceTemplate.setId(priceTemplateId);
                        fnPriceTemplateService.remove(priceTemplate);
                        priceTemplate.setName(priceTemplateName);
                        priceTemplate.setType(priceTemplateType);
                        priceTemplate.setContent(priceTemplateContent);
                        priceTemplate.setCreateTime(new Date());
                        priceTemplate.setCreateUserId("DJ");
                        priceTemplate.setEnergyTypeId(energyTypeId);
                        priceTemplate.setInvalidTime(new Date());
                        priceTemplate.setIsValid(EnumValidStatus.VALID.getValue());
                        priceTemplate.setLastUpdateTime(new Date());
                        if(!dbPriceTemplateMap.containsKey(priceTemplateId)){
                            dbPriceTemplateMap.put(priceTemplateId, priceTemplate);
                            fnPriceTemplateList.add(priceTemplate);
                        }
                        FnTenantPrice tenantPrice = new FnTenantPrice();
                        tenantPrice.setEnergyTypeId(energyTypeId);
                        tenantPrice.setTenantId(tenantId);
                        fnTenantPriceService.remove(tenantPrice);

                        tenantPrice.setPriceTemplateId(priceTemplateId);
                        tenantPrice.setId(UUID.randomUUID().toString());
                        tenantPrice.setLastUpdateTime(currentTime);
                        tenantPrice.setLastUpdateUserId("DJ");
                        fnTenantPriceList.add(tenantPrice);
                    }
                }
                fnTenant.setRoomCodes(roomCodeBuffer.toString());
                fnTenant.setEnergyTypeIds(energyTypeBuffer.toString());
                fnTenantSaveList.add(fnTenant);
            }
            fnTenantService.save(fnTenantSaveList);
            fnTenantPayTypeService.save(fnTenantPayTypeList);
            fnTenantRoomService.save(fnTenantRoomList);
            fnTenantPriceService.save(fnTenantPriceList);
            fnPriceTemplateService.save(fnPriceTemplateList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNWkCollectStaticDataService");
        }
    }
}
