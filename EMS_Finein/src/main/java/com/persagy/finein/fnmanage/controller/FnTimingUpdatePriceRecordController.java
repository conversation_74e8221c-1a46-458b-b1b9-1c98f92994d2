package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnTimingUpdatePriceRecord;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.util.PathUtil;
import com.persagy.finein.enumeration.EnumResult;
import com.persagy.finein.service.FNFileResourceService;
import com.persagy.finein.service.FNTimingUpdatePriceRecordService;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.hssf.usermodel.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;

/**
 * @Author: ls
 * @Date: 2022/2/10 11:17
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FnTimingUpdatePriceRecordController extends BaseController {


    @Resource(name = "FNTimingUpdatePriceRecordService")
    private FNTimingUpdatePriceRecordService fntTimingUpdatePriceRecordService;

    @Resource(name = "FNFileResourceService")
    private FNFileResourceService fnFileResourceService;

    @Resource(name = "PathUtil")
    private PathUtil pathUtil;

    /**
     * 定时修改电价记录
     *
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTimingUpdatePriceRecordService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult timingUpdatePriceRecord(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");
            Integer result = (Integer) dto.get("result");
            Integer isDownload = (Integer) dto.get("isDownload");
            String tenantName = (String) dto.get("tenantName");
            List content = new ArrayList();

            if (timeFrom == null || timeTo == null|| isDownload == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            FnTimingUpdatePriceRecord query = new FnTimingUpdatePriceRecord();
            query.setSpecialOperation("updateTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("updateTime", SpecialOperator.$lt, standard.parse(timeTo));
            if (tenantName != null && !"".equals(tenantName.trim())) {
                query.setSpecialOperation("tenantName", SpecialOperator.$like, "%" + tenantName.trim() + "%");
            }
            if (result != null) {
                query.setResult(result);
            }
            int count = fntTimingUpdatePriceRecordService.count(query);
            if (isDownload == 0) {
                if (pageIndex == null || pageSize == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
                query.setSkip(Long.valueOf(pageIndex) * pageSize);
                query.setLimit(Long.valueOf(pageSize));
            }
            query.setSort("updateTime", EMSOrder.Desc);
            List<FnTimingUpdatePriceRecord> recordList = fntTimingUpdatePriceRecordService.query(query);
            List<Map<String, Object>> resultList = new ArrayList<Map<String, Object>>();
            if (recordList != null && recordList.size() > 0) {
                for (FnTimingUpdatePriceRecord record : recordList) {
                    HashMap<String, Object> map = new HashMap<String, Object>();
                    map.put("tenantId", record.getTenantId());
                    map.put("tenantName", record.getTenantName());
                    map.put("meterId", record.getMeterId());
                    map.put("result", record.getResult());
                    map.put("updateTime", record.getUpdateTime());
                    map.put("setContent", record.getContent());
                    resultList.add(map);
                }
            }
            if (isDownload == 0) {
                HashMap<String, Object> contentMap = new HashMap<>();
                contentMap.put("count", count);
                contentMap.put("dataList", resultList);
                content.add(contentMap);
            } else {
                String subdirectory = pathUtil.getTempDownloadSubDir();
                String path = pathUtil.getPath();
                String fileDir = Paths.get(path, subdirectory).toString();
                File file = new File(fileDir);
                if (!file.exists()) {
                    file.mkdirs();
                }
                FileResource fileResource = new FileResource();
                String id = UUID.randomUUID().toString();
                fileResource.setId(id);
                StringBuffer stringBuffer = new StringBuffer();
                stringBuffer.append("定时批量设置电价记录").append("-")
                        .append(standard.format(standard.parse(timeFrom)).substring(0, 10));
                stringBuffer.append("_").append(standard.format(standard.parse(timeTo)).substring(0, 10));
                fileResource.setName(stringBuffer.toString());
                fileResource.setSubdirectory(subdirectory);
                fileResource.setSuffix("xls");
                this.UpdatePriceRecordBuildExcel(path, subdirectory, id, resultList);
                fnFileResourceService.save(fileResource);
                Map<String, Object> newContentObj = new HashMap<>();
                newContentObj.put("id", id);
                content.add(newContentObj);
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTimingUpdatePriceRecordService");
        }
    }

    @SuppressWarnings("deprecation")
    private void UpdatePriceRecordBuildExcel(String path, String subdirectory, String id, List<Map<String, Object>> result) {
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        // 创建标题
        HSSFRow row0 = sheet.createRow((short) 0);

        {
            HSSFCell cell = row0.createCell((short) 0);
            cell.setCellStyle(style);
            cell.setCellValue("租户编号");
        }
        {
            HSSFCell cell = row0.createCell((short) 1);
            cell.setCellStyle(style);
            cell.setCellValue("租户名称");
        }
        {
            HSSFCell cell = row0.createCell((short) 2);
            cell.setCellStyle(style);
            cell.setCellValue("仪表ID");
        }
        {
            HSSFCell cell = row0.createCell((short) 3);
            cell.setCellStyle(style);
            cell.setCellValue("执行时间");
        }
        {
            HSSFCell cell = row0.createCell((short) 4);
            cell.setCellStyle(style);
            cell.setCellValue("执行结果");
        }
        {
            HSSFCell cell = row0.createCell((short) 5);
            cell.setCellStyle(style);
            cell.setCellValue("设置内容");
        }

        if (result != null) {
            int currentRow = 1;
            for (int i = 0; i < result.size(); i++) {
                Map<String, Object> object = result.get(i);
                String tenantId = (String) object.get("tenantId");
                String tenantName = (String) object.get("tenantName");
                String meterId = (String) object.get("meterId");
                Integer results = (Integer) object.get("result");
                Date updateTime = (Date) object.get("updateTime");
                String setContent = (String) object.get("setContent");
                HSSFRow row = sheet.createRow((short) currentRow);
                {
                    HSSFCell cell = row.createCell((short) (0));
                    cell.setCellValue(tenantId == null ? " " : tenantId);
                }
                {
                    HSSFCell cell = row.createCell((short) (1));
                    cell.setCellValue(tenantName == null ? " " : tenantName);
                }
                {
                    HSSFCell cell = row.createCell((short) (2));
                    cell.setCellValue(meterId == null ? " " : meterId);
                }
                {
                    HSSFCell cell = row.createCell((short) (3));
                    cell.setCellValue(updateTime == null ? " " : standard.format(updateTime));
                }
                {
                    HSSFCell cell = row.createCell((short) (4));
                    cell.setCellValue(results == null ? " " : EnumResult.valueOf(results).getView());
                }
                {
                    HSSFCell cell = row.createCell((short) (5));
                    cell.setCellValue(setContent == null ? " " : setContent);
                }

                currentRow++;
            }
        }
        String newFilePath = Paths.get(path, subdirectory, id).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fout != null) {
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
    }
}
