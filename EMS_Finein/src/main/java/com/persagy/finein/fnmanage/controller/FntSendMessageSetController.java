package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnScheduleJob;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.fnmanage.quartz.QuartzJobFactory;
import com.persagy.finein.service.*;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理 接口名：自动发送短信设置
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntSendMessageSetController extends BaseController {

    @Resource(name = "FNScheduleJobService")
    private FNScheduleJobService fnScheduleJobService;

    @Autowired
    private SchedulerFactoryBean schedulerFactoryBean;

    private final static String JOB_NAME = "定时发送短信";
    private final static String JOB_Group = "FNQuartzProcessAlarmHandler";


    @RequestMapping("FNTSendMessageSetService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult sendMessageSet(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer sendStuats = (Integer) dto.get("sendStuats");

            if (sendStuats == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnScheduleJob query = new FnScheduleJob();
            query.setJobName(JOB_NAME);
            query.setJobGroup(JOB_Group);
            List<FnScheduleJob> list = fnScheduleJobService.query(query);

            if (sendStuats == EnumYesNo.YES.getValue().intValue()) {// 开启自动发送
                // Integer fristTime = (Integer) dto.get("fristTime");
                // Integer secondTime = (Integer) dto.get("secondTime");
                List<Integer> timeList = (List<Integer>) dto.get("timeList");
                if (timeList == null || timeList.size() == 0) {
                    throw new Exception("时间不能为空");
                }

                // CronExpression时间表达式
                StringBuffer stringBuffer = new StringBuffer();
                StringBuffer timeListStr = new StringBuffer();
                stringBuffer.append("0 0 ");
                for (int i = 0; i < timeList.size(); i++) {
                    timeListStr.append(timeList.get(i));
                    if(i<(timeList.size()-1)){
                        timeListStr.append(",");
                    }
                }
                stringBuffer.append(timeListStr.toString()).append(" * * ?");
                if (list != null && list.size() > 0) {
                    FnScheduleJob job = list.get(0);
                    FnScheduleJob update = new FnScheduleJob();
                    update.setCronExpression(stringBuffer.toString());
                    update.setUpdateTime(new Date());
                    update.setTimeList(timeListStr.toString());
                    update.setJobStatus(sendStuats);
                    processTrigger(job, stringBuffer.toString());
                    fnScheduleJobService.update(job, update);
                } else {
                    FnScheduleJob save = new FnScheduleJob();
                    save.setId(UUID.randomUUID().toString());
                    save.setJobName(JOB_NAME);
                    save.setJobGroup(JOB_Group);
                    save.setJobStatus(sendStuats);
                    save.setCronExpression(stringBuffer.toString());
                    save.setTimeList(timeListStr.toString());
                    Date now = new Date();
                    save.setCreateTime(now);
                    save.setUpdateTime(now);

                    processTrigger(save, stringBuffer.toString());
                    fnScheduleJobService.save(save);
                }
            } else {// 关闭自动发送
                if (list != null && list.size() > 0) {
                    FnScheduleJob job = list.get(0);
                    deleteJob(job);

                    FnScheduleJob update = new FnScheduleJob();
                    update.setUpdateTime(new Date());
                    update.setJobStatus(sendStuats);
                    fnScheduleJobService.update(job, update);
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTSendMessageSetService");
        }
    }

    private void processTrigger(FnScheduleJob job, String cronExpression) throws SchedulerException {
        TriggerKey triggerKey = TriggerKey.triggerKey(job.getJobName(), job.getJobGroup());
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        // 获取trigger，即在spring配置文件中定义的 bean id="myTrigger"
        CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
        // 不存在，创建一个
        if (null == trigger) {
            JobDetail jobDetail = JobBuilder.newJob(QuartzJobFactory.class)
                    .withIdentity(job.getJobName(), job.getJobGroup()).build();
            jobDetail.getJobDataMap().put("scheduleJob", job);

            // 表达式调度构建器
            // CronScheduleBuilder scheduleBuilder =
            // CronScheduleBuilder.cronSchedule(job
            // .getCronExpression());
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(job.getCronExpression())
                    .withMisfireHandlingInstructionDoNothing();

            // 按新的cronExpression表达式构建一个新的trigger
            trigger = TriggerBuilder.newTrigger().withIdentity(job.getJobName(), job.getJobGroup())
                    .withSchedule(scheduleBuilder).build();

            scheduler.scheduleJob(jobDetail, trigger);
        } else {
            // Trigger已存在，那么更新相应的定时设置
            // 表达式调度构建器
            CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(cronExpression);
            // 按新的cronExpression表达式重新构建trigger
            trigger = trigger.getTriggerBuilder().withIdentity(triggerKey).withSchedule(scheduleBuilder).build();
            // 按新的trigger重新设置job执行
            scheduler.rescheduleJob(triggerKey, trigger);
        }
    }

    public void deleteJob(FnScheduleJob scheduleJob) throws SchedulerException {
        Scheduler scheduler = schedulerFactoryBean.getScheduler();
        JobKey jobKey = JobKey.jobKey(scheduleJob.getJobName(), scheduleJob.getJobGroup());
        scheduler.deleteJob(jobKey);
    }

}
