package com.persagy.finein.fnmanage.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.dto.DTORoomMeter;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnRecordPrePay;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.core.util.PathUtil;
import com.persagy.finein.enumeration.EnumPayBodyType;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;

/**
 * 项目名：租户管理 接口名：批量操作-充值记录(软充表扣)（查询+下载）
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes", "deprecation" })
public class FntBatchPrePay2GridController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNRecordPrePayService")
    private FNRecordPrePayService fnRecordPrePayService;

    @Resource(name = "PathUtil")
    private PathUtil pathUtil;

    @Resource(name = "FNFileResourceService")
    private FNFileResourceService fnFileResourceService;

    @Resource(name = "FNRoomService")
    private FNRoomService fnRoomService;


    @RequestMapping("FNTBatchPrePay2GridService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchPrePay2Grid(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String) dto.get("energyTypeId");
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            Integer isDownload = (Integer) dto.get("isDownload");
            Integer timeType = (Integer) dto.get("timeType");

            if (dto.get("tenantList") == null || energyTypeId == null || timeFrom == null || timeTo == null
                    || isDownload == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            List<String> tenantList = (List<String>) dto.get("tenantList");

            Date from = standard.parse(timeFrom);
            Date to = standard.parse(timeTo);

            String energyUnit = UnitUtil.getCumulantUnit(energyTypeId);
            String energyTypeName = EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId);

            List<FnTenant> fnTenantList = fnTenantService.queryListByIds(tenantList);
            List<Object> tenantObjList = new ArrayList<>();
            Double moneySum = 0.0;
            Double amountSum = 0.0;
            if (fnTenantList != null) {
                for (FnTenant tenant : fnTenantList) {
                    int orderCount = 0;
                    Map<String, Object> tenantMap = new HashMap<>();
                    tenantMap.put("tenantId", tenant.getId());
                    tenantMap.put("tenantName", tenant.getName());
                    List<Map<String, Object>> roomList = new ArrayList<>();
                    tenantMap.put("roomList", roomList);
                    tenantObjList.add(tenantMap);
                    List<DTORoomMeter> DTORoomMeterList = fnRoomService.queryRoomListByTenantId(tenant.getId(),
                            energyTypeId);
                    Double tenantMoneySum = 0.0;// 充值钱汇总
                    Double tenantAmountSum = 0.0;// 充值量汇总
                    if (DTORoomMeterList != null) {
                        for (DTORoomMeter dtoRoomMeter : DTORoomMeterList) {
                            int roomOrderCount = 0;
                            List<DTOMeter> meterList = dtoRoomMeter.getMeterList();
                            if (meterList != null) {
                                Map<String, Object> roomMap = new HashMap<>();
                                roomList.add(roomMap);
                                roomMap.put("roomCode", dtoRoomMeter.getRoomCode());
                                List<Object> orderList = new ArrayList<>();
                                roomMap.put("orderList", orderList);
                                for (DTOMeter dtoMeter : meterList) {
                                    List<FnRecordPrePay> recordList = fnRecordPrePayService.queryTenantPrePayRecord(
                                            tenant.getId(), EnumPayBodyType.METER, energyTypeId, dtoMeter.getMeterId(),
                                            from, to);
                                    if (recordList != null) {
                                        for (FnRecordPrePay fnRecordPrePay : recordList) {
                                            Map<String, Object> recordMap = new HashMap<>();
                                            String extend = dtoMeter.getExtend();
                                            Map<String, Object> extendMap = SystemConstant.jsonMapper.readValue(extend,
                                                    Map.class);
                                            recordMap.put("address", (String) extendMap.get("address"));
                                            recordMap.put("payTime", standard.format(fnRecordPrePay.getOperateTime()));
                                            recordMap.put("orderId", fnRecordPrePay.getOrderId());
                                            recordMap.put("money", fnRecordPrePay.getMoney());
                                            recordMap.put("amount", fnRecordPrePay.getAmount());
                                            recordMap.put("userName", fnRecordPrePay.getUserName());
                                            orderList.add(recordMap);
                                            // 账单充值量汇总
                                            amountSum += (fnRecordPrePay.getAmount() == null ? 0.0
                                                    : fnRecordPrePay.getAmount());
                                            tenantAmountSum += (fnRecordPrePay.getAmount() == null ? 0.0
                                                    : fnRecordPrePay.getAmount());
                                            moneySum += (fnRecordPrePay.getMoney() == null ? 0.0
                                                    : fnRecordPrePay.getMoney());
                                            tenantMoneySum += (fnRecordPrePay.getMoney() == null ? 0.0
                                                    : fnRecordPrePay.getMoney());
                                        }
                                        orderCount += recordList.size();// 充值记录汇总条数
                                        roomOrderCount += recordList.size();// 每个房间下充值记录条数
                                    }
                                }
                                roomMap.put("roomOrderCount", roomOrderCount);
                            }
                        }
                    }
                    tenantMap.put("orderCount", orderCount);
                    tenantMap.put("tenantMoneySum", tenantMoneySum);
                    tenantMap.put("tenantAmountSum", tenantAmountSum);
                }
            }

            Map<String, Object> contentObj = new HashMap<>();
            contentObj.put("energyUnit", energyUnit);
            contentObj.put("energyTypeName", energyTypeName);
            contentObj.put("amountSum", amountSum);
            contentObj.put("moneySum", moneySum);
            contentObj.put("tenantList", tenantObjList);

            if (isDownload.intValue() == EnumYesNo.NO.getValue().intValue()) {
                content.add(contentObj);
            } else {
                String subdirectory = pathUtil.getTempDownloadSubDir();

                String resouceId = UUID.randomUUID().toString();
                FileResource resource = new FileResource();
                resource.setId(resouceId);
                Date toTime = DateUtils.addDays(to, -1);
                timeTo = standard.format(toTime);
                if (timeType == EnumTimeType.T4.getValue().intValue()) {
                    timeFrom = timeFrom.substring(0, 7);
                    timeTo = timeTo.substring(0, 7);

                } else if (timeType == EnumTimeType.T5.getValue().intValue()) {
                    timeFrom = timeFrom.substring(0, 4);
                    timeTo = timeTo.substring(0, 4);
                } else {
                    timeFrom = timeFrom.substring(0, 10);
                    timeTo = timeTo.substring(0, 10);
                }
                StringBuffer fileNameSb = new StringBuffer();
                fileNameSb.append("充值记录-").append(EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId)).append("-")
                        .append(timeFrom).append("~").append(timeTo);

                resource.setName(fileNameSb.toString());
                resource.setSuffix("xls");
                resource.setSubdirectory(subdirectory);

                String path = pathUtil.getPath();

                String fileDir = Paths.get(path, subdirectory).toString();
                File file = new File(fileDir);
                if (!file.exists()) {
                    file.mkdirs();
                }
                this.buildExcel(path, subdirectory, resouceId, contentObj);

                fnFileResourceService.save(resource);
                Map<String, Object> newContentObj = new HashMap<>();
                newContentObj.put("id", resouceId);
                content.add(newContentObj);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTBatchPrePay2GridService");
        }
    }

    @SuppressWarnings({ "unchecked" })
    private void buildExcel(String path, String subPath, String fileName, Map<String, Object> contentObj) {
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        String energyUnit = (String) contentObj.get("energyUnit");
        String energyTypeName = (String) contentObj.get("energyTypeName");
        Double moneySum = (Double) contentObj.get("moneySum");
        Double amountSum = (Double) contentObj.get("amountSum");

        List<Map<String, Object>> tenantList = (List<Map<String, Object>>) contentObj.get("tenantList");

        // 创建标题
        HSSFRow row0 = sheet.createRow((short) 0);
        {
            HSSFCell cell = row0.createCell((short) 1);
            cell.setCellStyle(style);
            cell.setCellValue("租户编号");
        }
        {
            HSSFCell cell = row0.createCell((short) 0);
            cell.setCellStyle(style);
            cell.setCellValue("租户名称");
        }

        {
            HSSFCell cell = row0.createCell((short) 2);
            cell.setCellStyle(style);
            cell.setCellValue("房间编号");
        }
        {
            HSSFCell cell = row0.createCell((short) 3);
            cell.setCellStyle(style);
            cell.setCellValue("仪表地址");
        }
        {
            HSSFCell cell = row0.createCell((short) 4);
            cell.setCellStyle(style);
            cell.setCellValue("充值时间");
        }
        {
            HSSFCell cell = row0.createCell((short) 5);
            cell.setCellStyle(style);
            cell.setCellValue("充值单号");
        }
        {
            HSSFCell cell = row0.createCell((short) 6);
            cell.setCellStyle(style);
            cell.setCellValue("充值金额(元)");
        }
        {
            HSSFCell cell = row0.createCell((short) 7);
            cell.setCellStyle(style);
            cell.setCellValue("充值" + energyTypeName + "量(" + energyUnit + ")");
        }
        {
            HSSFCell cell = row0.createCell((short) 8);
            cell.setCellStyle(style);
            cell.setCellValue("操作人");
        }

        if (tenantList != null) {
            int rowCount = 1;
            for (int i = 0; i < tenantList.size(); i++) {
                Map<String, Object> tenant = (Map<String, Object>) tenantList.get(i);
                String tenantId = (String) tenant.get("tenantId");
                int orderCount = (int) tenant.get("orderCount");
                String tenantName = (String) tenant.get("tenantName");
                List<Object> roomList = (List<Object>) tenant.get("roomList");
                HSSFRow row = sheet.createRow((short) rowCount);
                {
                    HSSFCell cell = row.createCell((short) (0));
                    cell.setCellValue(tenantName == null ? " " : tenantName);
                }
                {
                    HSSFCell cell = row.createCell((short) (1));
                    cell.setCellValue(tenantId == null ? " " : tenantId);
                }
                if (roomList != null && roomList.size() > 0) {
                    if (orderCount > 0) {
                        sheet.addMergedRegion(new CellRangeAddress(rowCount, rowCount + orderCount - 1, 0, 0));
                        sheet.addMergedRegion(new CellRangeAddress(rowCount, rowCount + orderCount - 1, 1, 1));
                    }

                    for (int j = 0; j < roomList.size(); j++) {
                        if (j != 0) {
                            row = sheet.createRow((short) rowCount);
                        }
                        Map<String, Object> roomMap = (Map<String, Object>) roomList.get(j);
                        String roomCode = (String) roomMap.get("roomCode");
                        int roomOrderCount = (int) roomMap.get("roomOrderCount");
                        if (roomOrderCount > 0) {
                            sheet.addMergedRegion(new CellRangeAddress(rowCount, rowCount + roomOrderCount - 1, 2, 2));
                        }
                        List<Map<String, Object>> orderList = (List<Map<String, Object>>) roomMap.get("orderList");
                        if (orderList != null && orderList.size() != 0) {
                            for (int k = 0; k < orderList.size(); k++) {
                                if (k != 0) {
                                    row = sheet.createRow((short) rowCount);
                                }
                                Map<String, Object> map = orderList.get(k);
                                Double amount = DoubleFormatUtil.Instance().getDoubleData_00(map.get("amount"));
                                Double money = DoubleFormatUtil.Instance().getDoubleData_00(map.get("money"));
                                String userName = (String) map.get("userName");
                                String orderId = (String) map.get("orderId");
                                String payTime = (String) map.get("payTime");
                                String address = (String) map.get("address");

                                {
                                    HSSFCell cell = row.createCell((short) (2));
                                    cell.setCellValue(roomCode == null ? "--" : roomCode);
                                }
                                {
                                    HSSFCell cell = row.createCell((short) (3));
                                    cell.setCellValue(address == null ? "--" : address);
                                }
                                {
                                    HSSFCell cell = row.createCell((short) (4));
                                    cell.setCellValue(payTime == null ? "--" : payTime);
                                }
                                {
                                    HSSFCell cell = row.createCell((short) (5));
                                    cell.setCellValue(orderId == null ? "--" : orderId);
                                }
                                {
                                    HSSFCell cell = row.createCell((short) (6));
                                    cell.setCellValue(money == null ? 0: money);
                                }
                                {
                                    HSSFCell cell = row.createCell((short) (7));
                                    cell.setCellValue(amount == null ? 0: amount);
                                }
                                {
                                    HSSFCell cell = row.createCell((short) (8));
                                    cell.setCellValue(userName == null ? "--" : userName);
                                }
                                rowCount++;
                            }
                        } else {
                            {
                                HSSFCell cell = row.createCell((short) (2));
                                cell.setCellValue(roomCode == null ? "--" : roomCode);
                            }

                            {
                                HSSFCell cell = row.createCell((short) (3));
                                cell.setCellValue("--");
                            }
                            {
                                HSSFCell cell = row.createCell((short) (4));
                                cell.setCellValue("--");
                            }
                            {
                                HSSFCell cell = row.createCell((short) (5));
                                cell.setCellValue("--");
                            }
                            {
                                HSSFCell cell = row.createCell((short) (6));
                                cell.setCellValue("--");
                            }
                            {
                                HSSFCell cell = row.createCell((short) (7));
                                cell.setCellValue("--");
                            }
                            {
                                HSSFCell cell = row.createCell((short) (8));
                                cell.setCellValue("--");
                            }
                            rowCount++;
                        }
                        HSSFRow row2 = sheet.createRow((short) rowCount);
                        {
                            HSSFCell cell = row2.createCell((short) (0));
                            cell.setCellValue(tenantName+"合计充值");
                        }
                        {
                            HSSFCell cell = row2.createCell((short) (7));
                            cell.setCellValue((double) tenant.get("tenantAmountSum"));
                        }
                        {
                            HSSFCell cell = row2.createCell((short) (6));
                            cell.setCellValue((double) tenant.get("tenantMoneySum"));
                        }
                        rowCount++;
                    }
                }
                HSSFRow row3 = sheet.createRow((short) rowCount);
                {
                    HSSFCell cell = row3.createCell((short) (0));
                    cell.setCellValue("所选租户合计充值");
                }
                {
                    HSSFCell cell = row3.createCell((short) (7));
                    cell.setCellValue(amountSum);
                }
                {
                    HSSFCell cell = row3.createCell((short) (6));
                    cell.setCellValue(moneySum);
                }

            }

        }

        String newFilePath = Paths.get(path, subPath, fileName).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fout != null) {
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
    }
}
