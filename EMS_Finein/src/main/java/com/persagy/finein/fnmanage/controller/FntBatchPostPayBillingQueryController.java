package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnRecordPostClearingMeter;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantMeterData;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.ems.pojo.meterdata.MeterData;
import com.persagy.ems.pojo.originaldata.MonthData;
import com.persagy.finein.core.util.FunctionUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：批量操作-后付费账单结算-查询
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntBatchPostPayBillingQueryController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNTenantPriceService")
    private FNTenantPriceService fnTenantPriceService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNRecordPostClearingMeterService")
    private FNRecordPostClearingMeterService fnRecordPostClearingMeterService;

    @Resource(name = "FNProjectService")
    private FNProjectService fnProjectService;

    @Resource(name = "FNMeterDataService")
    private FNMeterDataService fnMeterDataService;

    @Resource(name = "FNRecordPostClearingPayService")
    private FNRecordPostClearingPayService fnRecordPostClearingPayService;

    @Resource(name = "FNTenantMeterDataService")
    private FNTenantMeterDataService fnTenantMeterDataService;

    @Resource(name = "FNOriginalDataService")
    private FNOriginalDataService fnOriginalDataService;

    @Resource(name = "FNTenantMeterComputeService")
    private FNTenantMeterComputeService fnTenantMeterComputeService;


    @RequestMapping("FNTBatchPostPayBillingQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchPostPayBillingQuery(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String) dto.get("energyTypeId");
            List<String> tenantIdList = (List<String>) dto.get("tenantList");
            String clearingTime = (String) dto.get("clearingTime");


            if (energyTypeId == null
                    || tenantIdList == null
                    || clearingTime == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Date clearingDate = standard.parse(clearingTime);

            Map<String, Building> buildingMap = fnBuildingService.query();

            String energyUnit = UnitUtil.getCumulantUnit(energyTypeId);
            Map<String, Object> contentMap = new HashMap<String, Object>();
            contentMap.put("energyUnit", energyUnit);
            contentMap.put("canClearing", 0);

            List<String> meterFaultList = new ArrayList<>();
            List<String> clearingTimeFaultList = new ArrayList<>();
            contentMap.put("meterFaultList", meterFaultList);
            contentMap.put("clearingTimeFaultList", clearingTimeFaultList);

            try {
                List<Object> tenantList = new ArrayList<>();
                List<Object> faultTenantList = new ArrayList<>();
                List<FnTenant> tenantEntityList = fnTenantService.queryListByIds(tenantIdList);
                if (tenantEntityList.size() > 0) {
                    Map<String, Map<String, Object>> tenantPriceMap = fnTenantPriceService.queryPriceMapByTenantIds(energyTypeId, tenantIdList);
                    Map<String, List<DTOMeter>> meterListMap = fnMeterService.queryMeterList(tenantIdList, energyTypeId);
                    Project project = fnProjectService.queryProject();

                    for (FnTenant fnTenant : tenantEntityList) {
                        try {
                            Map<String, Object> tenantObj = new HashMap<>();
                            tenantObj.put("tenantId", fnTenant.getId());
                            tenantObj.put("tenantName", fnTenant.getName());
                            tenantObj.put("roomIds", fnTenant.getRoomCodes());
                            tenantObj.put("buildingId", fnTenant.getBuildingId());
                            tenantObj.put("buildingName", buildingMap.containsKey(fnTenant.getBuildingId()) ? buildingMap.get(fnTenant.getBuildingId()).getName() : "");

                            //查询上次结算时间

                            Date tenantLastClearingDate = fnRecordPostClearingPayService.queryLastClearingDate(fnTenant.getBuildingId(), fnTenant.getId(), energyTypeId);

                            if (tenantLastClearingDate == null) {
                                tenantLastClearingDate = fnTenant.getActiveTime();
                            }
                            tenantObj.put("lastClearingTime", tenantLastClearingDate);
                            if (tenantLastClearingDate.getTime() >= clearingDate.getTime()) {
                                clearingTimeFaultList.add(fnTenant.getName());
                                continue;
                            }
                            //查询价格
                            Map<String, Double> priceMap = new HashMap<>();

                            Map<String, Object> price = tenantPriceMap.get(fnTenant.getId());
                            String c_id = (String) price.get("c_id");
                            Integer c_type = (Integer) price.get("c_type");
                            String c_name = (String) price.get("c_name");
                            String c_content = (String) price.get("c_content");

                            tenantObj.put("priceTemplateId", c_id);
                            tenantObj.put("priceTemplateType", c_type);
                            tenantObj.put("priceTemplateName", c_name);
                            if (c_type.intValue() == EnumPriceType.AVG.getValue()) {
                                Double priceData = DoubleFormatUtil.Instance().getDoubleData(c_content);
                                tenantObj.put("priceTemplateContent", priceData);
                                priceMap.put("L", priceData);
                            } else {
                                List<Object> priceTemplateContent = new ArrayList<>();
                                tenantObj.put("priceTemplateContent", priceTemplateContent);
                                JSONArray contentArray = (JSONArray) JSONValue.parse(c_content);
                                for (int i = 0; i < contentArray.size(); i++) {
                                    JSONObject priceContentObj = (JSONObject) contentArray.get(i);
                                    Map<String, Object> priceContentMap = new HashMap<>();
                                    priceContentMap.put("type", priceContentObj.get("type"));
                                    Double priceData = DoubleFormatUtil.Instance().getDoubleData(priceContentObj.get("value"));
                                    priceContentMap.put("value", priceData);
                                    priceTemplateContent.add(priceContentMap);
                                    priceMap.put((String) priceContentObj.get("type"), priceData);
                                }
                            }

                            List<DTOMeter> meterEntityList = meterListMap.get(fnTenant.getId());
                            tenantObj.put("currentBillingEnergy", null);
                            tenantObj.put("currentBillingMoney", null);
                            {//仪表
                                List<Object> meterList = new ArrayList<>();
                                tenantObj.put("meterList", meterList);

                                if (meterEntityList != null) {
                                    boolean isException = false;
                                    for (DTOMeter meter : meterEntityList) {
                                        Map<String, Object> meterMap = new HashMap<>();
                                        meterMap.put("meterId", meter.getMeterId());
                                        meterMap.put("meterType", meter.getMeterType());

                                        List<Object> functionList = new ArrayList<>();
                                        meterMap.put("functionList", functionList);
                                        meterList.add(meterMap);
                                        FnRecordPostClearingMeter record = fnRecordPostClearingMeterService.queryLastClearingMeter(fnTenant.getBuildingId(), fnTenant.getId(), energyTypeId, meter.getMeterId());

                                        if (meter.getMeterType().intValue() == EnumMeterType.Common.getValue().intValue()) {//累计量
                                            Integer functionId = FunctionTypeUtil.getCumulantFunctionId(energyTypeId);
                                            if (FunctionUtil.queryFunctionIdExsit(meter.getProtocolId(), energyTypeId, functionId)) {
                                                if (!this.processPostPayFunction(project, fnTenant, meter, functionId, energyTypeId, meterMap, functionList, clearingDate, priceMap, EnumPriceDetail.L.getValue(), record, tenantObj, meterFaultList)) {
                                                    isException = true;
                                                    break;
                                                }
                                            } else {
                                                throw new Exception("协议必须包含此功能号:" + meter.getProtocolId());
                                            }
                                        } else {
                                            Map<String, Integer> functionIdMap = FunctionTypeUtil.getCumulantDianMultiple();
                                            for (Map.Entry<String, Integer> functionEntry : functionIdMap.entrySet()) {
                                                if (FunctionUtil.queryFunctionIdExsit(meter.getProtocolId(), energyTypeId, functionEntry.getValue())) {
                                                    if (!this.processPostPayFunction(project, fnTenant, meter, functionEntry.getValue(), energyTypeId, meterMap, functionList, clearingDate, priceMap, functionEntry.getKey(), record, tenantObj, meterFaultList)) {
                                                        isException = true;
                                                        break;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    if (isException) {
                                        faultTenantList.add(tenantObj);
                                        continue;
                                    }
                                } else {
                                    meterFaultList.add(fnTenant.getName() + "（未找到仪表）");
                                    continue;
                                }
                            }
                            tenantList.add(tenantObj);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
                if (tenantList.size() > 0) {
                    contentMap.put("canClearing", 1);
                    contentMap.put("tenantList", tenantList);
                } else {
                    contentMap.put("canClearing", 0);
                    contentMap.put("reason", "未找到可结算租户");
                    contentMap.put("tenantList", faultTenantList);
                }
            } catch (Exception e) {
                e.printStackTrace();
                contentMap.put("canClearing", 0);
                contentMap.put("reason", "查询异常");
            }
            content.add(contentMap);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTBatchPostPayBillingQueryService");
        }
    }

    private boolean processPostPayFunction(Project project, FnTenant fnTenant, DTOMeter meter, int functionId, String energyTypeId, Map<String, Object> meterMap, List<Object> functionList, Date leaveDate, Map<String, Double> priceMap, String type, FnRecordPostClearingMeter record, Map<String, Object> tenantObj, List<String> meterFaultList) throws Exception {
        Double lastBillingData = null;
        Double currentBillingData = null;
        Date lastClearingTime = null;
        Double diffData = null;
        Double money = null;
        if (record == null) {
            //查询激活时间，查询激活时间点的读数
            MeterData meterData = fnMeterDataService.queryMeterDataEqual(project.getId(), meter.getMeterId(), functionId, EnumTimeType.T2.getValue(), fnTenant.getActiveTime());
            if (meterData != null) {
                Double radio = 1.0;
                try {
                    JSONObject extendObj = (JSONObject) JSONValue.parse(meter.getExtend());
                    Long ljlIsCt = (Long) extendObj.get("ljlIsCt");
                    if (ljlIsCt != null && ljlIsCt.intValue() == 1) {
                        radio = meter.getRadio();
                    }
                } catch (Exception e) {
                }

                lastClearingTime = fnTenant.getActiveTime();
                lastBillingData = meterData.getData() * radio;
            } else {
                lastClearingTime = fnTenant.getActiveTime();
                //查找前后原始读数
                Date from = new Date(lastClearingTime.getTime() - FineinConstant.Time.Minute_15 * 2);
                Date to = new Date(lastClearingTime.getTime() + FineinConstant.Time.Minute_15 * 2);
                MonthData monthData = fnOriginalDataService.queryLastMonthDataGteLte(project.getId(), meter.getMeterId(), functionId, from, to);
                if (monthData != null && monthData.getData() != null) {
                    Double ct = 1.0;
                    JSONObject extendObj = null;
                    try {
                        extendObj = (JSONObject) JSONValue.parse(meter.getExtend());
                        Double tempCt = DoubleFormatUtil.Instance().getDoubleData(extendObj.get("ljlIsCt"));
                        if (tempCt != null && tempCt.intValue() == 1) {
                            ct = meter.getRadio();
                        }
                    } catch (Exception e) {
                    }
                    lastBillingData = monthData.getData() * ct;
                } else {
                    meterFaultList.add(fnTenant.getName() + "（" + meter.getMeterId() + "）");
                    return false;
                }
            }
        } else {
            lastClearingTime = record.getCurrentClearingTime();
            try {
                JSONObject extendObj = (JSONObject) JSONValue.parse(record.getExtend());
                JSONArray fList = (JSONArray) extendObj.get("functionList");
                JSONObject functionObj = null;
                for (int i = 0; i < fList.size(); i++) {
                    functionObj = (JSONObject) fList.get(i);
                    if (type.equals(functionObj.get("type"))) {
                        lastBillingData = DoubleFormatUtil.Instance().getDoubleData(functionObj.get("currentData"));
                        break;
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (lastClearingTime == null) {
            meterFaultList.add(fnTenant.getName() + "（" + meter.getMeterId() + "）");
            return false;
        }
        if (lastBillingData == null) {
            meterFaultList.add(fnTenant.getName() + "（" + meter.getMeterId() + "）");
            return false;
        }
        {
            MeterData meterData = fnMeterDataService.queryMeterDataEqual(project.getId(), meter.getMeterId(), functionId, EnumTimeType.T2.getValue(), leaveDate);
            if (meterData != null) {
                Double radio = 1.0;
                try {
                    JSONObject extendObj = (JSONObject) JSONValue.parse(meter.getExtend());
                    Long ljlIsCt = (Long) extendObj.get("ljlIsCt");
                    if (ljlIsCt != null && ljlIsCt.intValue() == 1) {
                        radio = meter.getRadio();
                    }
                } catch (Exception e) {
                }
                currentBillingData = meterData.getData() * radio;
            } else {
                Date from = new Date(leaveDate.getTime() - FineinConstant.Time.Minute_15 * 2);
                Date to = new Date(leaveDate.getTime() + FineinConstant.Time.Minute_15 * 2);
                MonthData monthData = fnOriginalDataService.queryLastMonthDataGteLte(project.getId(), meter.getMeterId(), functionId, from, to);
                if (monthData != null && monthData.getData() != null) {
                    Double ct = 1.0;
                    JSONObject extendObj = null;
                    try {
                        extendObj = (JSONObject) JSONValue.parse(meter.getExtend());
                        Double tempCt = DoubleFormatUtil.Instance().getDoubleData(extendObj.get("ljlIsCt"));
                        if (tempCt != null && tempCt.intValue() == 1) {
                            ct = meter.getRadio();
                        }
                    } catch (Exception e) {
                    }
                    currentBillingData = monthData.getData() * ct;
                } else {
                    meterFaultList.add(fnTenant.getName() + "（" + meter.getMeterId() + "）");
                    return false;
                }
            }
        }
        Date lastComputeDate = fnTenantMeterComputeService.queryLastComputeTime(fnTenant.getBuildingId(), fnTenant.getId(), meter.getMeterId(), functionId);
        if (lastComputeDate == null || lastComputeDate.getTime() <= leaveDate.getTime()) {
            meterFaultList.add(fnTenant.getName() + "（" + meter.getMeterId() + "）");
            return false;
        }

        //查询能耗
        {
            List<FnTenantMeterData> dataList = fnTenantMeterDataService.queryListGteLt(fnTenant.getBuildingId(), fnTenant.getId(), meter.getMeterId(), functionId, energyTypeId, EnumTimeType.T2, lastClearingTime, leaveDate, EnumEnergyMoney.Energy);
            if (dataList != null) {
                for (FnTenantMeterData meterData : dataList) {
                    if (meterData.getData() != null) {
                        diffData = diffData == null ? meterData.getData() : diffData + meterData.getData();
                    }
                }
            }
        }
        //查询费用
        {
            List<FnTenantMeterData> dataList = fnTenantMeterDataService.queryListGteLt(fnTenant.getBuildingId(), fnTenant.getId(), meter.getMeterId(), functionId, energyTypeId, EnumTimeType.T2, lastClearingTime, leaveDate, EnumEnergyMoney.Money);
            if (dataList != null) {
                for (FnTenantMeterData meterData : dataList) {
                    if (meterData.getData() != null) {
                        money = money == null ? meterData.getData() : money + meterData.getData();
                    }
                }
            }
        }

        Map<String, Object> functionMap = new HashMap<>();
        functionMap.put("type", type);
        functionMap.put("functionId", functionId);
        functionMap.put("lastClearingData", lastBillingData);
        functionMap.put("currentClearingData", currentBillingData);
        if (diffData != null) {
            if (tenantObj.get("currentBillingEnergy") == null) {
                tenantObj.put("currentBillingEnergy", diffData);
            } else {
                tenantObj.put("currentBillingEnergy", DoubleFormatUtil.Instance().getDoubleData(tenantObj.get("currentBillingEnergy")) + diffData);
            }
        }
        functionMap.put("diffData", diffData);
        functionMap.put("money", money);
        if (money != null) {
            if (tenantObj.get("currentBillingMoney") == null) {
                tenantObj.put("currentBillingMoney", money);
            } else {
                tenantObj.put("currentBillingMoney", DoubleFormatUtil.Instance().getDoubleData(tenantObj.get("currentBillingMoney")) + money);
            }
        }
        functionList.add(functionMap);
        meterMap.put("lastClearingTime", lastClearingTime);
        return true;
    }


}
