package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.EnumMeterType;
import com.persagy.finein.enumeration.EnumPriceType;
import com.persagy.finein.service.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：批量操作-批量修改价格方案-保存
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntBatchPriceUpdateController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantEnergySplitService")
    private FNTenantEnergySplitService fnTenantEnergySplitService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNTenantPriceService")
    private FNTenantPriceService fnTenantPriceService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNPriceTemplateService")
    private FNPriceTemplateService fnPriceTemplateService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @Resource(name = "FNRecordPriceChangeService")
    private FNRecordPriceChangeService fnRecordPriceChangeService;


    @RequestMapping("FNTBatchPriceUpdateService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchPriceUpdate(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String)dto.get("energyTypeId");
            String priceTempldateId = (String)dto.get("priceTempldateId");
            String userId = this.getParamUserId(dto);

            if(dto.get("tenantList") == null || energyTypeId == null
                    || priceTempldateId == null || userId == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            List<String> tenantList = (List<String>)dto.get("tenantList");

            Date now = new Date();

            Map<String,Object> contentObj = new HashMap<>();
            contentObj.put("result", 0);
            content.add(contentObj);

            try {
                FnPriceTemplate newPriceTemplate = fnPriceTemplateService.query(priceTempldateId);
                if(newPriceTemplate.getType().intValue() == EnumPriceType.TOU.getValue()){//如果为分时，则需要验证租户下的仪表是否均为多费率类型
                    //如果租户用的拆分公式，则不允许分时价格
                    Map<String, FnTenantEnergySplit> splitMap = fnTenantEnergySplitService.queryEnergySplit(tenantList, energyTypeId);

                    Map<String,List<DTOMeter>> tenantMeterListMap = fnMeterService.queryMeterList(tenantList,energyTypeId);
                    for(String tenantId : tenantList){
                        if(splitMap.containsKey(tenantId)){
                            contentObj.put("result", 1);
                            contentObj.put("reason", "租户【"+tenantId+"】为能耗拆分模式，不可以设置分时价格");
                            return Result.SUCCESS(content);
                        }

                        List<DTOMeter> meterList = tenantMeterListMap.get(tenantId);
                        if(meterList == null || meterList.size() == 0){
                            continue;
                        }
                        for(DTOMeter meter : meterList){
                            if(meter.getMeterType().intValue() == EnumMeterType.Common.getValue()){
                                contentObj.put("result", 1);
                                contentObj.put("reason", "租户【"+tenantId+"】包含非多费率的仪表【"+meter.getMeterId()+"】");
                                return Result.SUCCESS(content);
                            }
                        }
                    }
                }

                //查询租户仪表类型

                List<FnTenant> fnTenantList = fnTenantService.queryListByIds(tenantList);
                List<FnRecordPriceChange> priceChangeList = new ArrayList<FnRecordPriceChange>();

                if(fnTenantList != null && fnTenantList.size() > 0){

                    String userName = null;

                    Map<String, Building> buildingMap = fnBuildingService.query();

                    if(userName == null){
                        try {
                            userName = fnUserService.queryUserByUserId(userId).getUserName();
                        } catch (Exception e) {
                        }
                    }

                    Map<String,Map<String,Object>> tenantPriceMapMap = fnTenantPriceService.queryPriceMapByTenantIds(energyTypeId, tenantList);

                    for(FnTenant fnTenant : fnTenantList){
                        FnRecordPriceChange recordPriceChange = new FnRecordPriceChange();
                        recordPriceChange.setId(UUID.randomUUID().toString());
                        recordPriceChange.setBuildingId(fnTenant.getBuildingId());
                        String oldPriceId = null;
                        String oldPriceName = null;
                        Map<String,Object> tenantPriceMap = tenantPriceMapMap.get(fnTenant.getId());
                        if(tenantPriceMap != null){
                            oldPriceId = (String)tenantPriceMap.get("c_price_template_id");
                            oldPriceName = (String)tenantPriceMap.get("c_name");
                        }
                        recordPriceChange.setBuildingName(buildingMap.get(fnTenant.getBuildingId()) == null ? "" : buildingMap.get(fnTenant.getBuildingId()).getName());
                        recordPriceChange.setTenantId(fnTenant.getId());
                        recordPriceChange.setEnergyTypeId(energyTypeId);
                        recordPriceChange.setTenantName(fnTenant.getName());
                        recordPriceChange.setChangeTime(now);
                        recordPriceChange.setBeforePriceId(oldPriceId);
                        recordPriceChange.setBeforePriceName(oldPriceName);

                        recordPriceChange.setAfterPriceId(newPriceTemplate.getId());
                        recordPriceChange.setAfterPriceName(newPriceTemplate.getName());
                        recordPriceChange.setUserId(userId);

                        recordPriceChange.setUserName(userName);
                        recordPriceChange.setRoomIds(fnTenant.getRoomCodes());
                        recordPriceChange.setCreateTime(now);
                        priceChangeList.add(recordPriceChange);
                    }
                }

                if(priceChangeList.size() > 0){
                    fnRecordPriceChangeService.save(priceChangeList);
                }

                {//更新租户价格
                    FnTenantPrice query = new FnTenantPrice();
                    query.setEnergyTypeId(energyTypeId);
                    query.setSpecialOperation("tenantId", SpecialOperator.$in, tenantList);

                    FnTenantPrice update = new FnTenantPrice();
                    update.setPriceTemplateId(priceTempldateId);

                    update.setLastUpdateTime(now);
                    update.setLastUpdateUserId(userId);
                    fnTenantPriceService.update(query, update);
                }
            } catch (Exception e) {
                e.printStackTrace();
                contentObj.put("result", 1);
                contentObj.put("reason", "批量修改价格方案异常");
                return Result.SUCCESS(content);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTBatchPriceUpdateService");
        }
    }


}
