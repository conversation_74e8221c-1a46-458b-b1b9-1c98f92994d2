package com.persagy.finein.fnmanage.quartz.handler.impl;

import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.*;
import com.persagy.finein.fnmanage.quartz.TimingBatchUpdatePrice;
import com.persagy.finein.fnmanage.quartz.handler.FNQuartzProcessJob;
import com.persagy.finein.service.*;
import com.persagy.web.controller.entrance.EntranceController;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Author: ls
 * @Date: 2022/2/14 13:27
 */
@Service("FNQuartzTimingUpdatePriceHandler")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNQuartzTimingUpdatePriceHandlerImpl implements FNQuartzProcessJob {

    private static Logger log = Logger.getLogger(FNQuartzTimingUpdatePriceHandlerImpl.class);

    @Resource(name = "FNRecordScheduleJobService")
    private FNRecordScheduleJobService FNRecordScheduleJobService;

    @Resource(name = "TimingBatchUpdatePrice")
    private TimingBatchUpdatePrice timingBatchUpdatePrice;

    private final String JOB_Name = "定时批量修改电价";
    private final String JOB_Group = "FNQuartzTimingUpdatePriceHandler";
    protected final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Override
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void handle() throws Exception {
        Properties properties = new Properties();
        properties.load(EntranceController.class.getResourceAsStream("/config/system.properties"));
        boolean flag = Boolean.parseBoolean(properties.getProperty("updatePrice.isOpen"));
        if (flag) {
            FnRecordScheduleJob queryR = new FnRecordScheduleJob();
            queryR.setJobGroup(JOB_Group);
            queryR.setJobName(JOB_Name);
            Date date = new Date();
            queryR.setSpecialOperation("executeTime", SpecialOperator.$gte, standard.parse(getMonthStartTimeStr()));
            queryR.setSpecialOperation("executeTime", SpecialOperator.$lt, standard.parse(getMonthEndTimeStr()));
            List<FnRecordScheduleJob> recordList = FNRecordScheduleJobService.query(queryR);
            Date day = DateUtils.truncate(date, Calendar.DATE);
            queryR.setExecuteTime(day);
            if (recordList != null && recordList.size() > 0) {
                FnRecordScheduleJob recordScheduleJob = recordList.get(0);
                Integer times = recordScheduleJob.getTimes();
                if (times == 1) {
                    log.info("当月定时修改电价已执行一次#########################");
                    return;
                }
            } else {
                queryR.setId(UUID.randomUUID().toString());
                queryR.setTimes(1);
                queryR.setUpdateTime(new Date());
                FNRecordScheduleJobService.save(queryR);
            }
            log.info("开始执行定时修改电价任务######################################");
            //执行
            timingBatchUpdatePrice.updatePrice();
        }
    }


    /**
     * 获取本月开始的时间的字符串
     *
     * @return
     */
    public static String getMonthStartTimeStr() {
        Calendar cal = Calendar.getInstance();
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
        SimpleDateFormat startTime = new SimpleDateFormat("yyyy-MM-dd 00:00:00");
        return startTime.format(cal.getTime());
    }

    /**
     * 获取本月结束的时间的字符串
     *
     * @return
     */
    public static String getMonthEndTimeStr() {
        Calendar cal = Calendar.getInstance();
        cal.set(cal.get(Calendar.YEAR), cal.get(Calendar.MONTH), cal.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMinimum(Calendar.DAY_OF_MONTH));
        cal.set(Calendar.DAY_OF_MONTH, cal.getActualMaximum(Calendar.DAY_OF_MONTH));
        SimpleDateFormat endTime = new SimpleDateFormat("yyyy-MM-dd 23:59:59");
        return endTime.format(cal.getTime());
    }
}
