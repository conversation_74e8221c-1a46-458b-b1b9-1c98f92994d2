package com.persagy.finein.fnmanage.compute.handler;

import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.pojo.finein.dictionary.Building;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月22日 下午12:58:25
 * 
 * 说明:租户总功率计算线程
 */
public interface FNTComputeMeterPowerHandler {
	public void processMeter(Building building, DTOMeter meter, Integer delaySecond) throws Exception;

	public void processBuilding(Building building, Integer delaySecond) throws Exception;
}
