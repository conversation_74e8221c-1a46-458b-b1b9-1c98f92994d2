package com.persagy.finein.fnmanage.compute.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.finein.common.util.TimeDataUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.ems.pojo.servicedata.ServiceData;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.util.ExpressionUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.alarm.handler.FNTAlarmProcessBuildingHandler;
import com.persagy.finein.fnmanage.compute.handler.FNTComputeTenantHandler;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.wltea.expression.ExpressionEvaluator;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月22日 下午12:58:25
 * 
 * 说明:租户计算线程
 */
@Component("FNTComputeTenantHandler")
public class FNTComputeTenantHandlerImpl extends CoreServiceImpl implements FNTComputeTenantHandler {

	private static Logger log = Logger.getLogger(FNTComputeTenantHandlerImpl.class);

	@Resource(name = "FNBuildingService")
	public FNBuildingService FNBuildingService;

	@Resource(name = "FNTAlarmProcessBuildingHandler")
	public FNTAlarmProcessBuildingHandler FNTAlarmProcessBuildingHandler;

	@Resource(name = "FNTenantService")
	public FNTenantService FNTenantService;

	@Resource(name = "FNMeterService")
	public FNMeterService FNMeterService;

	@Resource(name = "FNTenantPayTypeService")
	public FNTenantPayTypeService FNTenantPayTypeService;

	@Resource(name = "FNRecordPrePayService")
	public FNRecordPrePayService FNRecordPrePayService;

	@Resource(name = "FNMeterDataService")
	public FNMeterDataService FNMeterDataService;

	@Resource(name = "FNProjectService")
	public FNProjectService FNProjectService;

	@Resource(name = "FNTenantMeterComputeService")
	public FNTenantMeterComputeService FNTenantMeterComputeService;

	@Resource(name = "FNTenantMeterComputeTempService")
	public FNTenantMeterComputeTempService FNTenantMeterComputeTempService;


	@Resource(name = "FNTenantEnergySplitComputeService")
	public FNTenantEnergySplitComputeService FNTenantEnergySplitComputeService;

	@Resource(name = "FNServiceDataService")
	public FNServiceDataService FNServiceDataService;

	@Resource(name = "FNTenantMeterDataService")
	public FNTenantMeterDataService FNTenantMeterDataService;

	@Resource(name = "FNTenantDataService")
	public FNTenantDataService FNTenantDataService;

	@Resource(name = "FNTenantPriceService")
	public FNTenantPriceService FNTenantPriceService;

	@Resource(name = "FNTenantBackPayDataService")
	public FNTenantBackPayDataService FNTenantBackPayDataService;

	@Resource(name = "FNTenantBackPayRecordService")
	public FNTenantBackPayRecordService FNTenantBackPayRecordService;

	@Resource(name = "FNTenantEnergySplitService")
	public FNTenantEnergySplitService FNTenantEnergySplitService;


	@Resource(name = "ExpressionUtil")
	private ExpressionUtil ExpressionUtil;

	public static final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	public static boolean IsHistoryDataInited = false;

	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public void process() {
		try {
			// 1.查询所有建筑
			List<Building> buildingList = FNBuildingService.queryList(new Building());
			if (buildingList != null && buildingList.size() > 0) {
				Project project = FNProjectService.queryProject();
				if (project == null) {
					return;
				}
				for (Building building : buildingList) {
					try {
						this.processBuilding(project, building);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@SuppressWarnings("deprecation")
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void processBuilding(Project project, Building building) throws Exception {
		// 处理换表记录
		List<FnTenantMeterComputeTemp> tempList = FNTenantMeterComputeTempService.queryByBuilding(building.getId());
		if (tempList != null && tempList.size() > 0) {
			for (FnTenantMeterComputeTemp temp : tempList) {
				{// metercomputetime
					Date meterLastComputeTime = FNMeterDataService.queryMeterComputeTime(project.getId(),
							temp.getMeterId(), temp.getFunctionId());
					Date timeFrom = DateUtils.add(temp.getLastComputeTime(), Calendar.DATE, 1);
					if (meterLastComputeTime != null && meterLastComputeTime.getTime() > timeFrom.getTime()) {
						EnumTimeType[] timeTypeArray = { EnumTimeType.T0, EnumTimeType.T1, EnumTimeType.T2 };
						for (EnumTimeType timeType : timeTypeArray) {
							// 删除meterdata
							FNMeterDataService.removeMeterDataGte(project.getId(), temp.getMeterId(),
									temp.getFunctionId(), timeType.getValue(), timeFrom);
							// 删除servicedata
							FNServiceDataService.removeServiceDataGte(project.getId(), temp.getMeterId(),
									temp.getFunctionId(), timeType.getValue(), timeFrom);
						}
						FNMeterDataService.saveMeterComputeTime(project.getId(), temp.getMeterId(),
								temp.getFunctionId(), timeFrom);
					}
				}
				{
					Date lastComputeTime = FNTenantMeterComputeService.queryLastComputeTime(temp.getBuildingId(),
							temp.getTenantId(), temp.getMeterId(), temp.getFunctionId());
					if (lastComputeTime != null && lastComputeTime.getTime() > temp.getLastComputeTime().getTime()) {
						// 删除仪表数据
						FnMeter meter = FNMeterService.queryMeterById(temp.getMeterId());
						if (meter == null) {
							continue;
						}
						FnTenant fnTenant = FNTenantService.queryOne(temp.getTenantId());
						if (fnTenant == null) {
							continue;
						}

						int funcId = FunctionTypeUtil.getCumulantFunctionId(meter.getEnergyTypeId());
						if (funcId != temp.getFunctionId()) {
							FNTenantMeterDataService.removeData(fnTenant.getBuildingId(), temp.getTenantId(),
									temp.getMeterId(), funcId, temp.getLastComputeTime());
						}
						FNTenantMeterDataService.removeData(fnTenant.getBuildingId(), temp.getTenantId(),
								temp.getMeterId(), temp.getFunctionId(), temp.getLastComputeTime());
						// 删除租户数据
						FNTenantDataService.removeData(fnTenant.getBuildingId(), temp.getTenantId(),
								meter.getEnergyTypeId(), temp.getLastComputeTime());
						// 删除软件充软件扣数据
						FNTenantBackPayDataService.removeData(fnTenant.getBuildingId(), temp.getTenantId(),
								meter.getEnergyTypeId(), temp.getLastComputeTime());
						// 修改
						FNTenantMeterComputeService.saveComputeTime(fnTenant.getBuildingId(), temp.getTenantId(),
								temp.getMeterId(), temp.getFunctionId(), temp.getLastComputeTime());
						FNTenantMeterComputeTempService.removeComputeTime(temp.getBuildingId(), temp.getTenantId(),
								temp.getMeterId(), temp.getFunctionId());
					} else {
						// 直接删除
						FNTenantMeterComputeTempService.removeComputeTime(temp.getBuildingId(), temp.getTenantId(),
								temp.getMeterId(), temp.getFunctionId());
					}
				}
			}
		}
		Map<String, Map<String, Double>> buildingDataMap = new HashMap<String, Map<String, Double>>();
		// 查询建筑下所有租户
		List<FnTenant> tenantList = FNTenantService.queryListByValidStatus(building.getId(), EnumValidStatus.VALID);
		if (tenantList != null) {
			long outStart = System.currentTimeMillis();
			for (int i = 0; i < tenantList.size(); i++) {
				if (tenantList.get(i).getStatus().intValue() == EnumTenantStatus.ACTIVATED.getValue().intValue()
						|| tenantList.get(i).getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue()
								.intValue()) {
					// long start = System.currentTimeMillis();
					try {
						this.processTenant(project, building, tenantList.get(i), buildingDataMap);
					} catch (Exception e) {
						e.printStackTrace();
					}
					// long end = System.currentTimeMillis();
					// System.out.println("处理租户("+(i+1)+"):"+tenantList.get(i).getId()+"----"+((end-start)));
				}
			}
			long outEnd = System.currentTimeMillis();
			System.out.println("计算所有租户用时:" + "----" + ((outEnd - outStart)));
			if (tenantList.size() > 0) {
				System.out.println("计算所有租户平均用时:" + "----" + ((outEnd - outStart) / tenantList.size()));
			}
		}
	}

	public void processTenant(Project project, Building building, FnTenant tenant,
			Map<String, Map<String, Double>> buildingDataMap) throws Exception {
		// if (tenant.getId().equals("ZHBH_1013")) {
		// System.out.println("------------");
		// }
		Map<String, FnTenantPayType> payTypeMap = FNTenantPayTypeService.queryPayTypeMap(tenant.getId());
		for (Map.Entry<String, FnTenantPayType> entry : payTypeMap.entrySet()) {
			FnPriceTemplate priceTemplate = FNTenantPriceService.query(tenant.getId(), entry.getKey());
			// 租户该能耗类型是否根据拆分计算
			FnTenantEnergySplit energySplit = FNTenantEnergySplitService.queryEnergySplit(tenant.getId(),
					entry.getKey());
			if (energySplit == null || energySplit.getExpression() == null
					|| "".equals(energySplit.getExpression().trim())) {
				if (entry.getValue().getPrePayType().intValue() == EnumPrePayType.ONLINE_TENANTPAY.getValue()
						.intValue()) {
					// long start = System.currentTimeMillis();
					processTenantBackPayRecord(project, building, tenant, entry.getKey(),
							entry.getValue().getPrepayChargeType());
					// long end = System.currentTimeMillis();
					// System.out.println("----------------处理充值记录时间:" + (end -
					// start));
				}
				// long start = System.currentTimeMillis();
				this.processTenantMeter(project, building, tenant, entry.getKey(), entry.getValue(), priceTemplate,
						buildingDataMap);
				// long end = System.currentTimeMillis();
				// System.out.println("----------------处理租户仪表时间:" + (end -
				// start));
			} else {
				this.processTenantExpression(project, building, tenant, entry.getKey(), entry.getValue(), priceTemplate,
						energySplit.getExpression(), buildingDataMap);
			}
		}
	}

	// 拆分公式计算
	public void processTenantExpression(Project project, Building building, FnTenant tenant, String energyTypeId,
			FnTenantPayType tenantPayType, FnPriceTemplate priceTemplate, String expression,
			Map<String, Map<String, Double>> buildingDataMap) throws Exception {
		if (priceTemplate.getType().intValue() == EnumPriceType.TOU.getValue()) {// 分时价格
			log.error("租户【" + tenant.getName() + "】" + energyTypeId + "引用了分时价格:" + priceTemplate.getName());
			return;
		}

		Map<String, String> meterFunctionMap = ExpressionUtil.getSubExpressionMap(expression);
		if (meterFunctionMap == null || meterFunctionMap.size() == 0) {
			return;
		}
		Date timeFrom = null;
		Date timeTo = null;

		for (Map.Entry<String, String> entry : meterFunctionMap.entrySet()) {
			String[] array = entry.getKey().split("\\.");
			String meterId = array[0].trim();
			Integer functionId = Integer.parseInt(array[1].trim());
			if (meterId == null || functionId == null) {
				log.error("拆分公式解析表号功能号为空【" + expression + "】");
				return;
			}

			Date lastComputeTime = FNTenantEnergySplitComputeService.queryLastComputeTime(building.getId(),
					tenant.getId(), energyTypeId);

			if (lastComputeTime == null) {// 从激活时间开始计算
				lastComputeTime = tenant.getActiveTime();
				FNTenantEnergySplitComputeService.saveComputeTime(building.getId(), tenant.getId(), energyTypeId,
						lastComputeTime);
			}

			Date meterLastComputeTime = FNMeterDataService.queryMeterComputeTime(project.getId(), meterId, functionId);

			if (meterLastComputeTime == null) {
				return;
			}

			if (tenant.getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue()) {// 如果是已退租
				if (tenant.getLeaveTime() == null) {
					return;
				}
				if (lastComputeTime.getTime() > tenant.getLeaveTime().getTime() + FineinConstant.Time.Day_1) {// 上次计算时间大于退租后一天，不计算
					return;
				}
			}
			if (lastComputeTime.getTime() < meterLastComputeTime.getTime()) {
				Date innerTimeFrom = DateUtils.addMinutes(lastComputeTime, -15);
				Date innerTimeTo = meterLastComputeTime;
				if (timeFrom == null || timeFrom.getTime() > innerTimeFrom.getTime()) {
					timeFrom = innerTimeFrom;
				}
				if (timeTo == null || timeTo.getTime() > innerTimeTo.getTime()) {
					timeTo = innerTimeTo;
				}
			} else if (lastComputeTime.getTime() == meterLastComputeTime.getTime()) {// 等一轮
			} else {// 计算程序重算了，不操作
			}
		}

		if (timeFrom == null || timeTo == null) {
			return;
		}

		Map<String, Double> outEnergyDataMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.T0);

		Map<String, Map<String, Double>> meterDataMap = new HashMap<>();

		for (Map.Entry<String, String> entry : meterFunctionMap.entrySet()) {
			String[] array = entry.getKey().split("\\.");
			String meterId = array[0].trim();
			Integer functionId = Integer.parseInt(array[1].trim());
			Map<String, Double> energyDataMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.T0);

			// 处理仪表能耗
			List<ServiceData> dataList = FNServiceDataService.queryServiceDataGteLt(project.getId(), meterId,
					functionId, EnumTimeType.T0, timeFrom, timeTo);
			if (dataList != null && dataList.size() > 0) {
				for (ServiceData serviceData : dataList) {
					String key = standard.format(serviceData.getTimefrom());
					if (serviceData.getData() != null) {
						if (energyDataMap.containsKey(key)) {
							energyDataMap.put(key, serviceData.getData());
						}
					}
				}
			}
			meterDataMap.put(entry.getKey(), energyDataMap);
		}
		Double price = DoubleFormatUtil.Instance().getDoubleData(priceTemplate.getContent());
		// 能耗
		for (Map.Entry<String, Double> entry : outEnergyDataMap.entrySet()) {// 计算能耗
			String newExpression = expression.replace("[", "#").replace("]", "#");
			for (Map.Entry<String, String> expressionEntry : meterFunctionMap.entrySet()) {
				Double data = null;
				try {
					data = meterDataMap.get(expressionEntry.getKey()).get(entry.getKey());
				} catch (Exception e) {
				}
				newExpression = newExpression.replace(expressionEntry.getValue(), data + "");
			}
			Double data = null;
			try {
				data = DoubleFormatUtil.Instance().getDoubleData(ExpressionEvaluator.evaluate(newExpression));
			} catch (Exception e) {
			}

			FNTenantDataService.saveData(building.getId(), tenant.getId(), EnumTimeType.T0, energyTypeId,
					standard.parse(entry.getKey()), EnumEnergyMoney.Energy, data);

			// 计算费用
			if (price != null && data != null) {
				FNTenantDataService.saveData(building.getId(), tenant.getId(), EnumTimeType.T0, energyTypeId,
						standard.parse(entry.getKey()), EnumEnergyMoney.Money, data * price.doubleValue());
			} else {
				FNTenantDataService.saveData(building.getId(), tenant.getId(), EnumTimeType.T0, energyTypeId,
						standard.parse(entry.getKey()), EnumEnergyMoney.Money, null);
			}
		}

		EnumTimeType[] timeTypeArray = { EnumTimeType.T1, EnumTimeType.T2 };
		EnumEnergyMoney[] energyMoneyArray = { EnumEnergyMoney.Energy, EnumEnergyMoney.Money };
		for (EnumTimeType timeType : timeTypeArray) {
			Date newTimeFrom = null;
			Date newTimeTo = null;
			if (timeType == EnumTimeType.T1) {
				newTimeFrom = DateUtils.truncate(timeFrom, Calendar.HOUR);
				newTimeTo = DateUtils.truncate(DateUtils.addHours(timeTo, 1), Calendar.HOUR);
			} else if (timeType == EnumTimeType.T2) {
				newTimeFrom = DateUtils.truncate(timeFrom, Calendar.DATE);
				newTimeTo = DateUtils.truncate(DateUtils.addDays(timeTo, 1), Calendar.DATE);
			}

			for (EnumEnergyMoney energyMoney : energyMoneyArray) {
				Map<String, Double> dataMap = TimeDataUtil.getTimeDataMap(newTimeFrom, newTimeTo, timeType);
				List<FnTenantData> tenantDataList = FNTenantDataService.queryListGteLt(building.getId(), tenant.getId(),
						EnumTimeType.valueOf(timeType.getValue() - 1), energyTypeId, newTimeFrom, newTimeTo,
						energyMoney);

				if (tenantDataList != null) {
					for (FnTenantData tenantData : tenantDataList) {
						if (tenantData.getData() != null) {
							String timeFromKey = standard.format(tenantData.getTimeFrom());
							if (timeType == EnumTimeType.T1) {
								timeFromKey = timeFromKey.substring(0, 13) + ":00:00";
							} else if (timeType == EnumTimeType.T2) {
								timeFromKey = timeFromKey.substring(0, 10) + " 00:00:00";
							}
							if (dataMap.containsKey(timeFromKey)) {
								if (dataMap.get(timeFromKey) == null) {
									dataMap.put(timeFromKey, tenantData.getData());
								} else {
									dataMap.put(timeFromKey, dataMap.get(timeFromKey) + tenantData.getData());
								}
							}
							// if (timeType == EnumTimeType.T2) {
							// String energyMoneyType = energyMoney.getValue() +
							// "";
							// String key = buildEnergyKey(energyTypeId,
							// timeFromKey);
							// if (buildingDataMap.get(key) == null) {
							// Map<String, Double> map = new HashMap<String,
							// Double>();
							// map.put(energyMoneyType, tenantData.getData());
							// buildingDataMap.put(key, map);
							// } else {
							// if (buildingDataMap.get(key).get(energyMoneyType)
							// == null) {
							// buildingDataMap.get(key).put(energyMoneyType,
							// tenantData.getData());
							// } else {
							// buildingDataMap.get(key).put(energyMoneyType,
							// buildingDataMap.get(key).get(energyMoneyType) +
							// tenantData.getData());
							// }
							// }
							// }
						}
					}
				}
				if (dataMap.size() > 0) {
					for (Map.Entry<String, Double> entry : dataMap.entrySet()) {
						this.FNTenantDataService.saveData(building.getId(), tenant.getId(), timeType, energyTypeId,
								standard.parse(entry.getKey()), energyMoney, entry.getValue());
						// this.processTenantStat(building.getId(),
						// tenant.getId(), energyTypeId, timeType, energyMoney,
						// timeFrom, entry.getValue());
					}
				}
			}
		}

		if (tenantPayType.getPrePayType().intValue() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
			Date newTimeFrom = DateUtils.addMinutes(timeFrom, -15);
			Date newTimeTo = DateUtils.addMinutes(timeTo, 15);
			this.processTenantBackPay(project, building, tenant, newTimeFrom, newTimeTo, energyTypeId,
					tenantPayType.getPrepayChargeType());
		}

		FNTenantEnergySplitComputeService.saveComputeTime(building.getId(), tenant.getId(), energyTypeId, timeTo);
	}

	@SuppressWarnings("unchecked")
	public void processTenantMeter(Project project, Building building, FnTenant tenant, String energyTypeId,
			FnTenantPayType tenantPayType, FnPriceTemplate priceTemplate,
			Map<String, Map<String, Double>> buildingDataMap) throws Exception {
		// if("ZHBH_1013".equals(tenant.getId())){
		// System.out.println("--");
		// }
		// 查询租户能耗类型仪表

		List<DTOMeter> meterList = FNMeterService.queryMeterList(tenant.getId(), energyTypeId);
		if (meterList == null || meterList.size() == 0) {
			return;// 未找到仪表，计算其他能耗类型
		}
		Map<String, Map<String, Object>> tenantComputeMeterMap = new HashMap<>();
		Map<String, Date> meterFunctionComputeTimeMap = new HashMap<>();
		for (DTOMeter meter : meterList) {
			// 判断仪表类型是否为多费率
			Map<String, Object> result = null;
			if (meter.getMeterType().intValue() == EnumMeterType.Multiple.getValue().intValue()) {// 多费率
				Map<String, Integer> functionMap = FunctionTypeUtil.getCumulantDianMultiple();
				result = this.processMeterMultiple(project, building, tenant, meter, functionMap.get("J"),
						functionMap.get("F"), functionMap.get("G"), functionMap.get("P"), energyTypeId, priceTemplate);
			} else {// 普通仪表
					// 仪表对应协议的累计量是否存在
				// long start0 = System.currentTimeMillis();
				result = this.processMeterCommon(project, building, tenant, meter, energyTypeId, priceTemplate);
				// long end0 = System.currentTimeMillis();
				// System.out.println("处理一块仪表用时:"+(end0-start0));
			}
			if (result != null) {
				tenantComputeMeterMap.put(meter.getMeterId(), result);
			}
		}
		if (tenantComputeMeterMap.size() > 0) {// 根据仪表计算租户
			// 分析开始，结束时间
			Date timeFrom = null;
			Date timeTo = null;
			Map<String, List<Integer>> functionListMap = new HashMap<>();
			for (Map.Entry<String, Map<String, Object>> tenantComputeMeterEntry : tenantComputeMeterMap.entrySet()) {
				Map<String, Object> tenantComputeMeterParam = tenantComputeMeterEntry.getValue();
				Date tempTimeFrom = (Date) tenantComputeMeterParam.get("timeFrom");
				Date tempTimeTo = (Date) tenantComputeMeterParam.get("timeTo");

				if (tempTimeFrom == null || tempTimeTo == null) {
					continue;
				}

				List<Object> computeToList = (List<Object>) tenantComputeMeterParam.get("computeToList");
				if (computeToList != null && computeToList.size() > 0) {
					for (Object computeToObj : computeToList) {
						Map<String, Object> computeToMap = (Map<String, Object>) computeToObj;
						Integer functionId = (Integer) computeToMap.get("functionId");
						Date computeTo = (Date) computeToMap.get("computeTo");
						if (functionId != null && computeTo != null) {
							meterFunctionComputeTimeMap.put(tenantComputeMeterEntry.getKey() + "_" + functionId,
									computeTo);
						}
					}
				}

				if (timeFrom == null && timeTo == null) {
					timeFrom = tempTimeFrom;
					timeTo = tempTimeTo;
				} else {
					if (timeFrom.getTime() > tempTimeFrom.getTime()) {
						timeFrom = tempTimeFrom;
					}
					if (timeTo.getTime() < tempTimeTo.getTime()) {
						timeTo = tempTimeTo;
					}
				}
				// 统计要计算的功能号
				List<Integer> functionList = (List<Integer>) tenantComputeMeterParam.get("functionList");
				if (functionList != null && functionList.size() > 0) {
					functionListMap.put(tenantComputeMeterEntry.getKey(), functionList);
				}
			}
			if (timeFrom != null && timeTo != null) {
				timeFrom = DateUtils.addMinutes(timeFrom, -15);
				timeTo = DateUtils.addMinutes(timeTo, 15);
				this.processTenantData(building, tenant, energyTypeId, timeFrom, timeTo, meterList, buildingDataMap);

				if (tenantPayType.getPrePayType().intValue() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
					this.processTenantBackPay(project, building, tenant, timeFrom, timeTo, energyTypeId,
							tenantPayType.getPrepayChargeType());
				}
			}
		}

		if (meterFunctionComputeTimeMap.size() > 0) {// 更改仪表功能计算时间
			for (Map.Entry<String, Date> meterFunctionComputeTimeEntry : meterFunctionComputeTimeMap.entrySet()) {
				String[] array = meterFunctionComputeTimeEntry.getKey().split("_");
				this.FNTenantMeterComputeService.saveComputeTime(building.getId(), tenant.getId(), array[0],
						Integer.parseInt(array[1]), meterFunctionComputeTimeEntry.getValue());
			}
		}
	}

	public void processTenantBackPayRecord(Project project, Building building, FnTenant tenant, String energyTypeId,
			int prePayChargeType) throws Exception {

		// if("20180629".equals(tenant.getId()) &&
		// energyTypeId.equals("Dian")){
		// System.out.println("--");
		// }

		// 处理预付费租户充值，计算T0扣费
		// 1.查询上次计算的截止数据
		FnTenantBackPayData lastTenantBackPayData = this.FNTenantBackPayDataService.queryLastData(building.getId(),
				tenant.getId(), energyTypeId, EnumTimeType.T0, EnumPrepayChargeType.valueOf(prePayChargeType), null);
		Date lastTime = tenant.getActiveTime();
		if (lastTenantBackPayData != null) {
			lastTime = lastTenantBackPayData.getTimeFrom();
		}
		// 查询操作时间之前的账单
		List<FnTenantBackPayRecord> notProcessList = FNTenantBackPayRecordService.queryGteLte(building.getId(),
				tenant.getId(), energyTypeId, EnumTimeType.T0, EnumPrepayChargeType.valueOf(prePayChargeType),
				EnumYesNo.NO, tenant.getActiveTime(), null);
		if (notProcessList != null && notProcessList.size() > 0) {
			FnTenantBackPayRecord record = notProcessList.get(0);
			if (record != null) {
				if (record.getTimeFrom().getTime() < lastTime.getTime()) {
					lastTime = record.getTimeFrom();
				}
			}
		} else {
			return;
		}
		Double lastData = 0.0;
		Date timeTo = new Date();
		FnTenantBackPayData tenantBackPayData = this.FNTenantBackPayDataService.queryLastData(building.getId(),
				tenant.getId(), energyTypeId, EnumTimeType.T0, EnumPrepayChargeType.valueOf(prePayChargeType),
				lastTime);

		Map<String, Double> saveMap = new LinkedHashMap<>();

		if (tenantBackPayData != null) {
			lastData = tenantBackPayData.getData();// 记录上次计算截止数据
			if (tenantBackPayData.getTimeFrom().getTime() < lastTime.getTime()) {
				lastTime = tenantBackPayData.getTimeFrom();
			}
		} else {// 保存初始化读数
			if (lastTime.getTime() < tenant.getActiveTime().getTime()) {
				lastTime = tenant.getActiveTime();
			}
			saveMap.put(standard.format(lastTime), lastData);// 初始化0.0
		}

		// 查询开始到timeTo 所有充值记录做为Map
		List<FnTenantBackPayRecord> recordList = FNTenantBackPayRecordService.queryGteLte(building.getId(),
				tenant.getId(), energyTypeId, EnumTimeType.T0, EnumPrepayChargeType.valueOf(prePayChargeType), null,
				lastTime, timeTo);
		Map<String, List<FnTenantBackPayRecord>> recordMap = new HashMap<>();
		if (recordList != null) {
			for (FnTenantBackPayRecord record : recordList) {
				String key = standard.format(record.getTimeFrom());
				if (!recordMap.containsKey(key)) {
					recordMap.put(key, new ArrayList<FnTenantBackPayRecord>());
				}
				recordMap.get(key).add(record);
			}
		}

		Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(lastTime, timeTo, EnumTimeType.T0);
		// 获取充值类型
		// long start11 = System.currentTimeMillis();
		List<FnTenantData> dataList = FNTenantDataService.queryListGteLt(building.getId(), tenant.getId(),
				EnumTimeType.T0, energyTypeId, lastTime, timeTo, EnumEnergyMoney.valueOf(prePayChargeType));
		// long end11 = System.currentTimeMillis();
		// System.out.println("步骤11:"+(end11-start11)+":"+dataList.size());

		Map<String, Double> timeDataMap = new LinkedHashMap<>();
		Date lastDataTime = null;
		if (dataList != null && dataList.size() > 0) {
			lastDataTime = dataList.get(dataList.size() - 1).getTimeFrom();
			for (FnTenantData tenantData : dataList) {
				timeDataMap.put(standard.format(tenantData.getTimeFrom()), tenantData.getData());
			}
		}
		for (Map.Entry<String, Double> timeEntry : timeMap.entrySet()) {
			Date time = standard.parse(timeEntry.getKey());
			// if (time.getTime() > lastDataTime.getTime()) {
			// break;
			// }
			if (recordMap.containsKey(timeEntry.getKey())) {
				List<FnTenantBackPayRecord> backPayRecordList = recordMap.get(timeEntry.getKey());
				for (FnTenantBackPayRecord record : backPayRecordList) {
					if (record != null) {
						lastData = lastData + record.getData();
					}
				}
			}
			Date after15Time = new Date(time.getTime() + FineinConstant.Time.Minute_15);
			String after15TimeKey = standard.format(after15Time);
			if (timeDataMap.get(timeEntry.getKey()) != null) {
				lastData = lastData - timeDataMap.get(timeEntry.getKey());
			}
			saveMap.put(after15TimeKey, lastData.doubleValue());
		}
		if (saveMap.size() > 0) {// 保存

			// long start12 = System.currentTimeMillis();
			for (Map.Entry<String, Double> saveEntry : saveMap.entrySet()) {
				String hour = saveEntry.getKey().substring(0, 13) + ":00:00";
				String day = saveEntry.getKey().substring(0, 10) + " 00:00:00";
				if (hour.equals(saveEntry.getKey())) {
					this.FNTenantBackPayDataService.saveData(building.getId(), tenant.getId(), energyTypeId,
							EnumTimeType.T1, EnumPrepayChargeType.valueOf(prePayChargeType),
							standard.parse(saveEntry.getKey()), saveEntry.getValue());
				}
				if (day.equals(saveEntry.getKey())) {
					this.FNTenantBackPayDataService.saveData(building.getId(), tenant.getId(), energyTypeId,
							EnumTimeType.T2, EnumPrepayChargeType.valueOf(prePayChargeType),
							standard.parse(saveEntry.getKey()), saveEntry.getValue());
				}
				this.FNTenantBackPayDataService.saveData(building.getId(), tenant.getId(), energyTypeId,
						EnumTimeType.T0, EnumPrepayChargeType.valueOf(prePayChargeType),
						standard.parse(saveEntry.getKey()), saveEntry.getValue());
			}
			// long end12 = System.currentTimeMillis();
			// System.out.println("步骤12:"+(end12-start12)+":"+saveMap.size());

		}
		// 修改交费记录的状态
		if (recordMap.size() > 0) {
			for (Map.Entry<String, List<FnTenantBackPayRecord>> recordEntry : recordMap.entrySet()) {
				List<FnTenantBackPayRecord> backPayRecordList = recordEntry.getValue();
				if (backPayRecordList != null && backPayRecordList.size() > 0) {
					for (FnTenantBackPayRecord record : backPayRecordList) {
						this.FNTenantBackPayRecordService.updateProcessStatus(record.getId(), EnumYesNo.YES);
					}
				}
			}
		}

		// 处理预付费租户充值，计算T0扣费
		// 1.查询上次计算的截止数据
		// Date lastTime = tenant.getActiveTime();
		// Double lastData = 0.0;
		// FnTenantBackPayData lastTenantBackPayData =
		// this.FNTenantBackPayDataService.queryLastData(building.getId(),
		// tenant.getId(), energyTypeId, EnumTimeType.T0,
		// EnumPrepayChargeType.valueOf(prePayChargeType), null);
		// if (lastTenantBackPayData != null) {
		// lastTime = lastTenantBackPayData.getTimeFrom();
		// lastData = lastTenantBackPayData.getData();
		// }
		// Double payData = 0.0;
		// List<FnTenantBackPayRecord> notProcessList =
		// FNTenantBackPayRecordService.queryGteLte(building.getId(),
		// tenant.getId(), energyTypeId, EnumTimeType.T0,
		// EnumPrepayChargeType.valueOf(prePayChargeType),
		// EnumYesNo.NO, lastTime, null);
		// if (notProcessList != null && notProcessList.size() > 0) {
		// for (FnTenantBackPayRecord fnTenantBackPayRecord : notProcessList) {
		// payData += fnTenantBackPayRecord.getData();
		// }
		// } else {
		// return;
		// }
		// Map<String, Double> saveMap = new LinkedHashMap<>();
		// saveMap.put(standard.format(lastTime), lastData.doubleValue() +
		// payData.doubleValue());
		// if (saveMap.size() > 0) {// 保存
		// // long start12 = System.currentTimeMillis();
		// for (Map.Entry<String, Double> saveEntry : saveMap.entrySet()) {
		// String hour = saveEntry.getKey().substring(0, 13) + ":00:00";
		// String day = saveEntry.getKey().substring(0, 10) + " 00:00:00";
		// this.FNTenantBackPayDataService.saveData(building.getId(),
		// tenant.getId(), energyTypeId,
		// EnumTimeType.T1, EnumPrepayChargeType.valueOf(prePayChargeType),
		// standard.parse(hour),
		// saveEntry.getValue());
		// this.FNTenantBackPayDataService.saveData(building.getId(),
		// tenant.getId(), energyTypeId,
		// EnumTimeType.T2, EnumPrepayChargeType.valueOf(prePayChargeType),
		// standard.parse(day),
		// saveEntry.getValue());
		// this.FNTenantBackPayDataService.saveData(building.getId(),
		// tenant.getId(), energyTypeId,
		// EnumTimeType.T0, EnumPrepayChargeType.valueOf(prePayChargeType),
		// standard.parse(saveEntry.getKey()), saveEntry.getValue());
		// }
		// // long end12 = System.currentTimeMillis();
		// // System.out.println("步骤12:"+(end12-start12)+":"+saveMap.size());
		//
		// }
		//// 修改交费记录的状态
		// for (FnTenantBackPayRecord record : notProcessList) {
		// this.FNTenantBackPayRecordService.updateProcessStatus(record.getId(),
		// EnumYesNo.YES);
		// }
	}

	public void processTenantBackPay(Project project, Building building, FnTenant tenant, Date timeFrom, Date timeTo,
			String energyTypeId, int prePayChargeType) throws Exception {
		// if("ZHBH_1104".equals(tenant.getId()) &&
		// energyTypeId.equals("Dian")){
		// System.out.println("--");
		// }

		// 处理预付费租户充值，计算T0扣费
		// 1.查询上次计算的截止数据
		FnTenantBackPayData lastTenantBackPayData = this.FNTenantBackPayDataService.queryLastData(building.getId(),
				tenant.getId(), energyTypeId, EnumTimeType.T0, EnumPrepayChargeType.valueOf(prePayChargeType), null);
		Date lastTime = timeFrom;
		if (lastTenantBackPayData != null) {
			if (lastTenantBackPayData.getTimeFrom().getTime() < lastTime.getTime()) {
				lastTime = lastTenantBackPayData.getTimeFrom();
			}
		}
		// 查询操作时间之前的账单
		List<FnTenantBackPayRecord> notProcessList = FNTenantBackPayRecordService.queryGteLte(building.getId(),
				tenant.getId(), energyTypeId, EnumTimeType.T0, EnumPrepayChargeType.valueOf(prePayChargeType),
				EnumYesNo.NO, null, lastTime);
		if (notProcessList != null && notProcessList.size() > 0) {
			FnTenantBackPayRecord record = notProcessList.get(0);
			if (record != null) {
				if (record.getTimeFrom().getTime() < lastTime.getTime()) {
					lastTime = record.getTimeFrom();
				}
			}
		}
		if (tenant.getActiveTime().getTime() > lastTime.getTime()) {
			lastTime = tenant.getActiveTime();
		}
		Double lastData = 0.0;
		FnTenantBackPayData tenantBackPayData = this.FNTenantBackPayDataService.queryLastData(building.getId(),
				tenant.getId(), energyTypeId, EnumTimeType.T0, EnumPrepayChargeType.valueOf(prePayChargeType),
				lastTime);

		Map<String, Double> saveMap = new LinkedHashMap<>();

		if (tenantBackPayData != null) {
			lastData = tenantBackPayData.getData();// 记录上次计算截止数据
			if (tenantBackPayData.getTimeFrom().getTime() < lastTime.getTime()) {
				lastTime = tenantBackPayData.getTimeFrom();
			}
		} else {// 保存初始化读数
			if (lastTime.getTime() < tenant.getActiveTime().getTime()) {
				lastTime = tenant.getActiveTime();
			}
			saveMap.put(standard.format(lastTime), lastData);// 初始化0.0
		}

		// 查询开始到timeTo 所有充值记录做为Map
		List<FnTenantBackPayRecord> recordList = FNTenantBackPayRecordService.queryGteLte(building.getId(),
				tenant.getId(), energyTypeId, EnumTimeType.T0, EnumPrepayChargeType.valueOf(prePayChargeType), null,
				lastTime, new Date());
		Map<String, List<FnTenantBackPayRecord>> recordMap = new HashMap<>();
		if (recordList != null) {
			for (FnTenantBackPayRecord record : recordList) {
				String key = standard.format(record.getTimeFrom());
				if (!recordMap.containsKey(key)) {
					recordMap.put(key, new ArrayList<FnTenantBackPayRecord>());
				}
				recordMap.get(key).add(record);
			}
		}

		Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(lastTime, new Date(), EnumTimeType.T0);
		// 获取充值类型
		if (prePayChargeType == EnumPrepayChargeType.Qian.getValue().intValue()) {// 查询费用
			// long start11 = System.currentTimeMillis();
			List<FnTenantData> dataList = FNTenantDataService.queryListGteLt(building.getId(), tenant.getId(),
					EnumTimeType.T0, energyTypeId, lastTime, timeTo, EnumEnergyMoney.Money);
			// long end11 = System.currentTimeMillis();
			// System.out.println("步骤11:"+(end11-start11)+":"+dataList.size());

			Map<String, Double> timeDataMap = new LinkedHashMap<>();
			Date lastDataTime = null;
			if (dataList != null && dataList.size() > 0) {
				lastDataTime = dataList.get(dataList.size() - 1).getTimeFrom();
				for (FnTenantData tenantData : dataList) {
					timeDataMap.put(standard.format(tenantData.getTimeFrom()), tenantData.getData());
				}
			}
			if (lastDataTime == null) {// 无数据
				return;
			}
			for (Map.Entry<String, Double> timeEntry : timeMap.entrySet()) {
				Date time = standard.parse(timeEntry.getKey());
				if (time.getTime() > lastDataTime.getTime()) {
					break;
				}
				if (recordMap.containsKey(timeEntry.getKey())) {
					List<FnTenantBackPayRecord> backPayRecordList = recordMap.get(timeEntry.getKey());
					for (FnTenantBackPayRecord record : backPayRecordList) {
						if (record != null) {
							lastData = lastData + record.getData();
						}
					}
				}
				Date after15Time = new Date(time.getTime() + FineinConstant.Time.Minute_15);
				String after15TimeKey = standard.format(after15Time);
				if (timeDataMap.get(timeEntry.getKey()) != null) {
					lastData = lastData - timeDataMap.get(timeEntry.getKey());
				}
				saveMap.put(after15TimeKey, lastData.doubleValue());
			}
			if (saveMap.size() > 0) {// 保存

				// long start12 = System.currentTimeMillis();
				for (Map.Entry<String, Double> saveEntry : saveMap.entrySet()) {
					String hour = saveEntry.getKey().substring(0, 13) + ":00:00";
					String day = saveEntry.getKey().substring(0, 10) + " 00:00:00";
					if (hour.equals(saveEntry.getKey())) {
						this.FNTenantBackPayDataService.saveData(building.getId(), tenant.getId(), energyTypeId,
								EnumTimeType.T1, EnumPrepayChargeType.Qian, standard.parse(saveEntry.getKey()),
								saveEntry.getValue());
					}
					if (day.equals(saveEntry.getKey())) {
						this.FNTenantBackPayDataService.saveData(building.getId(), tenant.getId(), energyTypeId,
								EnumTimeType.T2, EnumPrepayChargeType.Qian, standard.parse(saveEntry.getKey()),
								saveEntry.getValue());
					}
					this.FNTenantBackPayDataService.saveData(building.getId(), tenant.getId(), energyTypeId,
							EnumTimeType.T0, EnumPrepayChargeType.Qian, standard.parse(saveEntry.getKey()),
							saveEntry.getValue());
				}
				// long end12 = System.currentTimeMillis();
				// System.out.println("步骤12:"+(end12-start12)+":"+saveMap.size());

			}
		} else {// 查询能耗
			List<FnTenantData> dataList = FNTenantDataService.queryListGteLt(building.getId(), tenant.getId(),
					EnumTimeType.T0, energyTypeId, lastTime, timeTo, EnumEnergyMoney.Energy);
			Map<String, Double> timeDataMap = new HashMap<>();
			Date lastDataTime = null;
			if (dataList != null && dataList.size() > 0) {
				lastDataTime = dataList.get(dataList.size() - 1).getTimeFrom();
				for (FnTenantData tenantData : dataList) {
					timeDataMap.put(standard.format(tenantData.getTimeFrom()), tenantData.getData());
				}
			}

			if (lastDataTime == null) {
				return;
			}

			for (Map.Entry<String, Double> timeEntry : timeMap.entrySet()) {
				// if(timeEntry.getKey().equals(lastTimeString)){
				// continue;
				// }
				Date time = standard.parse(timeEntry.getKey());
				if (time.getTime() > lastDataTime.getTime()) {
					break;
				}
				if (recordMap.containsKey(timeEntry.getKey())) {
					List<FnTenantBackPayRecord> backPayRecordList = recordMap.get(timeEntry.getKey());
					for (FnTenantBackPayRecord record : backPayRecordList) {
						if (record != null) {
							lastData = lastData + record.getData();
						}
					}
				}
				Date after15Time = new Date(time.getTime() + FineinConstant.Time.Minute_15);
				String after15TimeKey = standard.format(after15Time);
				if (timeDataMap.get(timeEntry.getKey()) != null) {
					lastData = lastData - timeDataMap.get(timeEntry.getKey());
				}
				saveMap.put(after15TimeKey, lastData.doubleValue());
			}

			if (saveMap.size() > 0) {// 保存yyyy-MM-dd HH:mm:ss
				for (Map.Entry<String, Double> saveEntry : saveMap.entrySet()) {
					String hour = saveEntry.getKey().substring(0, 13) + ":00:00";
					String day = saveEntry.getKey().substring(0, 10) + " 00:00:00";
					if (hour.equals(saveEntry.getKey())) {
						this.FNTenantBackPayDataService.saveData(building.getId(), tenant.getId(), energyTypeId,
								EnumTimeType.T1, EnumPrepayChargeType.Liang, standard.parse(saveEntry.getKey()),
								saveEntry.getValue());
					}
					if (day.equals(saveEntry.getKey())) {
						this.FNTenantBackPayDataService.saveData(building.getId(), tenant.getId(), energyTypeId,
								EnumTimeType.T2, EnumPrepayChargeType.Liang, standard.parse(saveEntry.getKey()),
								saveEntry.getValue());
					}
					this.FNTenantBackPayDataService.saveData(building.getId(), tenant.getId(), energyTypeId,
							EnumTimeType.T0, EnumPrepayChargeType.Liang, standard.parse(saveEntry.getKey()),
							saveEntry.getValue());
				}
			}
		}
		// 修改交费记录的状态
		if (recordMap.size() > 0) {
			for (Map.Entry<String, List<FnTenantBackPayRecord>> recordEntry : recordMap.entrySet()) {
				List<FnTenantBackPayRecord> backPayRecordList = recordEntry.getValue();
				if (backPayRecordList != null && backPayRecordList.size() > 0) {
					for (FnTenantBackPayRecord record : backPayRecordList) {
						this.FNTenantBackPayRecordService.updateProcessStatus(record.getId(), EnumYesNo.YES);
					}
				}
			}
		}
	}

	public void processTenantData(Building building, FnTenant tenant, String energyTypeId, Date timeFrom, Date timeTo,
			List<DTOMeter> meterList, Map<String, Map<String, Double>> buildingDataMap) throws Exception {
		// if("ZHBH_1019".equals(tenant.getId()) &&
		// energyTypeId.equals("Dian")){
		// System.out.println("--");
		// }
		if (meterList == null || meterList.size() == 0) {
			return;// 未找到仪表，计算其他能耗类型
		}
		EnumTimeType[] timeTypeArray = { EnumTimeType.T0, EnumTimeType.T1, EnumTimeType.T2 };
		EnumEnergyMoney[] energyMoneyArray = { EnumEnergyMoney.Energy, EnumEnergyMoney.Money };
		for (EnumTimeType timeType : timeTypeArray) {
			if (timeType == EnumTimeType.T1) {
				timeFrom = DateUtils.truncate(timeFrom, Calendar.HOUR);
				timeTo = DateUtils.truncate(DateUtils.addHours(timeTo, 1), Calendar.HOUR);
			} else if (timeType == EnumTimeType.T2) {
				timeFrom = DateUtils.truncate(timeFrom, Calendar.DATE);
				timeTo = DateUtils.truncate(DateUtils.addDays(timeTo, 1), Calendar.DATE);
			}

			for (EnumEnergyMoney energyMoney : energyMoneyArray) {
				Map<String, Double> dataMap = new HashMap<>();

				for (DTOMeter meter : meterList) {
					List<Integer> functionList = new ArrayList<>();
					if (meter.getMeterType().intValue() == EnumMeterType.Multiple.getValue().intValue()) {// 多费率-只有电有多费率
						Map<String, Integer> functionMap = FunctionTypeUtil.getCumulantDianMultiple();
						Integer jCumulantFunctionId = this.queryCumulantFunctionId(meter.getProtocolId(),
								meter.getEnergyTypeId(), functionMap.get("J"));
						Integer fCumulantFunctionId = this.queryCumulantFunctionId(meter.getProtocolId(),
								meter.getEnergyTypeId(), functionMap.get("F"));
						Integer gCumulantFunctionId = this.queryCumulantFunctionId(meter.getProtocolId(),
								meter.getEnergyTypeId(), functionMap.get("G"));
						Integer pCumulantFunctionId = this.queryCumulantFunctionId(meter.getProtocolId(),
								meter.getEnergyTypeId(), functionMap.get("P"));
						if (jCumulantFunctionId != null) {
							functionList.add(jCumulantFunctionId);
						}
						if (fCumulantFunctionId != null) {
							functionList.add(fCumulantFunctionId);
						}
						if (gCumulantFunctionId != null) {
							functionList.add(gCumulantFunctionId);
						}
						if (pCumulantFunctionId != null) {
							functionList.add(pCumulantFunctionId);
						}
					} else {// 普通仪表
						Integer destCumulantFunctionId = FunctionTypeUtil.getCumulantFunctionId(energyTypeId);
						Integer cumulantFunctionId = this.queryCumulantFunctionId(meter.getProtocolId(),
								meter.getEnergyTypeId(), destCumulantFunctionId);
						if (cumulantFunctionId != null) {
							functionList.add(cumulantFunctionId);
						}
					}
					if (functionList.size() == 0) {
						continue;
					}
					Map<String, Double> meterDataMap = new HashMap<>();

					for (Integer functionId : functionList) {
						List<FnTenantMeterData> tenantMeterDataList = FNTenantMeterDataService.queryListGteLt(
								building.getId(), tenant.getId(), meter.getMeterId(), functionId, energyTypeId,
								timeType, timeFrom, timeTo, energyMoney);
						if (tenantMeterDataList != null) {
							for (FnTenantMeterData tenantMeterData : tenantMeterDataList) {
								if (tenantMeterData.getData() != null) {
									String timeFromKey = standard.format(tenantMeterData.getTimeFrom());
									if (dataMap.get(timeFromKey) != null) {
										dataMap.put(timeFromKey, tenantMeterData.getData() + dataMap.get(timeFromKey));
									} else {
										dataMap.put(timeFromKey, tenantMeterData.getData());
									}
									if (meter.getMeterType().intValue() == EnumMeterType.Multiple.getValue()
											.intValue()) {// 多费率，存储总有功数据
										if (meterDataMap.get(timeFromKey) != null) {
											meterDataMap.put(timeFromKey,
													tenantMeterData.getData() + meterDataMap.get(timeFromKey));
										} else {
											meterDataMap.put(timeFromKey, tenantMeterData.getData());
										}
									}
								}
							}
						}
					}
					// long start1 = System.currentTimeMillis();
					if (meterDataMap.size() > 0) {
						if (meterDataMap.size() > 0) {
							Integer destCumulantFunctionId = FunctionTypeUtil.getCumulantFunctionId(energyTypeId);
							for (Map.Entry<String, Double> entry : meterDataMap.entrySet()) {
								FNTenantMeterDataService.saveData(building.getId(), tenant.getId(), meter.getMeterId(),
										timeType, destCumulantFunctionId, energyTypeId, standard.parse(entry.getKey()),
										energyMoney, entry.getValue());
							}
						}
					}
					// long end1 = System.currentTimeMillis();
					// System.out.println("------------处理仪表总能耗:"+(end1-start1));
				}

				// long start = System.currentTimeMillis();
				if (dataMap.size() > 0) {
					// long start10 = System.currentTimeMillis();
					for (Map.Entry<String, Double> entry : dataMap.entrySet()) {
						this.FNTenantDataService.saveData(building.getId(), tenant.getId(), timeType, energyTypeId,
								standard.parse(entry.getKey()), energyMoney, entry.getValue());

					}
					// long end10 = System.currentTimeMillis();
					// System.out.println("处理租户总能耗时间:"+(end10-start10)+":"+dataMap.size());

				}
			}
		}
	}

	public Map<String, Object> processMeterCommon(Project project, Building building, FnTenant tenant, DTOMeter meter,
			String energyTypeId, FnPriceTemplate priceTemplate) throws Exception {
		Integer destCumulantFunctionId = FunctionTypeUtil.getCumulantFunctionId(energyTypeId);
		Integer cumulantFunctionId = this.queryCumulantFunctionId(meter.getProtocolId(), meter.getEnergyTypeId(),
				destCumulantFunctionId);
		Double avgPrice = DoubleFormatUtil.Instance().getDoubleData(priceTemplate.getContent());
		if (cumulantFunctionId == null) {// 不存在，则看剩余量功能号是否存在
			// Integer shengYuLiangFunctionId =
			// this.queryShengYuLiangFunctionId(meter.getProtocolId(),
			// meter.getEnergyTypeId());
			// if(shengYuLiangFunctionId != null){//，如果存在以剩余量作为能耗进行计算
			// return this.processMeterFunctionCommon(project, building, tenant,
			// meter, energyTypeId, shengYuLiangFunctionId,
			// destCumulantFunctionId, avgPrice,false);
			// }else{//不计算
			// Integer shenYuJinEFunctionId =
			// this.queryShengYuJinEFunctionId(meter.getProtocolId(),
			// meter.getEnergyTypeId());
			// if(shenYuJinEFunctionId != null){//只计算费用
			// return this.processMeterFunctionCommon(project, building, tenant,
			// meter, energyTypeId, shenYuJinEFunctionId,
			// destCumulantFunctionId, avgPrice,true);
			// }else{//不计算
			// return null;
			// }
			// }
			// 没有此累计量，能耗费用不计算
			log.info("计算仪表能耗无累计量功能,租户编码:" + tenant.getId() + ",仪表编码:" + meter.getMeterId() + ",能耗类型:" + energyTypeId);
			return null;
		} else {
			return this.processMeterFunctionCommon(project, building, tenant, meter, energyTypeId, cumulantFunctionId,
					cumulantFunctionId, avgPrice, false);
		}
	}

	public Map<String, Object> processMeterMultiple(Project project, Building building, FnTenant tenant, DTOMeter meter,
			int jFunctionId, int fFunctionId, int gFunctionId, int pFunctionId, String energyTypeId,
			FnPriceTemplate priceTemplate) throws Exception {
		Integer jCumulantFunctionId = this.queryCumulantFunctionId(meter.getProtocolId(), meter.getEnergyTypeId(),
				jFunctionId);
		Integer fCumulantFunctionId = this.queryCumulantFunctionId(meter.getProtocolId(), meter.getEnergyTypeId(),
				fFunctionId);
		Integer gCumulantFunctionId = this.queryCumulantFunctionId(meter.getProtocolId(), meter.getEnergyTypeId(),
				gFunctionId);
		Integer pCumulantFunctionId = this.queryCumulantFunctionId(meter.getProtocolId(), meter.getEnergyTypeId(),
				pFunctionId);
		Date timeFrom = null;
		Date timeTo = null;
		List<Integer> functionIdList = new ArrayList<>();
		Double jPrice = null;
		Double fPrice = null;
		Double gPrice = null;
		Double pPrice = null;
		if (priceTemplate.getType().intValue() == EnumPriceType.AVG.getValue().intValue()) {
			Double avgPrice = DoubleFormatUtil.Instance().getDoubleData(priceTemplate.getContent());
			jPrice = avgPrice;
			fPrice = avgPrice;
			gPrice = avgPrice;
			pPrice = avgPrice;
		} else {
			JSONArray contentArray = null;
			try {
				contentArray = (JSONArray) JSONValue.parse(priceTemplate.getContent());
				for (int i = 0; i < contentArray.size(); i++) {
					JSONObject contentObj = (JSONObject) contentArray.get(i);
					String type = (String) contentObj.get("type");
					Object value = contentObj.get("value");
					if ("J".equals(type)) {
						jPrice = DoubleFormatUtil.Instance().getDoubleData(value);
					} else if ("F".equals(type)) {
						fPrice = DoubleFormatUtil.Instance().getDoubleData(value);
					} else if ("G".equals(type)) {
						gPrice = DoubleFormatUtil.Instance().getDoubleData(value);
					} else if ("P".equals(type)) {
						pPrice = DoubleFormatUtil.Instance().getDoubleData(value);
					}
				}
			} catch (Exception e) {
			}
		}

		Map<String, Object> jResult = null;
		try {
			jResult = this.processMeterFunctionCommon(project, building, tenant, meter, energyTypeId,
					jCumulantFunctionId, jCumulantFunctionId, jPrice, false);
		} catch (Exception e) {
		}
		Map<String, Object> fResult = null;
		try {
			fResult = this.processMeterFunctionCommon(project, building, tenant, meter, energyTypeId,
					fCumulantFunctionId, fCumulantFunctionId, fPrice, false);
		} catch (Exception e) {
		}
		Map<String, Object> gResult = null;
		try {
			gResult = this.processMeterFunctionCommon(project, building, tenant, meter, energyTypeId,
					gCumulantFunctionId, gCumulantFunctionId, gPrice, false);
		} catch (Exception e) {
		}
		Map<String, Object> pResult = null;
		try {
			pResult = this.processMeterFunctionCommon(project, building, tenant, meter, energyTypeId,
					pCumulantFunctionId, pCumulantFunctionId, pPrice, false);
		} catch (Exception e) {
		}

		Map<String, Object> result = new HashMap<>();
		List<Map<String, Object>> computeToList = new ArrayList<>();
		result.put("computeToList", computeToList);

		if (jResult != null) {
			if (jResult.get("timeFrom") != null) {
				timeFrom = (Date) jResult.get("timeFrom");
			}
			if (jResult.get("timeTo") != null) {
				timeTo = (Date) jResult.get("timeTo");
			}
			functionIdList.add(jCumulantFunctionId);
			if (timeTo != null) {
				Map<String, Object> computeToObj = new HashMap<>();
				computeToObj.put("functionId", jFunctionId);
				computeToObj.put("computeTo", timeTo);
				computeToList.add(computeToObj);
			}
		}
		if (fResult != null) {
			if (fResult.get("timeFrom") != null) {
				Date tempFrom = (Date) fResult.get("timeFrom");
				if (timeFrom == null || tempFrom.getTime() < timeFrom.getTime()) {
					timeFrom = tempFrom;
				}
			}
			if (fResult.get("timeTo") != null) {
				Date tempTo = (Date) fResult.get("timeTo");
				if (timeTo == null || tempTo.getTime() > timeTo.getTime()) {
					timeTo = tempTo;
				}
			}
			functionIdList.add(fCumulantFunctionId);
			if (timeTo != null) {
				Map<String, Object> computeToObj = new HashMap<>();
				computeToObj.put("functionId", fFunctionId);
				computeToObj.put("computeTo", timeTo);
				computeToList.add(computeToObj);
			}
		}
		if (gResult != null) {
			if (gResult.get("timeFrom") != null) {
				Date tempFrom = (Date) gResult.get("timeFrom");
				if (timeFrom == null || tempFrom.getTime() < timeFrom.getTime()) {
					timeFrom = tempFrom;
				}
			}
			if (gResult.get("timeTo") != null) {
				Date tempTo = (Date) gResult.get("timeTo");
				if (timeTo == null || tempTo.getTime() > timeTo.getTime()) {
					timeTo = tempTo;
				}
			}
			functionIdList.add(gCumulantFunctionId);
			if (timeTo != null) {
				Map<String, Object> computeToObj = new HashMap<>();
				computeToObj.put("functionId", gFunctionId);
				computeToObj.put("computeTo", timeTo);
				computeToList.add(computeToObj);
			}
		}
		if (pResult != null) {
			if (pResult.get("timeFrom") != null) {
				Date tempFrom = (Date) pResult.get("timeFrom");
				if (timeFrom == null || tempFrom.getTime() < timeFrom.getTime()) {
					timeFrom = tempFrom;
				}
			}
			if (pResult.get("timeTo") != null) {
				Date tempTo = (Date) pResult.get("timeTo");
				if (timeTo == null || tempTo.getTime() > timeTo.getTime()) {
					timeTo = tempTo;
				}
			}
			functionIdList.add(pCumulantFunctionId);
			if (timeTo != null) {
				Map<String, Object> computeToObj = new HashMap<>();
				computeToObj.put("functionId", pFunctionId);
				computeToObj.put("computeTo", timeTo);
				computeToList.add(computeToObj);
			}
		}

		if (timeFrom != null && timeTo != null && functionIdList.size() > 0) {
			result.put("timeFrom", timeFrom);
			result.put("timeTo", timeTo);
			result.put("functionList", functionIdList);
			return result;
		}
		return null;
	}

	public Map<String, Object> processMeterFunctionCommon(Project project, Building building, FnTenant tenant,
			DTOMeter meter, String energyTypeId, int srcFunctionId, int destFunctionId, Double price,
			boolean isOnlyComputeMoeny) throws Exception {
		// 查询上次计算时间
		// long start1 = System.currentTimeMillis();
		Date meterLastComputeTime = FNMeterDataService.queryMeterComputeTime(project.getId(), meter.getMeterId(),
				srcFunctionId);
		// long end1 = System.currentTimeMillis();
		// System.out.println("步骤1:"+(end1-start1));

		if (meterLastComputeTime == null) {
			return null;//
		}
		// 查询能耗计算时间
		// long start2 = System.currentTimeMillis();
		Date lastComputeTime = FNTenantMeterComputeService.queryLastComputeTime(building.getId(), tenant.getId(),
				meter.getMeterId(), destFunctionId);
		// long end2 = System.currentTimeMillis();
		// System.out.println("步骤2:"+(end2-start2));

		if (lastComputeTime == null) {// 从激活时间开始计算
			lastComputeTime = tenant.getActiveTime();
			FNTenantMeterComputeService.saveComputeTime(building.getId(), tenant.getId(), meter.getMeterId(),
					destFunctionId, lastComputeTime);
		}

		if (tenant.getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue()) {// 如果是已退租
			if (tenant.getLeaveTime() == null) {
				return null;
			}
			if (lastComputeTime.getTime() > tenant.getLeaveTime().getTime() + FineinConstant.Time.Day_1) {// 上次计算时间大于退租后一天，不计算
				return null;
			}
		}

		if (lastComputeTime.getTime() < meterLastComputeTime.getTime()) {
			{// T0
				Date timeFrom = DateUtils.addMinutes(lastComputeTime, -15);
				Date timeTo = DateUtils.addMinutes(meterLastComputeTime, 15);

				Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.T0);

				// 查询能耗
				// long start3 = System.currentTimeMillis();
				List<ServiceData> dataList = FNServiceDataService.queryServiceDataGteLt(project.getId(),
						meter.getMeterId(), srcFunctionId, EnumTimeType.T0, timeFrom, timeTo);
				// long end3 = System.currentTimeMillis();
				// System.out.println("步骤3:"+(end3-start3)+":"+dataList.size());

				// long start4 = System.currentTimeMillis();
				if (dataList != null) {// 插入
					for (ServiceData serviceData : dataList) {
						String timeKey = standard.format(serviceData.getTimefrom());
						timeMap.put(timeKey, serviceData.getData());
						//
						// if(serviceData.getData() != null){
						// if(!isOnlyComputeMoeny){
						// FNTenantMeterDataService.saveData(building.getId(),
						// tenant.getId(), meter.getMeterId(), EnumTimeType.T0,
						// destFunctionId,energyTypeId,
						// serviceData.getTimefrom(), EnumEnergyMoney.Energy,
						// serviceData.getData());
						// //计算统计值
						//// this.processTenantMeterStat(building.getId(),
						// tenant.getId(), meter.getMeterId(), energyTypeId,
						// EnumTimeType.T0, destFunctionId,
						// EnumEnergyMoney.Energy, serviceData.getTimefrom(),
						// serviceData.getData());
						// }
						// //计算费用
						// if(isOnlyComputeMoeny){
						// FNTenantMeterDataService.saveData(building.getId(),
						// tenant.getId(), meter.getMeterId(), EnumTimeType.T0,
						// destFunctionId,energyTypeId,
						// serviceData.getTimefrom(), EnumEnergyMoney.Money,
						// serviceData.getData());
						// //计算统计值
						//// this.processTenantMeterStat(building.getId(),
						// tenant.getId(), meter.getMeterId(), energyTypeId,
						// EnumTimeType.T0, destFunctionId,
						// EnumEnergyMoney.Money, serviceData.getTimefrom(),
						// serviceData.getData());
						// }else{
						// if(price != null){
						// FNTenantMeterDataService.saveData(building.getId(),
						// tenant.getId(), meter.getMeterId(), EnumTimeType.T0,
						// destFunctionId,energyTypeId,
						// serviceData.getTimefrom(), EnumEnergyMoney.Money,
						// serviceData.getData() * price);
						// //计算统计值
						//// this.processTenantMeterStat(building.getId(),
						// tenant.getId(), meter.getMeterId(), energyTypeId,
						// EnumTimeType.T0, destFunctionId,
						// EnumEnergyMoney.Money, serviceData.getTimefrom(),
						// serviceData.getData() * price);
						// }
						// }
						// }
					}
				}
				for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
					if (!isOnlyComputeMoeny) {
						FNTenantMeterDataService.saveData(building.getId(), tenant.getId(), meter.getMeterId(),
								EnumTimeType.T0, destFunctionId, energyTypeId, standard.parse(entry.getKey()),
								EnumEnergyMoney.Energy, entry.getValue());
					}
					// 计算费用
					if (isOnlyComputeMoeny) {

						FNTenantMeterDataService.saveData(building.getId(), tenant.getId(), meter.getMeterId(),
								EnumTimeType.T0, destFunctionId, energyTypeId, standard.parse(entry.getKey()),
								EnumEnergyMoney.Money, entry.getValue());
					} else {
						if (price != null && entry.getValue() != null) {
							FNTenantMeterDataService.saveData(building.getId(), tenant.getId(), meter.getMeterId(),
									EnumTimeType.T0, destFunctionId, energyTypeId, standard.parse(entry.getKey()),
									EnumEnergyMoney.Money, entry.getValue() * price);
						} else {
							FNTenantMeterDataService.saveData(building.getId(), tenant.getId(), meter.getMeterId(),
									EnumTimeType.T0, destFunctionId, energyTypeId, standard.parse(entry.getKey()),
									EnumEnergyMoney.Money, null);
						}
					}
				}

				// long end4 = System.currentTimeMillis();
				// System.out.println("步骤4:"+(end4-start4)+":"+dataList.size());
			}
			{// T1
				Date timeFrom = DateUtils.addMinutes(lastComputeTime, -15);
				Date timeTo = DateUtils.addMinutes(meterLastComputeTime, 15);
				timeFrom = DateUtils.truncate(timeFrom, Calendar.HOUR);
				timeTo = DateUtils.addHours(DateUtils.truncate(timeTo, Calendar.HOUR), 1);
				{// 查询能耗
					// long start5 = System.currentTimeMillis();
					Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.T1);

					List<FnTenantMeterData> dataList = FNTenantMeterDataService.queryListGteLt(tenant.getBuildingId(),
							tenant.getId(), meter.getMeterId(), destFunctionId, energyTypeId, EnumTimeType.T0, timeFrom,
							timeTo, EnumEnergyMoney.Energy);
					if (dataList != null) {// 插入
						for (FnTenantMeterData tenantMeterData : dataList) {
							if (tenantMeterData.getData() != null) {
								String timeKey = standard.format(tenantMeterData.getTimeFrom()).substring(0, 13)
										+ ":00:00";
								if (timeMap.get(timeKey) == null) {
									timeMap.put(timeKey, tenantMeterData.getData());
								} else {
									timeMap.put(timeKey, timeMap.get(timeKey) + tenantMeterData.getData());
								}
							}
						}
						for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
							FNTenantMeterDataService.saveData(building.getId(), tenant.getId(), meter.getMeterId(),
									EnumTimeType.T1, destFunctionId, energyTypeId, standard.parse(entry.getKey()),
									EnumEnergyMoney.Energy, entry.getValue());
						}
					}
					// long end5 = System.currentTimeMillis();
					// System.out.println("步骤5:"+(end5-start5)+":"+dataList.size());
				}

				{// 查询费用
					// long start6 = System.currentTimeMillis();
					Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.T1);

					List<FnTenantMeterData> dataList = FNTenantMeterDataService.queryListGteLt(tenant.getBuildingId(),
							tenant.getId(), meter.getMeterId(), destFunctionId, energyTypeId, EnumTimeType.T0, timeFrom,
							timeTo, EnumEnergyMoney.Money);
					if (dataList != null) {// 插入
						for (FnTenantMeterData tenantMeterData : dataList) {
							if (tenantMeterData.getData() != null) {
								String timeKey = standard.format(tenantMeterData.getTimeFrom()).substring(0, 13)
										+ ":00:00";
								if (timeMap.get(timeKey) == null) {
									timeMap.put(timeKey, tenantMeterData.getData());
								} else {
									timeMap.put(timeKey, timeMap.get(timeKey) + tenantMeterData.getData());
								}
							}
						}
						for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
							FNTenantMeterDataService.saveData(building.getId(), tenant.getId(), meter.getMeterId(),
									EnumTimeType.T1, destFunctionId, energyTypeId, standard.parse(entry.getKey()),
									EnumEnergyMoney.Money, entry.getValue());
						}
					}
					// long end6 = System.currentTimeMillis();
					// System.out.println("步骤6:"+(end6-start6)+":"+dataList.size());
				}
			}
			{// T2
				Date timeFrom = DateUtils.addMinutes(lastComputeTime, -15);
				Date timeTo = DateUtils.addMinutes(meterLastComputeTime, 15);
				timeFrom = DateUtils.truncate(timeFrom, Calendar.DATE);
				timeTo = DateUtils.addDays(DateUtils.truncate(timeTo, Calendar.DATE), 1);
				{// 查询能耗
					// long start7 = System.currentTimeMillis();
					Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.T2);

					List<FnTenantMeterData> dataList = FNTenantMeterDataService.queryListGteLt(tenant.getBuildingId(),
							tenant.getId(), meter.getMeterId(), destFunctionId, energyTypeId, EnumTimeType.T1, timeFrom,
							timeTo, EnumEnergyMoney.Energy);
					if (dataList != null) {// 插入
						for (FnTenantMeterData tenantMeterData : dataList) {
							if (tenantMeterData.getData() != null) {
								String timeKey = standard.format(tenantMeterData.getTimeFrom()).substring(0, 10)
										+ " 00:00:00";
								if (timeMap.get(timeKey) == null) {
									timeMap.put(timeKey, tenantMeterData.getData());
								} else {
									timeMap.put(timeKey, timeMap.get(timeKey) + tenantMeterData.getData());
								}
							}
						}
						for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
							FNTenantMeterDataService.saveData(building.getId(), tenant.getId(), meter.getMeterId(),
									EnumTimeType.T2, destFunctionId, energyTypeId, standard.parse(entry.getKey()),
									EnumEnergyMoney.Energy, entry.getValue());
							// 计算统计值
							// this.processTenantMeterStat(building.getId(),
							// tenant.getId(), meter.getMeterId(), energyTypeId,
							// EnumTimeType.T2, destFunctionId,
							// EnumEnergyMoney.Energy,
							// standard.parse(entry.getKey()),
							// entry.getValue());
						}
					}
					// long end7 = System.currentTimeMillis();
					// System.out.println("步骤7:"+(end7-start7)+":"+dataList.size());
				}
				{// 查询费用
					// long start8 = System.currentTimeMillis();
					Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.T2);
					List<FnTenantMeterData> dataList = FNTenantMeterDataService.queryListGteLt(tenant.getBuildingId(),
							tenant.getId(), meter.getMeterId(), destFunctionId, energyTypeId, EnumTimeType.T1, timeFrom,
							timeTo, EnumEnergyMoney.Money);
					if (dataList != null) {// 插入
						for (FnTenantMeterData tenantMeterData : dataList) {
							if (tenantMeterData.getData() != null) {
								String timeKey = standard.format(tenantMeterData.getTimeFrom()).substring(0, 10)
										+ " 00:00:00";
								if (timeMap.get(timeKey) == null) {
									timeMap.put(timeKey, tenantMeterData.getData());
								} else {
									timeMap.put(timeKey, timeMap.get(timeKey) + tenantMeterData.getData());
								}
							}
						}
						for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
							FNTenantMeterDataService.saveData(building.getId(), tenant.getId(), meter.getMeterId(),
									EnumTimeType.T2, destFunctionId, energyTypeId, standard.parse(entry.getKey()),
									EnumEnergyMoney.Money, entry.getValue());
							// 计算统计值
							// this.processTenantMeterStat(building.getId(),
							// tenant.getId(), meter.getMeterId(), energyTypeId,
							// EnumTimeType.T2, destFunctionId,
							// EnumEnergyMoney.Money,
							// standard.parse(entry.getKey()),
							// entry.getValue());
						}
					}
					// long end8 = System.currentTimeMillis();
					// System.out.println("步骤8:"+(end8-start8)+":"+dataList.size());
				}
			}

			Date timeFrom = lastComputeTime;
			Date timeTo = meterLastComputeTime;
			List<Integer> functionIdList = new ArrayList<>();
			functionIdList.add(destFunctionId);
			Map<String, Object> result = new HashMap<>();
			result.put("timeFrom", timeFrom);
			result.put("timeTo", timeTo);
			result.put("functionList", functionIdList);
			List<Map<String, Object>> computeToList = new ArrayList<>();
			{
				Map<String, Object> computeToObj = new HashMap<>();
				computeToObj.put("functionId", srcFunctionId);
				computeToObj.put("computeTo", meterLastComputeTime);
				computeToList.add(computeToObj);
			}
			result.put("computeToList", computeToList);
			return result;
		} else if (lastComputeTime.getTime() == meterLastComputeTime.getTime()) {// 等一轮
		} else {// 计算程序重算了，不操作
		}
		return null;
	}

	// public void processTenantStat(String buildingId,String tenantId,String
	// energyTypeId,EnumTimeType timeType,EnumEnergyMoney energyMoney,Date
	// timeFrom,Double data) throws Exception{
	// if(timeType == EnumTimeType.T2 && data != null){
	// {//最大值
	// String key = this.getTenantStatKey(buildingId, tenantId,energyTypeId,
	// timeType, EnumStatType.Max, energyMoney);
	// Double oldData = TenantDataStatMap.get(key);
	// if(oldData == null || data.doubleValue() > oldData.doubleValue()){
	// //保存
	// this.FNTenantStatService.saveData(buildingId, tenantId, energyTypeId,
	// timeType, EnumStatType.Max, energyMoney, timeFrom, data);
	// TenantDataStatMap.put(key, data);
	// }
	// }
	//// {//最小值
	//// String key = this.getTenantStatKey(buildingId, tenantId,energyTypeId,
	// timeType, EnumStatType.Min, energyMoney);
	//// Double oldData = TenantDataStatMap.get(key);
	//// if(oldData == null || data.doubleValue() < oldData.doubleValue()){
	//// //保存
	//// this.FNTenantStatService.saveData(buildingId, tenantId, energyTypeId,
	// timeType, EnumStatType.Min, energyMoney, timeFrom, data);
	//// TenantDataStatMap.put(key, data);
	//// }
	//// }
	// }
	// }

	// public void processTenantMeterStat(String buildingId,String
	// tenantId,String meterId,String energyTypeId,EnumTimeType timeType,int
	// functionId,EnumEnergyMoney energyMoney,Date timeFrom,Double data) throws
	// Exception{
	// if(data != null){//最大值
	// String key = this.getTenantMeterStatKey(buildingId,
	// tenantId,meterId,energyTypeId, timeType, functionId, EnumStatType.Max,
	// energyMoney);
	// Double oldData = TenantMeterDataStatMap.get(key);
	// if(oldData == null || data.doubleValue() > oldData.doubleValue()){
	// //保存
	// this.FNTenantMeterStatService.saveData(buildingId, tenantId, meterId,
	// energyTypeId, timeType, functionId, EnumStatType.Max, energyMoney,
	// timeFrom, data);
	// TenantMeterDataStatMap.put(key, data);
	// }
	// }
	//// {//最小值
	//// String key = this.getTenantMeterStatKey(buildingId,
	// tenantId,meterId,energyTypeId, timeType, functionId, EnumStatType.Min,
	// energyMoney);
	//// Double oldData = TenantMeterDataStatMap.get(key);
	//// if(oldData == null || data.doubleValue() < oldData.doubleValue()){
	//// //保存
	//// this.FNTenantMeterStatService.saveData(buildingId, tenantId, meterId,
	// energyTypeId, timeType, functionId, EnumStatType.Min, energyMoney,
	// timeFrom, data);
	//// TenantMeterDataStatMap.put(key, data);
	//// }
	//// }
	// }

	public Integer queryShengYuLiangFunctionId(String protocolId, String energyTypeId) {
		int cumulantFunctionId = FunctionTypeUtil.getShengYuLiangFunctionId(energyTypeId);
		List<FnProtocolFunction> functionList = ConstantDBBaseData.ProtocolFunctionMap.get(protocolId);
		if (functionList != null) {
			for (FnProtocolFunction protocolFunction : functionList) {
				if (protocolFunction.getFunctionId().intValue() == cumulantFunctionId) {
					return cumulantFunctionId;
				}
			}
		}
		return null;
	}

	public Integer queryShengYuJinEFunctionId(String protocolId, String energyTypeId) {
		int cumulantFunctionId = FunctionTypeUtil.getShengYuJinEFunctionId(energyTypeId);
		List<FnProtocolFunction> functionList = ConstantDBBaseData.ProtocolFunctionMap.get(protocolId);
		if (functionList != null) {
			for (FnProtocolFunction protocolFunction : functionList) {
				if (protocolFunction.getFunctionId().intValue() == cumulantFunctionId) {
					return cumulantFunctionId;
				}
			}
		}
		return null;
	}

	public Integer queryCumulantFunctionId(String protocolId, String energyTypeId, int functionId) {
		List<FnProtocolFunction> functionList = ConstantDBBaseData.ProtocolFunctionMap.get(protocolId);
		if (functionList != null) {
			for (FnProtocolFunction protocolFunction : functionList) {
				if (protocolFunction.getFunctionId().intValue() == functionId) {
					return functionId;
				}
			}
		}
		return null;
	}

	public double add(double value1, double value2) {
		BigDecimal b1 = new BigDecimal(Double.valueOf(value1).toString());
		BigDecimal b2 = new BigDecimal(Double.valueOf(value2).toString());
		return b1.add(b2).doubleValue();
	}
}
