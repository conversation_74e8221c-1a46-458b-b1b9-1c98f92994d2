package com.persagy.finein.fnmanage.compute.handler;

import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.dictionary.Building;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月22日 下午12:58:25

* 说明:计算最近*天平均值线程
*/
public interface FNTComputeTenantMeterAvgHandler {

	public void processBuilding(Building building, long days) throws Exception;

	public void processTenant(Building building, FnTenant tenant, long days) throws Exception;
}

