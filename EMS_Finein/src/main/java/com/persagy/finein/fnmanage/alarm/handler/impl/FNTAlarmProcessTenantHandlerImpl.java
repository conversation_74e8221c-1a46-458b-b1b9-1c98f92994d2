package com.persagy.finein.fnmanage.alarm.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnAlarm;
import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.EnumAlarmStatus;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.enumeration.EnumTenantStatus;
import com.persagy.finein.fnmanage.alarm.handler.*;
import com.persagy.finein.service.FNAlarmService;
import com.persagy.finein.service.FNTenantPayTypeService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月21日 下午1:45:10
 * 
 * 说明:
 */
@Component("FNTAlarmProcessTenantHandler")
public class FNTAlarmProcessTenantHandlerImpl extends CoreServiceImpl implements FNTAlarmProcessTenantHandler {

	@Resource(name = "FNAlarmService")
	private FNAlarmService FNAlarmService;

	@Resource(name = "FNTenantPayTypeService")
	private FNTenantPayTypeService FNTenantPayTypeService;

	@Resource(name = "FNTAlarmProcessBuZuHandler")
	private FNTAlarmProcessBuZuHandler FNTAlarmProcessBuZuHandler;

	@Resource(name = "FNTAlarmProcessFuZaiLvGaoHandler")
	private FNTAlarmProcessFuZaiLvGaoHandler FNTAlarmProcessFuZaiLvGaoHandler;

	@Resource(name = "FNTAlarmProcessRemainBuZuHandler")
	private FNTAlarmProcessRemainBuZuHandler FNTAlarmProcessRemainBuZuHandler;
//	
	@Resource(name = "FNTAlarmProcessMeterInterruptHandler")
	private FNTAlarmProcessMeterInterruptHandler FNTAlarmProcessMeterInterruptHandler;

	public void handle(Building building, FnTenant tenant, Map<String, FnAlarmLimitGlobal> globalAlarmLimitMap,Integer dataSpaceSecond)
			throws Exception {
		if (tenant.getStatus().intValue() == EnumTenantStatus.NOT_ACTIVE.getValue().intValue()) {
			return;
		} else if (tenant.getStatus().intValue() == EnumTenantStatus.ACTIVATED.getValue().intValue()) {
			// 查询租户能耗类型
			Map<String, FnTenantPayType> payTypeMap = FNTenantPayTypeService.queryPayTypeMap(tenant.getId());
			// 处理报警类型
			for (Map.Entry<String, FnTenantPayType> entry : payTypeMap.entrySet()) {
				switch (entry.getKey()) {
				case FineinConstant.EnergyType.Dian:
					if (entry.getValue().getPayType().intValue() == EnumPayType.PREPAY.getValue().intValue()) {
						FNTAlarmProcessBuZuHandler.handle(building, tenant, entry.getKey(),
								FineinConstant.AlarmType.DIANFEIYONGBUZU, entry.getValue(), globalAlarmLimitMap);
						if (entry.getValue().getPrepayChargeType().intValue() == EnumPrepayChargeType.Liang.getValue()
								.intValue()) {
							FNTAlarmProcessRemainBuZuHandler.handle(building, tenant, entry.getKey(),
									FineinConstant.AlarmType.DIANSHENGYULIANG, entry.getValue(), globalAlarmLimitMap);
						} else {
							FNTAlarmProcessRemainBuZuHandler.handle(building, tenant, entry.getKey(),
									FineinConstant.AlarmType.DIANSHENGYUJINE, entry.getValue(), globalAlarmLimitMap);
						}
					}
					FNTAlarmProcessFuZaiLvGaoHandler.handle(building, tenant, entry.getKey(),
							FineinConstant.AlarmType.FUHELVGUOGAO, entry.getValue(), globalAlarmLimitMap);
					FNTAlarmProcessMeterInterruptHandler.handle(building, tenant, entry.getKey(),
							FineinConstant.AlarmType.DIANINTERRUPT, entry.getValue(),dataSpaceSecond);
					break;
				case FineinConstant.EnergyType.Shui:
					if (entry.getValue().getPayType().intValue() == EnumPayType.PREPAY.getValue().intValue()) {
						FNTAlarmProcessBuZuHandler.handle(building, tenant, entry.getKey(),
								FineinConstant.AlarmType.SHUIFEIYONGBUZU, entry.getValue(), globalAlarmLimitMap);
						if (entry.getValue().getPrepayChargeType().intValue() == EnumPrepayChargeType.Liang.getValue()
								.intValue()) {
							FNTAlarmProcessRemainBuZuHandler.handle(building, tenant, entry.getKey(),
									FineinConstant.AlarmType.SHUISHENGYULIANG, entry.getValue(), globalAlarmLimitMap);
						} else {
							FNTAlarmProcessRemainBuZuHandler.handle(building, tenant, entry.getKey(),
									FineinConstant.AlarmType.SHUISHENGYUJINE, entry.getValue(), globalAlarmLimitMap);
						}
					}
					FNTAlarmProcessMeterInterruptHandler.handle(building, tenant, entry.getKey(),
							FineinConstant.AlarmType.SHUIINTERRUPT, entry.getValue(),dataSpaceSecond);
					break;
				case FineinConstant.EnergyType.ReShui:
					if (entry.getValue().getPayType().intValue() == EnumPayType.PREPAY.getValue().intValue()) {
						FNTAlarmProcessBuZuHandler.handle(building, tenant, entry.getKey(),
								FineinConstant.AlarmType.RESHUIFEIYONGBUZU, entry.getValue(), globalAlarmLimitMap);
						if (entry.getValue().getPrepayChargeType().intValue() == EnumPrepayChargeType.Liang.getValue()
								.intValue()) {
							FNTAlarmProcessRemainBuZuHandler.handle(building, tenant, entry.getKey(),
									FineinConstant.AlarmType.RESHUISHENGYULIANG, entry.getValue(), globalAlarmLimitMap);
						} else {
							FNTAlarmProcessRemainBuZuHandler.handle(building, tenant, entry.getKey(),
									FineinConstant.AlarmType.RESHUISHENGYUJINE, entry.getValue(), globalAlarmLimitMap);
						}
					}
					FNTAlarmProcessMeterInterruptHandler.handle(building, tenant, entry.getKey(),
							FineinConstant.AlarmType.RESHUIINTERRUPT, entry.getValue(),dataSpaceSecond);
					break;
				case FineinConstant.EnergyType.RanQi:
					if (entry.getValue().getPayType().intValue() == EnumPayType.PREPAY.getValue().intValue()) {
						FNTAlarmProcessBuZuHandler.handle(building, tenant, entry.getKey(),
								FineinConstant.AlarmType.RANQIFEIYONGBUZU, entry.getValue(), globalAlarmLimitMap);
						if (entry.getValue().getPrepayChargeType().intValue() == EnumPrepayChargeType.Liang.getValue()
								.intValue()) {
							FNTAlarmProcessRemainBuZuHandler.handle(building, tenant, entry.getKey(),
									FineinConstant.AlarmType.RANQISHENGYULIANG, entry.getValue(), globalAlarmLimitMap);
						} else {
							FNTAlarmProcessRemainBuZuHandler.handle(building, tenant, entry.getKey(),
									FineinConstant.AlarmType.RANQISHENGYUJINE, entry.getValue(), globalAlarmLimitMap);
						}
					}
					FNTAlarmProcessMeterInterruptHandler.handle(building, tenant, entry.getKey(),
							FineinConstant.AlarmType.RANQIINTERRUPT, entry.getValue(),dataSpaceSecond);
					break;
				default:
					break;
				}
			}
			
		} else if (tenant.getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue().intValue()) {// 将报警置为过期
			// 查询所有报警
			List<FnAlarm> alarmList = FNAlarmService.queryAlarmByTenantId(tenant.getId(), EnumAlarmStatus.WeiHuiHu);
			if (alarmList != null && alarmList.size() > 0) {
				List<String> alarmIdList = new ArrayList<>();
				for (FnAlarm alarm : alarmList) {
					alarmIdList.add(alarm.getId());
				}
				FNAlarmService.updateAlarmStatus(alarmIdList, EnumAlarmStatus.YiGuoQi);
			}
		} else {
			return;
		}
	}
}
