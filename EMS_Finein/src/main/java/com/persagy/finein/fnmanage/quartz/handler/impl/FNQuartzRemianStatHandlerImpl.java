package com.persagy.finein.fnmanage.quartz.handler.impl;

import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.ems.pojo.originaldata.MonthData;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.quartz.handler.FNQuartzProcessJob;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.Map.Entry;

@Service("FNQuartzRemianStatHandler")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNQuartzRemianStatHandlerImpl implements FNQuartzProcessJob {

	private static Logger log = Logger.getLogger(FNQuartzRemianStatHandlerImpl.class);

	@Resource(name = "FNBuildingService")
	private FNBuildingService FNBuildingService;

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;

	@Resource(name = "FNTenantPriceService")
	private FNTenantPriceService FNTenantPriceService;

	@Resource(name = "FNTenantPayTypeService")
	private FNTenantPayTypeService FNTenantPayTypeService;

	@Resource(name = "FNRecordPrePayService")
	private FNRecordPrePayService FNRecordPrePayService;

	@Resource(name = "FNPriceTemplateService")
	private FNPriceTemplateService FNPriceTemplateService;

	@Resource(name = "FNTenantPrePayParamService")
	private FNTenantPrePayParamService FNTenantPrePayParamService;

	@Resource(name = "FNBuildingEnergyRemainStatService")
	private FNBuildingEnergyRemainStatService FNBuildingEnergyRemainStatService;

	@Resource(name = "FNTenantBackPayDataService")
	private FNTenantBackPayDataService FNTenantBackPayDataService;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNOriginalDataService")
	private FNOriginalDataService FNOriginalDataService;

	private final String Qian = "Qian";
	private final String Liang = "Liang";

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void handle() throws Exception {

		List<Building> buildingList = FNBuildingService.queryList(new Building());
		if (buildingList != null && buildingList.size() > 0) {
			for (Building building : buildingList) {
				int count = 0;
				while (count < 20) {
					try {
						processBuilding(building);
						break;
					} catch (Exception e) {
						e.printStackTrace();
						count++;
						Thread.sleep(60 * 1000);
					}
				}
			}
		}
	}

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void processBuilding(Building building) throws Exception {

		FnTenant query = new FnTenant();
		query.setBuildingId(building.getId());
		query.setIsValid(EnumValidStatus.VALID.getValue());
		query.setSpecialOperation("status", SpecialOperator.$ne, EnumTenantStatus.RETURNED_LEASE.getValue().intValue());
		List<FnTenant> tenantList = FNTenantService.query(query);
		if (tenantList == null || tenantList.size() == 0) {
			return;
		}
		Map<String, Map<String, FnTenantPayType>> payTypeMap = FNTenantPayTypeService
				.queryByBuildingId(building.getId());
		Map<String, Map<String, FnPriceTemplate>> priceTemplateMap = FNPriceTemplateService.queryMapByTenant();
		Date today = DateUtils.truncate(new Date(), Calendar.DATE);
		Map<String, Map<String, Double>> zongMap = new HashMap<String, Map<String, Double>>();
		for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
			Map<String, Double> paramMap = new HashMap<String, Double>();
			for (FnTenant fnTenant : tenantList) {
				processTenant(building, payTypeMap, priceTemplateMap, energyType, paramMap, fnTenant, today);
			}
			if (paramMap.size() > 0) {
				zongMap.put(energyType.getId(), paramMap);
			}
		}
		if (zongMap.size() > 0) {
			List<FnBuildingEnergyRemainStat> saveList = new ArrayList<FnBuildingEnergyRemainStat>();
			for (Entry<String, Map<String, Double>> entry : zongMap.entrySet()) {

				FnBuildingEnergyRemainStat save = new FnBuildingEnergyRemainStat();
				save.setBuildingId(building.getId());
				save.setEnergyTypeId(entry.getKey());
				save.setTimeFrom(DateUtils.addDays(today, -1));
				FNBuildingEnergyRemainStatService.remove(save);
				save.setRemainAmount(entry.getValue().get(Liang));
				save.setRemainMoney(entry.getValue().get(Qian));
				save.setMoneyToAmount(entry.getValue().get("moneyToAmount"));
				save.setAmountToMoney(entry.getValue().get("amountToMoney"));
				save.setLastUpdateTime(new Date());
				saveList.add(save);
			}
			FNBuildingEnergyRemainStatService.save(saveList);
		}
	}

	@SuppressWarnings("unused")
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void processTenant(Building building, Map<String, Map<String, FnTenantPayType>> payTypeMap,
			Map<String, Map<String, FnPriceTemplate>> priceTemplateMap, FnEnergyType energyType,
			Map<String, Double> paramMap, FnTenant fnTenant, Date today) throws Exception {
		Map<String, FnTenantPayType> tenantPayTypeMap = payTypeMap.get(fnTenant.getId());
		if (tenantPayTypeMap == null) {
			return;
		}
		FnTenantPayType tenantPayType = tenantPayTypeMap.get(energyType.getId());
		if (tenantPayType == null || tenantPayType.getPayType() == EnumPayType.POSTPAY.getValue().intValue()) {
			return;
		}
		{// 统计剩余
			Map<String, FnPriceTemplate> priceMap = priceTemplateMap.get(fnTenant.getId());
			if (priceMap == null) {
				return;
			}
			FnPriceTemplate tenantPrice = priceMap.get(energyType.getId());
			if (tenantPrice == null) {
				return;
			}
			Double remainData = null;

			if (tenantPayType.getPayType().intValue() == EnumPayType.POSTPAY.getValue().intValue()) {
				return;// 后付费不计算
			}
			if (tenantPayType.getPrePayType().intValue() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {// 租户
				if (tenantPayType.getPrepayChargeType().intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {// 充钱
					FnTenantBackPayData lastData = FNTenantBackPayDataService.queryLastData(building.getId(),
							fnTenant.getId(), energyType.getId(), EnumTimeType.T0, EnumPrepayChargeType.Qian, null);
					if (lastData != null && lastData.getData() != null) {
						remainData = lastData.getData();
					}
				} else {
					FnTenantBackPayData lastData = FNTenantBackPayDataService.queryLastData(building.getId(),
							fnTenant.getId(), energyType.getId(), EnumTimeType.T0, EnumPrepayChargeType.Liang, null);
					if (lastData != null && lastData.getData() != null) {
						remainData = lastData.getData();
					}
				}
			} else {
				// 查询租户下的仪表
				List<DTOMeter> meterList = FNMeterService.queryMeterList(fnTenant.getId(), energyType.getId());
				// System.out.println("***********查询仪表:" +
				// meterList.size()+"_"+meterList.get(0).getMeterId());
				// 查询最后数据
				Integer functionId = null;
				if (tenantPayType.getPrepayChargeType() != null && tenantPayType.getPrepayChargeType()
						.intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
					functionId = FunctionTypeUtil.getShengYuJinEFunctionId(energyType.getId());
				} else if (tenantPayType.getPrepayChargeType() != null && tenantPayType.getPrepayChargeType()
						.intValue() == EnumPrepayChargeType.Liang.getValue().intValue()) {
					functionId = FunctionTypeUtil.getShengYuLiangFunctionId(energyType.getId());
				}
				if (functionId == null) {
					return;
				}
				if (meterList != null && meterList.size() > 0) {
					for (DTOMeter meter : meterList) {
						Double radio = 1.0;
						try {
							Long syjIsCt = (Long) ((JSONObject) JSONValue.parse(meter.getExtend())).get("syjIsCt");
							if (syjIsCt != null && syjIsCt.intValue() == 1) {
								radio = meter.getRadio();
							}
						} catch (Exception e) {
						}
						Integer meterMaxDays = null;
						Integer meterMinDays = null;
						// 查询仪表最后数据
						MonthData monthData = FNOriginalDataService.queryLastMonthDataGteLte(meter.getMeterId(),
								functionId, DateUtils.addMinutes(today, -30), today);
						Double lastData = null;
						if (monthData != null && monthData.getData() != null) {
							lastData = monthData.getData() * radio;
							remainData = remainData == null ? lastData : remainData + lastData;
							// log.error("*****tenant:" + fnTenant.getId() +
							// ",meterId:" + meter.getMeterId() + ","
							// + "remainData:" + remainData);
						}
					}
				}
			}
			if (remainData != null) {
				if (tenantPayType.getPrepayChargeType() != null && tenantPayType.getPrepayChargeType()
						.intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
					paramMap.put(Qian, paramMap.get(Qian) == null ? remainData : paramMap.get(Qian) + remainData);
					// log.error("*****tenant:" + fnTenant.getId() +
					// ",paramMap:" + paramMap.get(Qian));
					if (EnumPriceType.AVG.getValue().intValue() == tenantPrice.getType()) {
						paramMap.put("moneyToAmount",
								paramMap.get("moneyToAmount") == null
										? div(remainData, Double.valueOf(tenantPrice.getContent()), 8)
										: paramMap.get("moneyToAmount")
												+ div(remainData, Double.valueOf(tenantPrice.getContent()), 8));
					}

				} else if (tenantPayType.getPrepayChargeType() != null && tenantPayType.getPrepayChargeType()
						.intValue() == EnumPrepayChargeType.Liang.getValue().intValue()) {
					paramMap.put(Liang, paramMap.get(Liang) == null ? remainData : paramMap.get(Liang) + remainData);
					if (EnumPriceType.AVG.getValue().intValue() == tenantPrice.getType()) {
						paramMap.put("amountToMoney",
								paramMap.get("amountToMoney") == null
										? remainData * Double.valueOf(tenantPrice.getContent())
										: paramMap.get("amountToMoney")
												+ remainData * Double.valueOf(tenantPrice.getContent()));
					}
				}
			}
		}
	}

	public Double div(double d1, double d2, int len) {// 进行除法运算
		BigDecimal b1 = new BigDecimal(d1);
		BigDecimal b2 = new BigDecimal(d2);
		return b1.divide(b2, len, BigDecimal.ROUND_HALF_UP).doubleValue();
	}

}
