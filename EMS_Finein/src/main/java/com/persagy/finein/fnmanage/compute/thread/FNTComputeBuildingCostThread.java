package com.persagy.finein.fnmanage.compute.thread;

import com.persagy.core.thread.BaseThread;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.fnmanage.compute.handler.FNTComputeBuildingEnergyCostHandler;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
* 说明:项目成本统计
*/
@Component("FNTComputeBuildingCostThread")
public class FNTComputeBuildingCostThread extends BaseThread{

	private static boolean CONSTANT_THREAD_IS_OPEN = true;
	private static long CONSTANT_SLEEP = 60*30;
	
	private static Logger log = Logger.getLogger(FNTComputeBuildingCostThread.class);
	
	private static boolean IsSysParamValueInited = false;
	
	@Resource(name = "FNTComputeBuildingEnergyCostHandler")
	private FNTComputeBuildingEnergyCostHandler FNTComputeBuildingEnergyCostHandler;
	
	@Override
	protected void business() throws Exception {
		try {
			this.initSysParamValue();
			
			if(!CONSTANT_THREAD_IS_OPEN){
				this.setStop(true);
				log.error("【租户管理】租户计算线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}
			
			this.FNTComputeBuildingEnergyCostHandler.process();
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}
	}
	
	private void initSysParamValue(){
		if(IsSysParamValueInited){
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_ComputeBuildingCostThreadIsOpen);
			if(threadIsOpen != null){
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Long sleep = (Long)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_ComputeBuildingCostThreadSleepSecond);
			if(sleep != null){
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}
		
		IsSysParamValueInited = true;
		log.error("【租户管理】项目统计能耗成本线程开始运行。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}
	
}

