package com.persagy.finein.fnmanage.alarm.thread;

import com.persagy.core.thread.BaseThread;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.fnmanage.alarm.handler.FNTAlarmProcessBuildingHandler;
import com.persagy.finein.service.FNAlarmLimitGlobalService;
import com.persagy.finein.service.FNBuildingService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月21日 下午12:58:25

* 说明:租户管理报警线程
*/
@Component("FNTAlarmThread")
public class FNTAlarmThread extends BaseThread{

	private static boolean CONSTANT_THREAD_IS_OPEN = true;
	private static int CONSTANT_SLEEP = 30;
	
	private static Logger log = Logger.getLogger(FNTAlarmThread.class);
	
	@Resource(name = "FNBuildingService")
	private FNBuildingService FNBuildingService;

	@Resource(name = "FNTAlarmProcessBuildingHandler")
	private FNTAlarmProcessBuildingHandler FNTAlarmProcessBuildingHandler;
	
	@Resource(name = "FNAlarmLimitGlobalService")
	private FNAlarmLimitGlobalService FNAlarmLimitGlobalService;

	private static boolean IsSysParamValueInited = false;
	
	private static Integer DataSpaceSecond=60*30;
	
	
	@Override
	protected void business() throws Exception {
		
		this.initSysParamValue();
		
		try {
			if(!CONSTANT_THREAD_IS_OPEN){
				this.setStop(true);
				log.error("【租户管理】租户报警线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}
			
			this.process();
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}
	}
	
	private void initSysParamValue(){
		if(IsSysParamValueInited){
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_AlarmThreadIsOpen);
			if(threadIsOpen != null){
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Integer sleep = (Integer)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_AlarmThreadSleepSecond);
			if(sleep != null){
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}
		
		try {
			Integer spaceSecond = (Integer)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_AlarmDataSpaceSecond);
			if(spaceSecond != null){
				DataSpaceSecond = spaceSecond;
			}
		} catch (Exception e1) {
		}
		
		IsSysParamValueInited = true;
		log.error("【租户管理】租户报警线程开始运行。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}
	
	private void process(){
		try {
			//1.查询所有建筑
			List<Building> buildingList = FNBuildingService.queryList(new Building());
			if(buildingList != null && buildingList.size() > 0){
				//查询全局报警设置
				Map<String,FnAlarmLimitGlobal> globalAlarmLimitMap = FNAlarmLimitGlobalService.queryMap();
				for(Building building : buildingList){
					this.FNTAlarmProcessBuildingHandler.handle(building,globalAlarmLimitMap,DataSpaceSecond);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
}

