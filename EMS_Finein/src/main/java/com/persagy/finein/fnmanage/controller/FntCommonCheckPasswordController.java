package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.MD5Tools;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNUserService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理
 * 接口名：租户管理-验证当前用户登录密码
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntCommonCheckPasswordController extends BaseController {

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @RequestMapping("FNTCommonCheckPasswordService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonCheckPassword(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String userId = this.getParamUserId(dto);
            String password = (String) dto.get("password");
            if (userId == null || password == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            String md5 = MD5Tools.MD5(password);
            String queryPassWord = fnUserService.queryUserPassWordByUserId(userId);
            Map<String, Object> map = new HashMap<String, Object>();
            if (md5.equals(queryPassWord)) {
                map.put("status", EnumYesNo.YES.getValue());
            } else {
                map.put("status", EnumYesNo.NO.getValue());
            }
            content.add(map);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTCommonCheckPasswordService");
        }
    }


}
