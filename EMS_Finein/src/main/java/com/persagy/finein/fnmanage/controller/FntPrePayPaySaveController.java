package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;


/**
 * 项目名：租户管理 接口名：预付费软件充表扣-保存
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntPrePayPaySaveController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNOrderIdService")
    private FNOrderIdService fnOrderIdService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @Resource(name = "FNRecordPrePayService")
    private FNRecordPrePayService fnRecordPrePayService;

    @Resource(name = "FNBeiDianReturnRecordService")
    private FNBeiDianReturnRecordService fnBeiDianReturnRecordService;

    @Resource(name = "FNRecordPrePayExtendService")
    private FNRecordPrePayExtendService fnRecordPrePayExtendService;

    @Resource(name = "FNChargeMessageService")
    private FNChargeMessageService fnChargeMessageService;

    public static Map<String, Date> timeMap = new HashMap<String, Date>();



    @RequestMapping("FNTPrePayPaySaveService")
    @ResponseBody
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult prePayPaySave(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");
            String meterId = (String) dto.get("meterId");
            String energyTypeId = (String) dto.get("energyTypeId");
            Integer prePayType = (Integer) dto.get("prePayType");
            Integer billingType = (Integer) dto.get("billingType");
            Double value = DoubleFormatUtil.Instance().getDoubleData(dto.get("value"));
            Double remainData = DoubleFormatUtil.Instance().getDoubleData(dto.get("remainData"));
            Double deductData = DoubleFormatUtil.Instance().getDoubleData(dto.get("deductData"));
            String userId = this.getParamUserId(dto);
            if (tenantId == null || energyTypeId == null || prePayType == null || value == null || billingType == null
                    || userId == null || remainData == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Map<String, Object> contentObj = new HashMap<String, Object>();
            if (prePayType.intValue() == EnumPrePayType.ONLINE_METERPAY.getValue().intValue()) {
                if (meterId == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull("meterId"));
                }
                this.processMeterPay(contentObj, tenantId, meterId, energyTypeId, billingType, value, userId, remainData,
                        deductData);
            } else {
                throw new Exception("不知道的prePayType:" + prePayType);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPrePayPaySaveService");
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void processMeterPay(Map<String, Object> contentObj, String tenantId, String meterId, String energyTypeId,
                                 Integer billingType, Double value, String userId, Double remainData, Double deductData) throws Exception {
        // 查询租户
        FnTenant fnTenant = fnTenantService.queryOne(tenantId);
        if (fnTenant == null) {
            throw new Exception("租户不存在:" + tenantId);
        }

        Building building = fnBuildingService.query(fnTenant.getBuildingId());
        if (building == null) {
            throw new Exception("租户所属建筑不存在:" + fnTenant.getBuildingId());
        }

        Date now = new Date();
        Date lastPayTime = timeMap.get(tenantId + "_" + meterId);
        if ((now.getTime() - (lastPayTime == null ? 0 : lastPayTime.getTime())) <= 30000) {// 时间少于3秒
            timeMap.put(tenantId + "_" + meterId, now);
            return;
        }
        contentObj.put("result", 0);
        FnMeter fnMeter = fnMeterService.queryMeterById(meterId);
        if (fnMeter == null) {
            throw new Exception("仪表不存在:" + meterId);
        }
        if (fnMeter.getProtocolId().equals("DI_C_01_Y_Q_001")) {
            FnBeiDianReturnRecord query = new FnBeiDianReturnRecord();
            query.setMeterId(fnMeter.getId());
            List<FnBeiDianReturnRecord> list = fnBeiDianReturnRecordService.query(query);
            if (list != null && list.size() > 0) {
                FnBeiDianReturnRecord returnRecord = list.get(0);
                FnBeiDianReturnRecord update = new FnBeiDianReturnRecord();
                Double money = returnRecord.getMoney() - deductData;
                if (money < 0) {
                    return;
                }
                update.setMoney(money);
                update.setUpdateTime(new Date());
                fnBeiDianReturnRecordService.update(returnRecord, update);
            }
        }

        FnRecordPrePay saveObj = new FnRecordPrePay();
        String id = UUID.randomUUID().toString();
        saveObj.setId(id);
        saveObj.setBuildingId(fnTenant.getBuildingId());
        saveObj.setBuildingName(building.getName());
        saveObj.setType(EnumPayBodyType.METER.getValue());
        saveObj.setTenantId(tenantId);
        saveObj.setTenantName(fnTenant.getName());
        saveObj.setCode(meterId);
        saveObj.setName(fnMeter.getInstallAddress());
        saveObj.setEnergyTypeId(energyTypeId);

        String orderId = fnOrderIdService.queryOrderId(EnumPayType.PREPAY.getValue(), now);
        saveObj.setOrderId(orderId);
        if (billingType.intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
            saveObj.setMoney(value);
        } else if (billingType.intValue() == EnumPrepayChargeType.Liang.getValue().intValue()) {
            saveObj.setAmount(value);
        } else {
            throw new Exception("未知的billingType:" + billingType);
        }
        String unit = UnitUtil.getCumulantUnit(energyTypeId);
        saveObj.setAmountUnit(unit);
        saveObj.setOperateTime(now);
        saveObj.setUserId(userId);
        DTOUser user = fnUserService.queryUserByUserId(userId);
        saveObj.setUserId(userId);
        saveObj.setUserName(user == null ? null : user.getUserName());
        saveObj.setCreateTime(now);

        fnRecordPrePayService.save(saveObj);
        {
            // 保存账单扩展字段
            FnOrderExtend save = new FnOrderExtend();
            save.setOrderId(orderId);
            save.setTenantId(tenantId);
            save.setOperateType(EnumPrePayOrReturn.PREPAY.getValue());
            save.setData(value);
            save.setRemainData(remainData);
            save.setChargeType(billingType);
            save.setOperateTime(now);
            fnOrderIdService.save(save);
        }

        {// 充值记录扩展字段
            FnRecordPrePayExtend save = new FnRecordPrePayExtend();
            save.setId(id);
            save.setPrePaySystemCode("Local");
            save.setSource(EnumPrePaySource.LOCAL.getValue());
            fnRecordPrePayExtendService.save(save);
        }
        {// 发送充值记录
            if ((boolean) ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendPayMessage)) {
                String perPaySuccessTemplate = (String) ConstantDBBaseData.SysParamValueMap
                        .get(FineinConstant.SysParamValueKey.Id_PerPaySuccessTemplate);
                String messageSignature = (String) ConstantDBBaseData.SysParamValueMap
                        .get(FineinConstant.SysParamValueKey.Id_MessageSignature);
                fnChargeMessageService.paySendMessage(saveObj, remainData, fnTenant, perPaySuccessTemplate,
                        messageSignature);
            }
        }
        timeMap.put(tenantId + "_" + meterId, now);
    }

}
