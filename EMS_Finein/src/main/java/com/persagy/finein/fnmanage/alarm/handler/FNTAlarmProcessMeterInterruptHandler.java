package com.persagy.finein.fnmanage.alarm.handler;

import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.dictionary.Building;

/**
 * 时间:2017年10月21日 下午1:45:10 说明:剩余不足
 */
public interface FNTAlarmProcessMeterInterruptHandler {

	public void handle(Building building, FnTenant tenant, String energyTypeId, String alarmTypeId,
                       FnTenantPayType tenantPayType, Integer dataSpaceSecond) throws Exception;

	public void alarm(Building building, FnTenant tenant, String energyTypeId, String alarmTypeId, DTOMeter meterId,
                      Integer interruptTime) throws Exception;
}
