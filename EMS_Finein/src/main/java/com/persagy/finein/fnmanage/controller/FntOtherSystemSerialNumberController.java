package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnCommonPrePaySystemParam;
import com.persagy.finein.service.FNCommonPrePaySystemParamService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：其他系统生成序列号
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntOtherSystemSerialNumberController extends BaseController {

    @Resource(name = "FNCommonPrePaySystemParamService")
    private FNCommonPrePaySystemParamService fnCommonPrePaySystemParamService;

    @RequestMapping("FNTOtherSystemSerialNumberService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult otherSystemSerialNumber(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String systemCode = (String)dto.get("systemCode");

            if(systemCode == null ){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            //验证秘钥
//	   if(!FNCommonPrePaySystemParamService.checkEncryptionKey(systemCode,MD5Tools.MD5(encryptionKey))){
//		   throw new Exception("秘钥验证错误");
//	   }

            Map<String,Object> contentObj = new HashMap<String,Object>();
//		stringBuffer.append(encryptionKey).append("_").append(new Date().getTime());
//		String serialNumber = AESUtil.encrypt(stringBuffer.toString());

            //生成序列号
            String sequence = UUID.randomUUID().toString();
            //查询密钥
            FnCommonPrePaySystemParam FNCommonPrePaySystemParam= fnCommonPrePaySystemParamService.queryByCode(systemCode);
            if(FNCommonPrePaySystemParam==null){
                throw new Exception("系统编码查询无效:"+systemCode);
            }
            StringBuffer stringBuffer = new StringBuffer();
            stringBuffer.append(FNCommonPrePaySystemParam.getEncryptionKey()).append("_").append(sequence).append("_").append(new Date().getTime());
            FineinConstant.systemSerialNumberMap.put(systemCode, stringBuffer.toString());
            contentObj.put("sequence", sequence);
            content.add(contentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTOtherSystemSerialNumberService");
        }
    }


}
