package com.persagy.finein.fnmanage.quartz;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.persagy.core.constant.SystemConstant;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.communication.exception.MeterSetException;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @Author: ls
 * @Date: 2022/2/16 16:29
 */
@Component("TimingBatchUpdatePrice")
public class TimingBatchUpdatePrice {

    private static Logger log = Logger.getLogger(TimingBatchUpdatePrice.class);

    @Resource(name = "FNTimingUpdatePriceService")
    private FNTimingUpdatePriceService fnTimingUpdatePriceService;

    @Resource(name = "FNTimingUpdatePriceRecordService")
    private FNTimingUpdatePriceRecordService fnTimingUpdatePriceRecordService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNPriceTemplateService")
    private FNPriceTemplateService fnPriceTemplateService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantEnergySplitService")
    private FNTenantEnergySplitService fnTenantEnergySplitService;

    @Resource(name = "FNTenantPriceService")
    private FNTenantPriceService fnTenantPriceService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNRecordPriceChangeService")
    private FNRecordPriceChangeService fnRecordPriceChangeService;

    @Resource(name = "FNRecordMeterSetService")
    private FNRecordMeterSetService fnRecordMeterSetService;

    public void updatePrice() {
        Runnable my = new Runnable() {
            @Override
            public void run() {
                try {
                    String energyTypeId = "Dian";
                    FnTimingUpdatePrice query = new FnTimingUpdatePrice();
                    query.setEnergyTypeId(energyTypeId);
                    query.setIsValid(EnumValidStatus.VALID.getValue());
                    List<FnTimingUpdatePrice> timingUpdatePriceList = fnTimingUpdatePriceService.query(query);
                    if (timingUpdatePriceList.size() > 0) {
                        query = timingUpdatePriceList.get(0);
                        Map<String, Object> map = new HashMap<>();
                        DTOUser user = new DTOUser();
                        user.setUserId("system");
                        user.setUserName("system");
                        FnPriceTemplate fnPriceTemplate = fnPriceTemplateService.query(query.getPriceTemplateId());
                        if (fnPriceTemplate.getType().intValue() == EnumPriceType.TOU.getValue()) {
                            //修改
                            FnTimingUpdatePrice timingUpdatePrice = new FnTimingUpdatePrice();
                            timingUpdatePrice.setIsValid(EnumValidStatus.INVALID.getValue());
                            fnTimingUpdatePriceService.update(query, timingUpdatePrice);
                            //电价
                            ObjectMapper objectMapper = new ObjectMapper();
                            List<Map<String, Object>> priceList = objectMapper.readValue(fnPriceTemplate.getContent(), new TypeReference<List<Map<String, Object>>>() {
                            });
                            StringBuilder content = new StringBuilder();
                            content.append(fnPriceTemplate.getName());
                            content.append(" : ");
                            Map<String, Double> price = new HashMap<>();
                            for (Map<String, Object> priceMap : priceList) {
                                price.put((String) priceMap.get("type"), DoubleFormatUtil.Instance().getDoubleData(priceMap.get("value")));
                                content.append(EnumPriceDetail.parse((String) priceMap.get("type")).getView()).append(DoubleFormatUtil.Instance().getDoubleData(priceMap.get("value"))).append("元;");
                            }
                            Map<String, Object> map1 = new HashMap<>();
                            map1.put("value", fnPriceTemplate.getName());
                            String extend = SystemConstant.jsonMapper.writeValueAsString(map1);
                            List<FnTenant> tenantList = fnTenantService.queryTenantList();
                            List<String> tenantIdList = new ArrayList<>();
                            for (FnTenant t : tenantList) {
                                tenantIdList.add(t.getId());
                            }

                            Map<String, Map<String, Object>> tenantPriceMapMap = fnTenantPriceService.queryPriceMapByTenantIds(energyTypeId, tenantIdList);
                            Map<String, Building> buildingMap = fnBuildingService.query();
                            Map<String, FnTenantEnergySplit> splitMap = fnTenantEnergySplitService.queryEnergySplit(tenantIdList, energyTypeId);
                            Map<String, List<FnMeter>> tenantMeterListMap = fnMeterService.queryAllMeterList(tenantIdList, energyTypeId);
                            for (FnTenant fnTenant : tenantList) {
                                if (splitMap.containsKey(fnTenant.getId())) {
                                    continue;
                                }
                                List<FnMeter> meterList = tenantMeterListMap.get(fnTenant.getId());
                                if (meterList == null || meterList.size() == 0) {
                                    continue;
                                }
                                for (FnMeter meter : meterList) {
                                    if (!meter.getMeterType().equals(EnumMeterType.Multiple.getValue()) || !meter.getPayType().equals(EnumPayType.PREPAY.getValue())) {
                                        continue;
                                    }
                                    //批量修改电价日志记录
                                    FnTimingUpdatePriceRecord priceRecord = new FnTimingUpdatePriceRecord();
                                    priceRecord.setId(UUID.randomUUID().toString());
                                    priceRecord.setTenantId(fnTenant.getId());
                                    priceRecord.setTenantName(fnTenant.getName());
                                    priceRecord.setMeterId(meter.getId());
                                    priceRecord.setUpdateTime(new Date());
                                    priceRecord.setContent(content.toString());
                                    boolean flag = false;
                                    Map<String, Double> batchPrice = null;
                                    ICommunication communication = (ICommunication) SystemConstant.context.getBean(meter.getProtocolId());
                                    if (communication == null) {
                                        continue;
                                    }
                                    try {
                                        flag = communication.settingBatchPrice(meter, price, map);
                                        Thread.sleep(500);
                                    } catch (Exception | MeterSetException e) {
                                        log.error(meter.getId() + "修改电价失败###########");
                                    }
                                    try {
                                        batchPrice = communication.getBatchPrice(meter);
                                    } catch (Exception | MeterSetException e) {
                                        log.error(meter.getId() + "查询电价失败###########");
                                    }
                                    if (batchPrice != null) {
                                        flag = batchPrice.get(EnumPriceDetail.J.getValue()).equals(price.get(EnumPriceDetail.J.getValue()))
                                                && batchPrice.get(EnumPriceDetail.F.getValue()).equals(price.get(EnumPriceDetail.F.getValue()))
                                                && batchPrice.get(EnumPriceDetail.P.getValue()).equals(price.get(EnumPriceDetail.P.getValue()))
                                                && batchPrice.get(EnumPriceDetail.G.getValue()).equals(price.get(EnumPriceDetail.G.getValue()));
                                    }
                                    if (flag) {
                                        priceRecord.setResult(EnumResult.SUCCESS.getValue());
                                        String buffer = EnumPriceDetail.J.getView() + "峰:" + batchPrice.get(EnumPriceDetail.J.getValue()) + "元 " +
                                                "高" + EnumPriceDetail.F.getView() + ":" + batchPrice.get(EnumPriceDetail.F.getValue()) + "元 " +
                                                EnumPriceDetail.P.getView() + "段:" + batchPrice.get(EnumPriceDetail.J.getValue()) + "元 " +
                                                "低" + EnumPriceDetail.G.getView() + ":" + batchPrice.get(EnumPriceDetail.F.getValue()) + "元";
                                        priceRecord.setAfterPrice(buffer);
                                        {
                                            //保存仪表设置记录
                                            FnRecordMeterSet save = new FnRecordMeterSet();
                                            save.setId(UUID.randomUUID().toString());
                                            save.setTenantId(fnTenant.getId());
                                            save.setMeterId(meter.getId());
                                            save.setRoomCode(meter.getInstallAddress());
                                            save.setEnergyTypeId(meter.getEnergyTypeId());
                                            save.setOperateType(EnumMeterSetType.UPDATEPRICE.getValue());
                                            save.setCreateTime(new Date());
                                            save.setUserId(user.getUserId());
                                            save.setUserName(user.getUserName());
                                            save.setExtend(extend);
                                            fnRecordMeterSetService.save(save);
                                        }
                                        //价格方案变更记录
                                        {
                                            FnRecordPriceChange recordPriceChange = new FnRecordPriceChange();
                                            recordPriceChange.setId(UUID.randomUUID().toString());
                                            recordPriceChange.setBuildingId(fnTenant.getBuildingId());
                                            String oldPriceId = null;
                                            String oldPriceName = null;
                                            Map<String, Object> tenantPriceMap = tenantPriceMapMap.get(fnTenant.getId());
                                            if (tenantPriceMap != null) {
                                                oldPriceId = (String) tenantPriceMap.get("c_price_template_id");
                                                oldPriceName = (String) tenantPriceMap.get("c_name");
                                            }
                                            Date date = new Date();
                                            recordPriceChange.setBuildingName(buildingMap.get(fnTenant.getBuildingId()) == null ? "" : buildingMap.get(fnTenant.getBuildingId()).getName());
                                            recordPriceChange.setTenantId(fnTenant.getId());
                                            recordPriceChange.setEnergyTypeId(energyTypeId);
                                            recordPriceChange.setTenantName(fnTenant.getName());
                                            recordPriceChange.setChangeTime(date);
                                            recordPriceChange.setBeforePriceId(oldPriceId);
                                            recordPriceChange.setBeforePriceName(oldPriceName);
                                            recordPriceChange.setAfterPriceId(fnPriceTemplate.getId());
                                            recordPriceChange.setAfterPriceName(fnPriceTemplate.getName());
                                            recordPriceChange.setUserId(user.getUserId());
                                            recordPriceChange.setUserName(user.getUserName());
                                            recordPriceChange.setRoomIds(fnTenant.getRoomCodes());
                                            recordPriceChange.setCreateTime(date);
                                            fnRecordPriceChangeService.save(recordPriceChange);
                                        }
                                        //更新租户价格
                                        {
                                            FnTenantPrice queryTenantPrice = new FnTenantPrice();
                                            queryTenantPrice.setEnergyTypeId(energyTypeId);
                                            queryTenantPrice.setTenantId(fnTenant.getId());
                                            FnTenantPrice update = new FnTenantPrice();
                                            update.setPriceTemplateId(fnPriceTemplate.getId());
                                            update.setLastUpdateTime(new Date());
                                            update.setLastUpdateUserId(user.getUserId());
                                            fnTenantPriceService.update(queryTenantPrice, update);
                                        }
                                    } else {
                                        priceRecord.setResult(EnumResult.FAIL.getValue());
                                    }
                                    fnTimingUpdatePriceRecordService.save(priceRecord);
                                }
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        };
        Thread thread = new Thread(my);
        thread.start();

    }


}
