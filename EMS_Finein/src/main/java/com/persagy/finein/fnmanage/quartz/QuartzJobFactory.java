package com.persagy.finein.fnmanage.quartz;


import com.persagy.core.constant.SystemConstant;
import com.persagy.core.utils.CommonUtils;
import com.persagy.ems.pojo.finein.FnScheduleJob;
import com.persagy.finein.fnmanage.quartz.handler.FNQuartzProcessJob;
import org.apache.log4j.Logger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;


//定时发送短信提醒
@DisallowConcurrentExecution
public class QuartzJobFactory implements Job{
	
	private static Logger log = Logger.getLogger(QuartzJobFactory.class);
	
	
	@Override
	public void execute(JobExecutionContext context) throws JobExecutionException {
		
		try {
//			FNQuartzProcessAlarmHandler bean = (FNQuartzProcessAlarmHandler) SystemConstant.context.getBean("FNQuartzProcessAlarmHandler");
//			bean.handle();
			FnScheduleJob scheduleJob = (FnScheduleJob) context.getMergedJobDataMap().get("scheduleJob");
			log.error("【租户管理】"+scheduleJob.getJobName()+"开始执行............");
			FNQuartzProcessJob quartzProcessJob = (FNQuartzProcessJob) SystemConstant.context.getBean(scheduleJob.getJobGroup());
			quartzProcessJob.handle();
		} catch (Exception e) {
			e.printStackTrace();
			log.error(CommonUtils.getExceptionStackTrace(e));
		}
	}
}
