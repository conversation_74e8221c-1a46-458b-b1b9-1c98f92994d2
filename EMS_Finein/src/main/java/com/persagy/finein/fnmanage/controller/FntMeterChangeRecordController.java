package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnEnergyType;
import com.persagy.ems.pojo.finein.FnRecordMeterChange;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNRecordMeterChangeService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理
 * 接口名：租户管理-换表记录
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntMeterChangeRecordController extends BaseController {

    @Resource(name = "FNRecordMeterChangeService")
    private FNRecordMeterChangeService fnRecordMeterChangeService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @RequestMapping("FNTMeterChangeRecordService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult meterChangeRecord(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");

            if (tenantId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            List<DTOMeter> meterList = fnMeterService.queryMeterList(tenantId);
            if(meterList != null && meterList.size() > 0){
                List<String> meterIdList = new ArrayList<String>();
                for(DTOMeter dtoMeter : meterList){
                    meterIdList.add(dtoMeter.getMeterId());
                }

                Map<String, FnEnergyType> energyTypeMap = new HashMap<String,FnEnergyType>();
                for(FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList){
                    energyTypeMap.put(energyType.getId(), energyType);
                }

                FnRecordMeterChange query = new FnRecordMeterChange();
                query.setSpecialOperation("meterId", SpecialOperator.$in, meterIdList);
                query.setSort("changeTime", EMSOrder.Desc);

                List<FnRecordMeterChange> dataList = fnRecordMeterChangeService.query(query);
                List<Map<String,Object>> resultList = new ArrayList<Map<String,Object>>();
                if(dataList != null){
                    for(FnRecordMeterChange record : dataList){
                        Map<String,Object> dataMap = new HashMap<String,Object>();
                        dataMap.put("changeTime", standard.format(record.getChangeTime()));
                        dataMap.put("meterId", record.getMeterId());
                        dataMap.put("energyTypeId", record.getEnergyTypeId());
                        dataMap.put("energyTypeName", energyTypeMap.get(record.getEnergyTypeId()) == null ? "" : energyTypeMap.get(record.getEnergyTypeId()).getName());
                        dataMap.put("userId",record.getUserId());
                        dataMap.put("userName",record.getUserName());
                        resultList.add(dataMap);
                    }
                }
                content.addAll(resultList);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTMeterChangeRecordService");
        }
    }


}
