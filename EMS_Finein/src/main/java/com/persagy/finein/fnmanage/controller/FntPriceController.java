package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnPriceTemplate;
import com.persagy.ems.pojo.finein.FnTenantPrice;
import com.persagy.finein.enumeration.EnumPriceType;
import com.persagy.finein.service.FNPriceTemplateService;
import com.persagy.finein.service.FNTenantPriceService;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 价格方案
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntPriceController extends BaseController {

    @Resource(name = "FNPriceTemplateService")
    private FNPriceTemplateService fnPriceTemplateService;

    @Resource(name = "FNTenantPriceService")
    private FNTenantPriceService fnTenantPriceService;

    /**
     * 价格方案 -- 添加
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTPriceAddService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult priceAdd(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String)dto.get("energyTypeId");
            String name = (String)dto.get("name");
            String userId = this.getParamUserId(dto);
            Integer type = (Integer)dto.get("type");//0 平均 1 分时

            if(name == null
                    || energyTypeId == null
                    || userId == null
                    || type == null
                    || dto.get("content") == null
            ){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            if(type.intValue() == EnumPriceType.AVG.getValue()){
                FnPriceTemplate saveObj = new FnPriceTemplate();
                saveObj.setId(UUID.randomUUID().toString());
                saveObj.setEnergyTypeId(energyTypeId);
                saveObj.setName(name);
                saveObj.setCreateUserId(userId);
                saveObj.setCreateTime(new Date());
                saveObj.setType(type);
                saveObj.setContent(dto.get("content")+"");
                saveObj.setLastUpdateTime(new Date());
                saveObj.setLastUpdateUserId(userId);
                saveObj.setIsValid(1);

                fnPriceTemplateService.save(saveObj);

            }else if(type.intValue() == EnumPriceType.TOU.getValue()){
                FnPriceTemplate saveObj = new FnPriceTemplate();
                saveObj.setId(UUID.randomUUID().toString());
                saveObj.setEnergyTypeId(energyTypeId);
                saveObj.setName(name);
                saveObj.setCreateUserId(userId);
                saveObj.setCreateTime(new Date());
                saveObj.setType(type);

                List<Object> list = (List<Object>)dto.get("content");
                JSONArray array = new JSONArray();
                for(Object obj : list){
                    Map<String,Object> map = (Map<String,Object>)obj;
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("type", map.get("type"));
                    jsonObject.put("value", DoubleFormatUtil.Instance().doubleFormat(map.get("value"),4L));
                    array.add(jsonObject);
                }

                saveObj.setContent(array.toJSONString());
                saveObj.setLastUpdateTime(new Date());
                saveObj.setLastUpdateUserId(userId);
                saveObj.setIsValid(1);

                fnPriceTemplateService.save(saveObj);
            }else{
                throw new Exception("价格类型不存在:"+type);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPriceAddService");
        }
    }

    /**
     * 价格方案 -- 删除
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTPriceDeleteService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult priceDelete(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String)dto.get("id");
            if(id == null){
                throw new Exception(ExceptionUtil.ParamIsNull("id"));
            }

            //检查价格模板是否在使用
            FnTenantPrice tenantPriceQuery = new FnTenantPrice();
            tenantPriceQuery.setPriceTemplateId(id);

            int count = fnTenantPriceService.count(tenantPriceQuery);

            Map<String,Integer> result = new HashMap<String, Integer>();
            if(count > 0){
                result.put("result", 1);
            }else{
                FnPriceTemplate query = new FnPriceTemplate();
                query.setId(id);
                fnPriceTemplateService.remove(query);
                result.put("result", 0);
            }

            content.add(result);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPriceDeleteService");
        }
    }

    /**
     * 价格方案 -- 详情
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTPriceDetailService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult priceDetail(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String)dto.get("buildingId");
            String energyTypeId = (String)dto.get("energyTypeId");
            String id = (String)dto.get("id");

            if(buildingId == null || energyTypeId == null || id == null ){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnPriceTemplate query = new FnPriceTemplate();
            query.setId(id);
            query.setEnergyTypeId(energyTypeId);

            FnPriceTemplate result = (FnPriceTemplate) fnPriceTemplateService.queryObject(query);

            Map<String,Object> priceMap = new HashMap<String,Object>();
            priceMap.put("id", result.getId());
            priceMap.put("name", result.getName());
            priceMap.put("type", result.getType());
            if(result.getType().intValue() == EnumPriceType.AVG.getValue().intValue()){
                priceMap.put("content", DoubleFormatUtil.Instance().doubleFormat(result.getContent(), 4L));
            }else{
                JSONArray contentValueArray = (JSONArray) JSONValue.parse(result.getContent());
                List<Object> contentValueList = new ArrayList<Object>();
                if(contentValueArray != null){
                    for(int i=0;i<contentValueArray.size();i++){
                        JSONObject contentValueObj = (JSONObject)contentValueArray.get(i);
                        Map<String,Object> contentObjMap = new HashMap<String,Object>();
                        contentObjMap.put("type", contentValueObj.get("type"));
                        contentObjMap.put("value", DoubleFormatUtil.Instance().doubleFormat(contentValueObj.get("value"), 4L));
                        contentValueList.add(contentObjMap);
                    }
                }
                priceMap.put("content", contentValueList);
            }
            content.add(priceMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPriceDetailService");
        }
    }


    /**
     * 价格方案 -- 列表
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTPriceListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult priceList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String)dto.get("energyTypeId");

            if(energyTypeId == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            List<Object> priceList = new ArrayList<Object>();
            Map<String,Object> contentMap = new HashMap<String,Object>();

            contentMap.put("priceList", priceList);

            List<FnPriceTemplate> list = fnPriceTemplateService.queryList(energyTypeId);

            for (FnPriceTemplate fnPriceTemplate : list) {
                Map<String,Object> priceMap = new HashMap<String,Object>();
                priceMap.put("id", fnPriceTemplate.getId());
                priceMap.put("name", fnPriceTemplate.getName());
                priceMap.put("unit", "元/"+ UnitUtil.getCumulantUnit(energyTypeId));
                priceMap.put("type", fnPriceTemplate.getType());
                if(fnPriceTemplate.getType().intValue() == EnumPriceType.AVG.getValue().intValue()){
                    priceMap.put("content", DoubleFormatUtil.Instance().doubleFormat(fnPriceTemplate.getContent(), 4L));
                }else{
                    JSONArray contentValueArray = (JSONArray) JSONValue.parse(fnPriceTemplate.getContent());
                    List<Object> contentValueList = new ArrayList<Object>();
                    if(contentValueArray != null){
                        for(int i=0;i<contentValueArray.size();i++){
                            JSONObject contentValueObj = (JSONObject)contentValueArray.get(i);
                            Map<String,Object> contentObjMap = new HashMap<String,Object>();
                            contentObjMap.put("type", contentValueObj.get("type"));
                            contentObjMap.put("value", DoubleFormatUtil.Instance().doubleFormat(contentValueObj.get("value"), 4L));
                            contentValueList.add(contentObjMap);
                        }
                    }
                    priceMap.put("content", contentValueList);
                }
                priceList.add(priceMap);
            }

            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPriceListService");
        }
    }


    /**
     * 价格方案 -- 更新
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTPriceUpdateService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult priceUpdate(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String)dto.get("id");
            Integer type = (Integer)dto.get("type");
            String name = (String)dto.get("name");
            String userId = this.getParamUserId(dto);

            if(id == null || type == null || dto.get("content") == null || userId == null || name == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            //查找是否有其他软冲表扣租户引用价格模板
            int tenantCount = fnPriceTemplateService.queryCountOnline(id);
            if (tenantCount>0){
                content.add("该价格方案被软冲表扣租户引用，请新增价格方案。");
                return Result.SUCCESS(content);
            }

            FnPriceTemplate query = new FnPriceTemplate();
            query.setId(id);

            FnPriceTemplate update = new FnPriceTemplate();

            if(type.intValue() == EnumPriceType.AVG.getValue()){
                update.setName(name);
                update.setContent(dto.get("content")+"");
                update.setLastUpdateTime(new Date());
                update.setLastUpdateUserId(userId);

                fnPriceTemplateService.update(query, update);
            }else if(type.intValue() == EnumPriceType.TOU.getValue()){
                update.setName(name);
                update.setCreateUserId(userId);

                List<Object> list = (List<Object>)dto.get("content");
                JSONArray array = new JSONArray();
                for(Object obj : list){
                    Map<String,Object> map = (Map<String,Object>)obj;
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("type", map.get("type"));
                    jsonObject.put("value", DoubleFormatUtil.Instance().doubleFormat(map.get("value"),4L));
                    array.add(jsonObject);
                }

                update.setContent(array.toJSONString());
                update.setLastUpdateTime(new Date());
                update.setLastUpdateUserId(userId);

                fnPriceTemplateService.update(query, update);
            }else{
                throw new Exception("价格类型不存在:"+type);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPriceUpdateService");
        }
    }



}
