package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnRecordPostClearingPay;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.service.FNRecordPostClearingPayService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理
 * 接口名：后付费-后付费缴费前查询
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntPostPayBeforePayController extends BaseController {

    @Resource(name = "FNRecordPostClearingPayService")
    private FNRecordPostClearingPayService fnRecordPostClearingPayService;


    @RequestMapping("FNTPostPayBeforePayService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult postPayBeforePay(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String)dto.get("buildingId");
            String tenantId = (String)dto.get("tenantId");
            String energyTypeId = (String)dto.get("energyTypeId");


            if(buildingId == null || tenantId == null || energyTypeId == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Map<String,Object> contentObj = new HashMap<String,Object>();
            contentObj.put("tenantId", tenantId);
            contentObj.put("energyTypeId", energyTypeId);
            contentObj.put("energyTypeName", EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId));


            List<Object> orderList = new ArrayList<Object>();
            contentObj.put("orderList", orderList);

            List<FnRecordPostClearingPay> recordList = fnRecordPostClearingPayService.queryNotPayRecord(buildingId,tenantId,energyTypeId);
            if(recordList != null){
                for(FnRecordPostClearingPay record : recordList){
                    Map<String,Object> map = new HashMap<String,Object>();
                    map.put("orderId", record.getOrderId());
                    map.put("orderTime", record.getOrderTime());
                    map.put("money", record.getMoney());
                    orderList.add(map);
                }
            }

            content.add(contentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPostPayBeforePayService");
        }
    }


}
