package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.finein.FnMessageBlackList;
import com.persagy.finein.service.FNMessageBlackListService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：自动发送短信设置
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntMessageBlacklistQueryController extends BaseController {

    @Resource(name = "FNMessageBlackListService")
    private FNMessageBlackListService fnMessageBlackListService;

    @RequestMapping("FNTMessageBlacklistQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult messageBlacklistQuery(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            FnMessageBlackList query = new FnMessageBlackList();
            query.setBuildingId(buildingId);
            List<FnMessageBlackList> list = fnMessageBlackListService.query(query);
            List<Object> contentList = new ArrayList<Object>();
            if (list != null) {
                for (FnMessageBlackList blacklist : list) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("buildingId", blacklist.getBuildingId());
                    map.put("buildingName", blacklist.getBuildingName());
                    map.put("tenantId", blacklist.getTenantId());
                    map.put("tenantName", blacklist.getTenantName());
                    contentList.add(map);
                }
            }
            content.add(contentList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTMessageBlacklistQueryService");
        }
    }


}
