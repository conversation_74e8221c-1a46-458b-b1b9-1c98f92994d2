package com.persagy.finein.fnmanage.compute.handler;

import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.dictionary.Building;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月22日 下午12:58:25

* 说明:计算租户仪表剩余使用天数线程
*/
public interface FNTComputeRemainDaysHandler{

	
	
	public void processBuilding(Building building, long toleraterSecond) throws Exception;

	public void processTenant(Building building, FnTenant tenant, long toleraterSecond) throws Exception;
}

