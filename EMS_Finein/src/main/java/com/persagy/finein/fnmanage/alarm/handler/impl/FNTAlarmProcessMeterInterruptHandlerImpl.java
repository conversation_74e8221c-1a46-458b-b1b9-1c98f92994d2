package com.persagy.finein.fnmanage.alarm.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.pojo.dictionary.meter.DictionaryFunction;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.ems.pojo.originaldata.ElectricCurrentData;
import com.persagy.ems.pojo.originaldata.MonthData;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.util.AlarmTypeUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.alarm.handler.FNTAlarmProcessMeterInterruptHandler;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 说明:剩余不足
 */
@Component("FNTAlarmProcessMeterInterruptHandler")
public class FNTAlarmProcessMeterInterruptHandlerImpl extends CoreServiceImpl
		implements FNTAlarmProcessMeterInterruptHandler {

	@Resource(name = "FNAlarmService")
	private FNAlarmService FNAlarmService;
	
	@Resource(name = "FNAlarmPushStatusService")
	private FNAlarmPushStatusService FNAlarmPushStatusService;

	@Resource(name = "FNProjectService")
	private FNProjectService FNProjectService;

	@Resource(name = "FNOriginalDataService")
	private FNOriginalDataService FNOriginalDataService;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	private static Logger log = Logger.getLogger(FNTAlarmProcessMeterInterruptHandlerImpl.class);

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void handle(Building building, FnTenant tenant, String energyTypeId, String alarmTypeId,
			FnTenantPayType tenantPayType, Integer dataSpaceSecond) throws Exception {

		List<DTOMeter> meterList = FNMeterService.queryMeterList(tenant.getId(), energyTypeId);
		if (meterList == null || meterList.size() == 0) {
			return;
		}
		for (DTOMeter dtoMeter : meterList) {
			FnAlarm queryAlarmWeiHuiFu = FNAlarmService.queryAlarmWeiHuiFu(tenant.getId(), dtoMeter.getMeterId(),
					alarmTypeId);
			if (queryAlarmWeiHuiFu != null) {// 更新已过期
				if (queryAlarmWeiHuiFu.getAlarmTime().getTime() < DateUtils.truncate(new Date(), Calendar.DATE)
						.getTime()) {// 昨天报警
					FNAlarmService.updateAlarmStatus(tenant.getId(), alarmTypeId, EnumAlarmStatus.YiGuoQi,
							dtoMeter.getMeterId());
				}
			}
			// 查询仪表对应协议
			if (dtoMeter.getProtocolId() == null
					|| ConstantDBBaseData.ProtocolMap.get(dtoMeter.getProtocolId()) == null) {
				continue;
			}
			List<FnProtocolFunction> functionList = ConstantDBBaseData.ProtocolFunctionMap
					.get(dtoMeter.getProtocolId());
			if (functionList == null || functionList.size() == 0) {
				continue;
			}

			Project project = FNProjectService.queryProject();
			if (project == null) {
				return;
			}
			boolean isFault = true;
			for (FnProtocolFunction function : functionList) {
				Integer functionId = function.getFunctionId();
				DictionaryFunction dictionaryFunction = ConstantDBBaseData.DictionaryFunctionMap.get(functionId + "");
				if (dictionaryFunction == null) {
					log.error("【租户管理】仪表通讯故障检测,数据字典未找到功能号:" + functionId);
					continue;
				}
				String tableType = null;
				try {
					JSONObject remarkObj = (JSONObject) JSONValue.parse(dictionaryFunction.getRemark());
					tableType = (String) remarkObj.get("entityName");
				} catch (Exception e) {

				}
				if (tableType == null) {
					if ("00".equals(dictionaryFunction.getType())) {
						tableType = EnumOriginalDataType.MonthData.getValue();
					} else {
						tableType = EnumOriginalDataType.ElectriccurrentData.getValue();
					}
				}
				Date timeTo = new Date();
				if (tableType.equals(EnumOriginalDataType.MonthData.getValue())) {
					Date timeFrom = new Date(timeTo.getTime() - dataSpaceSecond * 1000);
					// 查询数据
					MonthData lastData = FNOriginalDataService.queryLastMonthDataGteLte(project.getId(),
							dtoMeter.getMeterId(), functionId, timeFrom, timeTo);
					if (lastData != null && lastData.getData() != null) {
						isFault = false;
					}
				} else {
					Date timeFrom = new Date(timeTo.getTime() - dataSpaceSecond * 1000);
					// 查询数据
					ElectricCurrentData lastData = FNOriginalDataService.queryLastEleDataGteLte(project.getId(),
							dtoMeter.getMeterId(), functionId, timeFrom, timeTo);
					if (lastData != null && lastData.getData() != null) {
						isFault = false;
					}
				}
			}

			FnAlarm alarmWeiHuiFu = FNAlarmService.queryAlarmWeiHuiFu(tenant.getId(), dtoMeter.getMeterId(),
					alarmTypeId);
			if (isFault) {// 报警
				if (alarmWeiHuiFu == null) {// 不存在报警,新增
					this.alarm(building, tenant, energyTypeId, alarmTypeId, dtoMeter, dataSpaceSecond);
				}
			} else {// 恢复
				if (alarmWeiHuiFu != null) {
					FNAlarmService.updateAlarmStatus(tenant.getId(), alarmTypeId, EnumAlarmStatus.YiHuiFu,
							dtoMeter.getMeterId());
				}
			}
		}

	}

	public void alarm(Building building, FnTenant tenant, String energyTypeId, String alarmTypeId, DTOMeter meter,
			Integer dataSpaceSecond) throws Exception {
		FnAlarm saveObj = new FnAlarm();
		String id = UUID.randomUUID().toString();
		saveObj.setId(id);
		saveObj.setBuildingId(building.getId());
		saveObj.setTenantId(tenant.getId());
		saveObj.setAlarmTypeId(alarmTypeId);
		FnAlarmType alarmType = AlarmTypeUtil.queryAlarmTypeById(alarmTypeId);
		saveObj.setParentAlarmTypeId(alarmType.getParentId());
		saveObj.setTreeId(alarmType.getTreeId());
		saveObj.setEnergyTypeId(energyTypeId);
		Date now = new Date();
		saveObj.setAlarmTime(now);
		saveObj.setAlarmPositionType(EnumAlarmPositionType.Meter.getValue());
		saveObj.setAlarmPositionId(meter.getMeterId());
		saveObj.setAlarmPositionName(meter.getMeterName());
		saveObj.setStatus(EnumAlarmStatus.WeiHuiHu.getValue());
		saveObj.setCreateTime(now);
		saveObj.setLastUpdateTime(now);
		saveObj.setUnit(alarmType.getUnit());
		saveObj.setIsRead(EnumYesNo.NO.getValue());
		saveObj.setPushStatus(EnumAlarmPushStatus.WaitPush.getValue());
		JSONObject extendObj = new JSONObject();
		Date interruptTime = new Date(now.getTime() - (dataSpaceSecond * 1000));
		SimpleDateFormat sd = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		extendObj.put("interruptTime", sd.format(interruptTime));
		saveObj.setExtend(extendObj.toString());
		FNAlarmService.save(saveObj);
		
		FnAlarmPushStatus save = new FnAlarmPushStatus();
		save.setBuildingId(building.getId());
		save.setTenantId(tenant.getId());
		save.setAlarmPositionType(EnumAlarmPositionType.Meter.getValue());
		save.setAlarmPositionId(meter.getMeterId());
		save.setStatus(EnumAlarmStatus.WeiHuiHu.getValue());
		save.setAlarmTypeId(alarmTypeId);
		save.setEnergyTypeId(energyTypeId);
		save.setId(id);
		save.setLastUpdateTime(now);
		save.setPushStatus(EnumAlarmPushStatus.WaitPush.getValue());
		FNAlarmPushStatusService.save(save);
	}
}
