package com.persagy.finein.fnmanage.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnProtocol;
import com.persagy.finein.communication.exception.MeterSetException;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumMeterSetType;
import com.persagy.finein.enumeration.EnumMeterType;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.enumeration.EnumPriceDetail;
import com.persagy.finein.service.FNMeterService;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;
import java.util.Map.Entry;

/**
 * 项目名：租户管理 接口名：仪表设置-仪表设置前查询
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntMeterSetBeforeController extends BaseController {

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;


    @RequestMapping("FNTMeterSetBeforeService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult meterSetBefore(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String meterId = (String) dto.get("meterId");
            Integer type = (Integer) dto.get("setType");
            if (meterId == null || type == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            Map<String, Object> map = new HashMap<>();
            FnMeter meter = fnMeterService.queryMeterById(meterId);
            ICommunication iCommunication = (ICommunication) SystemConstant.context.getBean(meter.getProtocolId());
            if (type == EnumMeterSetType.PAULELE.getValue().intValue()) {// 保电
                Integer status = iCommunication.paulEleStatus(meter);
                map.put("value", status);
            } else if (type == EnumMeterSetType.GATE.getValue().intValue()) {// 闸状态
                Integer status = iCommunication.gateStatus(meter);
                map.put("value", status);
            } else if (type == EnumMeterSetType.REMAINCLEAR.getValue().intValue()) {// 查询剩余量

                Double remainData = null;
                try {
                    ICommunication communication = (ICommunication) SystemConstant.context.getBean(meter.getProtocolId());
                    if (communication != null) {
                        try {
                            remainData = communication.queryRemainData(meter);
                        } catch (MeterSetException e) {
                            e.printStackTrace();
                        }
                        if (remainData == null) {
                            throw new Exception("查询剩余量失败");
                        }
                        if (!"DI_C_07_Y_Q_001".equals(meter.getProtocolId())) {
                            Double overdraft = communication.queryOverdraft(meter);// 透支金额
                            if (overdraft == null) {
                                throw new Exception("查询透支金额失败");
                            }
                            remainData = remainData - overdraft;
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }

                if (remainData != null) {
                    // 剩余量是否乘变比
                    Double radio = 1.0;
                    try {
                        Long syjIsCt = (Long) ((JSONObject) JSONValue.parse(meter.getExtend())).get("syjIsCt");
                        if (syjIsCt != null && syjIsCt.intValue() == 1) {
                            radio = meter.getRadio();
                        }
                        map.put("value", remainData == null ? null : remainData * radio);
                        Map<String, FnProtocol> protocolmap = ConstantDBBaseData.ProtocolMap;
                        FnProtocol protocol = protocolmap.get(meter.getProtocolId());
                        if (protocol.getBillingMode() == EnumPrepayChargeType.Liang.getValue().intValue()) {
                            map.put("unit", UnitUtil.getCumulantUnit(meter.getEnergyTypeId()));
                        } else {
                            map.put("unit", "元");
                        }
                    } catch (Exception e) {
                    }
                }

            } else if (type == EnumMeterSetType.OVERDRAFT.getValue().intValue()) {// 透支金额
                Double overdraft = iCommunication.queryOverdraft(meter);
                map.put("value", overdraft);
                map.put("unit", "元");
            } else if (type == EnumMeterSetType.UPDATEPRICE.getValue().intValue()) {// 查询价格
                List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                map.put("result", list);
                Integer meterType = meter.getMeterType();
                if (meterType == EnumMeterType.Common.getValue().intValue()) {// 普通仪表
                    Map<String, Object> lMap = new HashMap<String, Object>();
                    try {
                        lMap.put("type", "L");
                        lMap.put("value", iCommunication.getPrice(meter, EnumPriceDetail.L));
                        lMap.put("unit", "元/" + UnitUtil.getCumulantUnit(meter.getEnergyTypeId()));
                        list.add(lMap);
                    } catch (MeterSetException e) {
                        e.printStackTrace();
                    }
                } else {// 多费率表
                    try {
                        Map<String, Double> batchPrice = iCommunication.getBatchPrice(meter);
                        if(batchPrice!=null){
                            for (Entry<String, Double> price : batchPrice.entrySet()) {
                                Map<String, Object> priceMap = new HashMap<String, Object>();
                                priceMap.put("type",price.getKey());
                                priceMap.put("value", price.getValue());
                                priceMap.put("unit", "元/" + UnitUtil.getCumulantUnit(meter.getEnergyTypeId()));
                                list.add(priceMap);
                            }
                        }
                    } catch (MeterSetException e1) {
                        // TODO Auto-generated catch block
                        e1.printStackTrace();
                    }
                }
            }
            map.put("lastUpdateTime", new Date());
            content.add(map);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTMeterSetBeforeService");
        }
    }


}
