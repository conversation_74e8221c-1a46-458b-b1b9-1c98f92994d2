package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.finein.service.FNRoomMeterService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理
 * 接口名：通用-获取仪表列表
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntCommonMeterListController extends BaseController {

    @Resource(name = "FNRoomMeterService")
    private FNRoomMeterService fnRoomMeterService;

    @RequestMapping("FNTCommonMeterListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonMeterList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String)dto.get("buildingId");
            String roomId = (String)dto.get("roomId");
            String energyTypeId = (String)dto.get("energyTypeId");

            if(buildingId == null || roomId == null ){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Map<String,Object> contentMap = new HashMap<String,Object>();

            List<DTOMeter> roomMeterList = fnRoomMeterService.queryRoomMeter(buildingId, roomId, energyTypeId);
            List<Object> meterList = new ArrayList<>();
            if(roomMeterList != null){
                for(DTOMeter roomMeter : roomMeterList){
                    Map<String,Object> map = new HashMap<>();
                    map.put("meterId", roomMeter.getMeterId());
                    map.put("energyTypeId", roomMeter.getEnergyTypeId());
                    map.put("meterType", roomMeter.getMeterType());
                    map.put("isUse", roomMeter.getIsUse());
                    meterList.add(map);
                }
            }

            contentMap.put("meterList", meterList);
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTCommonMeterListService");
        }
    }


}
