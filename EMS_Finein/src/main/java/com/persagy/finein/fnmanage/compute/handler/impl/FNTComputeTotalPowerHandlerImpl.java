package com.persagy.finein.fnmanage.compute.handler.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.compute.handler.FNTComputeTotalPowerHandler;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月22日 下午12:58:25
 * 
 * 说明:租户总功率计算线程
 */
@Component("FNTComputeTotalPowerHandler")
public class FNTComputeTotalPowerHandlerImpl extends CoreServiceImpl implements FNTComputeTotalPowerHandler {

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;

	@Resource(name = "FNTenantMeterPowerComputeService")
	private FNTenantMeterPowerComputeService FNTenantMeterPowerComputeService;

	@Resource(name = "FNTenantMeterPowerStatService")
	private FNTenantMeterPowerStatService FNTenantMeterPowerStatService;

	@Resource(name = "FNTenantMeterComputeService")
	private FNTenantMeterComputeService FNTenantMeterComputeService;

	@Resource(name = "FNTenantMeterPowerService")
	private FNTenantMeterPowerService FNTenantMeterPowerService;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNTenantMeterDataService")
	private FNTenantMeterDataService FNTenantMeterDataService;

	@Resource(name = "FNTenantDataService")
	private FNTenantDataService FNTenantDataService;

	// buildingId_tenantId_functionId_timeType_dataType,value
	private Map<String, Map<String, Object>> tenantHistoryStatMap = new HashMap<>();

	// buildingId_tenantId_meterId_functionId_timeType_dataType,value

	private Map<String, Map<String, Object>> tenantMeterHistoryStatMap = new HashMap<>();

	private static Map<String, Boolean> InitStatMap = new HashMap<>();

	private static final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public void processBuilding(Building building, Integer delaySecond) throws Exception {
		if (!InitStatMap.containsKey(building.getId())) {
			this.initStat(building);
			InitStatMap.put(building.getId(), true);
		}
		// 查询建筑下所有租户
		List<FnTenant> tenantList = FNTenantService.queryListByValidStatus(building.getId(), EnumValidStatus.VALID);
		if (tenantList != null) {
			for (FnTenant tenant : tenantList) {
				if (tenant.getStatus().intValue() == EnumTenantStatus.ACTIVATED.getValue().intValue()
						|| tenant.getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue().intValue()) {// 只计算已激活和以退租
					this.processTenant(building, tenant, delaySecond);
				}
			}
		}
	}

	private void initStat(Building building) throws Exception {
		FnTenantMeterPower query = new FnTenantMeterPower();
		query.setBuildingId(building.getId());
		query.setDataType(EnumStatType.Max.getValue());
		List<FnTenantMeterPower> list = FNTenantMeterPowerService.query(query);
		if (list != null) {
			for (FnTenantMeterPower stat : list) {
				if (stat.getData() != null) {
					if (stat.getBodyType() == EnumBodyType.TENANT.getValue().intValue()) {// 租户
						String key = getTenantFunctionKey(stat.getBuildingId(), stat.getTenantId(), stat.getDataType());
						Map<String, Object> hashMap = new HashMap<String, Object>();
						hashMap.put("time", stat.getTimeFrom());
						hashMap.put("data", stat.getData());
						this.tenantHistoryStatMap.put(key, hashMap);
					} else {// 仪表
						String key = getTenantMeterFunctionKey(stat.getBuildingId(), stat.getTenantId(),
								stat.getBodyCode(), stat.getDataType());
						Map<String, Object> hashMap = new HashMap<String, Object>();
						hashMap.put("time", stat.getTimeFrom());
						hashMap.put("data", stat.getData());
						this.tenantMeterHistoryStatMap.put(key, hashMap);
					}
				}
			}
		}
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void processTenant(Building building, FnTenant tenant, Integer delaySecond) throws Exception {
		List<DTOMeter> meterList = FNMeterService.queryMeterList(tenant.getId(), FineinConstant.EnergyType.Dian);
		if (meterList == null || meterList.size() == 0) {
			return;
		}
		List<FnTenantMeterPowerStat> saveList = new ArrayList<FnTenantMeterPowerStat>();
		List<FnTenantMeterPowerCompute> powerSaveList = new ArrayList<FnTenantMeterPowerCompute>();
		for (DTOMeter dtoMeter : meterList) {
			Map<String, FnTenantMeterData> tenantMeterDataMap = new HashMap<String, FnTenantMeterData>();
			processMeterPower(dtoMeter, powerSaveList, building, tenant, delaySecond, tenantMeterDataMap);

			// 处理日峰值
			for (Entry<String, FnTenantMeterData> entry : tenantMeterDataMap.entrySet()) {
				FnTenantMeterPowerStat save = new FnTenantMeterPowerStat();
				save.setBuildingId(entry.getValue().getBuildingId());
				save.setTenantId(entry.getValue().getTenantId());
				save.setBodyType(EnumBodyType.METER.getValue());// 仪表类型
				save.setBodyId(entry.getValue().getMeterId());
				save.setEnergyTypeId(FineinConstant.EnergyType.Dian);
				save.setData(entry.getValue().getData() * 4);
				save.setTimeFrom(entry.getValue().getTimeFrom());
				save.setCountTime(standard.parse(entry.getKey()));
				save.setLastUpdateTime(new Date());
				saveList.add(save);
			}
		}

		Map<String, FnTenantData> tenantDataMap = new HashMap<String, FnTenantData>();
		{// 处理租户
			FnTenantMeterPowerCompute query = new FnTenantMeterPowerCompute();
			query.setBuildingId(tenant.getBuildingId());
			query.setTenantId(tenant.getId());
			query.setSort("lastComputeTime", EMSOrder.Asc);
			query.setLimit((long) 1);
			List<FnTenantMeterPowerCompute> list = FNTenantMeterPowerComputeService.query(query);
			Date lastComputeTime;
			if (list != null && list.size() > 0) {
				lastComputeTime = list.get(0).getLastComputeTime();
			} else {
				lastComputeTime = tenant.getActiveTime();
			}

			FnTenantMeterCompute queryMeterCompute = new FnTenantMeterCompute();
			queryMeterCompute.setBuildingId(tenant.getBuildingId());
			queryMeterCompute.setTenantId(tenant.getId());
			queryMeterCompute.setFunctionId(FineinConstant.FuntionTypeId.Cumulant_Dian);
			queryMeterCompute.setSort("lastComputeTime", EMSOrder.Desc);
			queryMeterCompute.setLimit((long) 1);
			List<FnTenantMeterCompute> meterComputelist = FNTenantMeterComputeService.query(queryMeterCompute);
			if (meterComputelist != null && meterComputelist.size() > 0) {
				Date meterLastComputeTime = meterComputelist.get(0).getLastComputeTime();
				processTenantPower(building, tenant, delaySecond, lastComputeTime, meterLastComputeTime, tenantDataMap);

				// 处理租户峰值
				for (Entry<String, FnTenantData> entry : tenantDataMap.entrySet()) {
					FnTenantMeterPowerStat save = new FnTenantMeterPowerStat();
					save.setBuildingId(entry.getValue().getBuildingId());
					save.setTenantId(entry.getValue().getTenantId());
					save.setBodyType(EnumBodyType.TENANT.getValue());// 租户类型
					save.setBodyId(entry.getValue().getTenantId());
					save.setEnergyTypeId(FineinConstant.EnergyType.Dian);
					save.setData(entry.getValue().getData() * 4);
					save.setTimeFrom(entry.getValue().getTimeFrom());
					save.setCountTime(standard.parse(entry.getKey()));
					save.setLastUpdateTime(new Date());
					saveList.add(save);
				}
			}
		}
		if (saveList.size() > 0) {
			FNTenantMeterPowerStatService.saveDataList(saveList);// 保存
		}
		FNTenantMeterPowerComputeService.update(powerSaveList);
	}

	private void processTenantPower(Building building, FnTenant tenant, Integer delaySecond, Date lastComputeTime,
			Date meterLastComputeTime, Map<String, FnTenantData> tenantDataMap) throws Exception {
		String maxKey = getTenantFunctionKey(building.getId(), tenant.getId(), EnumStatType.Max.getValue());
		Date delayTime = DateUtils.addSeconds(meterLastComputeTime, -delaySecond);
		Date timeFrom = lastComputeTime;
		while (lastComputeTime.getTime() < delayTime.getTime()) {
			timeFrom = lastComputeTime;
			Date timeTo = DateUtils.addDays(timeFrom, 7);
			if (timeTo.getTime() > delayTime.getTime()) {
				timeTo = delayTime;
			}
			List<FnTenantData> dataList = FNTenantDataService.queryListGteLt(tenant.getBuildingId(), tenant.getId(),
					EnumTimeType.T0, FineinConstant.EnergyType.Dian, timeFrom, timeTo, EnumEnergyMoney.Energy);
			if (dataList != null) {
				for (FnTenantData statData : dataList) {
					if (statData.getData() != null) {
						// 处理租户历史最大值
						if (tenantHistoryStatMap.get(maxKey) == null) {
							tenantHistoryStatMap.put(maxKey, new HashMap<String, Object>());
						}
						if (tenantHistoryStatMap.get(maxKey).get("data") == null) {
							tenantHistoryStatMap.get(maxKey).put("data", statData.getData() * 4);
							tenantHistoryStatMap.get(maxKey).put("time", statData.getTimeFrom());
						} else if ((Double) tenantHistoryStatMap.get(maxKey).get("data") < statData.getData() * 4) {
							tenantHistoryStatMap.get(maxKey).put("data", statData.getData() * 4);
							tenantHistoryStatMap.get(maxKey).put("time", statData.getTimeFrom());
						}

						// 处理仪表日最大值
						Date day = DateUtils.truncate(statData.getTimeFrom(), Calendar.DATE);
						String meterPowerDateStatKey = standard.format(day);
						if (tenantDataMap.get(meterPowerDateStatKey) == null
								|| tenantDataMap.get(meterPowerDateStatKey).getData() < statData.getData()) {
							tenantDataMap.put(meterPowerDateStatKey, statData);
						}
					}
				}
			}
			lastComputeTime = timeTo;
			Thread.sleep(3);
		}
		if (tenantHistoryStatMap.get(maxKey) != null) {
			this.FNTenantMeterPowerService.saveData(building.getId(), tenant.getId(), EnumBodyType.TENANT,
					tenant.getId(), EnumStatType.Max, (Date) tenantHistoryStatMap.get(maxKey).get("time"),
					(Double) tenantHistoryStatMap.get(maxKey).get("data"));
		}
	}

	public void processMeterPower(DTOMeter dtoMeter, List<FnTenantMeterPowerCompute> powerSaveList, Building building,
			FnTenant tenant, Integer delaySecond, Map<String, FnTenantMeterData> tenantMeterDataMap) throws Exception {
		FnTenantMeterPowerCompute compute = FNTenantMeterPowerComputeService.query(building.getId(), tenant.getId(),
				dtoMeter.getMeterId(), EnumStatType.Max);
		Date lastComputeTime = null;
		if (compute == null) {
			lastComputeTime = tenant.getActiveTime();
		} else {
			lastComputeTime = compute.getLastComputeTime();
		}
		Date meterLastCompute = FNTenantMeterComputeService.queryLastComputeTime(building.getId(), tenant.getId(),
				dtoMeter.getMeterId(), FineinConstant.FuntionTypeId.Cumulant_Dian);
		if (meterLastCompute == null) {
			return;
		}
		String maxKey = getTenantMeterFunctionKey(building.getId(), tenant.getId(), dtoMeter.getMeterId(),
				EnumStatType.Max.getValue());
		Date delayTime = DateUtils.addSeconds(meterLastCompute, -delaySecond);
		Date timeFrom = lastComputeTime;
		while (lastComputeTime.getTime() < delayTime.getTime()) {
			timeFrom = lastComputeTime;
			Date timeTo = DateUtils.addDays(timeFrom, 7);
			if (timeTo.getTime() > delayTime.getTime()) {
				timeTo = delayTime;
			}
			List<FnTenantMeterData> dataList = FNTenantMeterDataService.queryListGteLt(tenant.getBuildingId(),
					tenant.getId(), dtoMeter.getMeterId(), FineinConstant.FuntionTypeId.Cumulant_Dian,
					FineinConstant.EnergyType.Dian, EnumTimeType.T0, timeFrom, timeTo, EnumEnergyMoney.Energy);
			if (dataList != null) {
				for (FnTenantMeterData statData : dataList) {
					if (statData.getData() != null) {
						// 处理仪表历史最大值
						if (tenantMeterHistoryStatMap.get(maxKey) == null) {
							tenantMeterHistoryStatMap.put(maxKey, new HashMap<String, Object>());
						}
						if (tenantMeterHistoryStatMap.get(maxKey).get("data") == null) {
							tenantMeterHistoryStatMap.get(maxKey).put("data", statData.getData() * 4);
							tenantMeterHistoryStatMap.get(maxKey).put("time", statData.getTimeFrom());
						} else if ((Double) tenantMeterHistoryStatMap.get(maxKey).get("data") < statData.getData()
								* 4) {
							tenantMeterHistoryStatMap.get(maxKey).put("data", statData.getData() * 4);
							tenantMeterHistoryStatMap.get(maxKey).put("time", statData.getTimeFrom());
						}

						// 处理仪表日最大值
						Date day = DateUtils.truncate(statData.getTimeFrom(), Calendar.DATE);
						String meterPowerDateStatKey = standard.format(day);
						if (tenantMeterDataMap.get(meterPowerDateStatKey) == null
								|| tenantMeterDataMap.get(meterPowerDateStatKey).getData() < statData.getData()) {
							tenantMeterDataMap.put(meterPowerDateStatKey, statData);
						}
					}
				}
			}
			lastComputeTime = timeTo;
			Thread.sleep(3);
		}
		if (tenantMeterHistoryStatMap.get(maxKey) != null) {
			this.FNTenantMeterPowerService.saveData(building.getId(), tenant.getId(), EnumBodyType.METER,
					dtoMeter.getMeterId(), EnumStatType.Max, (Date) tenantMeterHistoryStatMap.get(maxKey).get("time"),
					(Double) tenantMeterHistoryStatMap.get(maxKey).get("data"));

			FnTenantMeterPowerCompute query = new FnTenantMeterPowerCompute();
			query.setBuildingId(building.getId());
			query.setTenantId(tenant.getId());
			query.setMeterId(dtoMeter.getMeterId());
			query.setDataType(EnumStatType.Max.getValue());
			query.setLastComputeTime(delayTime);
			query.setLastUpdateTime(new Date());
			powerSaveList.add(query);
		}
	}

	private String getTenantFunctionKey(String buildingId, String tenantId, int dataType) {
		StringBuffer sb = new StringBuffer();
		sb.append(buildingId).append("_");
		sb.append(tenantId).append("_");
		sb.append(dataType);
		return sb.toString();
	}

	private String getTenantMeterFunctionKey(String buildingId, String tenantId, String meterId, int dataType) {
		StringBuffer sb = new StringBuffer();
		sb.append(buildingId).append("_");
		sb.append(tenantId).append("_");
		sb.append(meterId).append("_");
		sb.append(dataType);
		return sb.toString();
	}
}
