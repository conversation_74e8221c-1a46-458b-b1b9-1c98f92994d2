package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.finein.core.util.ExpressionUtil;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：租户-验证拆分公式
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FntEnergySplitExressionVerifyController extends BaseController {

    @Resource(name = "ExpressionUtil")
    private ExpressionUtil expressionUtil;

    @RequestMapping("FNTEnergySplitExressionVerifyService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult energySplitExressionVerify(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            List<Map<String, String>> expressionList = (List<Map<String, String>>) dto.get("expressionList");
            List<Object> list = new ArrayList<Object>();
            for (Map<String, String> map : expressionList) {
                String energyTypeId = (String) map.get("energyTypeId");
                String expression = (String) map.get("expression");
                if (energyTypeId == null || expression == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
                Map<String, Object> contentObj = new HashMap<String, Object>();
                contentObj.put("energyTypeId", energyTypeId);
                contentObj.put("expression", expression);
                if (expressionUtil.verifyExpression(energyTypeId, expression)) {
                    contentObj.put("result", 0);
                } else {
                    contentObj.put("result", 1);
                }
                list.add(contentObj);
            }
            Map<Object, Object> contentMap = new HashMap<>();
            contentMap.put("expressionList", list);
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTEnergySplitExressionVerifyService");
        }
    }


}
