package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnTenantPrice;
import com.persagy.finein.service.FNTenantPriceService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理
 * 接口名：批量操作-批量修改价格方案-根据租户进行查询
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntBatchPriceQueryController extends BaseController {

    @Resource(name = "FNTenantPriceService")
    private FNTenantPriceService fnTenantPriceService;

    @RequestMapping("FNTBatchPriceQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchPriceQuery(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String)dto.get("energyTypeId");

            if(dto.get("tenantList") == null || energyTypeId == null ){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnTenantPrice query = new FnTenantPrice();
            query.setEnergyTypeId(energyTypeId);
            query.setSpecialOperation("tenantId", SpecialOperator.$in, dto.get("tenantList"));
            List<FnTenantPrice> list = fnTenantPriceService.query(query);

            String result = null;
            String first = null;
            if(list != null && list.size() > 0){
                for(int i=0;i<list.size();i++){
                    if(i == 0){
                        first = list.get(0).getPriceTemplateId();
                    }else{
                        if(!first.equals(list.get(i).getPriceTemplateId())){
                            first = null;
                            break;
                        }
                    }
                }
            }
            result = first;
            Map<String,Object> contentMap = new HashMap<String,Object>();
            contentMap.put("priceTempldateId", result);
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTBatchPriceQueryService");
        }
    }


}
