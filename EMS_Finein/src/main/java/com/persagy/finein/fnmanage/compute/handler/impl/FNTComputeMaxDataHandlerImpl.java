package com.persagy.finein.fnmanage.compute.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantData;
import com.persagy.ems.pojo.finein.FnTenantMeterData;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.compute.handler.FNTComputeMaxDataHandler;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月22日 下午12:58:25
 * 
 * 说明:计算租户仪表剩余使用天数线程
 */
@Component("FNTComputeMaxDataHandler")
public class FNTComputeMaxDataHandlerImpl extends CoreServiceImpl implements FNTComputeMaxDataHandler {

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;

	@Resource(name = "FNTenantPayTypeService")
	private FNTenantPayTypeService FNTenantPayTypeService;

	@Resource(name = "FNTenantStatService")
	private FNTenantStatService FNTenantStatService;

	@Resource(name = "FNTenantDataService")
	private FNTenantDataService FNTenantDataService;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNTenantMeterStatService")
	private FNTenantMeterStatService FNTenantMeterStatService;

	@Resource(name = "FNTenantMeterDataService")
	private FNTenantMeterDataService FNTenantMeterDataService;

	@Override
	public void processBuilding(Building building) throws Exception {
		// 查询建筑下所有租户
		List<FnTenant> tenantList = FNTenantService.queryListByValidStatus(building.getId(), EnumValidStatus.VALID);
		if (tenantList != null) {
			long start = System.currentTimeMillis();
			for (FnTenant tenant : tenantList) {
				if (tenant.getStatus().intValue() == EnumTenantStatus.ACTIVATED.getValue().intValue()
						|| tenant.getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue().intValue()) {
					this.processTenant(building, tenant);
				}
			}
			long end = System.currentTimeMillis();
			System.out.println("统计【" + building.getName() + "】租户数量：" + tenantList.size() + "日最大值用时" + (end - start));
		}
	}

	@Override
	@SuppressWarnings("deprecation")
	public void processTenant(Building building, FnTenant tenant) throws Exception {
		Date timeFrom = tenant.getActiveTime();
		Date timeTo = null;
		if (tenant.getStatus().intValue() != EnumTenantStatus.RETURNED_LEASE.getValue()) {
			// 下月初
			timeTo = DateUtils.truncate(DateUtils.add(new Date(), Calendar.MONTH, 1), Calendar.MONTH);
		} else {
			timeTo = tenant.getLeaveTime();
		}
		if (timeTo == null) {
			return;
		}
		Date monthFrom = DateUtils.truncate(DateUtils.add(timeFrom, Calendar.MONTH, 1), Calendar.MONTH);
		List<Date> timeList = new ArrayList<>();
		timeList.add(timeFrom);
		while (monthFrom.getTime() < timeTo.getTime()) {
			timeList.add(monthFrom);
			monthFrom = DateUtils.add(monthFrom, Calendar.MONTH, 1);
		}
		timeList.add(timeTo);

		Map<String, FnTenantPayType> payTypeMap = FNTenantPayTypeService.queryPayTypeMap(tenant.getId());
		for (Map.Entry<String, FnTenantPayType> entry : payTypeMap.entrySet()) {
			// 处理租户
			{
				// 查询日期历史
				EnumTimeType[] timeTypeArray = { EnumTimeType.T2 };
				EnumEnergyMoney[] energyMoneyArray = { EnumEnergyMoney.Energy, EnumEnergyMoney.Money };
				for (EnumTimeType timeType : timeTypeArray) {
					for (EnumEnergyMoney energyMoney : energyMoneyArray) {
						Double maxData = null;
						Date maxTime = null;
						if (timeList.size() >= 2) {
							for (int i = 0; i < timeList.size() - 1; i++) {
								Date from = timeList.get(i);
								Date to = timeList.get(i + 1);
								FnTenantData tenantData = FNTenantDataService.queryHistoryMaxData(building.getId(),
										tenant.getId(), from, to, entry.getKey(), timeType, energyMoney);
								if (tenantData != null && tenantData.getData() != null) {
									if (maxData == null || maxData.doubleValue() < tenantData.getData()) {
										maxData = tenantData.getData();
										maxTime = tenantData.getTimeFrom();
									}
								}
							}
						}
						FNTenantStatService.saveData(building.getId(), tenant.getId(), entry.getKey(), timeType,
								EnumStatType.Max, energyMoney, maxTime, maxData);
					}
				}
			}
			{
				// 处理仪表
				List<DTOMeter> meterList = FNMeterService.queryMeterList(tenant.getId(), entry.getKey());
				if (meterList != null && meterList.size() > 0) {
					for (DTOMeter meter : meterList) {
						int functionId = FunctionTypeUtil.getCumulantFunctionId(entry.getKey());
						// 查询日期历史
						EnumTimeType[] timeTypeArray = { EnumTimeType.T2 };
						EnumEnergyMoney[] energyMoneyArray = { EnumEnergyMoney.Energy, EnumEnergyMoney.Money };
						for (EnumTimeType timeType : timeTypeArray) {
							for (EnumEnergyMoney energyMoney : energyMoneyArray) {
								Double maxData = null;
								Date maxTime = null;
								if (timeList.size() >= 2) {
									for (int i = 0; i < timeList.size() - 1; i++) {
										Date from = timeList.get(i);
										Date to = timeList.get(i + 1);
										FnTenantMeterData tenantMeterData = FNTenantMeterDataService
												.queryHistoryMaxData(building.getId(), tenant.getId(),
														meter.getMeterId(), functionId, from, to, entry.getKey(),
														timeType, energyMoney);
										if (tenantMeterData != null && tenantMeterData.getData() != null) {
											if (maxData == null || maxData.doubleValue() < tenantMeterData.getData()) {
												maxData = tenantMeterData.getData();
												maxTime = tenantMeterData.getTimeFrom();
											}
										}
									}
								}
								FNTenantMeterStatService.saveData(building.getId(), tenant.getId(), meter.getMeterId(),
										entry.getKey(), timeType, functionId, EnumStatType.Max, energyMoney, maxTime,
										maxData);
							}
						}
					}
				}
			}
		}
	}

}
