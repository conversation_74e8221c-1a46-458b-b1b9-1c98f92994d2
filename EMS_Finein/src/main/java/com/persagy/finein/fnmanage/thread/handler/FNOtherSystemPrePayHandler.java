package com.persagy.finein.fnmanage.thread.handler;

import com.persagy.ems.pojo.finein.FnCommonPrePaySystemParam;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnRecordPrePayForOtherSystem;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.finein.communication.exception.MeterSetException;

import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月22日 下午12:58:25
 * 
 * 说明:计算租户仪表最大值
 */
public interface FNOtherSystemPrePayHandler {

	public boolean processRecord(FnRecordPrePayForOtherSystem record, Double minMoney,
                                 Map<String, FnCommonPrePaySystemParam> systemCodeMap) throws Exception, MeterSetException;

	public void paySave(FnRecordPrePayForOtherSystem record, Double value, int billingType, FnMeter meter,
                        Double payAfter, Map<String, FnCommonPrePaySystemParam> systemCodeMap, FnTenant tenant) throws Exception;

	/**
	 * Description:
	 * 
	 * @param record
	 * @param systemCodeMap
	 *            void
	 * <AUTHOR>
	 * @throws Exception
	 * @since 2019年4月26日: 下午7:10:40 Update By 邵泓博 2019年4月26日: 下午7:10:40
	 */

	public void processFail(FnRecordPrePayForOtherSystem record, Map<String, FnCommonPrePaySystemParam> systemCodeMap)
			throws Exception;
}
