package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.dictionary.meter.DictionaryFunction;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnProtocolFunction;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.service.FNMeterService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理
 * 接口名：租户管理-获取仪表功能号
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
@Transactional(propagation= Propagation.REQUIRED,rollbackFor=Exception.class)
public class FntMeterFunctionController extends BaseController {

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @RequestMapping("FNTMeterFunctionService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
    public InterfaceResult meterFunction(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String meterId = (String) dto.get("meterId");

            if (meterId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnMeter meter = fnMeterService.queryMeterById(meterId);
            if(meter == null){
                throw new Exception("仪表不存在");
            }

            String protocolId = meter.getProtocolId();
            String energyTypeId = meter.getEnergyTypeId();
            String energyTypeName = EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId);

            Map<String,Object> contentObj = new HashMap<>();
            content.add(contentObj);
            contentObj.put("meterId", meterId);
            contentObj.put("energyTypeId", energyTypeId);
            contentObj.put("energyTypeName", energyTypeName);
            List<Object> functionList = new ArrayList<>();
            contentObj.put("functionList", functionList);
            List<FnProtocolFunction> protocolFunctionList = ConstantDBBaseData.ProtocolFunctionMap.get(protocolId);
            if(protocolFunctionList != null && protocolFunctionList.size() > 0){
                for(FnProtocolFunction protocolFunction : protocolFunctionList){
                    Integer functionId = protocolFunction.getFunctionId();
                    String strFunctionId = functionId+"";
                    if(ConstantDBBaseData.DictionaryFunctionMap.containsKey(strFunctionId)){
                        DictionaryFunction dictionaryFunction = ConstantDBBaseData.DictionaryFunctionMap.get(strFunctionId);
                        if("01".equals(dictionaryFunction.getType())){//01 代表累计量
                            Map<String,Object> functionMap = new HashMap<>();
                            functionMap.put("functionId", functionId);
                            functionMap.put("functionName", dictionaryFunction.getName());
                            functionMap.put("unit", dictionaryFunction.getUnit());
                            functionList.add(functionMap);
                        }
                    }
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTMeterFunctionService");
        }
    }


}
