package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOEnergyTypeMeter;
import com.persagy.ems.dto.DTORoom;
import com.persagy.ems.finein.common.util.*;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.core.util.PathUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.hssf.usermodel.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;

/**
 * 租户
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntTenantController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantContactService")
    private FNTenantContactService fnTenantContactService;

    @Resource(name = "FNTenantEnergySplitService")
    private FNTenantEnergySplitService fnTenantEnergySplitService;

    @Resource(name = "FNRoomService")
    private FNRoomService fnRoomService;

    @Resource(name = "FNTenantRoomService")
    private FNTenantRoomService fnTenantRoomService;

    @Resource(name = "FNTenantPriceService")
    private FNTenantPriceService fnTenantPriceService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNTenantSpellNameService")
    private FNTenantSpellNameService fnTenantSpellNameService;

    @Resource(name = "FNTenantFlagService")
    private FNTenantFlagService fnTenantFlagService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNPriceTemplateService")
    private FNPriceTemplateService fnPriceTemplateService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @Resource(name = "FNRecordPriceChangeService")
    private FNRecordPriceChangeService fnRecordPriceChangeService;

    @Resource(name = "FNTenantTypeService")
    private FNTenantTypeService fnTenantTypeService;

    @Resource(name = "FNTenantDataService")
    private FNTenantDataService fnTenantDataService;

    @Resource(name = "FNTenantPostPayParamService")
    private FNTenantPostPayParamService fnTenantPostPayParamService;

    @Resource(name = "FNTenantPrePayParamService")
    private FNTenantPrePayParamService fnTenantPrePayParamService;

    @Resource(name = "FNRecordPostClearingPayService")
    private FNRecordPostClearingPayService fnRecordPostClearingPayService;

    @Resource(name = "FNTenantPrePayMeterParamService")
    private FNTenantPrePayMeterParamService fnTenantPrePayMeterParamService;

    @Resource(name = "FNRemoteRechargeStatusService")
    private FNRemoteRechargeStatusService fnRemoteRechargeStatusService;

    @Resource(name = "PathUtil")
    private PathUtil pathUtil;

    @Resource(name = "FNFileResourceService")
    private FNFileResourceService fnFileResourceService;


    /**
     * 租户 -- 添加
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantAddService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation= Propagation.REQUIRED,rollbackFor=Exception.class)
    public InterfaceResult tenantAdd(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");
            String tenantName = (String) dto.get("tenantName");
            String buildingId = (String) dto.get("buildingId");
            String tenantTypeId = (String) dto.get("tenantTypeId");
            Double area = DoubleFormatUtil.Instance().getDoubleData(dto.get("area"));
            String contactName = (String) dto.get("contactName");
            String contactMobile = (String) dto.get("contactMobile");
            String remark = (String) dto.get("remark");
            String userId = this.getParamUserId(dto);
            if (
                    userId == null ||
                            tenantId == null
                            || tenantName == null
                            || buildingId == null
                            || tenantTypeId == null
                            || area == null
                            || contactName == null
                            || contactMobile == null
                            || dto.get("energyList") == null
                            || dto.get("roomList") == null
            ) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnTenant saveObj = new FnTenant();
            saveObj.setId(tenantId);
            saveObj.setName(tenantName);
            saveObj.setBuildingId(buildingId);
            saveObj.setTenantTypeId(tenantTypeId);
            saveObj.setArea(area);
            saveObj.setContactName(contactName);
            saveObj.setContactMobile(contactMobile);
            saveObj.setRemark(remark);
            saveObj.setStatus(EnumTenantStatus.NOT_ACTIVE.getValue());
            saveObj.setIsValid(EnumValidStatus.VALID.getValue());
            Date now = new Date();
            saveObj.setCreateTime(now);
            saveObj.setLastUpdateTime(now);
            saveObj.setCreateUserId(userId);
            saveObj.setLastUpdateUserId(userId);


            List<FnTenantPrice> tenantPriceList = new ArrayList<FnTenantPrice>();
            List<FnTenantRoom> tenantRoomList = new ArrayList<FnTenantRoom>();
            List<FnTenantPayType> tenantPayTypeList = new ArrayList<FnTenantPayType>();
            List<FnTenantContact> tenantContactList = new ArrayList<FnTenantContact>();
            List<FnTenantEnergySplit> tenantEnergySplitList = new ArrayList<FnTenantEnergySplit>();

            String energyTypeIds = "";
            //能耗类型
            List<Object> list = (List<Object>) dto.get("energyList");
            for (Object obj : list) {
                Map<String, Object> map = (Map<String, Object>) obj;
                String energyTypeId = (String) map.get("energyTypeId");
                if ("".equals(energyTypeIds)) {
                    energyTypeIds = energyTypeId;
                } else {
                    energyTypeIds = energyTypeIds + "," + energyTypeId;
                }
                Integer ffType = (Integer) map.get("ffType");
                Integer kfType = (Integer) map.get("kfType");
                Integer czType = (Integer) map.get("czType");
                String priceTemplateId = (String) map.get("priceTemplateId");

                {//价格
                    FnTenantPrice tenantPrice = new FnTenantPrice();
                    tenantPrice.setId(UUID.randomUUID().toString());
                    tenantPrice.setEnergyTypeId(energyTypeId);
                    tenantPrice.setLastUpdateTime(now);
                    tenantPrice.setTenantId(tenantId);
                    tenantPrice.setPriceTemplateId(priceTemplateId);
                    tenantPrice.setLastUpdateUserId(userId);
                    tenantPriceList.add(tenantPrice);
                }
                {//付费
                    FnTenantPayType tenantPayType = new FnTenantPayType();
                    tenantPayType.setBuildingId(buildingId);
                    tenantPayType.setId(UUID.randomUUID().toString());
                    tenantPayType.setTenantId(tenantId);
                    tenantPayType.setPayType(ffType);
                    if (kfType == null) {
                        tenantPayType.setPrePayType(EnumPrePayType.None.getValue());
                    } else {

                        tenantPayType.setPrePayType(kfType);
                    }
                    if (czType == null) {
                        tenantPayType.setPrepayChargeType(EnumPrepayChargeType.Liang.getValue());
                    } else {
                        tenantPayType.setPrepayChargeType(czType);
                    }


                    tenantPayType.setEnergyTypeId(energyTypeId);

                    tenantPayTypeList.add(tenantPayType);
                }

            }
            saveObj.setEnergyTypeIds(energyTypeIds);

            List<Object> energySplitExpressionList = (List<Object>) dto.get("energySplitExpressionList");
            for (Object obj : energySplitExpressionList) {
                Map<String, Object> map = (Map<String, Object>) obj;
                FnTenantEnergySplit fnTenantEnergySplit = new FnTenantEnergySplit();
                fnTenantEnergySplit.setId(UUID.randomUUID().toString());
                fnTenantEnergySplit.setTenantId(tenantId);
                fnTenantEnergySplit.setElements("");
                fnTenantEnergySplit.setEnergyTypeId((String) map.get("energyTypeId"));
                fnTenantEnergySplit.setExpression((String) map.get("expression"));
                tenantEnergySplitList.add(fnTenantEnergySplit);
            }


            List<Object> roomList = (List<Object>) dto.get("roomList");
            List<String> roomIdList = new ArrayList<>();
            String roomCodes = "";
            for (Object room : roomList) {
                Map<String, String> roomMap = (Map<String, String>) room;
                String roomId = roomMap.get("roomId");
                String roomCode = roomMap.get("roomCode");
                if ("".equals(roomCodes)) {
                    roomCodes = roomCode;
                } else {
                    roomCodes = roomCodes + "," + roomCode;
                }
                roomIdList.add(roomId);
                FnTenantRoom tenantRoom = new FnTenantRoom();
                tenantRoom.setId(UUID.randomUUID().toString());
                tenantRoom.setTenantId(tenantId);
                tenantRoom.setBuildingId(buildingId);
                tenantRoom.setRoomId(roomId);
                tenantRoom.setRoomCode(roomCode);
                tenantRoomList.add(tenantRoom);

            }
            saveObj.setRoomCodes(roomCodes);


            List<Object> contactList = (List<Object>) dto.get("contactList");
            for (Object obj : contactList) {
                Map<String, Object> map = (Map<String, Object>) obj;
                FnTenantContact fnTenantContact = new FnTenantContact();
                fnTenantContact.setId(UUID.randomUUID().toString());
                fnTenantContact.setTenantId(tenantId);
                fnTenantContact.setContactName(map.get("contactName").toString());
                fnTenantContact.setContactMobile(map.get("contactMobile").toString());
                tenantContactList.add(fnTenantContact);
            }

            fnTenantService.save(saveObj);
            fnTenantContactService.save(tenantContactList);
            fnTenantEnergySplitService.save(tenantEnergySplitList);
            fnTenantRoomService.save(tenantRoomList);
            fnTenantPriceService.save(tenantPriceList);
            fnTenantPayTypeService.save(tenantPayTypeList);
            if (roomIdList.size() > 0) {
                fnRoomService.updateRoomStatus(buildingId, roomIdList, EnumUseStatus.In_Use);
            }
            //添加租户英文名对照表
            FnTenantSpellName save = new FnTenantSpellName();
            save.setId(tenantId);
            save.setTenantName(tenantName);
            String spellName = ToSpellUtil.Instance().getStringPinYin(tenantName);
            save.setSpellName(spellName);
            fnTenantSpellNameService.save(save);

            //新增租户全码
            {
                StringBuffer sb = new StringBuffer();
                sb.append(buildingId).append("-").append(tenantId).append("-").append((int) (Math.random() * 90 + 10));
                FnTenantFlag tenantFlag = new FnTenantFlag();
                tenantFlag.setId(UUID.randomUUID().toString());
                tenantFlag.setBuildingId(buildingId);
                tenantFlag.setTenantId(tenantId);
                tenantFlag.setTenantFlag(sb.toString());
                fnTenantFlagService.save(tenantFlag);
            }

            Map<String, Object> contentObj = new HashMap<>();
            contentObj.put("result", 0);
            contentObj.put("message", "");
            content.add(contentObj);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "修改");
        }
    }

    /**
     * 租户 -- 更新
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantUpdateService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantUpdate(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String userId = this.getParamUserId(dto);
            String tenantId = (String) dto.get("tenantId");
            String tenantName = (String) dto.get("tenantName");
            String buildingId = (String) dto.get("buildingId");
            String tenantTypeId = (String) dto.get("tenantTypeId");
            Double area = DoubleFormatUtil.Instance().getDoubleData(dto.get("area"));
            String contactName = (String) dto.get("contactName");
            String contactMobile = (String) dto.get("contactMobile");
            String remark = (String) dto.get("remark");

            if (userId == null
                    || tenantId == null
                    || tenantName == null
                    || buildingId == null
                    || tenantTypeId == null
                    || area == null
                    || contactName == null
                    || contactMobile == null
                    || dto.get("energyList") == null
                    || dto.get("roomList") == null
            ) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnTenant old = fnTenantService.queryOne(tenantId);
            if (old == null) {
                throw new Exception("被修改的租户不存在:" + tenantId);
            }
            Date now = new Date();
            if (old.getStatus().intValue() == EnumTenantStatus.NOT_ACTIVE.getValue().intValue()) {
                FnTenant query = new FnTenant();
                query.setId(tenantId);

                FnTenant update = new FnTenant();
                update.setName(tenantName);
                update.setBuildingId(buildingId);
                update.setTenantTypeId(tenantTypeId);
                update.setArea(area);
                update.setContactName(contactName);
                update.setContactMobile(contactMobile);
                update.setRemark(remark);

                List<FnTenantPrice> tenantPriceList = new ArrayList<FnTenantPrice>();
                List<FnTenantRoom> tenantRoomList = new ArrayList<FnTenantRoom>();
                List<FnTenantPayType> tenantPayTypeList = new ArrayList<FnTenantPayType>();
                List<FnTenantContact> tenantContactList = new ArrayList<FnTenantContact>();
                List<FnTenantEnergySplit> tenantEnergySplitList = new ArrayList<FnTenantEnergySplit>();

                String energyTypeIds = "";

                List<Object> list = (List<Object>) dto.get("energyList"); //能耗类型
                for (Object obj : list) {
                    Map<String, Object> map = (Map<String, Object>) obj;
                    String energyTypeId = (String) map.get("energyTypeId");
                    if ("".equals(energyTypeIds)) {
                        energyTypeIds = energyTypeId;
                    } else {
                        energyTypeIds = energyTypeIds + "," + energyTypeId;
                    }
                    Integer ffType = (Integer) map.get("ffType");
                    Integer kfType = (Integer) map.get("kfType");
                    Integer czType = (Integer) map.get("czType");
                    String priceTemplateId = (String) map.get("priceTemplateId");

                    {//价格
                        FnTenantPrice tenantPrice = new FnTenantPrice();
                        tenantPrice.setId(UUID.randomUUID().toString());
                        tenantPrice.setEnergyTypeId(energyTypeId);
                        tenantPrice.setLastUpdateTime(now);
                        tenantPrice.setTenantId(tenantId);
                        tenantPrice.setPriceTemplateId(priceTemplateId);
                        tenantPrice.setLastUpdateUserId(userId);
                        tenantPriceList.add(tenantPrice);
                    }
                    {//付费
                        FnTenantPayType tenantPayType = new FnTenantPayType();
                        tenantPayType.setId(UUID.randomUUID().toString());
                        tenantPayType.setBuildingId(buildingId);
                        tenantPayType.setTenantId(tenantId);
                        tenantPayType.setPayType(ffType);
                        if (kfType == null) {
                            tenantPayType.setPrePayType(EnumPrePayType.None.getValue());
                        } else {
                            tenantPayType.setPrePayType(kfType);
                        }
                        if (czType == null) {
                            tenantPayType.setPrepayChargeType(EnumPrepayChargeType.None.getValue());
                        } else {
                            tenantPayType.setPrepayChargeType(czType);
                        }
                        tenantPayType.setEnergyTypeId(energyTypeId);

                        tenantPayTypeList.add(tenantPayType);
                    }

                }
                update.setEnergyTypeIds(energyTypeIds);

                List<Object> energySplitExpressionList = (List<Object>) dto.get("energySplitExpressionList");
                for (Object obj : energySplitExpressionList) {
                    Map<String, Object> map = (Map<String, Object>) obj;
                    FnTenantEnergySplit fnTenantEnergySplit = new FnTenantEnergySplit();
                    fnTenantEnergySplit.setId(UUID.randomUUID().toString());
                    fnTenantEnergySplit.setTenantId(tenantId);
                    fnTenantEnergySplit.setEnergyTypeId((String) map.get("energyTypeId"));
                    fnTenantEnergySplit.setExpression((String) map.get("expression"));
                    fnTenantEnergySplit.setElements("");
                    tenantEnergySplitList.add(fnTenantEnergySplit);
                }


                List<Object> roomList = (List<Object>) dto.get("roomList");
                List<String> roomIdList = new ArrayList<>();
                String roomCodes = "";
                for (Object room : roomList) {
                    Map<String, String> roomMap = (Map<String, String>) room;
                    String roomId = roomMap.get("roomId");
                    String roomCode = roomMap.get("roomCode");
                    if ("".equals(roomCodes)) {
                        roomCodes = roomCode;
                    } else {
                        roomCodes = roomCodes + "," + roomCode;
                    }
                    roomIdList.add(roomId);
                    FnTenantRoom tenantRoom = new FnTenantRoom();
                    tenantRoom.setId(UUID.randomUUID().toString());
                    tenantRoom.setTenantId(tenantId);
                    tenantRoom.setBuildingId(buildingId);
                    tenantRoom.setRoomId(roomId);
                    tenantRoom.setRoomCode(roomCode);
                    tenantRoomList.add(tenantRoom);
                }


                List<Object> contactList = (List<Object>) dto.get("contactList");
                for (Object obj : contactList) {
                    Map<String, Object> map = (Map<String, Object>) obj;
                    FnTenantContact fnTenantContact = new FnTenantContact();
                    fnTenantContact.setId(UUID.randomUUID().toString());
                    fnTenantContact.setTenantId(tenantId);
                    fnTenantContact.setContactName(map.get("contactName").toString());
                    fnTenantContact.setContactMobile(map.get("contactMobile").toString());
                    tenantContactList.add(fnTenantContact);
                }

                fnTenantService.update(query, update);
                {
                    FnTenantContact removeQuery = new FnTenantContact();
                    removeQuery.setTenantId(tenantId);
                    fnTenantContactService.remove(removeQuery);
                }
                fnTenantContactService.save(tenantContactList);
                {
                    FnTenantEnergySplit removeQuery = new FnTenantEnergySplit();
                    removeQuery.setTenantId(tenantId);
                    fnTenantEnergySplitService.remove(removeQuery);
                }
                fnTenantEnergySplitService.save(tenantEnergySplitList);
                {
                    FnTenantRoom removeQuery = new FnTenantRoom();
                    removeQuery.setTenantId(tenantId);
                    fnTenantRoomService.remove(removeQuery);
                }
                fnTenantRoomService.save(tenantRoomList);
                {
                    FnTenantPrice removeQuery = new FnTenantPrice();
                    removeQuery.setTenantId(tenantId);
                    fnTenantPriceService.remove(removeQuery);
                }
                fnTenantPriceService.save(tenantPriceList);

                {
                    FnTenantPayType removeQuery = new FnTenantPayType();
                    removeQuery.setTenantId(tenantId);
                    fnTenantPayTypeService.remove(removeQuery);
                }
                fnTenantPayTypeService.save(tenantPayTypeList);
                {//老房间
                    FnTenantRoom tenantRoomQuery = new FnTenantRoom();
                    tenantRoomQuery.setTenantId(tenantId);
                    List<FnTenantRoom> oldRoomList = fnTenantRoomService.query(tenantRoomQuery);
                    if (oldRoomList != null) {
                        List<String> oldIdList = new ArrayList<String>();
                        for (FnTenantRoom tenantRoom : oldRoomList) {
                            oldIdList.add(tenantRoom.getRoomId());
                        }
                        if (oldIdList.size() > 0) {
                            fnRoomService.updateRoomStatus(buildingId, oldIdList, EnumUseStatus.Not_In_Use);
                        }
                    }
                }
                if (roomIdList.size() > 0) {
                    fnRoomService.updateRoomStatus(buildingId, roomIdList, EnumUseStatus.In_Use);
                }
            } else if (old.getStatus().intValue() == EnumTenantStatus.ACTIVATED.getValue().intValue()) {
                FnTenant query = new FnTenant();
                query.setId(tenantId);

                FnTenant update = new FnTenant();
                update.setName(tenantName);
                update.setBuildingId(buildingId);
                update.setTenantTypeId(tenantTypeId);
                update.setArea(area);
                update.setContactName(contactName);
                update.setContactMobile(contactMobile);
                update.setRemark(remark);
                update.setLastUpdateTime(now);
                List<FnTenantContact> tenantContactList = new ArrayList<FnTenantContact>();

                List<FnRecordPriceChange> priceChangeList = new ArrayList<FnRecordPriceChange>();

                List<Object> list = (List<Object>) dto.get("energyList"); //能耗类型
                String buildingName = null;
                String userName = null;

                for (Object obj : list) {
                    Map<String, Object> map = (Map<String, Object>) obj;
                    String energyTypeId = (String) map.get("energyTypeId");
                    String priceTemplateId = (String) map.get("priceTemplateId");

                    {//价格
                        FnTenantPrice tenantPriceQuery = new FnTenantPrice();
                        tenantPriceQuery.setTenantId(tenantId);
                        tenantPriceQuery.setEnergyTypeId(energyTypeId);
                        FnTenantPrice oldPrice = (FnTenantPrice) fnTenantPriceService.queryObject(tenantPriceQuery);

                        if (!oldPrice.getPriceTemplateId().equals(priceTemplateId)) {
                            //更新
                            FnTenantPrice tenantPriceUpdate = new FnTenantPrice();
                            tenantPriceUpdate.setPriceTemplateId(priceTemplateId);
                            tenantPriceUpdate.setLastUpdateTime(now);
                            tenantPriceUpdate.setLastUpdateUserId(userId);
                            fnTenantPriceService.update(tenantPriceQuery, tenantPriceUpdate);
                            {
                                FnRecordPriceChange recordPriceChange = new FnRecordPriceChange();
                                recordPriceChange.setBuildingId(buildingId);
                                if (buildingName == null) {
                                    try {
                                        buildingName = fnBuildingService.query(buildingId).getName();
                                    } catch (Exception e) {
                                    }
                                }
                                recordPriceChange.setId(UUID.randomUUID().toString());
                                recordPriceChange.setBuildingName(buildingName);
                                recordPriceChange.setTenantId(tenantId);
                                recordPriceChange.setEnergyTypeId(energyTypeId);
                                recordPriceChange.setTenantName(tenantName);
                                recordPriceChange.setChangeTime(now);
                                recordPriceChange.setBeforePriceId(oldPrice.getPriceTemplateId());
                                {
                                    FnPriceTemplate priceTemplateQuery = new FnPriceTemplate();
                                    priceTemplateQuery.setId(oldPrice.getPriceTemplateId());
                                    FnPriceTemplate oldPriceTemplate = (FnPriceTemplate) fnPriceTemplateService.queryObject(priceTemplateQuery);
                                    recordPriceChange.setBeforePriceName(oldPriceTemplate.getName());
                                }
                                recordPriceChange.setAfterPriceId(priceTemplateId);
                                {
                                    FnPriceTemplate priceTemplateQuery = new FnPriceTemplate();
                                    priceTemplateQuery.setId(priceTemplateId);
                                    FnPriceTemplate newPriceTemplate = (FnPriceTemplate) fnPriceTemplateService.queryObject(priceTemplateQuery);
                                    recordPriceChange.setAfterPriceName(newPriceTemplate.getName());
                                }
                                recordPriceChange.setUserId(userId);
                                if (userName == null) {
                                    try {
                                        userName = fnUserService.queryUserByUserId(userId).getUserName();
                                    } catch (Exception e) {
                                    }
                                }
                                recordPriceChange.setUserName(userName);
                                recordPriceChange.setRoomIds(old.getRoomCodes());
                                recordPriceChange.setCreateTime(now);
                                priceChangeList.add(recordPriceChange);
                            }
                        }
                    }
                }

                {
                    List<Object> contactList = (List<Object>) dto.get("contactList");
                    for (Object obj : contactList) {
                        Map<String, Object> map = (Map<String, Object>) obj;
                        FnTenantContact fnTenantContact = new FnTenantContact();
                        fnTenantContact.setId(UUID.randomUUID().toString());
                        fnTenantContact.setTenantId(tenantId);
                        fnTenantContact.setContactName(map.get("contactName").toString());
                        fnTenantContact.setContactMobile(map.get("contactMobile").toString());
                        tenantContactList.add(fnTenantContact);
                    }
                    {
                        FnTenantContact removeQuery = new FnTenantContact();
                        removeQuery.setTenantId(tenantId);
                        fnTenantContactService.remove(removeQuery);
                    }
                    fnTenantContactService.save(tenantContactList);
                }

                if (priceChangeList.size() > 0) {
                    fnRecordPriceChangeService.save(priceChangeList);
                }

                fnTenantService.update(query, update);

            } else {
                throw new Exception("被修改的租户不支持修改:" + tenantId);
            }
            //更新租户英文名对照表
            FnTenantSpellName query = new FnTenantSpellName();
            query.setId(tenantId);
            FnTenantSpellName update = new FnTenantSpellName();
            String stringPinYin = ToSpellUtil.Instance().getStringPinYin(tenantName);
            update.setSpellName(stringPinYin);
            update.setTenantName(tenantName);
            fnTenantSpellNameService.update(query, update);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTTenantUpdateService");
        }
    }


    /**
     * 租户 -- 编辑
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantEditService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantEdit(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");

            if (tenantId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnTenant query = new FnTenant();
            query.setId(tenantId);

            FnTenant fnTenant = (FnTenant) fnTenantService.queryObject(query);

            Map<String, Object> tenantMap = new HashMap<String, Object>();
            tenantMap.put("tenantId", fnTenant.getId());
            tenantMap.put("tenantName", fnTenant.getName());
            tenantMap.put("buildingId", fnTenant.getBuildingId());
            Building building = fnBuildingService.query(fnTenant.getBuildingId());

            tenantMap.put("buildingName", building == null ? null : building.getName());//
            tenantMap.put("tenantTypeId", fnTenant.getTenantTypeId());

            FnTenantType fnTenantType = new FnTenantType();
            fnTenantType.setId(fnTenant.getTenantTypeId());

            FnTenantType queryFnTenantType = (FnTenantType) fnTenantTypeService.queryObject(fnTenantType);

            tenantMap.put("tenantTypeName", queryFnTenantType.getName());//
            tenantMap.put("area", fnTenant.getArea());
            tenantMap.put("contactName", fnTenant.getContactName());
            tenantMap.put("contactMobile", fnTenant.getContactMobile());
            tenantMap.put("remark", fnTenant.getRemark());

            {
                List<Object> contactList = new ArrayList<Object>();
                FnTenantContact fnTenantContactQuery = new FnTenantContact();
                fnTenantContactQuery.setTenantId(tenantId);
                List<FnTenantContact> fnTenantContactList = fnTenantContactService.query(fnTenantContactQuery);
                for (FnTenantContact fnTenantContact : fnTenantContactList) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("contactName", fnTenantContact.getContactName());
                    map.put("contactMobile", fnTenantContact.getContactMobile());
                    contactList.add(map);
                }
                tenantMap.put("contactList", contactList);
            }


            {
                List<Object> roomList = new ArrayList<Object>();
                List<DTORoom> dtoRoomList = fnRoomService.queryRoomListByTenantId(tenantId);

                for (DTORoom dtoRoom : dtoRoomList) {
                    Map<String, Object> roomMap = new HashMap<String, Object>();
                    roomMap.put("roomId", dtoRoom.getRoomId());
                    roomMap.put("roomCode", dtoRoom.getRoomCode());
                    List<Object> energyList = new ArrayList<Object>();
                    roomMap.put("energyList", energyList);
                    List<DTOEnergyTypeMeter> energyTypeMeterList = dtoRoom.getEneryList();
                    if (energyTypeMeterList != null && energyTypeMeterList.size() > 0) {
                        for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                            for (DTOEnergyTypeMeter energyTypeMeter : energyTypeMeterList) {
                                if (energyType.getId().equals(energyTypeMeter.getEnergyTypeId())) {
                                    Map<String, Object> energyTypeMeterMap = new HashMap<String, Object>();
                                    energyTypeMeterMap.put("energyTypeId", energyTypeMeter.getEnergyTypeId());
                                    energyTypeMeterMap.put("energyTypeName", EnergyTypeUtil.queryEnergyTypeNameById(energyTypeMeter.getEnergyTypeId()));
                                    energyList.add(energyTypeMeterMap);
                                    List<Object> meterList = new ArrayList<Object>();
                                    for (String meterId : energyTypeMeter.getMeterList()) {
                                        meterList.add(meterId);
                                    }
                                    energyTypeMeterMap.put("meterList", meterList);
                                }
                            }
                        }
                    }
                    roomList.add(roomMap);
                }

                tenantMap.put("roomList", roomList);
            }

            {
                List<Object> energySplitExpressionList = new ArrayList<Object>();
                FnTenantEnergySplit fnTenantEnergySplitQuery = new FnTenantEnergySplit();
                fnTenantEnergySplitQuery.setTenantId(tenantId);
                List<FnTenantEnergySplit> fnTenantEnergySplitList = fnTenantEnergySplitService.query(fnTenantEnergySplitQuery);
                for (FnTenantEnergySplit fnTenantEnergySplit : fnTenantEnergySplitList) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("energyTypeId", fnTenantEnergySplit.getEnergyTypeId());
                    map.put("expression", fnTenantEnergySplit.getExpression());
                    energySplitExpressionList.add(map);
                }
                tenantMap.put("energySplitExpressionList", energySplitExpressionList);
            }

            {
                List<Object> energyList = new ArrayList<Object>();
                FnTenantPayType fnTenantPayTypeQuery = new FnTenantPayType();
                fnTenantPayTypeQuery.setTenantId(tenantId);
                List<FnTenantPayType> fnTenantPayTypeList = fnTenantPayTypeService.query(fnTenantPayTypeQuery);

                Map<String, String> priceMap = fnTenantPriceService.queryPriceTemplateIdMap(tenantId);

                Map<String, FnPriceTemplate> priceTemplateMap = new HashMap<String, FnPriceTemplate>();
                if (fnTenantPayTypeList.size() > 0) {
                    List<FnPriceTemplate> priceTemplateList = fnPriceTemplateService.queryList();
                    if (priceTemplateList != null) {
                        for (FnPriceTemplate priceTemplate : priceTemplateList) {
                            priceTemplateMap.put(priceTemplate.getId(), priceTemplate);
                        }
                    }
                }
                for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                    for (FnTenantPayType fnTenantPayType : fnTenantPayTypeList) {
                        if (!energyType.getId().equals(fnTenantPayType.getEnergyTypeId())) {
                            continue;
                        }
                        Map<String, Object> map = new HashMap<String, Object>();
                        map.put("energyTypeId", fnTenantPayType.getEnergyTypeId());
                        map.put("ffType", fnTenantPayType.getPayType());
                        map.put("kfType", fnTenantPayType.getPrePayType());
                        map.put("cztype", fnTenantPayType.getPrepayChargeType());
                        FnPriceTemplate template = priceTemplateMap.get(priceMap.get(fnTenantPayType.getEnergyTypeId()));
                        Map<String, Object> priceTemplate = new HashMap<String, Object>();
                        map.put("priceTemplate", priceTemplate);
                        priceTemplate.put("id", template.getId());
                        priceTemplate.put("name", template.getName());
                        priceTemplate.put("type", template.getType());
                        energyList.add(map);
                    }
                }

                tenantMap.put("energyList", energyList);
            }

            content.add(tenantMap);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTTenantEditService");
        }
    }

    /**
     * 租户 -- 删除
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantRemoveService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantRemove(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");
            String userId = this.getParamUserId(dto);


            if (tenantId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            //查询租户
            FnTenant query = new FnTenant();//未激活
            query.setId(tenantId);

            FnTenant fnTenant = (FnTenant) fnTenantService.queryObject(query);
            if (fnTenant.getStatus().intValue() != EnumTenantStatus.NOT_ACTIVE.getValue().intValue()) {
                throw new Exception("租户状态不是未激活状态:" + tenantId);
            }

            Map<String, Integer> result = new HashMap<String, Integer>();
            FnTenant update = new FnTenant();
            update.setIsValid(EnumValidStatus.INVALID.getValue());
            Date now = new Date();
            update.setLastUpdateTime(now);
            update.setInvalidTime(now);
            update.setLastUpdateUserId(userId);
            fnTenantService.update(query, update);

            //修改租户房间占用关系
            List<FnTenantRoom> roomList = fnTenantRoomService.queryRoomByTenantId(tenantId);
            if (roomList != null && roomList.size() > 0) {
                List<String> roomIdList = new ArrayList<>();
                for (FnTenantRoom tenantRoom : roomList) {
                    roomIdList.add(tenantRoom.getRoomId());
                }
                if (roomIdList.size() > 0) {
                    fnRoomService.updateRoomStatus(fnTenant.getBuildingId(), roomIdList, EnumUseStatus.Not_In_Use);
                }
            }

            result.put("result", 0);
            content.add(result);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTTenantRemoveService");
        }
    }


    /**
     * 租户 -- 列表
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantList(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer tenantStatus = (Integer) dto.get("tenantStatus");
            String energyType = (String) dto.get("energyType");
            String buildingId = (String) dto.get("buildingId");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");
            List<Object> sort = (List<Object>) dto.get("sort");

            Map<String, Object> contentMap = new HashMap<String, Object>();
            content.add(contentMap);
            List<Object> tenantList = new ArrayList<Object>();
            contentMap.put("tenantList", tenantList);

            if (pageIndex == null || pageSize == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            if (tenantStatus == null) {
                if (energyType == null) {
                    this.processEnergyTypeNull(contentMap, tenantList, tenantStatus, buildingId, pageIndex, pageSize, sort);
                    return Result.SUCCESS(content);
                }
                String[] energyTypeArray = energyType.split("_");
                String energyTypeId = energyTypeArray[0].trim();
                Integer payType = Integer.parseInt(energyTypeArray[1].trim());
                if (payType.intValue() == EnumPayType.PREPAY.getValue()) {
                    this.processAllPre(contentMap, tenantList, buildingId, energyTypeId, pageIndex, pageSize, sort);
                } else {
                    this.processAllPost(contentMap, tenantList, buildingId, energyTypeId, pageIndex, pageSize, sort);
                }
            } else if (tenantStatus.intValue() == EnumTenantStatus.NOT_ACTIVE.getValue().intValue()) {
                if (energyType == null) {
                    this.processEnergyTypeNull(contentMap, tenantList, tenantStatus, buildingId, pageIndex, pageSize, sort);
                    return Result.SUCCESS(content);
                } else {
                    String[] energyTypeArray = energyType.split("_");
                    String energyTypeId = energyTypeArray[0].trim();
                    Integer payType = Integer.parseInt(energyTypeArray[1].trim());
                    Integer prePayType = null;
                    if (payType.intValue() == EnumPayType.PREPAY.getValue().intValue()) {
                        Map<String, Object> prePayParam = (Map<String, Object>) dto.get("prePayParam");
                        if (prePayParam != null) {
                            prePayType = (Integer) prePayParam.get("prePayType");
                        }
                    }
                    this.processNotActive(contentMap, tenantList, buildingId, energyTypeId, payType, prePayType, pageIndex,
                            pageSize, sort);
                }
            } else if (tenantStatus.intValue() == EnumTenantStatus.ACTIVATED.getValue().intValue()) {
                if (energyType == null) {
                    this.processEnergyTypeNull(contentMap, tenantList, tenantStatus, buildingId, pageIndex, pageSize, sort);
                    return Result.SUCCESS(content);
                }

                String[] energyTypeArray = energyType.split("_");
                String energyTypeId = energyTypeArray[0].trim();
                String energyUnit = UnitUtil.getCumulantUnit(energyTypeId);
                contentMap.put("energyUnit", energyUnit);
                Integer payType = Integer.parseInt(energyTypeArray[1].trim());
                if (payType.intValue() == EnumPayType.PREPAY.getValue().intValue()) {
                    Map<String, Object> prePayParam = (Map<String, Object>) dto.get("prePayParam");
                    Integer prePayType = null;
                    Integer remainType = null;
                    Integer limit = null;
                    if (prePayParam != null) {
                        prePayType = (Integer) prePayParam.get("prePayType");
                        remainType = (Integer) prePayParam.get("remainType");
                        limit = (Integer) prePayParam.get("limit");
                    }
                    this.processActivedPre(contentMap, tenantList, buildingId, energyTypeId, prePayType, remainType, limit,
                            pageIndex, pageSize, sort);
                } else if (payType.intValue() == EnumPayType.POSTPAY.getValue().intValue()) {
                    Map<String, Object> postPayParam = (Map<String, Object>) dto.get("postPayParam");
                    Integer amountType = null;
                    Integer limit = null;
                    if (postPayParam != null) {
                        amountType = (Integer) postPayParam.get("amountType");
                        limit = (Integer) postPayParam.get("limit");
                    }
                    this.processActivedPost(contentMap, tenantList, buildingId, energyTypeId, amountType, limit, pageIndex,
                            pageSize, sort);
                }
            } else if (tenantStatus.intValue() == EnumTenantStatus.RETURNED_LEASE.getValue().intValue()) {
                this.processReturnLease(contentMap, tenantList, buildingId, pageIndex, pageSize, sort);
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTTenantListService");
        }
    }

    @SuppressWarnings("unchecked")
    private void processAllPost(Map<String, Object> contentMap, List<Object> tenantResultList, String buildingId,
                                String energyTypeId, Integer pageIndex, Integer pageSize, List<Object> sort) throws Exception {
        Map<String, EMSOrder> orderMap = new HashMap<String, EMSOrder>();
        if (sort != null) {
            for (Object obj : sort) {
                Map<String, Object> sortObj = (Map<String, Object>) obj;
                String field = (String) sortObj.get("field");
                Integer type = (Integer) sortObj.get("type");

                if ("tenantId".equals(field)) {
                    orderMap.put("t.c_id", EnumSort.valueOf(type).getOrder());
                }
            }
        }

        if (orderMap.size() == 0) {
            orderMap.put("t.c_id", EMSOrder.Asc);
        }
        List<Map<String, Object>> tenantList = fnTenantService.queryByPayType(buildingId, energyTypeId, null,
                EnumPayType.POSTPAY, null, pageIndex, pageSize, orderMap);
        if (tenantList != null && tenantList.size() > 0) {
            Map<String, Building> buildingMap = fnBuildingService.query();
            for (Map<String, Object> tenant : tenantList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("tenantId", tenant.get("c_tenant_id"));
                map.put("tenantName", tenant.get("c_name"));
                map.put("roomIds", tenant.get("c_room_codes"));
                String c_building_id = (String) tenant.get("c_building_id");
                map.put("buildingName",
                        buildingMap.containsKey(c_building_id) ? buildingMap.get(c_building_id).getName() : null);
                map.put("area", tenant.get("c_area"));
                tenantResultList.add(map);
            }
        }
        contentMap.put("count",
                fnTenantService.countByPayType(buildingId, energyTypeId, null, EnumPayType.POSTPAY, null));
    }

    @SuppressWarnings("unchecked")
    private void processEnergyTypeNull(Map<String, Object> contentMap, List<Object> tenantResultList,
                                       Integer tenantStatus, String buildingId, Integer pageIndex, Integer pageSize, List<Object> sort)
            throws Exception {
        FnTenant query = new FnTenant();
        query.setBuildingId(buildingId);
        query.setStatus(tenantStatus);
        query.setIsValid(EnumValidStatus.VALID.getValue());
        query.setSort("buildingId", EMSOrder.Asc);
        contentMap.put("count", fnTenantService.count(query));
        query.setSkip(Long.valueOf(pageIndex) * pageSize);
        query.setLimit(Long.valueOf(pageSize));
        if (sort != null) {
            for (Object obj : sort) {
                Map<String, Object> sortObj = (Map<String, Object>) obj;
                String field = (String) sortObj.get("field");
                Integer type = (Integer) sortObj.get("type");

                if ("tenantId".equals(field)) {
                    query.setSort("id", EnumSort.valueOf(type).getOrder());
                }
            }
        }

        List<FnTenant> tenantList = fnTenantService.query(query);
        if (tenantList != null && tenantList.size() > 0) {

            Map<String, Building> buildingMap = fnBuildingService.query();

            for (FnTenant tenant : tenantList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("tenantId", tenant.getId());
                map.put("tenantStatus", tenant.getStatus());
                map.put("tenantName", tenant.getName());
                map.put("roomIds", tenant.getRoomCodes());
                map.put("buildingId", tenant.getBuildingId());
                map.put("buildingName", buildingMap.containsKey(tenant.getBuildingId())
                        ? buildingMap.get(tenant.getBuildingId()).getName() : null);
                map.put("area", tenant.getArea());
                map.put("contactName", tenant.getContactName());
                map.put("contactMobile", tenant.getContactMobile());
                tenantResultList.add(map);
            }
        }

    }

    /**
     * 未激活
     */
    @SuppressWarnings("unchecked")
    private void processNotActive(Map<String, Object> contentMap, List<Object> tenantResultList, String buildingId,
                                  String energyTypeId, Integer payType, Integer prePayType, Integer pageIndex, Integer pageSize,
                                  List<Object> sort) throws Exception {

        Map<String, EMSOrder> orderMap = new HashMap<String, EMSOrder>();
        if (sort != null) {
            for (Object obj : sort) {
                Map<String, Object> sortObj = (Map<String, Object>) obj;
                String field = (String) sortObj.get("field");
                Integer type = (Integer) sortObj.get("type");

                if ("tenantId".equals(field)) {
                    orderMap.put("t.c_id", EnumSort.valueOf(type).getOrder());
                }
            }
        }
        if (orderMap.size() == 0) {
            orderMap.put("t.c_id", EMSOrder.Asc);
        }
        EnumPrePayType enumPrePayType = null;
        if (prePayType != null && prePayType.intValue() != -1) {
            enumPrePayType = EnumPrePayType.valueOf(prePayType);
        }

        List<Map<String, Object>> tenantList = fnTenantService.queryByPayType(buildingId, energyTypeId,
                EnumTenantStatus.NOT_ACTIVE, EnumPayType.valueOf(payType), enumPrePayType, pageIndex, pageSize,
                orderMap);
        if (tenantList != null && tenantList.size() > 0) {

            Map<String, Building> buildingMap = fnBuildingService.query();

            for (Map<String, Object> tenant : tenantList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("tenantId", tenant.get("c_tenant_id"));
                map.put("tenantName", tenant.get("c_name"));
                map.put("tenantStatus", tenant.get("c_status"));
                map.put("roomIds", tenant.get("c_room_codes"));
                String c_building_id = (String) tenant.get("c_building_id");
                map.put("buildingId", tenant.get("c_building_id"));
                map.put("buildingName",
                        buildingMap.containsKey(c_building_id) ? buildingMap.get(c_building_id).getName() : null);
                map.put("area", tenant.get("c_area"));
                map.put("contactName", tenant.get("c_contact_name"));
                map.put("contactMobile", tenant.get("c_contact_mobile"));
                tenantResultList.add(map);
            }
        }
        contentMap.put("count", fnTenantService.countByPayType(buildingId, energyTypeId, EnumTenantStatus.NOT_ACTIVE,
                EnumPayType.valueOf(payType), enumPrePayType));
    }

    /**
     * 已激活-后付费
     */
    @SuppressWarnings("unchecked")
    private void processActivedPost(Map<String, Object> contentMap, List<Object> tenantResultList, String buildingId,
                                    String energyTypeId, Integer amountType, Integer limit, Integer pageIndex, Integer pageSize,
                                    List<Object> sort) throws Exception {

        Map<String, EMSOrder> orderMap = new LinkedHashMap<String, EMSOrder>();
        if (sort != null) {
            for (Object obj : sort) {
                Map<String, Object> sortObj = (Map<String, Object>) obj;
                String field = (String) sortObj.get("field");
                Integer type = (Integer) sortObj.get("type");

                if ("tenantId".equals(field)) {
                    orderMap.put("c_id", EnumSort.valueOf(type).getOrder());
                } else if ("lastClearingTime".equals(field)) {
                    orderMap.put("tppp.c_last_clearing_time", EnumSort.valueOf(type).getOrder());
                }
            }
        }
        if (orderMap.size() == 0) {
            orderMap.put("t.c_id", EMSOrder.Asc);
        }
        EnumAmountType enumAmountType = null;
        if (amountType != null) {
            enumAmountType = EnumAmountType.valueOf(amountType);
        }

        EnumAmountTypeLimit enumAmountTypeLimit = null;
        if (limit != null && limit.intValue() != 0) {
            enumAmountTypeLimit = EnumAmountTypeLimit.valueOf(limit);
        }

        List<Map<String, Object>> tenantList = fnTenantService.queryPostByParam(buildingId, energyTypeId,
                EnumTenantStatus.ACTIVATED, enumAmountType, enumAmountTypeLimit, pageIndex, pageSize, orderMap);
        if (tenantList != null && tenantList.size() > 0) {

            Map<String, Building> buildingMap = fnBuildingService.query();

            for (Map<String, Object> tenant : tenantList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("tenantId", tenant.get("c_id"));
                map.put("tenantName", tenant.get("c_name"));
                map.put("tenantStatus", tenant.get("c_status"));
                map.put("roomIds", tenant.get("c_room_codes"));
                String c_building_id = (String) tenant.get("c_building_id");
                map.put("buildingId", tenant.get("c_building_id"));
                map.put("buildingName",
                        buildingMap.containsKey(c_building_id) ? buildingMap.get(c_building_id).getName() : null);
                map.put("area", tenant.get("c_area"));
                map.put("contactName", tenant.get("c_contact_name"));
                map.put("contactMobile", tenant.get("c_contact_mobile"));
                map.put("lastClearingTime", tenant.get("c_last_clearing_time"));
                map.put("noBillingEnergy", tenant.get("c_no_billing_energy"));
                map.put("noBillingMoney", tenant.get("c_no_billing_money"));
                map.put("orderSize", tenant.get("c_no_pay_order_count"));
                map.put("billingEnergy", tenant.get("c_billing_energy"));
                map.put("billingMoney", tenant.get("c_billing_money"));
                tenantResultList.add(map);
            }
        }
        contentMap.put("count", fnTenantService.countPostByParam(buildingId, energyTypeId, EnumTenantStatus.ACTIVATED,
                enumAmountType, enumAmountTypeLimit));
    }

    /**
     * 已激活-预付费
     */
    @SuppressWarnings("unchecked")
    private void processActivedPre(Map<String, Object> contentMap, List<Object> tenantResultList, String buildingId,
                                   String energyTypeId, Integer prePayType, Integer remainType, Integer limit, Integer pageIndex,
                                   Integer pageSize, List<Object> sort) throws Exception {
        Map<String, EMSOrder> orderMap = new HashMap<String, EMSOrder>();

        if (sort != null) {
            for (Object obj : sort) {
                Map<String, Object> sortObj = (Map<String, Object>) obj;
                String field = (String) sortObj.get("field");
                Integer type = (Integer) sortObj.get("type");

                if ("tenantId".equals(field)) {
                    orderMap.put("c_id", EnumSort.valueOf(type).getOrder());
                }
                if ("remainMoney".equals(field)) {
                    orderMap.put("c_remain_data", EnumSort.valueOf(type).getOrder());
                    orderMap.put("c_prepay_charge_type", EnumSort.Desc.getOrder());

                }
                if ("remainEnergy".equals(field)) {
                    orderMap.put("c_remain_data", EnumSort.valueOf(type).getOrder());
                    orderMap.put("c_prepay_charge_type", EnumSort.Asc.getOrder());
                }

                if ("remainDays".equals(field)) {
                    orderMap.put("remain_min_day", EnumSort.valueOf(type).getOrder());
                }
            }
        }

        if (orderMap.size() == 0) {
            orderMap.put("c_id", EMSOrder.Asc);
        }

        orderMap.put("tppp.c_is_alarm", EMSOrder.Desc);

        EnumPrePayType enumPrePayType = null;
        if (prePayType != null && prePayType.intValue() != -1) {
            enumPrePayType = EnumPrePayType.valueOf(prePayType);
        }

        EnumPrepayChargeType enumBillingMode = null;
        if (remainType != null && remainType.intValue() != -1) {
            enumBillingMode = EnumPrepayChargeType.valueOf(remainType);
        }

        EnumYesNo enumYesNo = null;
        if (limit != null && limit.intValue() != 0) {
            enumYesNo = EnumYesNo.valueOf(limit);
        }

        List<Map<String, Object>> tenantList = fnTenantService.queryPreByParam(buildingId, energyTypeId,
                EnumTenantStatus.ACTIVATED, enumPrePayType, enumBillingMode, enumYesNo, pageIndex, pageSize, orderMap);
        if (tenantList != null && tenantList.size() > 0) {

            Map<String, Building> buildingMap = fnBuildingService.query();

            for (Map<String, Object> tenant : tenantList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("tenantId", tenant.get("c_id"));
                map.put("tenantName", tenant.get("c_name"));
                map.put("tenantStatus", tenant.get("c_status"));
                map.put("roomIds", tenant.get("c_room_codes"));
                String c_building_id = (String) tenant.get("c_building_id");
                map.put("buildingId", tenant.get("c_building_id"));
                map.put("buildingName",
                        buildingMap.containsKey(c_building_id) ? buildingMap.get(c_building_id).getName() : null);
                map.put("area", tenant.get("c_area"));
                map.put("contactName", tenant.get("c_contact_name"));
                map.put("contactMobile", tenant.get("c_contact_mobile"));
                map.put("monthEnergy", tenant.get("c_current_month_energy"));

                Integer c_prepay_charge_type = (Integer) tenant.get("c_prepay_charge_type");
                if (c_prepay_charge_type != null
                        && c_prepay_charge_type.intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
                    map.put("remainMoney", tenant.get("c_remain_data"));
                    map.put("remainEnergy", null);
                } else if (c_prepay_charge_type != null
                        && c_prepay_charge_type.intValue() == EnumPrepayChargeType.Liang.getValue().intValue()) {
                    map.put("remainMoney", null);
                    map.put("remainEnergy", tenant.get("c_remain_data"));
                }
                map.put("remainDays", tenant.get("c_remain_days"));
                map.put("prePayType", tenant.get("c_pre_pay_type"));
                map.put("isAlarm", tenant.get("c_is_alarm"));
                tenantResultList.add(map);
            }
        }
        contentMap.put("count", fnTenantService.countPreByParam(buildingId, energyTypeId, EnumTenantStatus.ACTIVATED,
                enumPrePayType, enumBillingMode, enumYesNo));
    }

    /**
     * 已退租
     */
    @SuppressWarnings("unchecked")
    private void processReturnLease(Map<String, Object> contentMap, List<Object> tenantResultList, String buildingId,
                                    Integer pageIndex, Integer pageSize, List<Object> sort) throws Exception {
        FnTenant query = new FnTenant();
        query.setBuildingId(buildingId);
        query.setStatus(EnumTenantStatus.RETURNED_LEASE.getValue());
        query.setIsValid(EnumValidStatus.VALID.getValue());
        query.setSkip(Long.valueOf(pageIndex) * pageSize);
        query.setLimit(Long.valueOf(pageSize));
        query.setSort("buildingId", EMSOrder.Asc);
        contentMap.put("count", fnTenantService.count(query));
        if (sort != null) {
            for (Object obj : sort) {
                Map<String, Object> sortObj = (Map<String, Object>) obj;
                String field = (String) sortObj.get("field");
                Integer type = (Integer) sortObj.get("type");

                if ("tenantId".equals(field)) {
                    query.setSort("id", EnumSort.valueOf(type).getOrder());
                }
            }
        }
        List<FnTenant> tenantList = fnTenantService.query(query);
        if (tenantList != null && tenantList.size() > 0) {

            Map<String, Building> buildingMap = fnBuildingService.query();

            for (FnTenant tenant : tenantList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("tenantId", tenant.getId());
                map.put("tenantName", tenant.getName());
                map.put("tenantStatus", tenant.getStatus());
                map.put("roomIds", tenant.getRoomCodes());
                map.put("buildingId", tenant.getBuildingId());
                map.put("buildingName", buildingMap.containsKey(tenant.getBuildingId())
                        ? buildingMap.get(tenant.getBuildingId()).getName() : null);
                map.put("activeTime", tenant.getActiveTime());
                map.put("leaveTime", tenant.getLeaveTime());
                map.put("contactName", tenant.getContactName());
                map.put("contactMobile", tenant.getContactMobile());
                map.put("area", tenant.getArea());
                tenantResultList.add(map);
            }
        }

    }

    @SuppressWarnings("unchecked")
    private void processAllPre(Map<String, Object> contentMap, List<Object> tenantResultList, String buildingId,
                               String energyTypeId, Integer pageIndex, Integer pageSize, List<Object> sort) throws Exception {
        Map<String, EMSOrder> orderMap = new HashMap<String, EMSOrder>();
        if (sort != null) {
            for (Object obj : sort) {
                Map<String, Object> sortObj = (Map<String, Object>) obj;
                String field = (String) sortObj.get("field");
                Integer type = (Integer) sortObj.get("type");

                if ("tenantId".equals(field)) {
                    orderMap.put("t.c_id", EnumSort.valueOf(type).getOrder());
                }
            }
        }

        if (orderMap.size() == 0) {
            orderMap.put("t.c_id", EMSOrder.Asc);
        }
        List<Map<String, Object>> tenantList = fnTenantService.queryByPayType(buildingId, energyTypeId, null,
                EnumPayType.PREPAY, null, pageIndex, pageSize, orderMap);
        if (tenantList != null && tenantList.size() > 0) {
            Map<String, Building> buildingMap = fnBuildingService.query();
            for (Map<String, Object> tenant : tenantList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("tenantId", tenant.get("c_tenant_id"));
                map.put("tenantName", tenant.get("c_name"));
                map.put("roomIds", tenant.get("c_room_codes"));
                String c_building_id = (String) tenant.get("c_building_id");
                map.put("buildingName", buildingMap.containsKey(c_building_id) ? buildingMap.get(c_building_id).getName() : null);
                map.put("area", tenant.get("c_area"));
                tenantResultList.add(map);
            }
        }
        contentMap.put("count",
                fnTenantService.countByPayType(buildingId, energyTypeId, null, EnumPayType.PREPAY, null));
    }


    /**
     * 此处需要进行改造。作记录
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantListParamService")
    @ResponseBody
    public InterfaceResult tenantListParam(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer tenantStatus = (Integer) dto.get("tenantStatus");
            String energyType = (String) dto.get("energyType");
            if (tenantStatus == null) {
                return Result.SUCCESS(content);
            }
            if (energyType == null) {
                return Result.SUCCESS(content);
            }

            String[] energyTypeArray = energyType.split("_");
            String energyTypeId = energyTypeArray[0].trim();
            Integer payType = Integer.parseInt(energyTypeArray[1].trim());
            if (tenantStatus.intValue() == EnumTenantStatus.NOT_ACTIVE.getValue().intValue()) {
                this.processNotActive(content, energyTypeId, payType);
            } else if (tenantStatus.intValue() == EnumTenantStatus.ACTIVATED.getValue().intValue()) {
                this.processActived(content, energyTypeId, payType);
            } else if (tenantStatus.intValue() == EnumTenantStatus.RETURNED_LEASE.getValue().intValue()) {
                this.processReturnedLease(content, energyTypeId, payType);
            } else {
                throw new Exception("租户状态不存在:" + tenantStatus);
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTTenantListParamService");
        }
    }

    //未激活
    @SuppressWarnings("unchecked")
    private void processNotActive(List content, String energyTypeId, Integer payType) {
        if (payType.intValue() == EnumPayType.PREPAY.getValue().intValue()) {
            Map<String, Object> contentMap = new HashMap<String, Object>();
            List<Object> chargeType = new ArrayList<Object>();
            contentMap.put("chargeType", chargeType);
            content.add(contentMap);
            {
                Map<String, Object> chargeTypeMap = new HashMap<String, Object>();
                chargeTypeMap.put("type", -1);
                chargeTypeMap.put("name", "全部");
                chargeType.add(chargeTypeMap);
            }
            try {
                List<String> typeList = fnTenantPayTypeService.queryChargeTypeList();
                if (typeList.contains(EnumPrePayType.OFFLINE_METERPAY.getValue() + "")) {
                    Map<String, Object> chargeTypeMap = new HashMap<String, Object>();
                    chargeTypeMap.put("type", 0);
                    chargeTypeMap.put("name", "仪表充值仪表扣费");
                    chargeType.add(chargeTypeMap);
                }
                if (typeList.contains(EnumPrePayType.ONLINE_METERPAY.getValue() + "")) {
                    Map<String, Object> chargeTypeMap = new HashMap<String, Object>();
                    chargeTypeMap.put("type", 1);
                    chargeTypeMap.put("name", "软件充值仪表扣费");
                    chargeType.add(chargeTypeMap);
                }
                if (typeList.contains(EnumPrePayType.ONLINE_TENANTPAY.getValue() + "")) {
                    Map<String, Object> chargeTypeMap = new HashMap<String, Object>();
                    chargeTypeMap.put("type", 2);
                    chargeTypeMap.put("name", "软件充值软件扣费");
                    chargeType.add(chargeTypeMap);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    //已激活
    @SuppressWarnings("unchecked")
    private void processActived(List content, String energyTypeId, Integer payType) {
        if (payType.intValue() == EnumPayType.PREPAY.getValue().intValue()) {
            List<Object> remainType = new ArrayList<Object>();
            {
                Map<String, Object> remainTypeMap = new HashMap<String, Object>();
                remainTypeMap.put("type", -1);
                remainTypeMap.put("name", "剩余天数");
                remainType.add(remainTypeMap);
            }
            {
                Map<String, Object> remainTypeMap = new HashMap<String, Object>();
                remainTypeMap.put("type", 1);
                remainTypeMap.put("name", "剩余金额");
                remainType.add(remainTypeMap);
            }
            {
                Map<String, Object> remainTypeMap = new HashMap<String, Object>();
                remainTypeMap.put("type", 0);
                remainTypeMap.put("name", "剩余" + EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId) + "量");
                remainType.add(remainTypeMap);
            }

            Map<String, Object> contentMap = new HashMap<String, Object>();
            List<Object> chargeType = new ArrayList<Object>();
            contentMap.put("chargeType", chargeType);
            content.add(contentMap);
            {
                Map<String, Object> chargeTypeMap = new HashMap<String, Object>();
                chargeTypeMap.put("type", -1);
                chargeTypeMap.put("name", "全部");
                chargeTypeMap.put("remainType", remainType);
                chargeType.add(chargeTypeMap);
            }
            try {
                List<String> typeList = fnTenantPayTypeService.queryChargeTypeList();
                if (typeList.contains(EnumPrePayType.OFFLINE_METERPAY.getValue() + "")) {
                    Map<String, Object> chargeTypeMap = new HashMap<String, Object>();
                    chargeTypeMap.put("type", 0);
                    chargeTypeMap.put("name", "仪表充值仪表扣费");
                    chargeTypeMap.put("remainType", remainType);
                    chargeType.add(chargeTypeMap);
                }
                if (typeList.contains(EnumPrePayType.ONLINE_METERPAY.getValue() + "")) {
                    Map<String, Object> chargeTypeMap = new HashMap<String, Object>();
                    chargeTypeMap.put("type", 1);
                    chargeTypeMap.put("name", "软件充值仪表扣费");
                    chargeTypeMap.put("remainType", remainType);
                    chargeType.add(chargeTypeMap);
                }
                if (typeList.contains(EnumPrePayType.ONLINE_TENANTPAY.getValue() + "")) {
                    Map<String, Object> chargeTypeMap = new HashMap<String, Object>();
                    chargeTypeMap.put("type", 2);
                    chargeTypeMap.put("name", "软件充值软件扣费");
                    chargeTypeMap.put("remainType", remainType);
                    chargeType.add(chargeTypeMap);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (payType.intValue() == EnumPayType.POSTPAY.getValue().intValue()) {
            Map<String, Object> contentMap = new HashMap<String, Object>();
            List<Object> amountType = new ArrayList<Object>();
            contentMap.put("amountType", amountType);
            content.add(contentMap);
            {
                Map<String, Object> chargeTypeMap = new HashMap<String, Object>();
                chargeTypeMap.put("type", -1);
                chargeTypeMap.put("name", "全部");
                amountType.add(chargeTypeMap);
            }
            {
                Map<String, Object> chargeTypeMap = new HashMap<String, Object>();
                chargeTypeMap.put("type", 0);
                chargeTypeMap.put("name", "欠费");
                amountType.add(chargeTypeMap);
            }
            {
                Map<String, Object> chargeTypeMap = new HashMap<String, Object>();
                chargeTypeMap.put("type", 1);
                chargeTypeMap.put("name", "长时间未结算");
                amountType.add(chargeTypeMap);
            }
        }
    }

    //已退租
    private void processReturnedLease(List content, String energyTypeId, Integer payType) {

    }


    /**
     * 租户 -- 模糊搜索
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantLikeQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantLikeQuery(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String like = (String) dto.get("like");

            if (like == null || "".equals(like.trim())) {
                return null;
            }
            like=like.replace("%","\\%");
            like=like.replace("_","\\_");
            like=like.replace("'","\\'");
            List<FnTenant> tenantList = fnTenantService.queryTenantLike(like.trim());
            if (tenantList.size() > 0) {
                Map<String, List<FnTenant>> tenantMap = new HashMap<String, List<FnTenant>>();
                for (FnTenant tenant : tenantList) {
                    if (!tenantMap.containsKey(tenant.getBuildingId())) {
                        tenantMap.put(tenant.getBuildingId(), new ArrayList<FnTenant>());
                    }

                    tenantMap.get(tenant.getBuildingId()).add(tenant);
                }

                List<Building> buildingList = fnBuildingService.queryList(new Building());
                if (buildingList != null) {
                    for (Building building : buildingList) {
                        if (tenantMap.containsKey(building.getId())) {
                            List<FnTenant> list = tenantMap.get(building.getId());
                            Map<String, Object> map = new HashMap<String, Object>();
                            map.put("buildingId", building.getId());
                            map.put("buildingName", building.getName());
                            List<Object> tList = new ArrayList<Object>();
                            map.put("tenantList", tList);
                            for (FnTenant tenant : list) {
                                Map<String, Object> subMap = new HashMap<String, Object>();
                                subMap.put("tenantId", tenant.getId());
                                subMap.put("tenantName", tenant.getName());
                                tList.add(subMap);
                            }
                            content.add(map);
                        }
                    }
                }
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTTenantLikeQueryService");
        }
    }


    /**
     * 租户 -- 详情
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantDetailService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantDetail(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");

            if (tenantId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull("tenantId"));
            }
            //未激活
            FnTenant query = new FnTenant();
            query.setId(tenantId);
            FnTenant fnTenant = (FnTenant) fnTenantService.queryObject(query);

            String tenantFlagcode = null;
            FnTenantFlag tenantFlag = new FnTenantFlag();
            tenantFlag.setBuildingId(fnTenant.getBuildingId());
            tenantFlag.setTenantId(tenantId);
            List<FnTenantFlag> list = fnTenantFlagService.query(tenantFlag);
            if (list != null && list.size() > 0) {
                tenantFlagcode = list.get(0).getTenantFlag();
            }
            Map<String, Object> tenantMap = new HashMap<String, Object>();
            tenantMap.put("tenantFlag", tenantFlagcode == null ? "" : tenantFlagcode);
            if (fnTenant.getStatus().intValue() == EnumTenantStatus.NOT_ACTIVE.getValue().intValue()) {
                this.processNotActive(tenantMap, tenantId, fnTenant);
            } else if (fnTenant.getStatus().intValue() == EnumTenantStatus.ACTIVATED.getValue().intValue()) {
                this.processActived(tenantMap, tenantId, fnTenant);
            } else if (fnTenant.getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue().intValue()) {
                this.processReturnedLease(tenantMap, tenantId, fnTenant);
            }
            content.add(tenantMap);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTTenantDetailService");
        }
    }

    private void processNotActive(Map<String, Object> tenantMap, String tenantId, FnTenant fnTenant) throws Exception {
        tenantMap.put("tenantId", fnTenant.getId());
        tenantMap.put("tenantName", fnTenant.getName());
        tenantMap.put("buildingId", fnTenant.getBuildingId());
        Building building = fnBuildingService.query(fnTenant.getBuildingId());

        tenantMap.put("buildingName", building == null ? null : building.getName());
        tenantMap.put("tenantTypeId", fnTenant.getTenantTypeId());

        FnTenantType fnTenantType = new FnTenantType();
        fnTenantType.setId(fnTenant.getTenantTypeId());
        FnTenantType queryFnTenantType = (FnTenantType) fnTenantTypeService.queryObject(fnTenantType);

        tenantMap.put("tenantTypeName", queryFnTenantType.getName());
        tenantMap.put("area", fnTenant.getArea());
        tenantMap.put("contactName", fnTenant.getContactName());
        tenantMap.put("contactMobile", fnTenant.getContactMobile());
        tenantMap.put("remark", fnTenant.getRemark());
        tenantMap.put("activeTime", fnTenant.getActiveTime());
        tenantMap.put("leaveTime", fnTenant.getLeaveTime());
        tenantMap.put("tenantStatus", fnTenant.getStatus());

        {
            List<Object> contactList = new ArrayList<Object>();
            FnTenantContact fnTenantContactQuery = new FnTenantContact();
            fnTenantContactQuery.setTenantId(tenantId);
            List<FnTenantContact> fnTenantContactList = fnTenantContactService.query(fnTenantContactQuery);
            for (FnTenantContact fnTenantContact : fnTenantContactList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("contactName", fnTenantContact.getContactName());
                map.put("contactMobile", fnTenantContact.getContactMobile());
                contactList.add(map);
            }
            tenantMap.put("contactList", contactList);
        }


        {
            List<Object> roomList = new ArrayList<Object>();
            List<DTORoom> dtoRoomList = (List<DTORoom>) fnRoomService.queryRoomListByTenantId(tenantId);

            for (DTORoom dtoRoom : dtoRoomList) {
                Map<String, Object> roomMap = new HashMap<String, Object>();
                roomMap.put("roomId", dtoRoom.getRoomId());
                roomMap.put("roomCode", dtoRoom.getRoomCode());
                List<Object> energyList = new ArrayList<Object>();
                roomMap.put("energyList", energyList);
                List<DTOEnergyTypeMeter> energyTypeMeterList = dtoRoom.getEneryList();
                if (energyTypeMeterList != null && energyTypeMeterList.size() > 0) {
                    for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                        for (DTOEnergyTypeMeter energyTypeMeter : energyTypeMeterList) {
                            if (energyType.getId().equals(energyTypeMeter.getEnergyTypeId())) {
                                Map<String, Object> energyTypeMeterMap = new HashMap<String, Object>();
                                energyTypeMeterMap.put("energyTypeId", energyTypeMeter.getEnergyTypeId());
                                energyTypeMeterMap.put("energyTypeName", EnergyTypeUtil.queryEnergyTypeNameById(energyTypeMeter.getEnergyTypeId()));
                                energyList.add(energyTypeMeterMap);
                                List<Object> meterList = new ArrayList<Object>();
                                for (String meterId : energyTypeMeter.getMeterList()) {
                                    meterList.add(meterId);
                                }
                                energyTypeMeterMap.put("meterList", meterList);
                            }
                        }
                    }
                }
                roomList.add(roomMap);
            }

            tenantMap.put("roomList", roomList);
        }

        Map<String, String> energySplitExpressionMap = new HashMap<String, String>();
        {
            FnTenantEnergySplit fnTenantEnergySplitQuery = new FnTenantEnergySplit();
            fnTenantEnergySplitQuery.setTenantId(tenantId);
            List<FnTenantEnergySplit> fnTenantEnergySplitList = fnTenantEnergySplitService.query(fnTenantEnergySplitQuery);
            for (FnTenantEnergySplit fnTenantEnergySplit : fnTenantEnergySplitList) {
                energySplitExpressionMap.put(fnTenantEnergySplit.getEnergyTypeId(), fnTenantEnergySplit.getExpression());
            }
        }

        {
            List<Object> energyList = new ArrayList<Object>();
            FnTenantPayType fnTenantPayTypeQuery = new FnTenantPayType();
            fnTenantPayTypeQuery.setTenantId(tenantId);
            List<FnTenantPayType> fnTenantPayTypeList = fnTenantPayTypeService.query(fnTenantPayTypeQuery);

            Map<String, String> priceMap = fnTenantPriceService.queryPriceTemplateIdMap(tenantId);

            Map<String, FnPriceTemplate> priceTemplateMap = new HashMap<String, FnPriceTemplate>();
            if (fnTenantPayTypeList.size() > 0) {
                List<FnPriceTemplate> priceTemplateList = fnPriceTemplateService.queryList();
                if (priceTemplateList != null) {
                    for (FnPriceTemplate priceTemplate : priceTemplateList) {
                        priceTemplateMap.put(priceTemplate.getId(), priceTemplate);
                    }
                }
            }
            for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                for (FnTenantPayType fnTenantPayType : fnTenantPayTypeList) {
                    if (!energyType.getId().equals(fnTenantPayType.getEnergyTypeId())) {
                        continue;
                    }
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("energyTypeId", fnTenantPayType.getEnergyTypeId());
                    map.put("ffType", fnTenantPayType.getPayType());
                    map.put("kfType", fnTenantPayType.getPrePayType());
                    map.put("cztype", fnTenantPayType.getPrepayChargeType());
                    map.put("energySplitExpression", energySplitExpressionMap.get(fnTenantPayType.getEnergyTypeId()));
                    StringBuffer typeName = new StringBuffer();
                    typeName.append(EnergyTypeUtil.queryEnergyTypeNameById(fnTenantPayType.getEnergyTypeId())).append("-");
                    if (EnumPayType.PREPAY.getValue().equals(EnumPayType.valueOf(fnTenantPayType.getPayType()).getValue())) {
                        typeName.append(EnumPrePayType.valueOf(fnTenantPayType.getPrePayType()).getView()).append("-").append("充").append(EnumPrepayChargeType.valueOf(fnTenantPayType.getPrepayChargeType()).getView());
                    } else {
                        typeName.append(EnumPayType.valueOf(fnTenantPayType.getPayType()).getView());
                    }
                    map.put("typeName", typeName.toString());
                    FnPriceTemplate template = priceTemplateMap.get(priceMap.get(fnTenantPayType.getEnergyTypeId()));
                    Map<String, Object> priceTemplate = new HashMap<String, Object>();
                    map.put("priceTemplate", priceTemplate);
                    priceTemplate.put("id", template.getId());
                    priceTemplate.put("name", template.getName());
                    priceTemplate.put("type", template.getType());
                    energyList.add(map);
                }
            }

            tenantMap.put("energyList", energyList);
        }
    }

    private void processActived(Map<String, Object> tenantMap, String tenantId, FnTenant fnTenant) throws Exception {
        tenantMap.put("tenantId", fnTenant.getId());
        tenantMap.put("tenantName", fnTenant.getName());
        tenantMap.put("buildingId", fnTenant.getBuildingId());
        Building building = fnBuildingService.query(fnTenant.getBuildingId());

        tenantMap.put("buildingName", building == null ? null : building.getName());
        tenantMap.put("tenantTypeId", fnTenant.getTenantTypeId());

        FnTenantType fnTenantType = new FnTenantType();
        fnTenantType.setId(fnTenant.getTenantTypeId());

        FnTenantType queryFnTenantType = (FnTenantType) fnTenantTypeService.queryObject(fnTenantType);

        tenantMap.put("tenantTypeName", queryFnTenantType.getName());
        tenantMap.put("area", fnTenant.getArea());
        tenantMap.put("contactName", fnTenant.getContactName());
        tenantMap.put("contactMobile", fnTenant.getContactMobile());
        tenantMap.put("remark", fnTenant.getRemark());
        tenantMap.put("activeTime", fnTenant.getActiveTime());
        tenantMap.put("leaveTime", fnTenant.getLeaveTime());
        tenantMap.put("tenantStatus", fnTenant.getStatus());

        {
            List<Object> contactList = new ArrayList<Object>();
            FnTenantContact fnTenantContactQuery = new FnTenantContact();
            fnTenantContactQuery.setTenantId(tenantId);
            List<FnTenantContact> fnTenantContactList = fnTenantContactService.query(fnTenantContactQuery);
            for (FnTenantContact fnTenantContact : fnTenantContactList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("contactName", fnTenantContact.getContactName());
                map.put("contactMobile", fnTenantContact.getContactMobile());
                contactList.add(map);
            }
            tenantMap.put("contactList", contactList);
        }

        Map<String, List<String>> energyTypeMeterListMap = new HashMap<String, List<String>>();
        {
            List<Object> roomList = new ArrayList<Object>();
            List<DTORoom> dtoRoomList = (List<DTORoom>) fnRoomService.queryRoomListByTenantId(tenantId);

            for (DTORoom dtoRoom : dtoRoomList) {
                Map<String, Object> roomMap = new HashMap<String, Object>();
                roomMap.put("roomId", dtoRoom.getRoomId());
                roomMap.put("roomCode", dtoRoom.getRoomCode());
                List<Object> energyList = new ArrayList<Object>();
                roomMap.put("energyList", energyList);
                List<DTOEnergyTypeMeter> energyTypeMeterList = dtoRoom.getEneryList();
                if (energyTypeMeterList != null && energyTypeMeterList.size() > 0) {
                    for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                        for (DTOEnergyTypeMeter energyTypeMeter : energyTypeMeterList) {
                            if (energyType.getId().equals(energyTypeMeter.getEnergyTypeId())) {
                                Map<String, Object> energyTypeMeterMap = new HashMap<String, Object>();
                                energyTypeMeterMap.put("energyTypeId", energyTypeMeter.getEnergyTypeId());
                                energyTypeMeterMap.put("energyTypeName", EnergyTypeUtil.queryEnergyTypeNameById(energyTypeMeter.getEnergyTypeId()));
                                energyList.add(energyTypeMeterMap);
                                List<Object> meterList = new ArrayList<Object>();
                                for (String meterId : energyTypeMeter.getMeterList()) {
                                    if (!energyTypeMeterListMap.containsKey(energyTypeMeter.getEnergyTypeId())) {
                                        energyTypeMeterListMap.put(energyTypeMeter.getEnergyTypeId(), new ArrayList<String>());
                                    }
                                    energyTypeMeterListMap.get(energyTypeMeter.getEnergyTypeId()).add(meterId);
                                    meterList.add(meterId);
                                }
                                energyTypeMeterMap.put("meterList", meterList);
                            }
                        }
                    }

                }
                roomList.add(roomMap);
            }

            tenantMap.put("roomList", roomList);
        }

        Map<String, String> energySplitExpressionMap = new HashMap<String, String>();
        {
            FnTenantEnergySplit fnTenantEnergySplitQuery = new FnTenantEnergySplit();
            fnTenantEnergySplitQuery.setTenantId(tenantId);
            List<FnTenantEnergySplit> fnTenantEnergySplitList = fnTenantEnergySplitService.query(fnTenantEnergySplitQuery);
            for (FnTenantEnergySplit fnTenantEnergySplit : fnTenantEnergySplitList) {
                energySplitExpressionMap.put(fnTenantEnergySplit.getEnergyTypeId(), fnTenantEnergySplit.getExpression());
            }
        }

        {
            List<Object> energyList = new ArrayList<Object>();
            FnTenantPayType fnTenantPayTypeQuery = new FnTenantPayType();
            fnTenantPayTypeQuery.setTenantId(tenantId);
            List<FnTenantPayType> fnTenantPayTypeList = fnTenantPayTypeService.query(fnTenantPayTypeQuery);

            Map<String, String> priceMap = fnTenantPriceService.queryPriceTemplateIdMap(tenantId);

            Map<String, FnPriceTemplate> priceTemplateMap = new HashMap<String, FnPriceTemplate>();
            if (fnTenantPayTypeList.size() > 0) {
                List<FnPriceTemplate> priceTemplateList = fnPriceTemplateService.queryList();
                if (priceTemplateList != null) {
                    for (FnPriceTemplate priceTemplate : priceTemplateList) {
                        priceTemplateMap.put(priceTemplate.getId(), priceTemplate);
                    }
                }
            }
            Map<String, List<FnRecordPostClearingPay>> notPayRecordMap = fnRecordPostClearingPayService.queryNotPayRecord(tenantId);
            Map<String, FnTenantPostPayParam> postParamMap = fnTenantPostPayParamService.queryTenantPostParam(tenantId);
            Map<String, FnTenantPrePayParam> preParamMap = fnTenantPrePayParamService.queryTenantPreParam(tenantId);
            Map<String, Map<String, FnTenantPrePayMeterParam>> preMeterParamMap = fnTenantPrePayMeterParamService.queryTenantPreParam(tenantId);

            for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                for (FnTenantPayType fnTenantPayType : fnTenantPayTypeList) {
                    if (!energyType.getId().equals(fnTenantPayType.getEnergyTypeId())) {
                        continue;
                    }
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("energyTypeId", fnTenantPayType.getEnergyTypeId());
                    map.put("payType", fnTenantPayType.getPayType());
                    map.put("prePayType", fnTenantPayType.getPrePayType());
                    map.put("energySplitExpression", energySplitExpressionMap.get(fnTenantPayType.getEnergyTypeId()));
                    StringBuffer typeName = new StringBuffer();
                    typeName.append(EnergyTypeUtil.queryEnergyTypeNameById(fnTenantPayType.getEnergyTypeId())).append("-");
                    if (EnumPayType.PREPAY.getValue().equals(EnumPayType.valueOf(fnTenantPayType.getPayType()).getValue())) {
                        typeName.append(EnumPrePayType.valueOf(fnTenantPayType.getPrePayType()).getView()).append("-").append("充").append(EnumPrepayChargeType.valueOf(fnTenantPayType.getPrepayChargeType()).getView());
                    } else {
                        typeName.append(EnumPayType.valueOf(fnTenantPayType.getPayType()).getView());
                    }
                    map.put("typeName", typeName.toString());

                    FnPriceTemplate template = priceTemplateMap.get(priceMap.get(fnTenantPayType.getEnergyTypeId()));
                    Map<String, Object> priceTemplate = new HashMap<String, Object>();
                    map.put("priceTemplate", priceTemplate);
                    priceTemplate.put("id", template.getId());
                    priceTemplate.put("name", template.getName());
                    priceTemplate.put("type", template.getType());
                    energyList.add(map);

                    //后付费
                    if (fnTenantPayType.getPayType().intValue() == EnumPayType.POSTPAY.getValue().intValue()) {
                        boolean isAlarm = false;
                        FnTenantPostPayParam param = postParamMap.get(fnTenantPayType.getEnergyTypeId());
                        Map<String, Object> postPay = new HashMap<String, Object>();

                        List<Object> orderList = new ArrayList<Object>();

                        List<FnRecordPostClearingPay> recordList = notPayRecordMap.get(fnTenantPayType.getEnergyTypeId());
                        Double yingJiaoJinE = null;
                        if (recordList != null && recordList.size() > 0) {
                            for (FnRecordPostClearingPay record : recordList) {
                                Map<String, Object> orderMap = new HashMap<String, Object>();
                                orderMap.put("orderId", record.getOrderId());
                                orderMap.put("orderTime", record.getOrderTime());
                                {
                                    orderMap.put("amount", record.getCurrentSumEnergy());
                                    orderMap.put("money", record.getMoney());
                                    if (record.getMoney() != null) {
                                        yingJiaoJinE = yingJiaoJinE == null ? record.getMoney() : yingJiaoJinE + record.getMoney();
                                    }
                                }
                                orderMap.put("amountUnit", UnitUtil.getCumulantUnit(record.getEnergyTypeId()));
                                orderList.add(orderMap);
                            }
                            isAlarm = true;
                        }
                        postPay.put("orderList", orderList);
                        if (param != null) {
                            postPay.put("weiJieSuanJinE", param.getNoBillingMoney());
                            postPay.put("yingJiaoJinE", yingJiaoJinE);
                        } else {
                            postPay.put("weiJieSuanJinE", null);
                            postPay.put("yingJiaoJinE", null);
                        }
                        //查询上次结算时间
                        Date lastClearingTime = fnRecordPostClearingPayService.queryLastClearingDate(fnTenant.getBuildingId(), tenantId, energyType.getId());
                        postPay.put("lastClearingTime", lastClearingTime);
                        map.put("postPay", postPay);
                        //0//正常  1.待缴费  2.余额不足
                        map.put("status", isAlarm ? 1 : 0);
                    } else {//预付费
                        boolean isAlarm = false;
                        FnTenantPrePayParam param = preParamMap.get(fnTenantPayType.getEnergyTypeId());
                        if (fnTenantPayType.getPrePayType().intValue() == EnumPrePayType.OFFLINE_METERPAY.getValue().intValue()) {
                            Map<String, Object> prePay_0 = new HashMap<String, Object>();
                            if (fnTenantPayType.getPrepayChargeType().intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
                                prePay_0.put("remainType", "剩余金额（元）");
                            } else {
                                prePay_0.put("remainType", "剩余" + EnergyTypeUtil.queryEnergyTypeNameById(fnTenantPayType.getEnergyTypeId()) + "量（" + UnitUtil.getBillModeUnit(fnTenantPayType.getEnergyTypeId(), EnumPrepayChargeType.Liang) + "）");
                            }
                            List<Object> meterList = new ArrayList<Object>();
                            prePay_0.put("meterList", meterList);
                            List<String> meterIdList = energyTypeMeterListMap.get(fnTenantPayType.getEnergyTypeId());
                            Map<String, FnTenantPrePayMeterParam> meterParamMap = preMeterParamMap.get(fnTenantPayType.getEnergyTypeId());
                            if (meterIdList != null) {
                                for (String meterId : meterIdList) {
                                    Map<String, Object> meterMap = new HashMap<String, Object>();
                                    meterMap.put("meterId", meterId);
                                    meterMap.put("isAlarm", false);
                                    if (meterParamMap != null && meterParamMap.get(meterId) != null) {
                                        meterMap.put("remainData", meterParamMap.get(meterId).getRemainData());
                                        meterMap.put("remainDays", meterParamMap.get(meterId).getRemainDays());
                                        if (meterParamMap.get(meterId).getIsAlarm() != null && meterParamMap.get(meterId).getIsAlarm().intValue() == EnumYesNo.YES.getValue().intValue()) {
                                            isAlarm = true;
                                            meterMap.put("isAlarm", true);
                                        }
                                    } else {
                                        meterMap.put("remainData", null);
                                        meterMap.put("remainDays", null);
                                    }
                                    meterList.add(meterMap);
                                }
                            }

                            map.put("prePay_0", prePay_0);
                        } else if (fnTenantPayType.getPrePayType().intValue() == EnumPrePayType.ONLINE_METERPAY.getValue().intValue()) {
                            Map<String, Object> prePay_1 = new HashMap<String, Object>();
                            if (fnTenantPayType.getPrepayChargeType().intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
                                prePay_1.put("remainType", "剩余金额（元）");
                            } else {
                                prePay_1.put("remainType", "剩余" + EnergyTypeUtil.queryEnergyTypeNameById(fnTenantPayType.getEnergyTypeId()) + "量（" + UnitUtil.getBillModeUnit(fnTenantPayType.getEnergyTypeId(), EnumPrepayChargeType.Liang) + "）");
                            }

                            //找到能耗类型下的仪表
                            List<Object> meterList = new ArrayList<Object>();
                            prePay_1.put("meterList", meterList);
                            List<String> meterIdList = energyTypeMeterListMap.get(fnTenantPayType.getEnergyTypeId());
                            Map<String, FnTenantPrePayMeterParam> meterParamMap = preMeterParamMap.get(fnTenantPayType.getEnergyTypeId());
                            if (meterIdList != null) {
                                for (String meterId : meterIdList) {
                                    Map<String, Object> meterMap = new HashMap<String, Object>();
                                    meterMap.put("meterId", meterId);
                                    meterMap.put("isAlarm", false);
                                    if (meterParamMap != null && meterParamMap.get(meterId) != null) {
                                        meterMap.put("remainData", meterParamMap.get(meterId).getRemainData());
                                        meterMap.put("remainDays", meterParamMap.get(meterId).getRemainDays());
                                        if (meterParamMap.get(meterId).getIsAlarm() != null && meterParamMap.get(meterId).getIsAlarm().intValue() == EnumYesNo.YES.getValue().intValue()) {
                                            isAlarm = true;
                                            meterMap.put("isAlarm", true);
                                        }
                                    } else {
                                        meterMap.put("remainData", null);
                                        meterMap.put("remainDays", null);
                                    }
                                    meterList.add(meterMap);
                                }
                            }
                            //查询剩余金额或剩余量
                            map.put("prePay_1", prePay_1);
                        } else if (fnTenantPayType.getPrePayType().intValue() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
                            Map<String, Object> prePay_2 = new HashMap<String, Object>();
                            if (fnTenantPayType.getPrepayChargeType().intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
                                prePay_2.put("remainType", "剩余金额（元）");
                            } else {
                                prePay_2.put("remainType", "剩余" + EnergyTypeUtil.queryEnergyTypeNameById(fnTenantPayType.getEnergyTypeId()) + "量（" + UnitUtil.getBillModeUnit(fnTenantPayType.getEnergyTypeId(), EnumPrepayChargeType.Liang) + "）");
                            }
                            if (param != null) {
                                prePay_2.put("remainData", param.getRemainData());
                                prePay_2.put("remainDays", param.getRemainDays());
                                if (param.getIsAlarm() != null && param.getIsAlarm().intValue() == EnumYesNo.YES.getValue().intValue()) {
                                    isAlarm = true;
                                }
                            } else {
                                prePay_2.put("remainData", null);
                                prePay_2.put("remainDays", null);
                            }
                            map.put("prePay_2", prePay_2);
                        } else {
                            throw new Exception("租户预付费充值类型无法解析:" + fnTenantPayType.getPrePayType());
                        }
                        //0//正常  1.待缴费  2.余额不足
                        map.put("status", isAlarm ? 2 : 0);
                    }
                }
            }

            tenantMap.put("energyList", energyList);
        }
    }

    private void processReturnedLease(Map<String, Object> tenantMap, String tenantId, FnTenant fnTenant) throws Exception {
        tenantMap.put("tenantId", fnTenant.getId());
        tenantMap.put("tenantName", fnTenant.getName());
        tenantMap.put("buildingId", fnTenant.getBuildingId());
        Building building = fnBuildingService.query(fnTenant.getBuildingId());

        tenantMap.put("buildingName", building == null ? null : building.getName());
        tenantMap.put("tenantTypeId", fnTenant.getTenantTypeId());

        FnTenantType fnTenantType = new FnTenantType();
        fnTenantType.setId(fnTenant.getTenantTypeId());

        FnTenantType queryFnTenantType = (FnTenantType) fnTenantTypeService.queryObject(fnTenantType);

        tenantMap.put("tenantTypeName", queryFnTenantType.getName());
        tenantMap.put("area", fnTenant.getArea());
        tenantMap.put("contactName", fnTenant.getContactName());
        tenantMap.put("contactMobile", fnTenant.getContactMobile());
        tenantMap.put("remark", fnTenant.getRemark());
        tenantMap.put("activeTime", fnTenant.getActiveTime());
        tenantMap.put("leaveTime", fnTenant.getLeaveTime());
        tenantMap.put("tenantStatus", fnTenant.getStatus());

        {
            List<Object> contactList = new ArrayList<Object>();
            FnTenantContact fnTenantContactQuery = new FnTenantContact();
            fnTenantContactQuery.setTenantId(tenantId);
            List<FnTenantContact> fnTenantContactList = fnTenantContactService.query(fnTenantContactQuery);
            for (FnTenantContact fnTenantContact : fnTenantContactList) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("contactName", fnTenantContact.getContactName());
                map.put("contactMobile", fnTenantContact.getContactMobile());
                contactList.add(map);
            }
            tenantMap.put("contactList", contactList);
        }


        {
            List<Object> roomList = new ArrayList<Object>();
            List<DTORoom> dtoRoomList = (List<DTORoom>) fnRoomService.queryRoomListByTenantId(tenantId);

            for (DTORoom dtoRoom : dtoRoomList) {
                Map<String, Object> roomMap = new HashMap<String, Object>();
                roomMap.put("roomId", dtoRoom.getRoomId());
                roomMap.put("roomCode", dtoRoom.getRoomCode());
                List<Object> energyList = new ArrayList<Object>();
                roomMap.put("energyList", energyList);
                List<DTOEnergyTypeMeter> energyTypeMeterList = dtoRoom.getEneryList();
                if (energyTypeMeterList != null && energyTypeMeterList.size() > 0) {
                    for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                        for (DTOEnergyTypeMeter energyTypeMeter : energyTypeMeterList) {
                            if (energyType.getId().equals(energyTypeMeter.getEnergyTypeId())) {
                                Map<String, Object> energyTypeMeterMap = new HashMap<String, Object>();
                                energyTypeMeterMap.put("energyTypeId", energyTypeMeter.getEnergyTypeId());
                                energyTypeMeterMap.put("energyTypeName", EnergyTypeUtil.queryEnergyTypeNameById(energyTypeMeter.getEnergyTypeId()));
                                energyList.add(energyTypeMeterMap);
                                List<Object> meterList = new ArrayList<Object>();
                                for (String meterId : energyTypeMeter.getMeterList()) {
                                    meterList.add(meterId);
                                }
                                energyTypeMeterMap.put("meterList", meterList);
                            }
                        }
                    }
                }
                roomList.add(roomMap);
            }

            tenantMap.put("roomList", roomList);
        }

        Map<String, String> energySplitExpressionMap = new HashMap<String, String>();
        {
            FnTenantEnergySplit fnTenantEnergySplitQuery = new FnTenantEnergySplit();
            fnTenantEnergySplitQuery.setTenantId(tenantId);
            List<FnTenantEnergySplit> fnTenantEnergySplitList = fnTenantEnergySplitService.query(fnTenantEnergySplitQuery);
            for (FnTenantEnergySplit fnTenantEnergySplit : fnTenantEnergySplitList) {
                energySplitExpressionMap.put(fnTenantEnergySplit.getEnergyTypeId(), fnTenantEnergySplit.getExpression());
            }
        }

        {
            List<Object> energyList = new ArrayList<Object>();
            FnTenantPayType fnTenantPayTypeQuery = new FnTenantPayType();
            fnTenantPayTypeQuery.setTenantId(tenantId);
            List<FnTenantPayType> fnTenantPayTypeList = fnTenantPayTypeService.query(fnTenantPayTypeQuery);

            Map<String, String> priceMap = fnTenantPriceService.queryPriceTemplateIdMap(tenantId);

            Map<String, FnPriceTemplate> priceTemplateMap = new HashMap<String, FnPriceTemplate>();
            if (fnTenantPayTypeList.size() > 0) {
                List<FnPriceTemplate> priceTemplateList = fnPriceTemplateService.queryList();
                if (priceTemplateList != null) {
                    for (FnPriceTemplate priceTemplate : priceTemplateList) {
                        priceTemplateMap.put(priceTemplate.getId(), priceTemplate);
                    }
                }
            }
            for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                for (FnTenantPayType fnTenantPayType : fnTenantPayTypeList) {
                    if (!energyType.getId().equals(fnTenantPayType.getEnergyTypeId())) {
                        continue;
                    }
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("energyTypeId", fnTenantPayType.getEnergyTypeId());
                    map.put("payType", fnTenantPayType.getPayType());
                    map.put("ffType", fnTenantPayType.getPayType());
                    map.put("kfType", fnTenantPayType.getPrePayType());
                    map.put("cztype", fnTenantPayType.getPrepayChargeType());
                    map.put("energySplitExpression", energySplitExpressionMap.get(fnTenantPayType.getEnergyTypeId()));
                    FnPriceTemplate template = priceTemplateMap.get(priceMap.get(fnTenantPayType.getEnergyTypeId()));
                    Map<String, Object> priceTemplate = new HashMap<String, Object>();
                    map.put("priceTemplate", priceTemplate);
                    priceTemplate.put("id", template.getId());
                    priceTemplate.put("name", template.getName());
                    priceTemplate.put("type", template.getType());
                    energyList.add(map);

                    map.put("energyUnit", UnitUtil.getCumulantUnit(fnTenantPayType.getEnergyTypeId()));
                    Date timeFrom = fnTenant.getActiveTime();
                    Date timeTo = fnTenant.getLeaveTime();
                    StringBuffer typeName = new StringBuffer();
                    typeName.append(EnergyTypeUtil.queryEnergyTypeNameById(fnTenantPayType.getEnergyTypeId())).append("-");
                    if (EnumPayType.PREPAY.getValue().equals(EnumPayType.valueOf(fnTenantPayType.getPayType()).getValue())) {
                        typeName.append(EnumPrePayType.valueOf(fnTenantPayType.getPrePayType()).getView()).append("-").append("充").append(EnumPrepayChargeType.valueOf(fnTenantPayType.getPrepayChargeType()).getView());
                    } else {
                        typeName.append(EnumPayType.valueOf(fnTenantPayType.getPayType()).getView());
                    }
                    map.put("typeName", typeName.toString());

                    Double totalEnergy = fnTenantDataService.queryDataGteLt(fnTenant.getBuildingId(), tenantId, EnumTimeType.T2, fnTenantPayType.getEnergyTypeId(), timeFrom, timeTo, EnumEnergyMoney.Energy);
                    Double totalMoney = fnTenantDataService.queryDataGteLt(fnTenant.getBuildingId(), tenantId, EnumTimeType.T2, fnTenantPayType.getEnergyTypeId(), timeFrom, timeTo, EnumEnergyMoney.Money);
                    map.put("totalEnergy", totalEnergy);
                    map.put("totalMoney", totalMoney);
                    if (fnTenantPayType.getPayType().intValue() == EnumPayType.POSTPAY.getValue()) {
                        Date lastClearingTime = fnRecordPostClearingPayService.queryLastClearingDate(fnTenant.getBuildingId(), tenantId, energyType.getId());
                        map.put("lastClearingTime", lastClearingTime);
                    } else {
                        map.put("lastClearingTime", null);
                    }
                }
            }

            tenantMap.put("energyList", energyList);
        }
    }


    /**
     * 作记录 前端没有调用
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantDetailByFlagService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantDetailByFlag(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantFlag = (String) dto.get("tenantFlag");

            if (tenantFlag == null) {
                throw new Exception(ExceptionUtil.ParamIsNull("tenantFlag"));
            }
            FnTenant fnTenant = (FnTenant) fnTenantFlagService.queryTenantByFlag(tenantFlag);
            Map<String, Object> tenantMap = new HashMap<String, Object>();

            tenantMap.put("tenantId", fnTenant.getId());
            tenantMap.put("remoteRechargeStatus", 1);
            {//查询租户微信充值是否被限制
                FnRemoteRechargeStatus query = new FnRemoteRechargeStatus();
                query.setTenantFlag(tenantFlag);
                List<FnRemoteRechargeStatus> list = fnRemoteRechargeStatusService.query(query);
                if(list!=null&&list.size()>0){
                    tenantMap.put("remoteRechargeStatus", list.get(0).getRemoteRechargeStatus());
                }
            }
            tenantMap.put("tenantName", fnTenant.getName());
            tenantMap.put("buildingId", fnTenant.getBuildingId());
            Building building = fnBuildingService.query(fnTenant.getBuildingId());
            tenantMap.put("buildingName", building == null ? null : building.getName());
            List<Object> energyList = new ArrayList<Object>();
            tenantMap.put("energyList", energyList);
            Map<String, FnTenantPayType> typeMap = fnTenantPayTypeService.queryPayTypeMap(fnTenant.getId());
            {// 能耗
                Date date = DateUtils.truncate(new Date(), Calendar.DATE);
                for (Map.Entry<String, FnTenantPayType> entry : typeMap.entrySet()) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("energyTypeId", entry.getKey());
                    Double amount = fnTenantDataService.queryData(fnTenant.getBuildingId(), fnTenant.getId(),
                            EnumTimeType.T2, entry.getKey(), date, EnumEnergyMoney.Energy);
                    Double money = fnTenantDataService.queryData(fnTenant.getBuildingId(), fnTenant.getId(),
                            EnumTimeType.T2, entry.getKey(), date, EnumEnergyMoney.Money);
                    map.put("money", money == null ? 0.0 : money);
                    map.put("amount", amount == null ? 0.0 : amount);
                    map.put("amountUnit", UnitUtil.getCumulantUnit(entry.getKey()));
                    energyList.add(map);
                }
            }
            List<Object> meterList = new ArrayList<Object>();
            tenantMap.put("meterList", meterList);
            Map<String, FnTenantPrePayParam> tenantPreParamMap = fnTenantPrePayParamService
                    .queryTenantPreParam(fnTenant.getId());
            Map<String, Map<String, FnTenantPrePayMeterParam>> meterPreParamMap = fnTenantPrePayMeterParamService
                    .queryTenantPreParam(fnTenant.getId());
            {// 仪表
                for (Map.Entry<String, FnTenantPayType> entry : typeMap.entrySet()) {
                    FnTenantPayType tenantPayType = entry.getValue();
                    if (tenantPayType.getPayType() == EnumPayType.PREPAY.getValue().intValue()) {// 预付费
                        if (tenantPayType.getPrepayChargeType() == EnumPrepayChargeType.Liang.getValue().intValue()) {
                            continue;
                        }
                        if (tenantPayType.getPrePayType() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
                            FnTenantPrePayParam payParam = tenantPreParamMap.get(entry.getKey());
                            if (payParam != null) {
                                Map<String, Object> map = new HashMap<String, Object>();
                                map.put("energyTypeId", entry.getKey());
                                map.put("remainData", payParam.getRemainData() == null ? 0.0 : payParam.getRemainData());
                                map.put("remainDays", payParam.getRemainDays());
                                map.put("isAlarm", payParam.getIsAlarm());
                                meterList.add(map);
                            }
                        } else {
                            Map<String, FnTenantPrePayMeterParam> meterMap = meterPreParamMap.get(entry.getKey());
                            if (meterMap != null) {
                                for (Map.Entry<String, FnTenantPrePayMeterParam> object : meterMap.entrySet()) {
                                    Map<String, Object> map = new HashMap<String, Object>();
                                    map.put("energyTypeId", entry.getKey());
                                    map.put("meterId", object.getValue().getMeterId());
                                    map.put("remainData", object.getValue().getRemainData()==null?0.0: object.getValue().getRemainData());
                                    map.put("remainDays", object.getValue().getRemainDays()==null?0.0:object.getValue().getRemainDays());
                                    map.put("isAlarm", object.getValue().getIsAlarm());
                                    meterList.add(map);
                                }
                            }
                        }
                    }
                }
            }
            content.add(tenantMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTTenantDetailByFlagService");
        }
    }

    /**
     * 作记录 前端没有调用
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantContantService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantContant(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            if(dto.get("tenantList") == null ){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            List<String> tenantList = (List<String>)dto.get("tenantList");
            if(tenantList != null && tenantList.size() > 0){
                List<FnTenant> tenantEntityList = fnTenantService.queryListByIds(tenantList);
                if(tenantEntityList != null){
                    for(FnTenant tenant : tenantEntityList){
                        Map<String,Object> map = new HashMap<String, Object>();
                        map.put("tenantId", tenant.getId());
                        map.put("tenantName", tenant.getName());
                        map.put("contactName", tenant.getContactName());
                        map.put("contactMobile", tenant.getContactMobile());
                        content.add(map);
                    }
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTTenantContantService");
        }
    }

    /**
     * 作记录 前端没有调用
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantInfoService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantInfo(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            List<String> tenantList = (List<String>) dto.get("tenantList");

            if (tenantList == null || tenantList.size() == 0) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            Map<String, Object> contentMap = new HashMap<String, Object>();
            content.add(contentMap);
            List<Object> tenants = new ArrayList<Object>();
            contentMap.put("tenantList", tenants);
            Map<String, FnTenant> tenantMap = fnTenantFlagService.queryTenantByFlags(tenantList);
            if (tenantMap != null && tenantMap.size() > 0) {
                for (Map.Entry<String, FnTenant> entry : tenantMap.entrySet()) {
                    if(EnumTenantStatus.ACTIVATED.getValue().intValue()!=entry.getValue().getStatus()){
                        continue;
                    }
                    Map<String, Object> obj = new HashMap<String, Object>();
                    obj.put("tenantId", entry.getValue().getId());
                    obj.put("tenantName", entry.getValue().getName());
                    obj.put("buildingId", entry.getValue().getBuildingId());
                    {//建筑
                        Building building = fnBuildingService.query(entry.getValue().getBuildingId());
                        obj.put("buildingName", building == null ? null : building.getName());//
                    }
                    obj.put("tenantTypeId", entry.getValue().getTenantTypeId());
                    {//租户业态名称
                        FnTenantType fnTenantType = new FnTenantType();
                        fnTenantType.setId(entry.getValue().getTenantTypeId());
                        FnTenantType queryFnTenantType = (FnTenantType) fnTenantTypeService.queryObject(fnTenantType);
                        obj.put("tenantTypeName", queryFnTenantType.getName());
                    }
                    obj.put("tenantFlag", entry.getKey());
                    obj.put("activeTime", entry.getValue().getActiveTime());
                    obj.put("area", entry.getValue().getArea());
                    tenants.add(obj);
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTTenantInfoService");
        }
    }

    /**
     * 租户 -- 检验租户编号是否存在
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantIdCheckService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantIdCheck(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String)dto.get("tenantId");

            if(tenantId == null){
                throw new Exception(ExceptionUtil.ParamIsNull("tenantId"));
            }

            FnTenant query = new FnTenant();
            query.setId(tenantId);

            FnTenant tenant = (FnTenant) fnTenantService.queryObject(query);
            Map<String,Integer> result = new HashMap<String, Integer>();
            if(tenant != null){
                result.put("isExsit", EnumExsistStatus.Exsist.getValue());
                content.add(result);
            }else{
                result.put("isExsit", EnumExsistStatus.NOT_EXSIT.getValue());
                content.add(result);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTTenantIdCheckService");
        }
    }


    /**
     * 租户 -- 全码下载
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantFlagDownloadService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantFlagDownload(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            FnTenant query = new FnTenant();
            query.setIsValid(EnumValidStatus.VALID.getValue());
            List<FnTenant> fnTenantList = fnTenantService.query(query);
            Map<String,FnTenantFlag>flagMap= fnTenantFlagService.queryFlag();
            Map<String, FnTenantType> typeMap = fnTenantTypeService.queryType();

            List<Object> tenantObjList = new ArrayList<>();
            if (fnTenantList != null) {
                for (FnTenant tenant : fnTenantList) {
                    Map<String,Object> obj = new HashMap<String,Object>();
                    FnTenantFlag tenantFlag = flagMap.get(tenant.getId());
                    obj.put("tenantFlag", tenantFlag.getTenantFlag());
                    obj.put("tenantId", tenant.getId());
                    obj.put("tenantName", tenant.getName());
                    obj.put("roomCodes", tenant.getRoomCodes());
                    obj.put("area", tenant.getArea());
                    obj.put("status", EnumTenantStatus.valueOf(tenant.getStatus()).getView());
                    obj.put("tenantType",typeMap.get(tenant.getTenantTypeId()).getName());
                    tenantObjList.add(obj);
                }
            }
            String subdirectory = pathUtil.getTempDownloadSubDir();
            String resouceId = UUID.randomUUID().toString();
            FileResource resource = new FileResource();
            resource.setId(resouceId);
            StringBuffer fileNameSb = new StringBuffer();
            fileNameSb.append("租户全码详情-").append(standard.format(new Date()).replace(":", "："));
            resource.setName(fileNameSb.toString());
            resource.setSuffix("xls");
            resource.setSubdirectory(subdirectory);
            String path = pathUtil.getPath();
            String fileDir = Paths.get(path, subdirectory).toString();
            File file = new File(fileDir);
            if (!file.exists()) {
                file.mkdirs();
            }
            this.tenantFlagDownloadBuildExcel(path, subdirectory, resouceId, tenantObjList);

            fnFileResourceService.save(resource);
            Map<String, Object> newContentObj = new HashMap<>();
            newContentObj.put("id", resouceId);
            content.add(newContentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTTenantFlagDownloadService");
        }
    }

    /**
     * 租户 -- 全码下载
     */
    @SuppressWarnings({ "unchecked" })
    private void tenantFlagDownloadBuildExcel(String path, String subPath, String fileName, List<Object> tenantObjList) {
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        // 创建标题
        HSSFRow row0 = sheet.createRow((short) 0);
        {
            HSSFCell cell = row0.createCell((short) 0);
            cell.setCellStyle(style);
            cell.setCellValue("租户全码");
        }
        {
            HSSFCell cell = row0.createCell((short) 1);
            cell.setCellStyle(style);
            cell.setCellValue("租户编号");
        }
        {
            HSSFCell cell = row0.createCell((short) 2);
            cell.setCellStyle(style);
            cell.setCellValue("租户名称");
        }
        {
            HSSFCell cell = row0.createCell((short) 3);
            cell.setCellStyle(style);
            cell.setCellValue("租户业态");
        }
        {
            HSSFCell cell = row0.createCell((short) 4);
            cell.setCellStyle(style);
            cell.setCellValue("租户房间");
        }
        {
            HSSFCell cell = row0.createCell((short) 5);
            cell.setCellStyle(style);
            cell.setCellValue("面积");
        }
        {
            HSSFCell cell = row0.createCell((short) 6);
            cell.setCellStyle(style);
            cell.setCellValue("状态");
        }
        if (tenantObjList != null) {
            int rowCount = 1;
            for (int i = 0; i < tenantObjList.size(); i++) {
                Map<String, Object> tenant = (Map<String, Object>) tenantObjList.get(i);
                String tenantId = (String) tenant.get("tenantId");
                String tenantFlag = (String) tenant.get("tenantFlag");
                String tenantName = (String) tenant.get("tenantName");
                String roomCodes = (String) tenant.get("roomCodes");
                String status = (String) tenant.get("status");
                String tenantType = (String) tenant.get("tenantType");
                Double area = (Double) tenant.get("area");
                HSSFRow row = sheet.createRow((short) rowCount);
                {
                    HSSFCell cell = row.createCell((short) (0));
                    cell.setCellValue(tenantFlag == null ? " " : tenantFlag);
                }
                {
                    HSSFCell cell = row.createCell((short) (1));
                    cell.setCellValue(tenantId == null ? " " : tenantId);
                }
                {
                    HSSFCell cell = row.createCell((short) (2));
                    cell.setCellValue(tenantName == null ? " " : tenantName);
                }
                {
                    HSSFCell cell = row.createCell((short) (3));
                    cell.setCellValue(tenantType == null ? " " : tenantType);
                }
                {
                    HSSFCell cell = row.createCell((short) (4));
                    cell.setCellValue(roomCodes == null ? " " : roomCodes);
                }
                {
                    HSSFCell cell = row.createCell((short) (5));
                    cell.setCellValue(area == null ? " " : area+"");
                }
                {
                    HSSFCell cell = row.createCell((short) (6));
                    cell.setCellValue(status == null ? " " : status);
                }
                rowCount++;
            }

        }

        String newFilePath = Paths.get(path, subPath, fileName).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fout != null) {
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
    }

}
