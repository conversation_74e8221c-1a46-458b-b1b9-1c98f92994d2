package com.persagy.finein.fnmanage.param.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumTenantStatus;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.fnmanage.param.handler.FNTTenantParamBuildingHandler;
import com.persagy.finein.fnmanage.param.handler.FNTTenantParamPostPayHandler;
import com.persagy.finein.fnmanage.param.handler.FNTTenantParamPrePayHandler;
import com.persagy.finein.service.FNTenantPayTypeService;
import com.persagy.finein.service.FNTenantPostPayParamService;
import com.persagy.finein.service.FNTenantPrePayMeterParamService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月21日 下午1:45:10

* 说明:
*/
@Component("FNTTenantParamBuildingHandler")
public class FNTTenantParamBuildingHandlerImpl extends CoreServiceImpl implements FNTTenantParamBuildingHandler{
	
	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;

	@Resource(name = "FNTenantPayTypeService")
	private FNTenantPayTypeService FNTenantPayTypeService;

	@Resource(name = "FNTenantPrePayMeterParamService")
	private FNTenantPrePayMeterParamService FNTenantPrePayMeterParamService;
	
	@Resource(name = "FNTenantPostPayParamService")
	private FNTenantPostPayParamService FNTenantPostPayParamService;
	
	@Resource(name = "FNTTenantParamPrePayHandler")
	private FNTTenantParamPrePayHandler FNTTenantParamPrePayHandler;
	
	@Resource(name = "FNTTenantParamPostPayHandler")
	private FNTTenantParamPostPayHandler FNTTenantParamPostPayHandler;
	
	
	public void handle(Building building) throws Exception{
		//查询建筑下所有租户
		List<FnTenant> tenantList = FNTenantService.queryListByValidStatus(building.getId(), EnumValidStatus.VALID);
		if(tenantList == null || tenantList.size() == 0){
			return;
		}
		
		for(FnTenant tenant : tenantList){
			if(tenant.getStatus().intValue() == EnumTenantStatus.NOT_ACTIVE.getValue().intValue()){
				continue;
			}else if(tenant.getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue().intValue()){
				this.FNTenantPostPayParamService.removeTenantParam(tenant.getId());
				this.FNTenantPrePayMeterParamService.removeTenantParam(tenant.getId());
				this.FNTenantPostPayParamService.removeTenantParam(tenant.getId());
			}else if(tenant.getStatus().intValue() == EnumTenantStatus.ACTIVATED.getValue().intValue()){
				//计算已激活租户参数
				Map<String,FnTenantPayType> payTypeMap = FNTenantPayTypeService.queryPayTypeMap(tenant.getId());
				if(payTypeMap == null){
					continue;
				}
				//删除租户的参数
				for(Map.Entry<String,FnTenantPayType> entry : payTypeMap.entrySet()){
					FnTenantPayType payType = entry.getValue();
					if(payType.getPayType().intValue() == EnumPayType.POSTPAY.getValue().intValue()){//后付费
						FNTTenantParamPostPayHandler.handle(building,tenant,entry.getValue());
						try {
							Thread.sleep(1);
						} catch (Exception e) {
						}
					}else{//预付费
						FNTTenantParamPrePayHandler.handle(building,tenant,entry.getValue());
					}
				}
			}
		}
	}

}

