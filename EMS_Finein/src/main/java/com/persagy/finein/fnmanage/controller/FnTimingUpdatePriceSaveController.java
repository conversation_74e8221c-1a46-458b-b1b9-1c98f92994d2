package com.persagy.finein.fnmanage.controller;


import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.quartz.TimingBatchUpdatePrice;
import com.persagy.finein.service.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: ls
 * @Date: 2022/2/14 15:47
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FnTimingUpdatePriceSaveController extends BaseController {
    @Resource(name = "FNPriceTemplateService")
    private FNPriceTemplateService fnPriceTemplateService;

    @Resource(name = "FNTimingUpdatePriceService")
    private FNTimingUpdatePriceService fnTimingUpdatePriceService;

    @Resource(name = "TimingBatchUpdatePrice")
    private TimingBatchUpdatePrice timingBatchUpdatePrice;

    @RequestMapping("FnQueryUpdatePriceService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult queryPrice(@RequestParam(value = "jsonString") String jsonString) {
        try {
            List content = new ArrayList();
            FnTimingUpdatePrice query = new FnTimingUpdatePrice();
            query.setIsValid(EnumValidStatus.VALID.getValue());
            List<FnTimingUpdatePrice> updatePriceList = fnTimingUpdatePriceService.query(query);
            if (updatePriceList != null && updatePriceList.size() > 0) {
                FnPriceTemplate priceTemplate = fnPriceTemplateService.query(updatePriceList.get(0).getPriceTemplateId());
                if (priceTemplate != null) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("priceTemplateId", priceTemplate.getId());
                    map.put("priceTemplateName", priceTemplate.getName());
                    content.add(map);
                }
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FnQueryUpdatePriceService");
        }
    }

    /**
     * 保存定时修改电价
     *
     * @param jsonString
     * @return
     */
    @RequestMapping("FnTimingUpdatePriceSaveService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult priceUpdate(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            String energyTypeId = (String) dto.get("energyTypeId");
            String priceTemplateId = (String) dto.get("priceTemplateId");
            //是否立即执行 0 否 1 是
            Integer isExecute = (Integer) dto.get("isExecute");
            String userId = this.getParamUserId(dto);
            List content = new ArrayList();
            if (energyTypeId == null || priceTemplateId == null || userId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnPriceTemplate query = fnPriceTemplateService.query(priceTemplateId);
            if (query == null) {
                throw new Exception("价格模板为空");
            }
            if (query.getType().intValue() == EnumPriceType.AVG.getValue()) {
                throw new Exception("价格类型不是分时电价");
            }
            FnTimingUpdatePrice save = new FnTimingUpdatePrice();
            save.setPriceTemplateId(priceTemplateId);
            save.setName(query.getName());
            save.setEnergyTypeId(energyTypeId);
            save.setIsValid(EnumValidStatus.VALID.getValue());
            save.setLastUpdateUserId(userId);
            save.setLastUpdateTime(new Date());

            FnTimingUpdatePrice price = new FnTimingUpdatePrice();
            price.setEnergyTypeId(energyTypeId);
            List<FnTimingUpdatePrice> queryList = fnTimingUpdatePriceService.query(price);
            if (queryList != null && queryList.size() > 0) {
                fnTimingUpdatePriceService.update(queryList.get(0), save);
            } else {
                save.setId(UUID.randomUUID().toString());
                fnTimingUpdatePriceService.save(save);
            }
            if (isExecute == 1) {
                timingBatchUpdatePrice.updatePrice();
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FnTimingUpdatePriceSaveService");
        }
    }
}
