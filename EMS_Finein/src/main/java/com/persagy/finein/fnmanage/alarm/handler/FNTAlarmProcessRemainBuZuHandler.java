package com.persagy.finein.fnmanage.alarm.handler;

import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.dictionary.Building;

import java.util.Map;

/**
* 时间:2017年10月21日 下午1:45:10
* 说明:剩余不足
*/
public interface FNTAlarmProcessRemainBuZuHandler {

	public void handle(Building building, FnTenant tenant, String energyTypeId, String alarmTypeId, FnTenantPayType tenantPayType, Map<String, FnAlarmLimitGlobal> globalAlarmLimitMap) throws Exception;

	public void alarm(Building building, FnTenant tenant, String energyTypeId, String alarmTypeId, Double limitValue, Double currentValue) throws Exception;
}

