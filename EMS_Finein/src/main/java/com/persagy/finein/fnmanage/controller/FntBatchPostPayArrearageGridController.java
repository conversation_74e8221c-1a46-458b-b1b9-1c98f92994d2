package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnRecordPostClearingPay;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.core.util.PathUtil;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNFileResourceService;
import com.persagy.finein.service.FNRecordPostClearingPayService;
import com.persagy.finein.service.FNTenantService;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：批量操作-欠费账单（查询+下载）
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes", "deprecation" })
public class FntBatchPostPayArrearageGridController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNRecordPostClearingPayService")
    private FNRecordPostClearingPayService fnRecordPostClearingPayService;

    @Resource(name = "PathUtil")
    private PathUtil pathUtil;

    @Resource(name = "FNFileResourceService")
    private FNFileResourceService fnFileResourceService;


    @RequestMapping("FNTBatchPostPayArrearageGridService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchPostPayArrearageGrid(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String)dto.get("energyTypeId");
            Integer isDownload = (Integer)dto.get("isDownload");

            if(dto.get("tenantList") == null || energyTypeId == null
                    || isDownload == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            List<String> tenantList = (List<String>)dto.get("tenantList");


            String energyUnit = UnitUtil.getCumulantUnit(energyTypeId);
            String energyTypeName = EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId);

            List<FnRecordPostClearingPay> recordList = fnRecordPostClearingPayService.queryNotPayRecord(tenantList, energyTypeId);
            Map<String,List<FnRecordPostClearingPay>> tenantRecordMap = new HashMap<>();
            if(recordList != null){
                for(FnRecordPostClearingPay recordPostPay : recordList){
                    if(!tenantRecordMap.containsKey(recordPostPay.getTenantId())){
                        tenantRecordMap.put(recordPostPay.getTenantId(),new ArrayList<FnRecordPostClearingPay>());
                    }
                    tenantRecordMap.get(recordPostPay.getTenantId()).add(recordPostPay);
                }
            }


            List<FnTenant> fnTenantList = fnTenantService.queryListByIds(tenantList);
            List<Object> tenantObjList = new ArrayList<>();
            if(fnTenantList != null){
                for(FnTenant tenant : fnTenantList){
                    Map<String,Object> tenantMap = new HashMap<>();
                    tenantMap.put("tenantId", tenant.getId());
                    tenantMap.put("tenantName", tenant.getName());
                    tenantMap.put("roomIds", tenant.getRoomCodes());
                    tenantObjList.add(tenantMap);
                    List<Object> orderList = new ArrayList<>();
                    tenantMap.put("orderList", orderList);
                    Double totalMoney = null;
                    List<FnRecordPostClearingPay> recordPostPayList = tenantRecordMap.get(tenant.getId());
                    if(recordPostPayList != null){
                        for(FnRecordPostClearingPay recordPostPay : recordPostPayList){
                            Map<String,Object> recordMap = new HashMap<>();
                            recordMap.put("orderTime", recordPostPay.getOrderTime());
                            recordMap.put("orderId", recordPostPay.getOrderId());
                            recordMap.put("money", recordPostPay.getMoney());
                            if(recordPostPay.getMoney() != null){
                                totalMoney = totalMoney == null ? recordPostPay.getMoney() : totalMoney + recordPostPay.getMoney();
                            }
                            recordMap.put("amount", recordPostPay.getCurrentSumEnergy());
                            recordMap.put("userName", recordPostPay.getUserName());
                            orderList.add(recordMap);
                        }
                    }
                    tenantMap.put("totalMoney", totalMoney);
                }
            }


            Map<String,Object> contentObj = new HashMap<>();
            contentObj.put("energyUnit", energyUnit);
            contentObj.put("energyTypeName", energyTypeName);
            contentObj.put("tenantList", tenantObjList);

            if(isDownload.intValue() == EnumYesNo.NO.getValue().intValue()){
                content.add(contentObj);
            }else{
                String subdirectory = pathUtil.getTempDownloadSubDir();

                String resouceId = UUID.randomUUID().toString();
                FileResource resource = new FileResource();
                resource.setId(resouceId);
                StringBuffer fileNameSb = new StringBuffer();
                fileNameSb.append("欠费账单-").append(EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId));

                resource.setName(fileNameSb.toString());
                resource.setSuffix("xls");
                resource.setSubdirectory(subdirectory);

                String path = pathUtil.getPath();

                String fileDir = Paths.get(path, subdirectory).toString();
                File file = new File(fileDir);
                if(!file.exists()){
                    file.mkdirs();
                }
                this.buildExcel(path,subdirectory, resouceId, contentObj);

                fnFileResourceService.save(resource);
                Map<String,Object> newContentObj = new HashMap<>();
                newContentObj.put("id", resouceId);
                content.add(newContentObj);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTBatchPostPayArrearageGridService");
        }
    }

    @SuppressWarnings({  "unchecked" })
    private void buildExcel(String path,String subPath,String fileName,Map<String,Object> contentObj){
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");

        HSSFCellStyle style = wb.createCellStyle();
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);

        String energyUnit = (String)contentObj.get("energyUnit");

        List<Map<String,Object>> tenantList = (List<Map<String,Object>>)contentObj.get("tenantList");

        //创建标题
        HSSFRow row0 = sheet.createRow((short) 0);
        {
            HSSFCell cell = row0.createCell((short)0);
            cell.setCellStyle(style);
            cell.setCellValue("租户名称");
        }
        {
            HSSFCell cell = row0.createCell((short)1);
            cell.setCellStyle(style);
            cell.setCellValue("租户编号");
        }
        {
            HSSFCell cell = row0.createCell((short)2);
            cell.setCellStyle(style);
            cell.setCellValue("房间编号");
        }
        {
            HSSFCell cell = row0.createCell((short)3);
            cell.setCellStyle(style);
            cell.setCellValue("账单");
        }
        {
            HSSFCell cell = row0.createCell((short)4);
            cell.setCellStyle(style);
            cell.setCellValue("账单号");
        }
        {
            HSSFCell cell = row0.createCell((short)5);
            cell.setCellStyle(style);
            cell.setCellValue("结算能耗("+energyUnit+")");
        }
        {
            HSSFCell cell = row0.createCell((short)6);
            cell.setCellStyle(style);
            cell.setCellValue("结算金额(元)");
        }
        {
            HSSFCell cell = row0.createCell((short)7);
            cell.setCellStyle(style);
            cell.setCellValue("操作人");
        }
        {
            HSSFCell cell = row0.createCell((short)8);
            cell.setCellStyle(style);
            cell.setCellValue("未缴金额总计(元)");
        }

        if(tenantList != null){
            int currentRow = 1;
            for(int i=0;i<tenantList.size();i++){
                Map<String,Object> tenant = (Map<String,Object>)tenantList.get(i);
                String tenantId = (String)tenant.get("tenantId");
                String tenantName = (String)tenant.get("tenantName");
                String roomIds = (String)tenant.get("roomIds");
                List<Object> orderList = (List<Object>)tenant.get("orderList");
                Double totalMoney = DoubleFormatUtil.Instance().getDoubleData_00(tenant.get("totalMoney"));

                HSSFRow row = sheet.createRow((short) currentRow);
                {
                    HSSFCell cell = row.createCell((short)(0));
                    cell.setCellValue(tenantId == null ? " " : tenantId);
                }
                {
                    HSSFCell cell = row.createCell((short)(1));
                    cell.setCellValue(tenantName == null ? " " : tenantName);
                }
                {
                    HSSFCell cell = row.createCell((short)(2));
                    cell.setCellValue(roomIds == null ? " " : roomIds);
                }

                if(orderList != null && orderList.size() > 0){
                    sheet.addMergedRegion(new CellRangeAddress(currentRow,currentRow+orderList.size()-1,0,0));
                    sheet.addMergedRegion(new CellRangeAddress(currentRow,currentRow+orderList.size()-1,1,1));
                    sheet.addMergedRegion(new CellRangeAddress(currentRow,currentRow+orderList.size()-1,2,2));
                    sheet.addMergedRegion(new CellRangeAddress(currentRow,currentRow+orderList.size()-1,8,8));
                    {
                        HSSFCell cell = row.createCell((short)(8));
                        cell.setCellValue(totalMoney == null ? " " : totalMoney+"");
                    }
                    for(int j=0;j<orderList.size();j++){
                        Map<String,Object> orderMap = (Map<String,Object>)orderList.get(j);
                        String orderTime = (String)orderMap.get("orderTime");
                        String orderId = (String)orderMap.get("orderId");
                        Double amount = DoubleFormatUtil.Instance().getDoubleData_00(orderMap.get("amount"));
                        Double money = DoubleFormatUtil.Instance().getDoubleData_00(orderMap.get("money"));
                        String userName = (String)orderMap.get("userName");
                        if(j != 0){
                            row = sheet.createRow((short) currentRow);
                        }
                        {
                            HSSFCell cell = row.createCell((short)(3));
                            cell.setCellValue(orderTime == null ? "--" : orderTime);
                        }
                        {
                            HSSFCell cell = row.createCell((short)(4));
                            cell.setCellValue(orderId == null ? "--" : orderId);
                        }
                        {
                            HSSFCell cell = row.createCell((short)(5));
                            cell.setCellValue(amount == null ? 0 : amount);
                        }
                        {
                            HSSFCell cell = row.createCell((short)(6));
                            cell.setCellValue(money == null ? 0 : money);
                        }
                        {
                            HSSFCell cell = row.createCell((short)(7));
                            cell.setCellValue(userName == null ? "--" : userName);
                        }
                        currentRow++;
                    }
                }else{
                    {
                        HSSFCell cell = row.createCell((short)(3));
                        cell.setCellValue("--");
                    }
                    {
                        HSSFCell cell = row.createCell((short)(4));
                        cell.setCellValue("--");
                    }
                    {
                        HSSFCell cell = row.createCell((short)(5));
                        cell.setCellValue("--");
                    }
                    {
                        HSSFCell cell = row.createCell((short)(6));
                        cell.setCellValue("--");
                    }
                    {
                        HSSFCell cell = row.createCell((short)(7));
                        cell.setCellValue("--");
                    }
                    {
                        HSSFCell cell = row.createCell((short)(8));
                        cell.setCellValue("--");
                    }
                    currentRow++;
                }
            }
        }

        String newFilePath = Paths.get(path, subPath,fileName).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            if(fout!=null){
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if(wb!=null){
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
    }
}
