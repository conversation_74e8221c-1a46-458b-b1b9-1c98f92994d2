package com.persagy.finein.fnmanage.alarm.thread;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.thread.BaseThread;
import com.persagy.core.utils.HttpUtils;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnAlarm;
import com.persagy.ems.pojo.finein.FnSysParamValue;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.util.AlarmTypeUtil;
import com.persagy.finein.enumeration.EnumAlarmPushStatus;
import com.persagy.finein.service.FNAlarmService;
import com.persagy.finein.service.FNSysParamValueService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 报警推送 尚格云
 * <AUTHOR>
 */
@Component("FNTAlarmPushThread")
public class FNTAlarmPushThread extends BaseThread{

	private static boolean CONSTANT_THREAD_IS_OPEN = true;
	private static int CONSTANT_SLEEP = 60;
	
	private static long CONSTANT_QUERY_LIMIT = 100;//处理报警每次查询100 条
	
	private static Logger log = Logger.getLogger(FNTAlarmPushThread.class);
	
//	@Resource(name = "EMSZookeeperClient")
//	private EMSZookeeperClient EMSZookeeperClient;
	
	@Resource(name = "FNAlarmService")
	private FNAlarmService FNAlarmService;
	
	@Resource(name = "FNSysParamValueService")
	private FNSysParamValueService FNSysParamValueService;
	
	private static boolean IsSysParamValueInited = false;
	
	@PostConstruct
	private void init(){
		setName("FNTAlarmPushThread");
	}
	
	@Override
	protected void business() throws Exception {
		
		this.initSysParamValue();
		
		try {
			if(!CONSTANT_THREAD_IS_OPEN){
				this.setStop(true);
				log.error("【租户管理】租户报警推送线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}
			
			this.process();
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}
		
	}
	
	private void initSysParamValue(){
		if(IsSysParamValueInited){
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_AlarmPushThreadIsOpen);
			if(threadIsOpen != null){
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Integer sleep = (Integer)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_AlarmPushThreadSleepSecond);
			if(sleep != null){
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}
		
		IsSysParamValueInited = true;
		log.error("【租户管理】租户报警推送线程开始运行。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}
	
	private void process(){
		try {
			List<FnAlarm> alarmList = FNAlarmService.queryAlarmWaitPush(CONSTANT_QUERY_LIMIT);
			if(alarmList != null && alarmList.size() > 0){
				for(FnAlarm alarm : alarmList){
					processOne(alarm);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void processOne(FnAlarm alarm) {
		try {
			Integer pushStatus = alarm.getPushStatus();

			Map jsonParams = new LinkedHashMap<>();
			jsonParams.put("id", alarm.getId());
			Map<String, Object> params = new HashMap<>();
			
			FnSysParamValue query = new FnSysParamValue();
			query.setId(FineinConstant.SysParamValueKey.Id_PersagyCloudUrl);
			List<FnSysParamValue> list = FNSysParamValueService.query(query);
			String url=null;
			if(list!=null&&list.size()>0){
				url=list.get(0).getValue()+ "/PushToCloudService";
			}

			FnAlarm alarmQuery = new FnAlarm();
			alarmQuery.setId(alarm.getId());
			
			FnAlarm alarmUpdate = new FnAlarm();
			alarmUpdate.setPushStatus(EnumAlarmPushStatus.Completed.getValue());
			InterfaceResult interfaceResult = null;
			if(pushStatus == EnumAlarmPushStatus.WaitPush.getValue() || pushStatus == EnumAlarmPushStatus.StatusChange.getValue()){
				//推送报警
				jsonParams.put("source", alarm.getTenantId());
				jsonParams.put("productId", FineinConstant.Product.ProductId.Finein);
				jsonParams.put("type", alarm.getParentAlarmTypeId());
				jsonParams.put("subType", alarm.getAlarmTypeId());
				jsonParams.put("subTypeName", AlarmTypeUtil.queryAlarmTypeById(alarm.getAlarmTypeId()).getName());
				jsonParams.put("alarmTime", alarm.getAlarmTime());

				jsonParams.put("location", alarm.getAlarmPositionName());

				jsonParams.put("status", alarm.getStatus()+1);//尚格云报警状态比全地状态+1
				jsonParams.put("finishTime", alarm.getFinishTime());
				jsonParams.put("threshold", alarm.getLimitValue() +"");
				jsonParams.put("value", alarm.getCurrentValue());
				jsonParams.put("unit", alarm.getUnit());

				jsonParams.put("title", alarm.getAlarmPositionName());
				jsonParams.put("fId", FineinConstant.Product.FunctionId.PropertyMonitor);
				
				params.put("jsonString", URLEncoder.encode(SystemConstant.jsonMapper.writeValueAsString(jsonParams),"UTF-8"));

				String result = HttpUtils.post(url, params, false);

				interfaceResult = (InterfaceResult) SystemConstant.jsonMapper.readValue(result,InterfaceResult.class);
			}
			
			if(interfaceResult == null || interfaceResult.getResult().equals("success")){
				FNAlarmService.update(alarmQuery, alarmUpdate);
			}else{
				throw new Exception("【租户管理】租户报警推送线程异常：" + interfaceResult.getReason());
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
