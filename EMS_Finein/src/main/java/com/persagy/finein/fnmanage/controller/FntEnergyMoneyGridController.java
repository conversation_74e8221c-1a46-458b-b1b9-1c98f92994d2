package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.TimeDataUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantData;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumTenantStatus;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.service.FNTenantDataService;
import com.persagy.finein.service.FNTenantService;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：能耗费用报表-能耗费用报表查询（单租户）
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntEnergyMoneyGridController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantDataService")
    private FNTenantDataService fnTenantDataService;

    @RequestMapping("FNTEnergyMoneyGridService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult energyMoneyGrid(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String)dto.get("buildingId");
            String tenantId = (String)dto.get("tenantId");
            String energyTypeId = (String)dto.get("energyTypeId");
            String timeFrom = (String)dto.get("timeFrom");
            String timeTo = (String)dto.get("timeTo");


            if(tenantId == null || energyTypeId == null
                    || buildingId == null
                    || timeFrom == null
                    || timeTo == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            String energyUnit = UnitUtil.getCumulantUnit(energyTypeId);
            Map<String,Object> contentMap = new HashMap<String,Object>();
            contentMap.put("energyUnit", energyUnit);

            FnTenant tenant = fnTenantService.queryOne(tenantId);
            if(tenant == null){
                throw new Exception("租户不存在:"+tenantId);
            }

            contentMap.put("tenantId", tenantId);
            contentMap.put("tenantName", tenant.getName());

            Date from = standard.parse(timeFrom);
            Date to = standard.parse(timeTo);

            if(tenant.getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue()){
                if(tenant.getLeaveTime() != null){
                    to = new Date(tenant.getLeaveTime().getTime());
                }
            }

            Map<String,Double> timeDataMap = TimeDataUtil.getTimeDataMap(from, to, EnumTimeType.T2);

            List<FnTenantData> energyList = fnTenantDataService.queryListGteLt(buildingId, tenantId, EnumTimeType.T2, energyTypeId, from, to, EnumEnergyMoney.Energy);
            List<FnTenantData> moneyList = fnTenantDataService.queryListGteLt(buildingId, tenantId, EnumTimeType.T2, energyTypeId, from, to, EnumEnergyMoney.Money);

            Double totalEnergy = null;
            Double totalMoney = null;
            Map<String,Double> energyMap = new HashMap<String,Double>();
            Map<String,Double> moneyMap = new HashMap<String,Double>();

            if(energyList != null){
                for(FnTenantData tenantSum : energyList){
                    if(tenantSum.getData() != null){
                        if(tenantSum.getTimeFrom().getTime() < tenant.getActiveTime().getTime()){//小于激活时间，不统计
                            continue;
                        }
                        energyMap.put(standard.format(tenantSum.getTimeFrom()), tenantSum.getData());
                        totalEnergy = totalEnergy == null ? tenantSum.getData() : totalEnergy + tenantSum.getData();
                    }
                }
            }
            if(moneyList != null){
                for(FnTenantData tenantSum : moneyList){
                    if(tenantSum.getData() != null){
                        if(tenantSum.getTimeFrom().getTime() < tenant.getActiveTime().getTime()){//小于激活时间，不统计
                            continue;
                        }
                        moneyMap.put(standard.format(tenantSum.getTimeFrom()), tenantSum.getData());
                        totalMoney = totalMoney == null ? tenantSum.getData() : totalMoney + tenantSum.getData();
                    }
                }
            }

            contentMap.put("totalEnergy", totalEnergy);
            contentMap.put("totalMoney", totalMoney);

            List<Object> dataList = new ArrayList<Object>();
            contentMap.put("dataList", dataList);

            Date today = DateUtils.truncate(new Date(), Calendar.DATE);
            Double sumEnergy = null;
            Double sumMoney = null;
            for(Map.Entry<String, Double> entry : timeDataMap.entrySet()){
                Map<String,Object> map = new HashMap<String,Object>();
                map.put("time", entry.getKey());
                Date time = standard.parse(entry.getKey());
                Double energy = energyMap.get(entry.getKey());

                if(time.getTime() < tenant.getActiveTime().getTime()){//小于激活时间，不统计
                    continue;
                }else{
                    if(time.getTime() <= today.getTime()){
                        if(energy != null){
                            map.put("energy", energy);
                        }else{
                            map.put("energy", null);
                        }
                    }else{
                        map.put("energy", null);
                    }

                    if(energy != null){
                        sumEnergy = sumEnergy == null ? energy : sumEnergy + energy;
                    }

                    if(time.getTime() <= today.getTime()){
                        map.put("sumEnergy", sumEnergy);
                    }else{
                        map.put("sumEnergy", null);
                    }
                    Double money = moneyMap.get(entry.getKey());

                    if(money != null){
                        sumMoney = sumMoney == null ? money : sumMoney + money;
                    }
                    if(time.getTime() <= today.getTime()){
                        map.put("money", sumMoney);
                    }else{
                        map.put("money", null);
                    }
                    dataList.add(map);
                }
            }
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTEnergyMoneyGridService");
        }
    }


}
