package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnChargeMessage;
import com.persagy.ems.pojo.finein.FnRecordPostClearingPay;
import com.persagy.ems.pojo.finein.FnTenantMeterRemainDays;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 项目名：租户管理 接口名：消息提醒-发送消息（预付费，后付费）
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntMessageSendController extends BaseController {

    @Resource(name = "FNRecordPostClearingPayService")
    private FNRecordPostClearingPayService fnRecordPostClearingPayService;

    @Resource(name = "FNTenantMeterRemainDaysService")
    private FNTenantMeterRemainDaysService fnTenantMeterRemainDaysService;

    @Resource(name = "FNChargeMessageService")
    private FNChargeMessageService fnChargeMessageService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    private final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH-mm");

    @RequestMapping("FNTMessageSendService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult messageSend(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer payType = (Integer) dto.get("payType");
            String energyTypeId = (String) dto.get("energyTypeId");
            String meterId = (String) dto.get("meterId");

            if (dto.get("tenantList") == null || payType == null || energyTypeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            List<Map<String, Object>> tenantList = (List<Map<String, Object>>) dto.get("tenantList");

            if (payType.intValue() == EnumPayType.POSTPAY.getValue().intValue()) {
                this.processPostPay(energyTypeId, tenantList);
            } else if (payType.intValue() == EnumPayType.PREPAY.getValue().intValue()) {
                this.processPrePay(energyTypeId, tenantList, meterId);
            } else {
                throw new Exception("payType 无法解析:" + payType);
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTMessageSendService");
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void processPostPay(String energyTypeId, List<Map<String, Object>> tenantList) throws Exception {
        Map<String, List<Map<String, Object>>> buildingTenantListMap = new HashMap<>();
        for (Map<String, Object> tenantMap : tenantList) {
            String buildingId = (String) tenantMap.get("buildingId");
            if (!buildingTenantListMap.containsKey(buildingId)) {
                buildingTenantListMap.put(buildingId, new ArrayList<Map<String, Object>>());
            }
            buildingTenantListMap.get(buildingId).add(tenantMap);
        }
        List<FnChargeMessage> messageList = new ArrayList<FnChargeMessage>();

        for (Map.Entry<String, List<Map<String, Object>>> entry : buildingTenantListMap.entrySet()) {
            Map<String, List<FnRecordPostClearingPay>> recordMap = fnRecordPostClearingPayService
                    .queryNotPayRecord(entry.getKey(), energyTypeId);
            // 查询后付费未结算的账单
            for (Map<String, Object> tenantMap : entry.getValue()) {
                String tenantId = (String) tenantMap.get("tenantId");
                List<FnRecordPostClearingPay> list = recordMap.get(tenantId);
                if (list != null && list.size() > 0) {
                    String buildingId = (String) tenantMap.get("buildingId");
                    Building building = fnBuildingService.query(buildingId);
                    for (FnRecordPostClearingPay record : list) {

                        String tenantName = (String) tenantMap.get("tenantName");
                        String contactName = (String) tenantMap.get("contactName");
                        String contactMobile = (String) tenantMap.get("contactMobile");

                        FnChargeMessage message = new FnChargeMessage();
                        message.setId(UUID.randomUUID().toString());
                        message.setTenantId(tenantId);
                        message.setTenantName(tenantName);
                        message.setContactName(contactName);
                        message.setContactMobile(contactMobile);

                        StringBuffer contentSb = new StringBuffer();
                        contentSb.append(ConstantDBBaseData.SysParamValueMap
                                .get(FineinConstant.SysParamValueKey.Id_MessageSignature));
                        String unit = record.getCurrentSumEnergyUnit();
                        if (unit != null && "m³".equals(unit)) {
                            unit = "立方米";
                        }

                        String template = (String) ConstantDBBaseData.SysParamValueMap
                                .get(FineinConstant.SysParamValueKey.Id_MessagePostPayTemplate);
                        template = template.replace("${buildingName}", building.getName());
                        template = template.replace("${tenantName}", tenantName);
                        template = template.replace("${tenantID}", tenantId);
                        template = template.replace("${orderTime}", record.getOrderTime());

                        String energyTypeName = EnergyTypeUtil.queryEnergyTypeNameById(record.getEnergyTypeId());
                        template = template.replace("${energyTypeName}", energyTypeName == null ? "" : energyTypeName);
                        template = template.replace("${energyData}",
                                DoubleFormatUtil.Instance().getDoubleData_00(record.getCurrentSumEnergy()) + "");
                        template = template.replace("${energyUnit}", unit);
                        template = template.replace("${money}",
                                DoubleFormatUtil.Instance().getDoubleData_00(record.getMoney()) + "");
                        contentSb.append(template);

                        message.setContent(contentSb.toString());
                        message.setCreateTime(new Date());
                        message.setLastUpdateTime(new Date());
                        message.setStatus(EnumMessageSendStatus.WAIT_SEND.getValue());
                        message.setTryTimes(0);

                        messageList.add(message);
                    }
                }
            }
        }

        if (messageList.size() > 0) {
            fnChargeMessageService.save(messageList);
        }
    }


    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void processPrePay(String energyTypeId, List<Map<String, Object>> tenantList, String meterId)
            throws Exception {
        Map<String, List<Map<String, Object>>> buildingTenantListMap = new HashMap<>();
        for (Map<String, Object> tenantMap : tenantList) {
            String buildingId = (String) tenantMap.get("buildingId");
            if (!buildingTenantListMap.containsKey(buildingId)) {
                buildingTenantListMap.put(buildingId, new ArrayList<Map<String, Object>>());
            }
            buildingTenantListMap.get(buildingId).add(tenantMap);
        }
        List<FnChargeMessage> messageList = new ArrayList<FnChargeMessage>();

        for (Map.Entry<String, List<Map<String, Object>>> entry : buildingTenantListMap.entrySet()) {
            Map<String, Map<String, FnTenantPayType>> tenantPayTypeMap = fnTenantPayTypeService
                    .queryByBuildingId(entry.getKey());
            String buildingId = entry.getKey();
            Building building = fnBuildingService.query(buildingId);
            for (Map<String, Object> tenantMap : entry.getValue()) {
                String tenantId = (String) tenantMap.get("tenantId");
                Map<String, FnTenantPayType> payTypeMap = tenantPayTypeMap.get(tenantId);
                if (payTypeMap == null) {
                    continue;
                }
                FnTenantPayType payType = payTypeMap.get(energyTypeId);
                if (payType == null) {
                    continue;
                }
                EnumPayBodyType payBodyType;
                String bodyId;
                if (payType.getPrePayType() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {// 软充软扣
                    payBodyType = EnumPayBodyType.TENANT;
                    bodyId = tenantId;
                } else {
                    payBodyType = EnumPayBodyType.METER;
                    bodyId = meterId;
                }
                List<FnTenantMeterRemainDays> remainDayList = fnTenantMeterRemainDaysService.queryList(buildingId, tenantId,
                        payBodyType, bodyId, energyTypeId);
                if (remainDayList != null) {
                    for (FnTenantMeterRemainDays remainDays : remainDayList) {
                        String tenantName = (String) tenantMap.get("tenantName");
                        String contactName = (String) tenantMap.get("contactName");
                        String contactMobile = (String) tenantMap.get("contactMobile");

                        FnChargeMessage message = new FnChargeMessage();
                        message.setId(UUID.randomUUID().toString());
                        message.setTenantId(tenantId);
                        message.setTenantName(tenantName);
                        message.setContactName(contactName);
                        message.setContactMobile(contactMobile);

                        StringBuffer contentSb = new StringBuffer();
                        contentSb.append(ConstantDBBaseData.SysParamValueMap
                                .get(FineinConstant.SysParamValueKey.Id_MessageSignature));

                        String template = (String) ConstantDBBaseData.SysParamValueMap
                                .get(FineinConstant.SysParamValueKey.Id_MessagePrePayTemplate);

                        template = template.replace("${buildingName}", building.getName());
                        template = template.replace("${tenantName}", tenantName);
                        template = template.replace("${tenantID}", tenantId);
                        String time = standard.format(remainDays.getLastComputeTime());
                        String replace = time.replace(" ", "-");
                        String[] split = replace.trim().split("-");
                        template = template.replace("${year}", split[0]);
                        template = template.replace("${month}", split[1]);
                        template = template.replace("${day}", split[2]);
                        template = template.replace("${hour}", split[3]);
                        String energyTypeName = EnergyTypeUtil.queryEnergyTypeNameById(remainDays.getEnergyTypeId());
                        StringBuffer stringBuffer = new StringBuffer();
                        if (remainDays.getPayBodyType() == EnumPayBodyType.METER.getValue().intValue()) {
                            stringBuffer.append("仪表(").append(remainDays.getBodyId()).append(")");
                        }
                        stringBuffer.append(energyTypeName);
                        template = template.replace("${energyTypeName}", stringBuffer.toString());
                        template = template.replace("${remainData}", remainDays.getRemainData() == null ? "--"
                                : DoubleFormatUtil.Instance().getDoubleData_00(remainDays.getRemainData()) + "");

                        // 租户充值类型
                        Map<String, FnTenantPayType> typeMap = fnTenantPayTypeService.queryPayTypeMap(tenantId);
                        FnTenantPayType fnTenantPayType = typeMap.get(remainDays.getEnergyTypeId());
                        String billModeUnit = UnitUtil.getBillModeUnit(energyTypeId,
                                EnumPrepayChargeType.valueOf(fnTenantPayType.getPrepayChargeType()));
                        if ("m³".equals(billModeUnit)) {
                            billModeUnit = "立方米";
                        }
                        template = template.replace("${unit}", billModeUnit);

                        template = template.replace("${minDay}",
                                remainDays.getMinDays() == null ? "--" : remainDays.getMinDays() + "");
                        template = template.replace("${maxDay}",
                                remainDays.getMaxDays() == null ? "--" : +remainDays.getMaxDays() + "");

                        contentSb.append(template);

                        message.setContent(contentSb.toString());
                        message.setCreateTime(new Date());
                        message.setLastUpdateTime(new Date());
                        message.setStatus(EnumMessageSendStatus.WAIT_SEND.getValue());
                        message.setTryTimes(0);
                        messageList.add(message);
                    }
                }
            }
        }

        if (messageList.size() > 0) {
            fnChargeMessageService.save(messageList);
        }
    }
}
