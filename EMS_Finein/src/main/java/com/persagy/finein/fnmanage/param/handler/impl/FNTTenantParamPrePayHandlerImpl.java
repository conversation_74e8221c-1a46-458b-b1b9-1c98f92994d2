package com.persagy.finein.fnmanage.param.handler.impl;

import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.alarm.util.FNAlarmUtil;
import com.persagy.finein.fnmanage.param.handler.FNTTenantParamPrePayHandler;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月21日 下午1:45:10
 * 
 * 说明:
 */
@Component("FNTTenantParamPrePayHandler")
public class FNTTenantParamPrePayHandlerImpl extends CoreServiceImpl implements FNTTenantParamPrePayHandler {

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;

	@Resource(name = "FNTenantPrePayParamService")
	private FNTenantPrePayParamService FNTenantPrePayParamService;

	@Resource(name = "FNTenantPrePayMeterParamService")
	private FNTenantPrePayMeterParamService FNTenantPrePayMeterParamService;

	@Resource(name = "FNTenantDataService")
	private FNTenantDataService FNTenantDataService;

	@Resource(name = "FNAlarmService")
	private FNAlarmService FNAlarmService;

	@Resource(name = "FNTenantMeterRemainDaysService")
	private FNTenantMeterRemainDaysService FNTenantMeterRemainDaysService;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNAlarmUtil")
	private FNAlarmUtil FNAlarmUtil;

	@Resource(name = "FNAlarmLimitGlobalService")
	private FNAlarmLimitGlobalService FNAlarmLimitGlobalService;

	public void handle(Building building, FnTenant tenant, FnTenantPayType tenantPayType) throws Exception {
//		if (tenant.getId().equals("ZHBH_1004") && tenantPayType.getEnergyTypeId().equals("Dian")) {
//			System.out.println("--");
//		}
		Integer prepayChargeType = tenantPayType.getPrepayChargeType();// 充钱?充量
		List<FnAlarmType> alarmTypeList = ConstantDBBaseData.EnergyTypeAlarmTypeMap
				.get(tenantPayType.getEnergyTypeId());
		Map<String, FnAlarmLimitGlobal> globalAlarmLimitMap = FNAlarmLimitGlobalService.queryMap();
		Double limitRemainDay = null;
		Double limitRemainData = null;
		for (FnAlarmType fnAlarmType : alarmTypeList) {
			String alarmId = fnAlarmType.getId();
			Integer remainType = ConstantDBBaseData.AlarmType_Type.get(fnAlarmType.getId());
			if (EnumAlarmRemainType.REMAIN_Day.getValue().intValue() == remainType) {
				limitRemainDay = FNAlarmUtil.queryAlarmLimit(building.getId(), tenant.getId(), alarmId,
						globalAlarmLimitMap);
			} else {
				if (prepayChargeType == EnumPrepayChargeType.Liang.getValue().intValue()) {
					if(remainType!=EnumAlarmRemainType.REMAIN_Data.getValue().intValue()){
						continue;
					}
				} else if (prepayChargeType == EnumPrepayChargeType.Qian.getValue().intValue()) {
					if(remainType!=EnumAlarmRemainType.REMAIN_JinE.getValue().intValue()){
						continue;
					}
				}
				limitRemainData = FNAlarmUtil.queryAlarmLimit(building.getId(), tenant.getId(), fnAlarmType.getId(),
						globalAlarmLimitMap);
			}
		}
		{// 处理租户参数

			FnTenantPrePayParam query = new FnTenantPrePayParam();
			query.setBuildingId(tenant.getBuildingId());
			query.setTenantId(tenant.getId());
			query.setEnergyTypeId(tenantPayType.getEnergyTypeId());

			FnTenantPrePayParam update = new FnTenantPrePayParam();

			FnTenantPrePayParam param = (FnTenantPrePayParam) FNTenantPrePayParamService.queryObject(query);

			update.setPrePayType(tenantPayType.getPrePayType());
			update.setPrepayChargeType(tenantPayType.getPrepayChargeType());
			update.setLastUpdateTime(new Date());
			// 本月时间
			Date timeFrom = DateUtils.truncate(new Date(), Calendar.MONTH);
			Date timeTo = DateUtils.addMonths(timeFrom, 1);

			Double currentMonthEnergy = FNTenantDataService.queryDataGteLt(building.getId(), tenant.getId(),
					EnumTimeType.T2, tenantPayType.getEnergyTypeId(), timeFrom, timeTo, EnumEnergyMoney.Energy);
			update.setCurrentMonthEnergy(currentMonthEnergy);

			FnTenantMeterRemainDays tenantMeterRemainDays = FNTenantMeterRemainDaysService.queryRemainDays(
					building.getId(), tenant.getId(), EnumPayBodyType.TENANT, tenant.getId(),
					tenantPayType.getEnergyTypeId());
			if (tenantMeterRemainDays != null) {
				{// 剩余数据
					update.setRemainData(tenantMeterRemainDays.getRemainData());
				}
				{// 剩余天数
					if (tenantMeterRemainDays.getMaxDays() != null && tenantMeterRemainDays.getMinDays() != null) {
						update.setRemainDays(
								tenantMeterRemainDays.getMinDays() + "~" + tenantMeterRemainDays.getMaxDays());
					}
				}
				{// 是否报警-直接计算
					if ((tenantMeterRemainDays != null && tenantMeterRemainDays.getMaxDays() != null
							&& limitRemainDay != null && tenantMeterRemainDays.getMaxDays() < limitRemainDay)
							|| (tenantMeterRemainDays != null && limitRemainData != null
									&& tenantMeterRemainDays.getRemainData() != null
									&& tenantMeterRemainDays.getRemainData() < limitRemainData)) {
						update.setIsAlarm(EnumYesNo.YES.getValue());
					} else {
						update.setIsAlarm(EnumYesNo.NO.getValue());
					}
				}
			} else {
				update.setIsAlarm(EnumYesNo.NO.getValue());
			}
			if (param == null) {// 保存
				update.setBuildingId(tenant.getBuildingId());
				update.setTenantId(tenant.getId());
				update.setEnergyTypeId(tenantPayType.getEnergyTypeId());

				FNTenantPrePayParamService.save(update);
			} else {// 更新
				if (update.getRemainData() == null) {
					update.setSpecialOperation("remainData", SpecialOperator.$exists, false);
				}
				if (update.getRemainDays() == null) {
					update.setSpecialOperation("remainDays", SpecialOperator.$exists, false);
				}
				if (update.getCurrentMonthEnergy() == null) {
					update.setSpecialOperation("currentMonthEnergy", SpecialOperator.$exists, false);
				}
				FNTenantPrePayParamService.update(query, update);
			}
		}
		{// 处理租户仪表参数
			// 根据能耗类型查询仪表
			List<DTOMeter> meterList = FNMeterService.queryMeterList(tenant.getId(), tenantPayType.getEnergyTypeId());
			if (meterList != null) {
				for (DTOMeter dtoMeter : meterList) {
					FnTenantPrePayMeterParam query = new FnTenantPrePayMeterParam();
					query.setBuildingId(tenant.getBuildingId());
					query.setTenantId(tenant.getId());
					query.setEnergyTypeId(tenantPayType.getEnergyTypeId());
					query.setMeterId(dtoMeter.getMeterId());

					FnTenantPrePayMeterParam update = new FnTenantPrePayMeterParam();

					FnTenantPrePayMeterParam param = (FnTenantPrePayMeterParam) FNTenantPrePayMeterParamService
							.queryObject(query);

					update.setPrePayType(tenantPayType.getPrePayType());
					update.setPrepayChargeType(tenantPayType.getPrepayChargeType());
					// 本月时间
					Date timeFrom = DateUtils.truncate(new Date(), Calendar.MONTH);
					Date timeTo = DateUtils.addMonths(timeFrom, 1);

					Double currentMonthEnergy = FNTenantDataService.queryDataGteLt(building.getId(), tenant.getId(),
							EnumTimeType.T2, tenantPayType.getEnergyTypeId(), timeFrom, timeTo, EnumEnergyMoney.Energy);
					update.setCurrentMonthEnergy(currentMonthEnergy);

					FnTenantMeterRemainDays tenantMeterRemainDays = FNTenantMeterRemainDaysService.queryRemainDays(
							building.getId(), tenant.getId(), EnumPayBodyType.METER, dtoMeter.getMeterId(),
							tenantPayType.getEnergyTypeId());
					if (tenantMeterRemainDays != null) {
						{// 剩余数据
							update.setRemainData(tenantMeterRemainDays.getRemainData());
						}
						{// 剩余天数
							if (tenantMeterRemainDays.getMaxDays() != null
									&& tenantMeterRemainDays.getMinDays() != null) {
								update.setRemainDays(
										tenantMeterRemainDays.getMinDays() + "~" + tenantMeterRemainDays.getMaxDays());
							}
						}
						{// 是否报警-直接计算
							if ((tenantMeterRemainDays != null && tenantMeterRemainDays.getMaxDays() != null
									&& limitRemainDay != null && tenantMeterRemainDays.getMaxDays() < limitRemainDay)
									|| (tenantMeterRemainDays != null && tenantMeterRemainDays.getRemainData() != null
											&& limitRemainData != null
											&& tenantMeterRemainDays.getRemainData() < limitRemainData)) {
								update.setIsAlarm(EnumYesNo.YES.getValue());
							} else {
								update.setIsAlarm(EnumYesNo.NO.getValue());
							}
						}
					} else {
						update.setIsAlarm(EnumYesNo.NO.getValue());
					}

					update.setLastUpdateTime(new Date());
					if (param == null) {// 保存
						update.setBuildingId(tenant.getBuildingId());
						update.setTenantId(tenant.getId());
						update.setEnergyTypeId(tenantPayType.getEnergyTypeId());
						update.setMeterId(dtoMeter.getMeterId());

						FNTenantPrePayMeterParamService.save(update);
					} else {// 更新
							// 设置null
						if (update.getRemainData() == null) {
							update.setSpecialOperation("remainData", SpecialOperator.$exists, false);
						}
						if (update.getRemainDays() == null) {
							update.setSpecialOperation("remainDays", SpecialOperator.$exists, false);
						}
						if (update.getCurrentMonthEnergy() == null) {
							update.setSpecialOperation("currentMonthEnergy", SpecialOperator.$exists, false);
						}
						FNTenantPrePayMeterParamService.update(query, update);
					}
				}
			}

		}
	}
}
