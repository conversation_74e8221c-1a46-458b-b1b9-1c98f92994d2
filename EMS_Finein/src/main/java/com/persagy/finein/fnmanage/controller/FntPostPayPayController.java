package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnRecordPostClearingPay;
import com.persagy.ems.pojo.finein.FnRecordPostPay;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.fnmanage.param.handler.FNTTenantParamPostPayHandler;
import com.persagy.finein.service.FNRecordPostClearingPayService;
import com.persagy.finein.service.FNRecordPostPayService;
import com.persagy.finein.service.FNTenantService;
import com.persagy.finein.service.FNUserService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：后付费-后付费缴费（单租户）
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
@Transactional(propagation= Propagation.REQUIRED,rollbackFor=Exception.class)
public class FntPostPayPayController extends BaseController {

    @Resource(name = "FNRecordPostClearingPayService")
    private FNRecordPostClearingPayService fnRecordPostClearingPayService;

    @Resource(name = "FNRecordPostPayService")
    private FNRecordPostPayService fnRecordPostPayService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @Resource(name = "FNTTenantParamPostPayHandler")
    private FNTTenantParamPostPayHandler fntTenantParamPostPayHandler;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;


    @RequestMapping("FNTPostPayPayService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
    public InterfaceResult postPayPay(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String)dto.get("buildingId");
            String tenantId = (String)dto.get("tenantId");
            String energyTypeId = (String)dto.get("energyTypeId");
            String userId = this.getParamUserId(dto);
            if(buildingId == null || tenantId == null || energyTypeId == null || userId == null ||dto.get("orderList") == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            List<String> orderList = (List<String>)dto.get("orderList");
            //查询所有账单
            List<FnRecordPostClearingPay> recordList = fnRecordPostClearingPayService.queryNotPayRecord(buildingId, tenantId, energyTypeId, orderList);
            //更新所有账单
            if(recordList != null && recordList.size() > 0){
                fnRecordPostClearingPayService.payRecord(buildingId, tenantId, energyTypeId, orderList);
                List<FnRecordPostPay> saveList = new ArrayList<FnRecordPostPay>();
                Date now = new Date();
                DTOUser user = fnUserService.queryUserByUserId(userId);

                for(FnRecordPostClearingPay record : recordList){
                    //保存缴费记录
                    FnRecordPostPay saveObj = new FnRecordPostPay();
                    saveObj.setId(UUID.randomUUID().toString());
                    saveObj.setBuildingId(record.getBuildingId());
                    saveObj.setBuildingName(record.getBuildingName());
                    saveObj.setTenantId(record.getTenantId());
                    saveObj.setTenantName(record.getTenantName());
                    saveObj.setEnergyTypeId(record.getEnergyTypeId());
                    saveObj.setOperateTime(now);
                    saveObj.setOrderId(record.getOrderId());
                    saveObj.setOrderTime(record.getOrderTime());
                    saveObj.setMoney(record.getMoney());
                    saveObj.setRoomIds(record.getRoomIds());
                    saveObj.setUserId(userId);
                    saveObj.setUserName(userId == null ? userId : user.getUserName());
                    saveObj.setCreateTime(now);
                    saveList.add(saveObj);
                }
                if(saveList.size() > 0){
                    fnRecordPostPayService.save(saveList);
                    //刷新后会费参数计算
                    FnTenant tenant = fnTenantService.queryOne(tenantId);
                    Building building = new Building();
                    building.setId(tenant.getBuildingId());
                    FnTenantPayType tenantPayType = new FnTenantPayType();
                    tenantPayType.setEnergyTypeId(energyTypeId);
                    fntTenantParamPostPayHandler.handle(building, tenant, tenantPayType);
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPostPayPayService");
        }
    }


}
