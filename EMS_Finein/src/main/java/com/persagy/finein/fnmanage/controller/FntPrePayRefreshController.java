package com.persagy.finein.fnmanage.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.FnTenantPrePayParam;
import com.persagy.finein.communication.exception.MeterSetException;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.finein.enumeration.EnumPrePayType;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNTenantPayTypeService;
import com.persagy.finein.service.FNTenantPrePayParamService;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理 接口名：预付费充值-刷新
 *
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntPrePayRefreshController extends BaseController {


    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNTenantPrePayParamService")
    private FNTenantPrePayParamService fnTenantPrePayParamService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;


    @RequestMapping("FNTPrePayRefreshService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult prePayRefresh(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");
            String meterId = (String) dto.get("meterId");
            String energyTypeId = (String) dto.get("energyTypeId");
            // Integer prePayType = (Integer)dto.get("prePayType");

            if (tenantId == null || energyTypeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            FnTenantPayType tenantPayType = fnTenantPayTypeService.query(tenantId, energyTypeId);
            if (tenantPayType == null) {
                throw new Exception("未找到租户的付费类型");
            }
            Map<String, Object> contentObj = new HashMap<String, Object>();
            if (tenantPayType.getPrePayType().intValue() == EnumPrePayType.ONLINE_METERPAY.getValue().intValue()) {
                if (meterId == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull("meterId"));
                }
                this.processMeterPay(contentObj, tenantId, meterId, energyTypeId);
            } else if (tenantPayType.getPrePayType().intValue() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
                this.processTenantPay(contentObj, tenantId, energyTypeId);
            } else {
                throw new Exception("不知道的prePayType:" + tenantPayType.getPrePayType());
            }
            content.add(contentObj);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTPrePayRefreshService");
        }
    }

    private void processTenantPay(Map<String, Object> contentObj, String tenantId, String energyTypeId)
            throws Exception {
        FnTenantPrePayParam preParam = fnTenantPrePayParamService.queryTenantPreParam(tenantId, energyTypeId);
        if (preParam != null) {
            contentObj.put("remainData", preParam.getRemainData());
            contentObj.put("lastUpdateTime", preParam.getLastUpdateTime());
        } else {
            contentObj.put("remainData", null);
            contentObj.put("lastUpdateTime", new Date());
        }
    }

    private void processMeterPay(Map<String, Object> contentObj, String tenantId, String meterId, String energyTypeId)
            throws Exception {
        FnMeter fnMeter = fnMeterService.queryMeterById(meterId);

        ICommunication communication = (ICommunication) SystemConstant.context.getBean(fnMeter.getProtocolId());

        Double remainData = null;
        try {
            remainData = communication.queryRemainData(fnMeter);
            if (!"DI_C_07_Y_Q_001".equals(fnMeter.getProtocolId())) {
                //透支金额
                Double overdraft = communication.queryOverdraft(fnMeter);
                if (overdraft != null && remainData != null) {
                    remainData = remainData - overdraft;
                }
            }
        } catch (MeterSetException e1) {
            e1.printStackTrace();
        }

        if (remainData != null) {
            // 剩余量是否乘变比
            Double radio = 1.0;
            try {
                Long syjIsCt = (Long) ((JSONObject) JSONValue.parse(fnMeter.getExtend())).get("syjIsCt");
                if (syjIsCt != null && syjIsCt.intValue() == 1) {
                    radio = fnMeter.getRadio();
                }
            } catch (Exception e) {
            }
            contentObj.put("remainData", remainData * radio);
            contentObj.put("lastUpdateTime", new Date());
        } else {
            contentObj.put("remainData", null);
            contentObj.put("lastUpdateTime", new Date());
        }
    }
}
