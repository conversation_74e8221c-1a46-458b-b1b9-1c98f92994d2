package com.persagy.finein.fnmanage.compute.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnMeterPowerCompute;
import com.persagy.ems.pojo.finein.FnMeterPowerDayPeak;
import com.persagy.ems.pojo.finein.FnMeterPowerStat;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.ems.pojo.servicedata.ServiceData;
import com.persagy.finein.enumeration.EnumStatType;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.fnmanage.compute.handler.FNTComputeMeterPowerHandler;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月22日 下午12:58:25
 * 
 * 说明:租户总功率计算线程
 */
@Component("FNTComputeMeterPowerHandler")
public class FNTComputeMeterPowerHandlerImpl extends CoreServiceImpl implements FNTComputeMeterPowerHandler {

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNMeterPowerStatService")
	private FNMeterPowerStatService FNMeterPowerStatService;

	@Resource(name = "FNMeterPowerComputeService")
	private FNMeterPowerComputeService FNMeterPowerComputeService;

	@Resource(name = "FNMeterDataService")
	private FNMeterDataService FNMeterDataService;

	@Resource(name = "FNServiceDataService")
	private FNServiceDataService FNServiceDataService;
	@Resource(name = "FNMeterPowerDayPeakService")
	private FNMeterPowerDayPeakService FNMeterPowerDayPeakService;
	@Resource(name = "FNRoomService")
	private FNRoomService FNRoomService;
	@Resource(name = "FNRoomMeterService")
	private FNRoomMeterService FNRoomMeterService;

	// buildingId_tenantId_functionId_timeType_dataType,value
	private Map<String, Map<String, Object>> meterHistoryStatMap = new HashMap<>();

	// buildingId_tenantId_meterId_functionId_timeType_dataType,value

	private static Boolean InitStat = false;

	private static final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	@Override
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public void processBuilding(Building building, Integer delaySecond) throws Exception {
		if (!InitStat) {
			this.initStat();
			InitStat = true;
		}
		// 查询建筑下所有电表
		Map<String, DTOMeter> meterMap = new HashMap<String, DTOMeter>();
		Map<String, List<DTOMeter>> dtoMeterMap = FNRoomMeterService.queryRoomMeter(building.getId());
		if (dtoMeterMap != null && dtoMeterMap.size() > 0) {
			for (Entry<String, List<DTOMeter>> entry : dtoMeterMap.entrySet()) {
				List<DTOMeter> list = entry.getValue();
				for (DTOMeter dtoMeter : list) {
					if (!dtoMeter.getEnergyTypeId().equals(FineinConstant.EnergyType.Dian)) {
						continue;
					}
					meterMap.put(dtoMeter.getMeterId(), dtoMeter);
				}
			}
		}
		for (Entry<String, DTOMeter> entry : meterMap.entrySet()) {
			this.processMeter(building, entry.getValue(), delaySecond);
		}

	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void processMeter(Building building, DTOMeter meter, Integer delaySecond) throws Exception {
		Map<String, ServiceData> meterPowerDayMap = new HashMap<String, ServiceData>();
		List<FnMeterPowerCompute> powerSaveList = new ArrayList<>();
		List<FnMeterPowerDayPeak> powerDaySaveList = new ArrayList<>();
		processMeterPower(building, meter, powerSaveList, delaySecond, meterPowerDayMap);
		// 处理日峰值
		for (Entry<String, ServiceData> entry : meterPowerDayMap.entrySet()) {
			FnMeterPowerDayPeak save = new FnMeterPowerDayPeak();
			save.setMeterId(entry.getValue().getSign());
			save.setData(entry.getValue().getData() * 4);
			save.setTimeFrom(entry.getValue().getTimefrom());
			save.setCountTime(standard.parse(entry.getKey()));
			save.setLastUpdateTime(new Date());
			powerDaySaveList.add(save);
		}
		if (powerDaySaveList.size() > 0) {
			FNMeterPowerDayPeakService.saveDataList(powerDaySaveList);
		}
		FNMeterPowerComputeService.update(powerSaveList);
	}

	private void initStat() throws Exception {
		List<FnMeterPowerStat> list = FNMeterPowerStatService.query(new FnMeterPowerStat());
		if (list != null) {
			for (FnMeterPowerStat stat : list) {
				if (stat.getData() != null) {
					String key = getMeterStatKey(stat.getMeterId(), stat.getDataType());
					Map<String, Object> hashMap = new HashMap<String, Object>();
					hashMap.put("time", stat.getTimeFrom());
					hashMap.put("data", stat.getData());
					this.meterHistoryStatMap.put(key, hashMap);
				}
			}
		}
	}

	private void processMeterPower(Building building, DTOMeter meter, List<FnMeterPowerCompute> powerSaveList,
			Integer delaySecond, Map<String, ServiceData> meterPowerDayMap) throws Exception {
		FnMeterPowerCompute compute = FNMeterPowerComputeService.query(meter.getMeterId(), EnumStatType.Max);
		Date lastComputeTime = null;
		if (compute == null) {
			lastComputeTime = standard.parse("1990-04-15 00:00:00");
		} else {
			lastComputeTime = compute.getLastComputeTime();
		}
		String maxKey = getMeterStatKey(meter.getMeterId(), EnumStatType.Max.getValue());
		Date meterLastComputeTime = FNMeterDataService.queryMeterComputeTime(building.getId(), meter.getMeterId(),
				FineinConstant.FuntionTypeId.Cumulant_Dian);
		if (meterLastComputeTime == null) {
			return;
		}
		Date delayTime = DateUtils.addSeconds(meterLastComputeTime, -delaySecond);
		Date timeFrom = lastComputeTime;
		while (lastComputeTime.getTime() < delayTime.getTime()) {
			timeFrom = lastComputeTime;
			Date timeTo = DateUtils.addDays(timeFrom, 7);
			if (timeTo.getTime() > delayTime.getTime()) {
				timeTo = delayTime;
			}

			List<ServiceData> list = FNServiceDataService.queryServiceDataGteLt(building.getId(), meter.getMeterId(),
					FineinConstant.FuntionTypeId.Cumulant_Dian, EnumTimeType.T0, timeFrom, timeTo);
			if (list != null) {
				for (ServiceData serviceData : list) {
					if (serviceData.getData() != null) {
						// 处理仪表历史最大值
						if (meterHistoryStatMap.get(maxKey) == null) {
							meterHistoryStatMap.put(maxKey, new HashMap<String, Object>());
						}
						if (meterHistoryStatMap.get(maxKey).get("data") == null) {
							meterHistoryStatMap.get(maxKey).put("data", serviceData.getData() * 4);
							meterHistoryStatMap.get(maxKey).put("time", serviceData.getTimefrom());
						} else if ((Double) meterHistoryStatMap.get(maxKey).get("data") < serviceData.getData() * 4) {
							meterHistoryStatMap.get(maxKey).put("data", serviceData.getData() * 4);
							meterHistoryStatMap.get(maxKey).put("time", serviceData.getTimefrom());
						}
						// 处理仪表日最大值
						Date day = DateUtils.truncate(serviceData.getTimefrom(), Calendar.DATE);
						String meterPowerDateStatKey = standard.format(day);
						if (meterPowerDayMap.get(meterPowerDateStatKey) == null
								|| meterPowerDayMap.get(meterPowerDateStatKey).getData() < serviceData.getData()) {
							meterPowerDayMap.put(meterPowerDateStatKey, serviceData);
						}
					}
				}
			}
			lastComputeTime = timeTo;
			Thread.sleep(3);
		}
		if (meterHistoryStatMap.get(maxKey) != null) {
			this.FNMeterPowerStatService.saveData(meter.getMeterId(), EnumStatType.Max,
					(Date) meterHistoryStatMap.get(maxKey).get("time"),
					(Double) meterHistoryStatMap.get(maxKey).get("data"));
			FnMeterPowerCompute query = new FnMeterPowerCompute();
			query.setMeterId(meter.getMeterId());
			query.setDataType(EnumStatType.Max.getValue());
			query.setLastComputeTime(delayTime);
			query.setLastUpdateTime(new Date());
			powerSaveList.add(query);
		}
	}

	private String getMeterStatKey(String meterId, int dataType) {
		StringBuffer sb = new StringBuffer();
		sb.append(meterId).append("_");
		sb.append(dataType);
		return sb.toString();
	}
}
