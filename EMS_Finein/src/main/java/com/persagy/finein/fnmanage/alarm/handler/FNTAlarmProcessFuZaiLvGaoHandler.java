package com.persagy.finein.fnmanage.alarm.handler;

import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.dictionary.Building;

import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月21日 下午1:45:10

* 说明:负荷率过高
*/
public interface FNTAlarmProcessFuZaiLvGaoHandler {
	
	
	public void handle(Building building, FnTenant tenant, String energyTypeId, String alarmTypeId, FnTenantPayType tenantPayType, Map<String, FnAlarmLimitGlobal> globalAlarmLimitMap) throws Exception;

	public void alarm(Building building, FnTenant tenant, String energyTypeId, String alarmTypeId, DTOMeter meter, Double limitValue, Double currentValue, Double baseLoad, Double powerValue) throws Exception;
}

