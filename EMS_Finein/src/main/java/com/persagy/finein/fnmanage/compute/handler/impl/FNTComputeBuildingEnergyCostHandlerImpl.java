package com.persagy.finein.fnmanage.compute.handler.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.fnmanage.compute.handler.FNTComputeBuildingEnergyCostHandler;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月22日 下午12:58:25
 * 
 * 说明:租户计算线程
 */
@Component("FNTComputeBuildingEnergyCostHandler")
public class FNTComputeBuildingEnergyCostHandlerImpl extends CoreServiceImpl
		implements FNTComputeBuildingEnergyCostHandler {

	private static Logger log = Logger.getLogger(FNTComputeBuildingEnergyCostHandlerImpl.class);

	@Resource(name = "FNBuildingService")
	public FNBuildingService FNBuildingService;

	@Resource(name = "FNRecordPrePayService")
	public FNRecordPrePayService FNRecordPrePayService;

	@Resource(name = "FNRecordReturnService")
	public FNRecordReturnService FNRecordReturnService;

	@Resource(name = "FNProjectService")
	public FNProjectService FNProjectService;

	@Resource(name = "FNBuildingEnergyCostService")
	public FNBuildingEnergyCostService FNBuildingEnergyCostService;

	@Resource(name = "FNTenantDataService")
	public FNTenantDataService FNTenantDataService;
	
	@Resource(name = "FNBuildingCostTimeService")
	public FNBuildingCostTimeService FNBuildingCostTimeService;

	public static final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	public static boolean IsHistoryDataInited = false;

	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public void process() {
		try {
			// 1.查询所有建筑
			List<Building> buildingList = FNBuildingService.queryList(new Building());
			if (buildingList != null && buildingList.size() > 0) {
				Project project = FNProjectService.queryProject();
				if (project == null) {
					return;
				}
				for (Building building : buildingList) {
					try {
						this.processBuilding(building);
					} catch (Exception e) {
						e.printStackTrace();
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void processBuilding(Building building) throws Exception {
		String[] energyType = { "Dian", "Shui", "RanQi", "ReShui" };
		Date to = new Date();
		Map<String, Map<String, Double>> buildingDataMap = new HashMap<String, Map<String, Double>>();
		for (String energy : energyType) {
			Date from;
			FnBuildingCostTime buildingCostTime =FNBuildingCostTimeService.queryByBuilding(building.getId(),energy);
			if(buildingCostTime==null){
				from=standard.parse("1990-04-15 00:00:00");
			}else{
				from=buildingCostTime.getTime();
			}
			Date timeFrom = FNTenantDataService.queryTimefromOrder(from, to, EnumTimeType.T2, null, building,
					EnumEnergyMoney.Energy, energy, EMSOrder.Asc);
			List<FnTenantData> list = FNTenantDataService.queryListGteLt(building.getId(), null, EnumTimeType.T2,
					energy, timeFrom, to, null);
			if (list != null && list.size() > 0) {
				for (FnTenantData fnTenantData : list) {
					String timeFromKey = standard.format(fnTenantData.getTimeFrom());
					String energyMoneyType = fnTenantData.getDataType() + "";
					String key = buildEnergyKey(energy, timeFromKey);
					if (buildingDataMap.get(key) == null) {
						Map<String, Double> map = new HashMap<String, Double>();
						map.put(energyMoneyType, fnTenantData.getData());
						buildingDataMap.put(key, map);
					} else {
						if (buildingDataMap.get(key).get(energyMoneyType) == null) {
							buildingDataMap.get(key).put(energyMoneyType, fnTenantData.getData());
						} else {
							double sum = add(buildingDataMap.get(key).get(energyMoneyType), fnTenantData.getData());
							buildingDataMap.get(key).put(energyMoneyType, sum);
						}
					}

				}
			}
			if(buildingCostTime!=null){
				FnBuildingCostTime update = new FnBuildingCostTime();
				update.setTime(new Date());
				FNBuildingCostTimeService.update(buildingCostTime, update);
			}else{
				FnBuildingCostTime save = new FnBuildingCostTime(); 
				save.setBuildingId(building.getId());
				save.setEnergyTypeId(energy);
				save.setTime(new Date());
			}
		}

		if (buildingDataMap.size() > 0) {
			List<FnBuildingEnergyCost> saveList = new ArrayList<FnBuildingEnergyCost>();
			for (Entry<String, Map<String, Double>> entry : buildingDataMap.entrySet()) {
				String[] split = entry.getKey().split("_");
				String energyTypeId = split[0];
				String timeFrom = split[1];
				Map<String, Double> dataMap = entry.getValue();
				if (dataMap != null) {
					FnBuildingEnergyCost save = new FnBuildingEnergyCost();
					save.setBuildingId(building.getId());
					save.setEnergyTypeId(energyTypeId);
					save.setTimeFrom(standard.parse(timeFrom));
					for (Entry<String, Double> dataEntry : dataMap.entrySet()) {
						if (dataEntry.getKey().equals("0")) {// 量
							save.setAmount(dataEntry.getValue());
						} else {
							save.setMoney(dataEntry.getValue());
						}
					}
					Map<String, Double> prePayMap = new HashMap<String, Double>();
					List<FnRecordPrePay> list = FNRecordPrePayService.queryByBuildingId(building.getId(), energyTypeId,
							standard.parse(timeFrom), DateUtils.addDays(standard.parse(timeFrom), 1));
					List<FnRecordReturn> returnList = FNRecordReturnService.queryByBuildingId(building.getId(),
							energyTypeId, standard.parse(timeFrom), DateUtils.addDays(standard.parse(timeFrom), 1));
					if (list != null && list.size() > 0) {// 处理充值
						for (FnRecordPrePay fnRecordPrePay : list) {
							if (prePayMap.get("amount") == null) {
								prePayMap.put("amount", fnRecordPrePay.getAmount());
							} else {
								prePayMap.put("amount", prePayMap.get("amount")
										+ (fnRecordPrePay.getAmount() == null ? 0.0 : fnRecordPrePay.getAmount()));
							}

							if (prePayMap.get("money") == null) {
								prePayMap.put("money", fnRecordPrePay.getMoney());
							} else {
								prePayMap.put("money", prePayMap.get("money")
										+ (fnRecordPrePay.getMoney() == null ? 0.0 : fnRecordPrePay.getMoney()));
							}
						}
					}
					if (returnList != null && returnList.size() > 0) {// 处理退费
						for (FnRecordReturn fnRecordReturn : returnList) {
							if (prePayMap.get("amount") == null) {
								prePayMap.put("amount",
										0.0 - (fnRecordReturn.getAmount() == null ? 0 : fnRecordReturn.getAmount()));
							} else {
								prePayMap.put("amount", prePayMap.get("amount")
										- (fnRecordReturn.getAmount() == null ? 0.0 : fnRecordReturn.getAmount()));
							}

							if (prePayMap.get("money") == null) {
								prePayMap.put("money",
										0.0 - (fnRecordReturn.getAmount() == null ? 0 : fnRecordReturn.getAmount()));
							} else {
								prePayMap.put("money", prePayMap.get("money")
										- (fnRecordReturn.getMoney() == null ? 0.0 : fnRecordReturn.getMoney()));
							}
						}
					}
					save.setPrePayAmount(prePayMap.get("amount") == null ? 0.0 : prePayMap.get("amount"));
					save.setPrePayMoney(prePayMap.get("money") == null ? 0.0 : prePayMap.get("money"));
					save.setLastUpdateTime(new Date());
					saveList.add(save);
				}
			}
			FNBuildingEnergyCostService.saveList(saveList);
		}

	}

	private String buildEnergyKey(String energyTypeId, String time) {
		StringBuffer buffer = new StringBuffer();
		buffer.append(energyTypeId).append("_").append(time);
		return buffer.toString();
	}

	public double add(double value1, double value2) {
		BigDecimal b1 = new BigDecimal(Double.valueOf(value1).toString());
		BigDecimal b2 = new BigDecimal(Double.valueOf(value2).toString());
		return b1.add(b2).doubleValue();
	}
}
