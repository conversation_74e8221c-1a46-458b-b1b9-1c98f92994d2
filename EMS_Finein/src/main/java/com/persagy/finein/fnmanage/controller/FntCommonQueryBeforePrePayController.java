package com.persagy.finein.fnmanage.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.finein.communication.exception.MeterSetException;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理 接口名：通用充值前查询接口
 *
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntCommonQueryBeforePrePayController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNTenantPrePayParamService")
    private FNTenantPrePayParamService fnTenantPrePayParamService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNTenantPrePayMeterParamService")
    private FNTenantPrePayMeterParamService fnTenantPrePayMeterParamService;

    @RequestMapping("FNTCommonQueryBeforePrePayService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonQueryBeforePrePay(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String tenantId = (String) dto.get("tenantId");
            String energyTypeId = (String) dto.get("energyTypeId");
            String meterId = (String) dto.get("meterId");

            if (buildingId == null || tenantId == null || energyTypeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnTenant queryTenant = fnTenantService.queryTenant(buildingId, tenantId, EnumTenantStatus.ACTIVATED,
                    EnumValidStatus.VALID);
            if (queryTenant == null) {
                throw new Exception("查询信息错误");
            }
            FnTenantPayType tenantPayType = fnTenantPayTypeService.query(tenantId, energyTypeId);
            if (tenantPayType == null) {
                throw new Exception("未找到租户的付费类型");
            }
            Map<String, Object> contentObj = new HashMap<String, Object>();
            contentObj.put("payType", tenantPayType.getPayType());
            if (tenantPayType.getPayType() == EnumPayType.PREPAY.getValue().intValue()) {
                Double money = (Double) ConstantDBBaseData.SysParamValueMap
                        .get(FineinConstant.SysParamValueKey.Id_FNOtherSystemPrePayMaxMoney);
                contentObj.put("minPayData", money);
                if (tenantPayType.getPrePayType().intValue() == EnumPrePayType.ONLINE_METERPAY.getValue().intValue()) {
                    if (meterId == null) {
                        throw new Exception(ExceptionUtil.ParamIsNull("meterId"));
                    }
                    try {
                        this.processMeterPay(contentObj, tenantId, meterId, energyTypeId, tenantPayType);
                    } catch (MeterSetException e) {
                        e.printStackTrace();
                    }
                } else if (tenantPayType.getPrePayType() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
                    this.processTenantPay(contentObj, tenantId, energyTypeId, tenantPayType);
                }
            }
            content.add(contentObj);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTCommonQueryBeforePrePayService");
        }
    }

    private void processTenantPay(Map<String, Object> contentObj, String tenantId, String energyTypeId,
                                  FnTenantPayType tenantPayType) throws Exception {

        Integer billingType = tenantPayType.getPrepayChargeType();
        String billingTypeUnit = UnitUtil.getBillModeUnit(energyTypeId,
                EnumPrepayChargeType.valueOf(tenantPayType.getPrepayChargeType()));

        FnTenantPrePayParam preParam = fnTenantPrePayParamService.queryTenantPreParam(tenantId, energyTypeId);

        if (preParam != null) {
            contentObj.put("remainData", preParam.getRemainData());
            contentObj.put("lastUpdateTime", preParam.getLastUpdateTime());
        } else {
            contentObj.put("remainData", null);
            contentObj.put("lastUpdateTime", new Date());
        }
        contentObj.put("tenantId", tenantId);
        contentObj.put("meterId", null);
        contentObj.put("billingType", billingType);
        contentObj.put("billingTypeUnit", billingTypeUnit);
        contentObj.put("prePayType", tenantPayType.getPayType());
        // contentObj.put("weiDaoZhangCount", count);
    }

    private void processMeterPay(Map<String, Object> contentObj, String tenantId, String meterId, String energyTypeId,
                                 FnTenantPayType tenantPayType) throws Exception, MeterSetException {

        FnMeter fnMeter = fnMeterService.queryMeterById(meterId);
        Integer billingType = tenantPayType.getPrepayChargeType();
        String billingTypeUnit = UnitUtil.getBillModeUnit(energyTypeId,
                EnumPrepayChargeType.valueOf(tenantPayType.getPrepayChargeType()));

        // FnMeter fnMeter = FNMeterService.queryMeterById(meterId);

        contentObj.put("tenantId", tenantId);
        contentObj.put("meterId", meterId);
        contentObj.put("billingType", billingType);
        contentObj.put("billingTypeUnit", billingTypeUnit);
        contentObj.put("prePayType", tenantPayType.getPayType());

        Double remainData = null;
        try {
            ICommunication communication = (ICommunication) SystemConstant.context.getBean(fnMeter.getProtocolId());
            if (communication != null) {
                remainData = communication.queryRemainData(fnMeter);
                if (!"DI_C_07_Y_Q_001".equals(fnMeter.getProtocolId())) {
                    Double overdraft = communication.queryOverdraft(fnMeter);// 透支金额
                    if (overdraft != null && remainData != null) {
                        remainData = remainData - overdraft;
                    }
                }
            } else {
                FnTenantPrePayMeterParam param = fnTenantPrePayMeterParamService.queryMeterPreParam(tenantId, meterId,
                        energyTypeId);
                if (param != null) {
                    remainData = param.getRemainData();

                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (remainData != null) {
            // 剩余量是否乘变比
            Double radio = 1.0;
            try {
                Long syjIsCt = (Long) ((JSONObject) JSONValue.parse(fnMeter.getExtend())).get("syjIsCt");
                if (syjIsCt != null && syjIsCt.intValue() == 1) {
                    radio = fnMeter.getRadio();
                }
            } catch (Exception e) {
            }
            contentObj.put("remainData", remainData * radio);
            contentObj.put("lastUpdateTime", new Date());
        } else {
            contentObj.put("remainData", null);
            contentObj.put("lastUpdateTime", new Date());
        }
    }
}
