package com.persagy.finein.fnmanage.alarm.handler.impl;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.finein.core.util.HttpUtils;
import com.persagy.finein.enumeration.EnumAlarmPushStatus;
import com.persagy.finein.fnmanage.alarm.handler.FNTGlobalAlarmPushHandler;
import com.persagy.finein.service.FNAlarmPushStatusService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月21日 下午1:45:10
 * 
 * 说明:
 */
@Component("FNTGlobalAlarmPushHandler")
public class FNTGlobalAlarmPushHandlerImpl extends CoreServiceImpl implements FNTGlobalAlarmPushHandler {

	@Resource(name = "FNAlarmPushStatusService")
	private FNAlarmPushStatusService FNAlarmPushStatusService;

	private static Logger log = Logger.getLogger(FNTGlobalAlarmPushHandlerImpl.class);

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void addGlobalAlarm(String url, Map param, String alarmId) throws Exception {
		String writeValueAsString = SystemConstant.jsonMapper.writeValueAsString(param);
		String postJson = HttpUtils.postJson(url, writeValueAsString);
		Map result = SystemConstant.jsonMapper.readValue(postJson, Map.class);
		if (!"success".equals((String) result.get("result"))) {
			log.error("【租户管理】租户全局报警推送线程:新增报警状态参数:" + writeValueAsString);
			throw new Exception((String) result.get("ResultMsg"));
		}
		FNAlarmPushStatusService.updateAlarmPushStatus(alarmId, EnumAlarmPushStatus.Completed);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void updateGlobalAlarm(String url, Map param, String alarmId) throws Exception {
		String resultJson = HttpUtils.postJson(url, SystemConstant.jsonMapper.writeValueAsString(param));
		Map resultMap = SystemConstant.jsonMapper.readValue(resultJson, Map.class);
		if (!"success".equals((String) resultMap.get("result"))) {
			log.error("【租户管理】租户全局报警推送线程:新增报警状态参数:" + resultJson);
			throw new Exception((String) resultMap.get("ResultMsg"));
		}
		FNAlarmPushStatusService.updateAlarmPushStatus(alarmId, EnumAlarmPushStatus.Completed);
	}

}
