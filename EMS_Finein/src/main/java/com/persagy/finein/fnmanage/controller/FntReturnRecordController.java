package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnOrderExtend;
import com.persagy.ems.pojo.finein.FnRecordReturn;
import com.persagy.ems.pojo.finein.FnTenantBackPayRecord;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.finein.enumeration.EnumPayBodyType;
import com.persagy.finein.enumeration.EnumPrePayType;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：预付费退费-退费记录-查询
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntReturnRecordController extends BaseController {

    @Resource(name = "FNRecordReturnService")
    private FNRecordReturnService fnRecordReturnService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNOrderExtendService")
    private FNOrderExtendService fnOrderExtendService;

    @Resource(name = "FNTenantBackPayRecordService")
    private FNTenantBackPayRecordService fnTenantBackPayRecordService;

    @RequestMapping("FNTReturnRecordService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult returnRecord(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String)dto.get("tenantId");
            String energyTypeId = (String)dto.get("energyTypeId");
            String timeFrom = (String)dto.get("timeFrom");
            String timeTo = (String)dto.get("timeTo");

            if(tenantId == null || energyTypeId == null
                    || timeFrom == null
                    || timeTo == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            FnTenantPayType tenantPayType = fnTenantPayTypeService.query(tenantId, energyTypeId);
            if(tenantPayType == null){
                throw new Exception("未找到租户的付费类型");
            }
            Date to=standard.parse(timeTo);
            List<FnRecordReturn> recordList;
            String billingTypeUnit = UnitUtil.getBillModeUnit(energyTypeId, EnumPrepayChargeType.valueOf(tenantPayType.getPrepayChargeType()));
            Map<String,Object> contentObj = new HashMap<String,Object>();
            contentObj.put("billingType", tenantPayType.getPrepayChargeType());
            contentObj.put("prePayType", tenantPayType.getPrePayType());
            contentObj.put("billingTypeUnit", billingTypeUnit);
            List<Map<String,Object>> dataList = new ArrayList<Map<String,Object>>();
            contentObj.put("dataList", dataList);
            if(tenantPayType.getPrePayType()== EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()){//只查询已经处理完的记录
                //查询租户仪表上次计算时间
                FnTenantBackPayRecord query = new FnTenantBackPayRecord();
                query.setTenantId(tenantId);
                query.setIsProcessed(EnumYesNo.YES.getValue().intValue());
                query.setEnergyTypeId(energyTypeId);
                query.setSort("lastUpdateTime", EMSOrder.Desc);
                query.setLimit((long) 1);
                List<FnTenantBackPayRecord> lastRecordList = fnTenantBackPayRecordService.query(query);
                if(lastRecordList!=null&&lastRecordList.size()>0){
                    Date time = lastRecordList.get(0).getLastUpdateTime();
                    if(time.getTime()>=standard.parse(timeTo).getTime()){
                        to=standard.parse(timeTo);
                    }else{
                        to=time;
                    }

                }else{//没计算
                    content.add(contentObj);
                    return Result.SUCCESS(content);
                }
            }
            recordList = fnRecordReturnService.queryTenantReturnRecord(tenantId, energyTypeId, standard.parse(timeFrom), to);
            if(recordList != null){
                for(FnRecordReturn record : recordList){
                    Map<String,Object> map = new HashMap<String,Object>();
                    Integer type = record.getType();
                    if(EnumPayBodyType.METER.getValue().intValue()==type){
                        map.put("meterId", record.getCode());
                    }
                    map.put("operateTime", record.getOperateTime());
                    map.put("userName", record.getUserName());
                    map.put("orderId", record.getOrderId());
                    if(tenantPayType.getPrepayChargeType().intValue() == EnumPrepayChargeType.Liang.getValue()){
                        map.put("money", DoubleFormatUtil.Instance().getDoubleData_00(record.getAmount()));
                    }else{
                        map.put("money", DoubleFormatUtil.Instance().getDoubleData_00(record.getMoney()));
                    }
                    FnOrderExtend order = fnOrderExtendService.queryById(record.getOrderId());
                    if(order!=null){
                        map.put("remainData", DoubleFormatUtil.Instance().getDoubleData_00(order.getRemainData()));
                    }else{
                        map.put("remainData", "--");
                    }
                    dataList.add(map);
                }
            }
            content.add(contentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTReturnRecordService");
        }
    }


}
