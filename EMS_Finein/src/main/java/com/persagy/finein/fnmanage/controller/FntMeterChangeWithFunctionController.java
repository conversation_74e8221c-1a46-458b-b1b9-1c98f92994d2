package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnRecordMeterChange;
import com.persagy.ems.pojo.finein.FnRecordMeterChangeExtend;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantMeterCompute;
import com.persagy.finein.enumeration.EnumTenantStatus;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：租户管理-换表带功能号
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
@Transactional(propagation= Propagation.REQUIRED,rollbackFor=Exception.class)
public class FntMeterChangeWithFunctionController extends BaseController {

    @Resource(name = "FNRecordMeterChangeService")
    private FNRecordMeterChangeService fnRecordMeterChangeService;

    @Resource(name = "FNRecordMeterChangeExtendService")
    private FNRecordMeterChangeExtendService fnRecordMeterChangeExtendService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantMeterComputeService")
    private FNTenantMeterComputeService fnTenantMeterComputeService;

    @Resource(name = "FNTenantMeterComputeTempService")
    private FNTenantMeterComputeTempService fnTenantMeterComputeTempService;


    @RequestMapping("FNTMeterChangeWithFunctionService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
    public InterfaceResult meterChangeWithFunction(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String meterId = (String) dto.get("meterId");
            String energyTypeId = (String) dto.get("energyTypeId");
            String changeTime = (String) dto.get("changeTime");
            List<Map<String,Object>> functionList = (List<Map<String,Object>>) dto.get("functionList");
            String userId = this.getParamUserId(dto);

            if (meterId == null || energyTypeId == null || changeTime == null || userId == null || functionList == null || functionList.size() == 0) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            Map<String,Object> contentObj = new HashMap<>();
            content.add(contentObj);
            Date change = standard.parse(changeTime);
            if(change.getTime() > new Date().getTime()){
                contentObj.put("result", 2);
                return Result.SUCCESS(content);
            }

            Date lastChangeTime = fnRecordMeterChangeService.queryLastChangeTime(meterId, energyTypeId);
            if(lastChangeTime != null && lastChangeTime.getTime() >= change.getTime()){
                contentObj.put("result", 1);
                return Result.SUCCESS(content);
            }

            FnRecordMeterChange saveObj = new FnRecordMeterChange();
            saveObj.setId(UUID.randomUUID().toString());
            saveObj.setMeterId(meterId);
            saveObj.setChangeTime(standard.parse(changeTime));
            saveObj.setCreateTime(new Date());
            saveObj.setEnergyTypeId(energyTypeId);
            saveObj.setUserId(userId);
            DTOUser dtoUser = fnUserService.queryUserByUserId(userId);
            if(dtoUser != null){
                saveObj.setUserName(dtoUser.getUserName());
            }

            List<FnRecordMeterChangeExtend> extendList = new ArrayList<>();
            for(Map<String,Object> map : functionList){
                Integer functionId = (Integer)map.get("functionId");
                Double data = DoubleFormatUtil.Instance().getDoubleData(map.get("data"));
                if(functionId == null){
                    throw new Exception("功能号不能为空");
                }
                if(data == null){
                    throw new Exception("数据不能为空");
                }

                FnRecordMeterChangeExtend extendSaveObj = new FnRecordMeterChangeExtend();
                extendSaveObj.setId(UUID.randomUUID().toString());
                extendSaveObj.setMeterId(meterId);
                extendSaveObj.setFunctionId(functionId);
                extendSaveObj.setChangeTime(standard.parse(changeTime));
                extendSaveObj.setData(data);
                extendSaveObj.setCreateTime(new Date());
                extendSaveObj.setEnergyTypeId(energyTypeId);
                extendSaveObj.setUserId(userId);
                if(dtoUser != null){
                    extendSaveObj.setUserName(dtoUser.getUserName());
                }
                extendList.add(extendSaveObj);
            }
            fnRecordMeterChangeService.save(saveObj);
            fnRecordMeterChangeExtendService.save(extendList);

            Date lastComputeTime = DateUtils.add(DateUtils.truncate(standard.parse(changeTime), Calendar.DATE), Calendar.DATE, -1);
            Date lastComputeTime_1 = DateUtils.add(lastComputeTime, Calendar.DATE, -1);
            for(Map<String,Object> map : functionList){
                Integer functionId = (Integer)map.get("functionId");
                //查找仪表功能号对应的租户，如果租户为激活中的状态，则需要修改计算时间
                List<FnTenantMeterCompute> meterFunctionComuputeList = fnTenantMeterComputeService.queryComputeTime(meterId, functionId);
                if(meterFunctionComuputeList != null && meterFunctionComuputeList.size() > 0){
                    for(FnTenantMeterCompute meterCompute : meterFunctionComuputeList){
                        if(meterCompute.getLastComputeTime().getTime() > lastComputeTime_1.getTime()){
                            FnTenant tenant = fnTenantService.queryOne(meterCompute.getTenantId());
                            if(tenant != null && tenant.getStatus().intValue() == EnumTenantStatus.ACTIVATED.getValue()){
                                //插入temp 表
                                fnTenantMeterComputeTempService.saveComputeTime(tenant.getBuildingId(), tenant.getId(), meterId, functionId, lastComputeTime_1);
                            }
                        }
                    }
                }
            }
            contentObj.put("result", 0);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTMeterChangeWithFunctionService");
        }
    }


}
