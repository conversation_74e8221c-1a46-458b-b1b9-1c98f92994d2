package com.persagy.finein.fnmanage.alarm.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.util.AlarmTypeUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.alarm.handler.FNTAlarmProcessFuZaiLvGaoHandler;
import com.persagy.finein.service.*;
import org.apache.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月21日 下午1:45:10
 * 
 * 说明:负荷率过高
 */
@Component("FNTAlarmProcessFuZaiLvGaoHandler")
public class FNTAlarmProcessFuZaiLvGaoHandlerImpl extends CoreServiceImpl implements FNTAlarmProcessFuZaiLvGaoHandler {

	@Resource(name = "FNAlarmService")
	private FNAlarmService FNAlarmService;
	
	@Resource(name = "FNAlarmPushStatusService")
	private FNAlarmPushStatusService FNAlarmPushStatusService;

	@Resource(name = "FNAlarmLimitCustomSettingService")
	private FNAlarmLimitCustomSettingService FNAlarmLimitCustomSettingService;

	@Resource(name = "FNAlarmLimitCustomObjService")
	private FNAlarmLimitCustomObjService FNAlarmLimitCustomObjService;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNOriginalDataService")
	private FNOriginalDataService FNOriginalDataService;

	@Resource(name = "FNProjectService")
	private FNProjectService FNProjectService;

	@Resource(name = "FNTenantMeterDataService")
	private FNTenantMeterDataService FNTenantMeterDataService;

	private static long Constant_Last_Data_Tolerate_Second = 1200;

	private static Logger log = Logger.getLogger(FNTAlarmProcessFuZaiLvGaoHandlerImpl.class);

	public void handle(Building building, FnTenant tenant, String energyTypeId, String alarmTypeId,
			FnTenantPayType tenantPayType, Map<String, FnAlarmLimitGlobal> globalAlarmLimitMap) throws Exception {
		// if("ZHBH_1013".equals(tenant.getId())){
		// System.out.println("---");
		// }
		try {
			Long AlarmGongLvGaoLastDataTolerateSecond = (Long) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_AlarmGongLvGaoLastDataTolerateSecond);
			if (AlarmGongLvGaoLastDataTolerateSecond != null) {
				Constant_Last_Data_Tolerate_Second = AlarmGongLvGaoLastDataTolerateSecond;
			}
		} catch (Exception e1) {
		}

		// 查询当前租户的报警门限
		Double limitValue = null;
		FnAlarmLimitCustomSetting alarmLimitCustomSetting = FNAlarmLimitCustomSettingService.query(building.getId(),
				tenant.getId());
		if (alarmLimitCustomSetting == null
				|| alarmLimitCustomSetting.getIsFollowGlobal().intValue() == EnumYesNo.YES.getValue().intValue()) {// 跟随全局报警
			FnAlarmLimitGlobal global = globalAlarmLimitMap.get(alarmTypeId);
			if (global == null || global.getIsOpen().intValue() == EnumYesNo.NO.getValue().intValue()) {
				FNAlarmService.updateAlarmStatus(tenant.getId(), alarmTypeId, EnumAlarmStatus.YiGuoQi, null);
			} else {
				limitValue = global.getLimitValue();
			}
		} else {
			FnAlarmLimitCustomObj customObj = FNAlarmLimitCustomObjService.queryList(building.getId(), tenant.getId(),
					alarmTypeId);
			if (customObj == null || customObj.getIsOpen().intValue() == EnumYesNo.NO.getValue().intValue()) {// 将历史未恢复报警转为过期
				FNAlarmService.updateAlarmStatus(tenant.getId(), alarmTypeId, EnumAlarmStatus.YiGuoQi, null);
			} else {// 检查租户是否报警
				limitValue = customObj.getLimitValue();
			}
		}
		if (limitValue == null) {
			FNAlarmService.updateAlarmStatus(tenant.getId(), alarmTypeId, EnumAlarmStatus.YiGuoQi, null);
			return;
		}

		// 查询租户下的所有电表
		List<DTOMeter> meterList = FNMeterService.queryMeterList(tenant.getId());
		if (meterList != null) {
			Project project = FNProjectService.queryProject();
			if (project == null) {
				log.error("【租户管理】电功率报警查询项目编号未找到！");
				return;
			}
			Date timeTo = new Date();
			Date timeFrom = new Date(timeTo.getTime() - Constant_Last_Data_Tolerate_Second * 1000);
			for (DTOMeter dtoMeter : meterList) {
				if (!dtoMeter.getEnergyTypeId().equals(FineinConstant.EnergyType.Dian)) {
					continue;
				}
				JSONObject extendObj = null;
				try {
					extendObj = (JSONObject) JSONValue.parse(dtoMeter.getExtend());
				} catch (Exception e2) {
				}
				Double kkValue = null;
				try {
					kkValue = DoubleFormatUtil.Instance().getDoubleData(extendObj.get("kkValue"));
				} catch (Exception e1) {
				}
				if (kkValue == null) {
					continue;
				}

				Integer isSanXiang = 0;
				try {
					isSanXiang = Integer.parseInt(extendObj.get("isSanXiang").toString());
				} catch (Exception e) {
				}
				Double originalData = null;
				// 查询最近的功率数据
				FnTenantMeterData queryLastGteLt = FNTenantMeterDataService.queryLastGteLt(tenant.getBuildingId(),
						tenant.getId(), dtoMeter.getMeterId(), FineinConstant.FuntionTypeId.Cumulant_Dian, energyTypeId,
						EnumTimeType.T0, timeFrom, timeTo, EnumEnergyMoney.Energy);
				if (queryLastGteLt != null) {
					originalData = queryLastGteLt.getData() * 4;
				}
				if (originalData == null) {
					continue;
				}

				/**
				 * 单相电表额定功率kWh=空开*电压/1000 三相电表额定功率kWh=空开*电压*3/1000
				 * 
				 * 负荷率 = 当前功率/额定功率* 100
				 */
				double baseLoad = kkValue.doubleValue() * 220 / 1000.0;
				if (isSanXiang != null && isSanXiang.intValue() == 1) {
					baseLoad *= 3;
				}
				double fuzailv = originalData.doubleValue() / baseLoad * 100;
				try {
					// 查询报警
					FnAlarm alarm = this.FNAlarmService.queryAlarmWeiHuiFu(tenant.getId(), dtoMeter.getMeterId(),
							alarmTypeId);
					if (fuzailv >= limitValue) {// 报警
						if (alarm == null) {// 新增报警
							this.alarm(building, tenant, energyTypeId, alarmTypeId, dtoMeter, limitValue, fuzailv,
									baseLoad, originalData.doubleValue());
						}
					} else {
						if (alarm != null) {
							FNAlarmService.updateAlarmStatus(tenant.getId(), alarmTypeId, EnumAlarmStatus.YiHuiFu,
									dtoMeter.getMeterId());
						}
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}
	}

	public void alarm(Building building, FnTenant tenant, String energyTypeId, String alarmTypeId, DTOMeter meter,
			Double limitValue, Double currentValue, Double baseLoad, Double powerValue) throws Exception {
		FnAlarm saveObj = new FnAlarm();
		String id = UUID.randomUUID().toString();
		saveObj.setId(id);
		saveObj.setBuildingId(building.getId());
		saveObj.setTenantId(tenant.getId());
		saveObj.setAlarmTypeId(alarmTypeId);
		FnAlarmType alarmType = AlarmTypeUtil.queryAlarmTypeById(alarmTypeId);
		saveObj.setParentAlarmTypeId(alarmType.getParentId());
		saveObj.setTreeId(alarmType.getTreeId());
		saveObj.setEnergyTypeId(energyTypeId);
		Date now = new Date();
		saveObj.setAlarmTime(now);
		saveObj.setAlarmPositionType(EnumAlarmPositionType.Meter.getValue());
		saveObj.setAlarmPositionId(meter.getMeterId());
		saveObj.setAlarmPositionName(meter.getMeterName());
		saveObj.setStatus(EnumAlarmStatus.WeiHuiHu.getValue());
		saveObj.setLimitValue(limitValue);
		saveObj.setCurrentValue(currentValue);
		saveObj.setCreateTime(now);
		saveObj.setLastUpdateTime(now);
		saveObj.setUnit(alarmType.getUnit());
		saveObj.setIsRead(EnumYesNo.NO.getValue());
		saveObj.setPushStatus(EnumAlarmPushStatus.WaitPush.getValue());

		JSONObject extendObj = new JSONObject();
		extendObj.put("baseLoad", baseLoad);
		extendObj.put("powerValue", powerValue);

		saveObj.setExtend(extendObj.toString());
		FNAlarmService.save(saveObj);

		FnAlarmPushStatus save = new FnAlarmPushStatus();
		save.setBuildingId(building.getId());
		save.setTenantId(tenant.getId());
		save.setAlarmPositionType(EnumAlarmPositionType.Meter.getValue());
		save.setAlarmPositionId(meter.getMeterId());
		save.setStatus(EnumAlarmStatus.WeiHuiHu.getValue());
		save.setAlarmTypeId(alarmTypeId);
		save.setEnergyTypeId(energyTypeId);
		save.setId(id);
		save.setLastUpdateTime(now);
		save.setPushStatus(EnumAlarmPushStatus.WaitPush.getValue());
		FNAlarmPushStatusService.save(save);
		
	}
}
