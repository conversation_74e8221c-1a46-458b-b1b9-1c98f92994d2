package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.finein.service.FNFloorService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理
 * 接口名：通用-获取楼层列表
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntCommonFloorListController extends BaseController {

    @Resource(name = "FNFloorService")
    private FNFloorService fnFloorService;

    @RequestMapping("FNTCommonFloorListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonFloorList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String)dto.get("buildingId");

            if(buildingId == null){
                throw new Exception(ExceptionUtil.ParamIsNull("buildingId"));
            }

            content.addAll(fnFloorService.queryList(buildingId, EMSOrder.Desc));
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTCommonFloorListService");
        }
    }


}
