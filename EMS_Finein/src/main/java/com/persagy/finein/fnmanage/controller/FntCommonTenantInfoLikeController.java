package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantFlag;
import com.persagy.ems.pojo.finein.FnTenantType;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.service.FNBuildingService;
import com.persagy.finein.service.FNTenantFlagService;
import com.persagy.finein.service.FNTenantService;
import com.persagy.finein.service.FNTenantTypeService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：租户-根据租户全码获取租户信息
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntCommonTenantInfoLikeController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNTenantTypeService")
    private FNTenantTypeService fnTenantTypeService;

    @Resource(name = "FNTenantFlagService")
    private FNTenantFlagService fnTenantFlagService;


    @RequestMapping("FNTCommonTenantInfoLikeService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonTenantInfoLike(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String like = (String) dto.get("like");
            if (like == null || "".equals(like.trim())) {
                return null;
            }
            List<Object> list = new ArrayList<Object>();
            List<FnTenant> tenantList = fnTenantService.queryTenantLike(like.trim().toLowerCase());
            if (tenantList.size() > 0) {
                Map<String, FnTenantFlag> flagMap = fnTenantFlagService.queryFlag();
                Map<String, Building> buildingMap = fnBuildingService.query();
                Map<String, FnTenantType> typeMap = fnTenantTypeService.queryType();
                for (FnTenant fnTenant : tenantList) {
                    FnTenantFlag tenantFlag = flagMap.get(fnTenant.getId());
                    if (tenantFlag == null) {
                        continue;
                    }
                    Building building = buildingMap.get(fnTenant.getBuildingId());
                    if (building == null) {
                        continue;
                    }
                    FnTenantType fnTenantType = typeMap.get(fnTenant.getTenantTypeId());
                    Map<String, Object> obj = new HashMap<String, Object>();
                    obj.put("tenantFlag", tenantFlag.getTenantFlag());
                    obj.put("tenantId", fnTenant.getId());
                    obj.put("tenantName", fnTenant.getName());
                    obj.put("buildingId", fnTenant.getBuildingId());
                    obj.put("buildingName", building.getName());
                    obj.put("tenantTypeId", fnTenant.getTenantTypeId());
                    obj.put("tenantTypeName", fnTenantType == null ? "" : fnTenantType.getName());
                    obj.put("activeTime", fnTenant.getActiveTime());
                    obj.put("area", fnTenant.getArea());
                    list.add(obj);
                }
            }
            Map<String, Object> contentMap = new HashMap<String, Object>();
            contentMap.put("tenantList", list);
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTCommonTenantInfoLikeService");
        }
    }


}
