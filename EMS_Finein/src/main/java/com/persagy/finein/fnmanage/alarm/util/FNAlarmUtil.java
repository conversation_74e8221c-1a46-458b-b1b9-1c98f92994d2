package com.persagy.finein.fnmanage.alarm.util;

import com.persagy.ems.pojo.finein.FnAlarmLimitCustomObj;
import com.persagy.ems.pojo.finein.FnAlarmLimitCustomSetting;
import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNAlarmLimitCustomObjService;
import com.persagy.finein.service.FNAlarmLimitCustomSettingService;
import com.persagy.finein.service.FNAlarmLimitGlobalService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月24日 上午9:33:30

* 说明:
*/
@Component("FNAlarmUtil")
public class FNAlarmUtil {
	
	@Resource(name = "FNAlarmLimitCustomObjService")
	private FNAlarmLimitCustomObjService FNAlarmLimitCustomObjService;
	
	@Resource(name = "FNAlarmLimitCustomSettingService")
	private FNAlarmLimitCustomSettingService FNAlarmLimitCustomSettingService;
	
	@Resource(name = "FNAlarmLimitGlobalService")
	private FNAlarmLimitGlobalService FNAlarmLimitGlobalService;
	
	public Double queryAlarmLimit(String buildingId,String tenantId,String alarmTypeId) throws Exception{
		return queryAlarmLimit(buildingId, tenantId, alarmTypeId,FNAlarmLimitGlobalService.queryMap());
	}

	public Double queryAlarmLimit(String buildingId,String tenantId,String alarmTypeId,Map<String,FnAlarmLimitGlobal> globalAlarmLimitMap) throws Exception{
		//查询当前租户的报警门限
		Double limitValue = null;
		FnAlarmLimitCustomSetting alarmLimitCustomSetting = FNAlarmLimitCustomSettingService.query(buildingId, tenantId);
		if(alarmLimitCustomSetting == null || alarmLimitCustomSetting.getIsFollowGlobal().intValue() == EnumYesNo.YES.getValue().intValue()){//跟随全局报警
			FnAlarmLimitGlobal global = globalAlarmLimitMap.get(alarmTypeId);
			if(global == null || global.getIsOpen().intValue() == EnumYesNo.NO.getValue().intValue()){
				
			}else{
				limitValue = global.getLimitValue();
			}
		}else{
			FnAlarmLimitCustomObj customObj = FNAlarmLimitCustomObjService.queryList(buildingId, tenantId, alarmTypeId);
			if(customObj == null || customObj.getIsOpen().intValue() == EnumYesNo.NO.getValue().intValue()){//将历史未恢复报警转为过期
				
			}else{//检查租户是否报警
				limitValue = customObj.getLimitValue();
			}
		}
		return limitValue;
	}
}

