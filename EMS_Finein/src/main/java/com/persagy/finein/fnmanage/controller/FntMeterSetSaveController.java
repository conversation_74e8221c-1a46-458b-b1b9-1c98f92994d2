package com.persagy.finein.fnmanage.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：仪表设置-保存仪表设置记录
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntMeterSetSaveController extends BaseController {

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @Resource(name = "FNRecordMeterSetService")
    private FNRecordMeterSetService fnRecordMeterSetService;

    @Resource(name = "FNPriceTemplateService")
    private FNPriceTemplateService fnPriceTemplateService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNTenantPriceService")
    private FNTenantPriceService fnTenantPriceService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNRecordPriceChangeService")
    private FNRecordPriceChangeService fnRecordPriceChangeService;


    @RequestMapping("FNTMeterSetSaveService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult meterSetSave(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
//		 "meterId":"1001",//仪表id
//	        "roomCode":"201"
//	        "energyTypeId":"Dian",
//	         "type":0 //0 剩余量清零 1保电  2解除保电   3合闸 4分闸 5透支金额  6更新价格
//	         "value":12.1//透支金额

            String meterId = (String) dto.get("meterId");
            String roomCode = (String) dto.get("roomCode");
            String energyTypeId = (String) dto.get("energyTypeId");
            String tenantId = (String) dto.get("tenantId");
            Integer type = (Integer) dto.get("type");
            Integer setType = (Integer) dto.get("setType");
            String userId = this.getParamUserId(dto);
            if (meterId == null || roomCode == null || energyTypeId == null || userId == null || setType == null || tenantId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            String extend = null;
            DTOUser user = fnUserService.queryUserByUserId(userId);
            Map<String, Object> map = new HashMap<>();
            if (setType == EnumMeterSetType.OVERDRAFT.getValue().intValue()) {//透支金额
                String data =  String.valueOf(dto.get("value"));
                Double newData=Double.parseDouble(data);
                map.put("value", newData);
                extend = SystemConstant.jsonMapper.writeValueAsString(map);
            } else if (setType == EnumMeterSetType.UPDATEPRICE.getValue().intValue()) {//更新价格
                Date now = new Date();
                List<Map<String, Object>> priceList = (List<Map<String, Object>>) dto.get("price");
                if (priceList == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
                List<FnMeter> fnMeters = fnMeterService.queryMeterListByTenantId(tenantId);
                Integer meterTypeFlag = fnMeters.get(0).getMeterType();
                FnPriceTemplate template;
                if (meterTypeFlag == EnumMeterType.Common.getValue().intValue()) { //普通仪表
                    Double price = null;
                    for (Map<String, Object> priceMap : priceList) {
                        if (EnumPriceDetail.L.getValue().equals(priceMap.get("type"))) {
                            price = DoubleFormatUtil.Instance().getDoubleData(priceMap.get("value"));
                        }
                    }
                    if (price == null) {
                        throw new Exception(ExceptionUtil.ParamIsNull());
                    }
                    template = fnPriceTemplateService.queryByContentOne(String.valueOf(price), "Dian");
                    if (template == null) {
                        FnPriceTemplate save = new FnPriceTemplate();
                        String id = UUID.randomUUID().toString();
                        save.setId(id);
                        save.setEnergyTypeId("Dian");
                        save.setName("均时电价 " + price + " 元");
                        save.setContent(String.valueOf(price));
                        save.setCreateUserId(userId);
                        save.setCreateTime(now);
                        save.setType(EnumPriceType.AVG.getValue());
                        save.setIsValid(1);
                        save.setLastUpdateTime(now);
                        save.setLastUpdateUserId(userId);
                        fnPriceTemplateService.save(save);
                        template = fnPriceTemplateService.query(id);
                    }
                } else {
                    Double jPrice = null;
                    Double fPrice = null;
                    Double gPrice = null;
                    Double pPrice = null;
                    JSONArray array = new JSONArray();
                    //防止数组越界
                    array.add(1);
                    array.add(1);
                    array.add(1);
                    array.add(1);
                    for (Map<String, Object> touMap : priceList) {
                        JSONObject jsonObject = new JSONObject();
                        switch ((String) touMap.get("type")) {
                            case "J":
                                jPrice = DoubleFormatUtil.Instance().getDoubleData(touMap.get("value"));
                                jsonObject.put("type", touMap.get("type"));
                                jsonObject.put("value", jPrice);
                                array.remove(0);
                                array.add(0, jsonObject);
                                break;
                            case "F":
                                fPrice = DoubleFormatUtil.Instance().getDoubleData(touMap.get("value"));
                                jsonObject.put("type", touMap.get("type"));
                                jsonObject.put("value", fPrice);
                                array.remove(1);
                                array.add(1, jsonObject);
                                break;
                            case "G":
                                gPrice = DoubleFormatUtil.Instance().getDoubleData(touMap.get("value"));
                                jsonObject.put("type", touMap.get("type"));
                                jsonObject.put("value", gPrice);
                                array.remove(2);
                                array.add(2, jsonObject);
                                break;
                            case "P":
                                pPrice = DoubleFormatUtil.Instance().getDoubleData(touMap.get("value"));
                                jsonObject.put("type", touMap.get("type"));
                                jsonObject.put("value", pPrice);
                                array.remove(3);
                                array.add(3, jsonObject);
                                break;
                            default:
                                break;
                        }
                    }
                    if (jPrice == null || fPrice == null || gPrice == null || pPrice == null) {
                        throw new Exception(ExceptionUtil.ParamIsNull());
                    }
                    template = fnPriceTemplateService.queryByContentOne(array.toJSONString(), "Dian");
                    if (template == null) {
                        FnPriceTemplate save = new FnPriceTemplate();
                        String id = UUID.randomUUID().toString();
                        save.setId(id);
                        save.setEnergyTypeId("Dian");
                        save.setName("分时电价尖峰" + jPrice + "元;高峰" + fPrice + "元;平段" + pPrice + "元;低谷;" + gPrice + "元");
                        save.setContent(array.toJSONString());
                        save.setCreateUserId(userId);
                        save.setCreateTime(now);
                        save.setType(EnumPriceType.TOU.getValue());
                        save.setIsValid(1);
                        save.setLastUpdateTime(now);
                        save.setLastUpdateUserId(userId);
                        fnPriceTemplateService.save(save);
                        template = fnPriceTemplateService.query(id);
                    }
                }

                //价格方案变更记录
                //修改前价格模板
                FnTenant tenant = fnTenantService.queryOne(tenantId);
                FnPriceTemplate beforePriceTemplate = fnTenantPriceService.query(tenantId, template.getEnergyTypeId());
                Map<String, Building> buildingMap = fnBuildingService.query();
                FnRecordPriceChange recordPriceChange = new FnRecordPriceChange();
                recordPriceChange.setId(UUID.randomUUID().toString());
                recordPriceChange.setBuildingId(tenant.getBuildingId());
                recordPriceChange.setBuildingName(buildingMap.get(tenant.getBuildingId()) == null ? "" : buildingMap.get(tenant.getBuildingId()).getName());
                recordPriceChange.setTenantId(tenant.getId());
                recordPriceChange.setEnergyTypeId(template.getEnergyTypeId());
                recordPriceChange.setTenantName(tenant.getName());
                recordPriceChange.setChangeTime(now);
                recordPriceChange.setBeforePriceId(beforePriceTemplate.getId());
                recordPriceChange.setBeforePriceName(beforePriceTemplate.getName());
                recordPriceChange.setAfterPriceId(template.getId());
                recordPriceChange.setAfterPriceName(template.getName());
                recordPriceChange.setUserId(user.getUserId());
                recordPriceChange.setUserName(user.getUserName());
                recordPriceChange.setRoomIds(tenant.getRoomCodes());
                recordPriceChange.setCreateTime(now);
                fnRecordPriceChangeService.save(recordPriceChange);

                //更新租户价格
                FnTenantPrice query = new FnTenantPrice();
                query.setEnergyTypeId(template.getEnergyTypeId());
                query.setTenantId(tenantId);
                FnTenantPrice update = new FnTenantPrice();
                update.setPriceTemplateId(template.getId());
                update.setLastUpdateTime(now);
                update.setLastUpdateUserId(userId);
                fnTenantPriceService.update(query, update);

                map.put("value", template.getName());
                extend = SystemConstant.jsonMapper.writeValueAsString(map);
            } else if (setType == EnumMeterSetType.GATE.getValue().intValue()) {//闸
                map.put("value", type == EnumYesNo.NO.getValue().intValue() ? EnumGateStatus.Fen.getView() : EnumGateStatus.He.getView());
                extend = SystemConstant.jsonMapper.writeValueAsString(map);
            } else if (setType == EnumMeterSetType.PAULELE.getValue().intValue()) {//保电
                map.put("value", type == EnumYesNo.NO.getValue().intValue() ? EnumPaulEleStatus.UnPaulEle.getView() : EnumPaulEleStatus.PaulEle.getView());
                extend = SystemConstant.jsonMapper.writeValueAsString(map);
            } else if (setType == EnumMeterSetType.REMAINCLEAR.getValue().intValue()) {//剩余量
                map.put("value", EnumMeterSetType.REMAINCLEAR.getView());
                extend = SystemConstant.jsonMapper.writeValueAsString(map);
            }

            FnRecordMeterSet save = new FnRecordMeterSet();
            save.setId(UUID.randomUUID().toString());
            save.setEnergyTypeId(energyTypeId);
            save.setMeterId(meterId);
            save.setTenantId(tenantId);
            save.setOperateType(setType);
            save.setCreateTime(new Date());
            save.setRoomCode(roomCode);
            save.setUserId(userId);
            save.setUserName(user.getUserName());
            save.setExtend(extend);
            fnRecordMeterSetService.save(save);
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("result", 0);
            content.add(resultMap);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTMeterSetSaveService");
        }
    }


}
