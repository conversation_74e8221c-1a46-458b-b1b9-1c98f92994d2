package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：预付费充值-充值记录-查询
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntPrePayRecordController extends BaseController {

    @Resource(name = "FNRecordPrePayService")
    private FNRecordPrePayService fnRecordPrePayService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNOrderExtendService")
    private FNOrderExtendService fnOrderExtendService;

    @Resource(name = "FNTenantBackPayRecordService")
    private FNTenantBackPayRecordService fnTenantBackPayRecordService;

    @Resource(name = "FNRecordPayChannelService")
    private FNRecordPayChannelService fnRecordPayChannelService;

    @RequestMapping("FNTPrePayRecordService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult prePayRecord(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");
            String energyTypeId = (String) dto.get("energyTypeId");
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");

            if (tenantId == null || energyTypeId == null
                    || timeFrom == null
                    || timeTo == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            FnTenantPayType tenantPayType = fnTenantPayTypeService.query(tenantId, energyTypeId);
            if (tenantPayType == null) {
                throw new Exception("未找到租户的付费类型");
            }
            Date to = standard.parse(timeTo);
            List<FnRecordPrePay> recordList;
            String billingTypeUnit = UnitUtil.getBillModeUnit(energyTypeId, EnumPrepayChargeType.valueOf(tenantPayType.getPrepayChargeType()));
            Map<String, Object> contentObj = new HashMap<String, Object>();
            contentObj.put("billingType", tenantPayType.getPrepayChargeType());
            contentObj.put("prePayType", tenantPayType.getPrePayType());
            contentObj.put("billingTypeUnit", billingTypeUnit);
            List<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();
            contentObj.put("dataList", dataList);
            if (tenantPayType.getPrePayType() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
                //查询租户仪表上次计算时间
                FnTenantBackPayRecord query = new FnTenantBackPayRecord();
                query.setTenantId(tenantId);
                query.setIsProcessed(EnumYesNo.YES.getValue().intValue());
                query.setEnergyTypeId(energyTypeId);
                query.setSort("lastUpdateTime", EMSOrder.Desc);
                query.setLimit((long) 1);
                List<FnTenantBackPayRecord> lastRecordList = fnTenantBackPayRecordService.query(query);
                if (lastRecordList != null && lastRecordList.size() > 0) {
                    Date time = lastRecordList.get(0).getLastUpdateTime();
                    if (time.getTime() >= standard.parse(timeTo).getTime()) {
                        to = standard.parse(timeTo);
                    } else {
                        to = time;
                    }

                } else {//没计算
                    content.add(contentObj);
                    return Result.SUCCESS(content);
                }
            }
            recordList = fnRecordPrePayService.queryTenantPrePayRecord(tenantId, energyTypeId, standard.parse(timeFrom), to);
            List<String> perPayIds = new ArrayList<>();
            for (FnRecordPrePay pays : recordList) {
                perPayIds.add(pays.getId());
            }
            List<FnRecordPayChannel> fnRecordPayChannels = new ArrayList<>();
            if (recordList.size() > 0) {
                FnRecordPayChannel query = new FnRecordPayChannel();
                query.setSpecialOperation("recordPayID", SpecialOperator.$in, perPayIds);
                fnRecordPayChannels = fnRecordPayChannelService.query(query);
            }
            Map<String, Object> RecordPayChannelsMap = new HashMap<>();
            for (FnRecordPayChannel channel : fnRecordPayChannels) {
                RecordPayChannelsMap.put(channel.getRecordPayID(), channel.getChannelType());
            }
            if (recordList.size() > 0) {
                for (FnRecordPrePay record : recordList) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    Integer type = record.getType();
                    if (EnumPayBodyType.METER.getValue().intValue() == type) {
                        map.put("meterId", record.getCode());
                    }
                    map.put("operateTime", record.getOperateTime());
                    map.put("userName", record.getUserName());
                    map.put("orderId", record.getOrderId());
                    if (tenantPayType.getPrepayChargeType().intValue() == EnumPrepayChargeType.Liang.getValue()) {
                        map.put("money", DoubleFormatUtil.Instance().getDoubleData_00(record.getAmount()));
                    } else {
                        map.put("money", DoubleFormatUtil.Instance().getDoubleData_00(record.getMoney()));
                    }
                    FnOrderExtend order = fnOrderExtendService.queryById(record.getOrderId());
                    if (order != null) {
                        map.put("remainData", DoubleFormatUtil.Instance().getDoubleData_00(order.getRemainData()));
                    } else {
                        map.put("remainData", "--");
                    }
                    String s = "";
                    if (RecordPayChannelsMap.get(record.getId()) != null && RecordPayChannelsMap.get(record.getId()).equals(EnumPayChannelType.WECHATNORMALPAY.getValue())) {
                        s = EnumPayChannelType.WECHATNORMALPAY.getView();
                    } else if (RecordPayChannelsMap.get(record.getId()) != null && RecordPayChannelsMap.get(record.getId()).equals(EnumPayChannelType.ERRORBILLFILL.getValue())) {
                        s = EnumPayChannelType.ERRORBILLFILL.getView();
                    }
                    map.put("channelType", s);
                    dataList.add(map);
                }
            }


            content.add(contentObj);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTPrePayRecordService");
        }
    }


}
