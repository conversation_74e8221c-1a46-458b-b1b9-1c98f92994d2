package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantData;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.core.util.PathUtil;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumTenantStatus;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNFileResourceService;
import com.persagy.finein.service.FNTenantDataService;
import com.persagy.finein.service.FNTenantService;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：批量操作-能耗费用报表（查询+下载）
 * */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes", "deprecation" })
public class FntBatchEnergyMoneyGridController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantDataService")
    private FNTenantDataService fnTenantDataService;

    @Resource(name = "PathUtil")
    private PathUtil pathUtil;

    @Resource(name = "FNFileResourceService")
    private FNFileResourceService fnFileResourceService;

    @RequestMapping("FNTBatchEnergyMoneyGridService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchEnergyMoneyGrid(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String)dto.get("energyTypeId");
            String timeFrom = (String)dto.get("timeFrom");
            String timeTo = (String)dto.get("timeTo");
            Integer isDownload = (Integer)dto.get("isDownload");
            Integer timeType = (Integer)dto.get("timeType");


            if(dto.get("tenantList") == null || energyTypeId == null
                    || timeFrom == null
                    || timeTo == null
                    || isDownload == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            List<String> tenantList = (List<String>)dto.get("tenantList");


            String energyUnit = UnitUtil.getCumulantUnit(energyTypeId);
            Date from = standard.parse(timeFrom);
            Date to = standard.parse(timeTo);
            Map<String,Double> energyMap = new LinkedHashMap<>();
            Map<String,Double> moneyMap = new LinkedHashMap<>();

            Map<String,Double> tempMap = new LinkedHashMap<>();

            Calendar calendar = Calendar.getInstance();
            calendar.setTime(from);
            while(calendar.getTime().getTime() < to.getTime()){
                String time = standard.format(calendar.getTime());
                energyMap.put(time, null);
                moneyMap.put(time, null);
                tempMap.put(time, null);
                calendar.add(Calendar.MONTH, 1);
            }

            List<FnTenant> fnTenantList = fnTenantService.queryListByIds(tenantList);
            List<Object> tenantObjList = new ArrayList<>();
            if(fnTenantList != null){
                for(FnTenant tenant : fnTenantList){
                    Map<String,Object> tenantMap = new HashMap<>();
                    tenantMap.put("tenantId", tenant.getId());
                    tenantMap.put("tenantName", tenant.getName());
                    tenantObjList.add(tenantMap);
                    Map<String,Double> newEnergyMap = new LinkedHashMap<>(tempMap);
                    Map<String,Double> newMoneyMap = new LinkedHashMap<>(tempMap);
                    Date newTimeFrom = from;
                    Date newTimeTo = to;
                    if(tenant.getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue()){
                        if(tenant.getLeaveTime() != null){
                            newTimeTo = new Date(tenant.getLeaveTime().getTime());
                        }
                    }
                    {
                        List<FnTenantData> tenantSumList = fnTenantDataService.queryListGteLt(tenant.getBuildingId(), tenant.getId(), EnumTimeType.T2, energyTypeId, newTimeFrom, newTimeTo, EnumEnergyMoney.Energy);
                        if(tenantSumList != null){
                            for(FnTenantData tenantSum : tenantSumList){
                                if(tenantSum.getData() != null){
                                    if(tenantSum.getTimeFrom().getTime() < tenant.getActiveTime().getTime()){//小于激活时间，不统计
                                        continue;
                                    }
                                    String month = standard.format(tenantSum.getTimeFrom()).substring(0, 7)+"-01 00:00:00";
                                    if(newEnergyMap.get(month) == null){
                                        newEnergyMap.put(month, tenantSum.getData());
                                    }else{
                                        newEnergyMap.put(month, newEnergyMap.get(month)+tenantSum.getData());
                                    }

                                    if(energyMap.get(month) == null){
                                        energyMap.put(month, tenantSum.getData());
                                    }else{
                                        energyMap.put(month, energyMap.get(month)+tenantSum.getData());
                                    }
                                }
                            }
                        }
                    }
                    {
                        List<FnTenantData> tenantSumList = fnTenantDataService.queryListGteLt(tenant.getBuildingId(), tenant.getId(), EnumTimeType.T2, energyTypeId, newTimeFrom, newTimeTo, EnumEnergyMoney.Money);
                        if(tenantSumList != null){
                            for(FnTenantData tenantSum : tenantSumList){
                                if(tenantSum.getData() != null){
                                    if(tenantSum.getTimeFrom().getTime() < tenant.getActiveTime().getTime()){//小于激活时间，不统计
                                        continue;
                                    }
                                    String month = standard.format(tenantSum.getTimeFrom()).substring(0, 7)+"-01 00:00:00";
                                    if(newMoneyMap.get(month) == null){
                                        newMoneyMap.put(month, tenantSum.getData());
                                    }else{
                                        newMoneyMap.put(month, newMoneyMap.get(month)+tenantSum.getData());
                                    }

                                    if(moneyMap.get(month) == null){
                                        moneyMap.put(month, tenantSum.getData());
                                    }else{
                                        moneyMap.put(month, moneyMap.get(month)+tenantSum.getData());
                                    }
                                }
                            }
                        }
                    }

                    List<Object> dataList = new ArrayList<>();
                    tenantMap.put("dataList", dataList);
                    for(Map.Entry<String, Double> entry : newEnergyMap.entrySet()){
                        Map<String,Object> map = new HashMap<>();
                        map.put("time", entry.getKey());
                        map.put("energy", entry.getValue());
                        map.put("money", newMoneyMap.get(entry.getKey()));
                        dataList.add(map);
                    }
                }
            }

            List<Object> timeList = new ArrayList<>();
            for(Map.Entry<String, Double> entry : energyMap.entrySet()){
                Map<String,Object> map = new HashMap<>();
                map.put("time", entry.getKey());
                map.put("energy", entry.getValue());
                map.put("money", moneyMap.get(entry.getKey()));
                timeList.add(map);
            }

            Map<String,Object> contentObj = new HashMap<>();
            contentObj.put("energyUnit", energyUnit);
            contentObj.put("timeList", timeList);
            contentObj.put("tenantList", tenantObjList);

            if(isDownload.intValue() == EnumYesNo.NO.getValue().intValue()){
                content.add(contentObj);
            }else{
                String subdirectory = pathUtil.getTempDownloadSubDir();

                String resouceId = UUID.randomUUID().toString();
                FileResource resource = new FileResource();
                resource.setId(resouceId);
                Date toTime = DateUtils.addDays(to, -1);
                timeTo=standard.format(toTime);
                if(timeType==EnumTimeType.T4.getValue().intValue()){
                    timeFrom = timeFrom.substring(0, 7);
                    timeTo =timeTo.substring(0, 7);
                }else if(timeType==EnumTimeType.T5.getValue().intValue()){
                    timeFrom = timeFrom.substring(0, 4);
                    timeTo =timeTo.substring(0, 4);
                }else{
                    timeFrom = timeFrom.substring(0, 10);
                    timeTo =timeTo.substring(0, 10);
                }
                StringBuffer fileNameSb = new StringBuffer();
                fileNameSb.append("能耗费用报表-").append(EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId)).append("-").append(timeFrom).append("~").append(timeTo);

                resource.setName(fileNameSb.toString());
                resource.setSuffix("xls");
                resource.setSubdirectory(subdirectory);

                String path = pathUtil.getPath();

                String fileDir = Paths.get(path, subdirectory).toString();
                File file = new File(fileDir);
                if(!file.exists()){
                    file.mkdirs();
                }
                this.buildExcel(path,subdirectory, resouceId, contentObj);

                fnFileResourceService.save(resource);
                Map<String,Object> newContentObj = new HashMap<>();
                newContentObj.put("id", resouceId);
                content.add(newContentObj);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTBatchEnergyMoneyGridService");
        }
    }

    @SuppressWarnings({  "unchecked" })
    private void buildExcel(String path,String subPath,String fileName,Map<String,Object> contentObj){
        //能耗费用报表-电-2017.02~2017.09
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");

        HSSFCellStyle style = wb.createCellStyle();
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);

        sheet.addMergedRegion(new CellRangeAddress(0,1,0,0));//一行-二行 第一列
        sheet.addMergedRegion(new CellRangeAddress(0,1,1,1));//一行-二行 第二列
        sheet.addMergedRegion(new CellRangeAddress(2,2,0,1));//合计

        String energyUnit = (String)contentObj.get("energyUnit");

        List<Map<String,Object>> timeList = (List<Map<String,Object>>)contentObj.get("timeList");
        List<Map<String,Object>> tenantList = (List<Map<String,Object>>)contentObj.get("tenantList");

        //创建标题
        HSSFRow row0 = sheet.createRow((short) 0);
        HSSFRow row1 = sheet.createRow((short) 1);
        HSSFRow row2 = sheet.createRow((short) 2);
        {
            HSSFCell cell = row0.createCell((short)0);
            cell.setCellStyle(style);
            cell.setCellValue("租户编号");
        }
        {
            HSSFCell cell = row0.createCell((short)1);
            cell.setCellStyle(style);
            cell.setCellValue("租户名称");
        }
        {
            HSSFCell cell = row2.createCell((short) 0);
            cell.setCellStyle(style);
            cell.setCellValue("合计");
        }
        for(int i=0;i<timeList.size();i++){
            sheet.addMergedRegion(new CellRangeAddress(0,0,2*i+2,2*i+1+2));//
            Map<String,Object> timeMap = (Map<String,Object>)timeList.get(i);
            String time = (String)timeMap.get("time");
            time = time.substring(0, 7).replace("-", ".");
            Double energy = DoubleFormatUtil.Instance().getDoubleData_00(timeMap.get("energy"));
            Double money = DoubleFormatUtil.Instance().getDoubleData_00(timeMap.get("money"));
            {
                HSSFCell cell = row0.createCell((short)(2*i+2));
                cell.setCellStyle(style);
                cell.setCellValue(time);
            }
            {
                HSSFCell cell = row1.createCell((short)(2*i+2));
                cell.setCellStyle(style);
                cell.setCellValue("能耗("+energyUnit+")");
            }
            {
                HSSFCell cell = row1.createCell((short)(2*i)+1+2);
                cell.setCellStyle(style);
                cell.setCellValue("费用(元)");
            }
            {
                HSSFCell cell = row2.createCell((short)(2*i+2));
                cell.setCellStyle(style);
                cell.setCellValue(energy == null ? 0 : energy);
            }
            {
                HSSFCell cell = row2.createCell((short)(2*i)+1+2);
                cell.setCellStyle(style);
                cell.setCellValue(money == null ? 0 : money);
            }
        }
        if(tenantList != null){
            for(int i=0;i<tenantList.size();i++){
                HSSFRow row = sheet.createRow((short) i+3);
                Map<String,Object> tenant = (Map<String,Object>)tenantList.get(i);
                String tenantId = (String)tenant.get("tenantId");
                String tenantName = (String)tenant.get("tenantName");
                List<Object> dataList = (List<Object>)tenant.get("dataList");
                {
                    HSSFCell cell = row.createCell((short)(0));
                    cell.setCellValue(tenantId == null ? " " : tenantId);
                }
                {
                    HSSFCell cell = row.createCell((short)(1));
                    cell.setCellValue(tenantName == null ? " " : tenantName);
                }
                if(dataList != null){
                    for(int j=0;j<dataList.size();j++){
                        Map<String,Object> timeMap = (Map<String,Object>)dataList.get(j);
                        Double energy = DoubleFormatUtil.Instance().getDoubleData_00(timeMap.get("energy"));
                        Double money = DoubleFormatUtil.Instance().getDoubleData_00(timeMap.get("money"));
                        {
                            HSSFCell cell = row.createCell((short)(2*j+2));
                            cell.setCellValue(energy == null ? 0 : energy);
                        }
                        {
                            HSSFCell cell = row.createCell((short)(2*j)+1+2);
                            cell.setCellValue(money == null ? 0 : money);
                        }
                    }
                }
            }
        }

        String newFilePath = Paths.get(path, subPath,fileName).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            if(fout!=null){
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if(wb!=null){
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
    }

}
