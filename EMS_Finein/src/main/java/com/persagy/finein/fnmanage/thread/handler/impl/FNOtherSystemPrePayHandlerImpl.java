package com.persagy.finein.fnmanage.thread.handler.impl;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.finein.communication.exception.MeterSetException;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.thread.handler.FNOtherSystemPrePayHandler;
import com.persagy.finein.service.*;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月22日 下午12:58:25
 * 
 * 说明:远程充值处理
 */
@Component("FNOtherSystemPrePayHandler")
public class FNOtherSystemPrePayHandlerImpl extends CoreServiceImpl implements FNOtherSystemPrePayHandler {

	@Resource(name = "FNRecordPrePayForOtherSystemService")
	private FNRecordPrePayForOtherSystemService FNRecordPrePayForOtherSystemService;

	@Resource(name = "FNRecordForOtherSystemErrorService")
	private FNRecordForOtherSystemErrorService FNRecordForOtherSystemErrorService;

	@Resource(name = "FNRecordPrePayExtendService")
	private FNRecordPrePayExtendService FNRecordPrePayExtendService;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNRecordForOtherSystemExtendService")
	private FNRecordForOtherSystemExtendService FNRecordForOtherSystemExtendService;

	@Resource(name = "FNRecordPrePayService")
	private FNRecordPrePayService FNRecordPrePayService;

	@Resource(name = "FNOrderIdService")
	private FNOrderIdService FNOrderIdService;

	@Resource(name = "FNRecordPrePayErrorOrderService")
	private FNRecordPrePayErrorOrderService FNRecordPrePayErrorOrderService;

	@Resource(name = "FNChargeMessageService")
	private FNChargeMessageService FNChargeMessageService;

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;

	@Resource(name = "FNRecordPayChannelService")
	private FNRecordPayChannelService FNRecordPayChannelService;

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public boolean processRecord(FnRecordPrePayForOtherSystem record, Double minMoney,
			Map<String, FnCommonPrePaySystemParam> systemCodeMap) throws Exception, MeterSetException {
		FnTenant tenant = FNTenantService.queryOne(record.getTenantId());
		if (tenant == null) {
			throw new Exception("租户不存在:" + record.getTenantId());
		}

		String meterId = record.getCode();
		Double value;
		int billingType;
		if (record.getAmount() == null) {
			value = record.getMoney();
			billingType = EnumPrepayChargeType.Qian.getValue();
		} else {
			value = record.getAmount();
			billingType = EnumPrepayChargeType.Liang.getValue();
		}
		if (value < minMoney) {
			return false;
		}
		DTOUser user = new DTOUser();
		user.setUserId(record.getUserId());
		user.setUserName(record.getUserName());
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("user", user);
		map.put("tenantId", record.getTenantId());
		map.put("tenantName", record.getTenantName());
		map.put("value", value);
		FnMeter meter = null;
		meter = FNMeterService.queryMeterById(meterId);
		if (meter == null) {
			throw new Exception("仪表不存在:" + meterId);
		}
		// 判断仪表是否计算变比
		Double radio = 1.0;
		try {
			Long syjIsCt = (Long) ((JSONObject) JSONValue.parse(meter.getExtend())).get("syjIsCt");
			if (syjIsCt != null && syjIsCt.intValue() == 1) {
				radio = meter.getRadio();
			}
		} catch (Exception e) {
		}
		Double div = div(value, radio, 2);

		// 查询充值前剩余量
		Double remainData = null;
		Double payBefore = null;
		ICommunication communication = (ICommunication) SystemConstant.context.getBean(meter.getProtocolId());
		try {
			Double queryRemainData = communication.queryRemainData(meter);
			if (queryRemainData == null) {
				throw new Exception("查询剩余量失败");
			}
            if (!"DI_C_07_Y_Q_001".equals(meter.getProtocolId())) {
                Double overdraft = communication.queryOverdraft(meter);// 透支金额
                if (overdraft == null) {
                    throw new Exception("查询透支金额失败");
                }
                payBefore = queryRemainData - overdraft;
            }else {
                payBefore=queryRemainData;
            }
			payBefore = payBefore * radio;
		} catch (Exception e) {
			FNRecordForOtherSystemErrorService.remainDataError(record.getId(), e);
			return false;
		}
		FnRecordForOtherSystemExtend recordExtend = FNRecordForOtherSystemExtendService.queryById(record.getId());
		if (recordExtend != null) {
			remainData = recordExtend.getRemainData();
			{// 更新扩展表
				FnRecordForOtherSystemExtend update = new FnRecordForOtherSystemExtend();
				update.setRemainData(payBefore);
				update.setUpdateTime(new Date());
				FNRecordForOtherSystemExtendService.update(recordExtend, update);
			}
		} else {
			// 新增扩展表
			saveRecordForOtherSystemExtend(record,payBefore);
			remainData = payBefore;
		}
		if (payBefore - remainData > 0) {
			paySave(record, value, billingType, meter, payBefore, systemCodeMap, tenant);
			return true;// 充值成功
		}
		try {// 忽略充值失败问题
			communication.pay(meter, div.floatValue(), map);
		} catch (Exception e) {
		}
		// 充值后剩余量
		Double payAfter = null;
		try {
			Double queryRemainData = communication.queryRemainData(meter);
			if (queryRemainData == null) {
				throw new Exception("查询剩余量失败");
			}
            if (!"DI_C_07_Y_Q_001".equals(meter.getProtocolId())) {
                Double overdraft = communication.queryOverdraft(meter);// 透支金额
                if (overdraft == null) {
                    throw new Exception("查询透支金额失败");
                }
                payAfter = (queryRemainData - overdraft) * radio;
            }else {
                payAfter=queryRemainData*radio;
            }

		} catch (Exception e) {
			FNRecordForOtherSystemErrorService.remainDataError(record.getId(), e);
			return false;
		}
		if (payAfter != null) {
			if ((payAfter - payBefore) > 0) {// 充值成功
				// 保存充值记录
				paySave(record, value, billingType, meter, payAfter, systemCodeMap, tenant);
				return true;
			} else {
				FNRecordForOtherSystemErrorService.payError(record.getId());
			}
		}
		return false;
	}

	/**
	 * Description:
	 * @param record
	 * @param payBefore void
	 * <AUTHOR>
	 * @throws Exception
	 * @since 2019年10月11日: 上午11:31:45
	 * Update By 邵泓博 2019年10月11日: 上午11:31:45
	 */
	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	public void saveRecordForOtherSystemExtend(FnRecordPrePayForOtherSystem record, Double payBefore) throws Exception {
		{// 新增扩展表
			FnRecordForOtherSystemExtend save = new FnRecordForOtherSystemExtend();
			save.setId(record.getId());
			save.setMeterId(record.getCode());
			save.setRemainData(payBefore);
			save.setUpdateTime(new Date());
			FNRecordForOtherSystemExtendService.save(save);
		}
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void paySave(FnRecordPrePayForOtherSystem record, Double value, int billingType, FnMeter meter,
			Double payAfter, Map<String, FnCommonPrePaySystemParam> systemCodeMap, FnTenant tenant) throws Exception {
		String id = UUID.randomUUID().toString();
		FnRecordPrePay saveObj = new FnRecordPrePay();
		saveObj.setId(id);
		saveObj.setBuildingId(record.getBuildingId());
		saveObj.setBuildingName(record.getBuildingName());
		saveObj.setType(EnumPayBodyType.METER.getValue());
		saveObj.setTenantId(record.getTenantId());
		saveObj.setTenantName(record.getTenantName());
		saveObj.setCode(record.getCode());
		saveObj.setName(meter.getInstallAddress());
		saveObj.setEnergyTypeId(record.getEnergyTypeId());
		Date now = new Date();
		if (billingType == EnumPrepayChargeType.Qian.getValue()) {
			saveObj.setMoney(value);
		} else {
			saveObj.setAmount(value);
		}
		saveObj.setOrderId(record.getOrderId());
		saveObj.setAmountUnit(UnitUtil.getCumulantUnit(record.getEnergyTypeId()));
		saveObj.setOperateTime(now);
		saveObj.setUserId(record.getUserId());
		saveObj.setUserName(record.getUserName());
		saveObj.setCreateTime(record.getCreateTime());
		FNRecordPrePayService.save(saveObj);

		//查看是否是已经存在异常表中，如果存在说明是再次充值的。
		FnRecordPrePayErrorOrder payErrorOrder = FNRecordPrePayErrorOrderService.queryByOrderId(record.getOrderId());
		if (payErrorOrder != null){
			//插入充值渠道扩展表
			FnRecordPayChannel save = new FnRecordPayChannel();
			save.setId(UUID.randomUUID().toString());
			save.setRecordPayID(id);
			save.setPayType(EnumPayType.PREPAY.getValue());
			save.setChannelType(EnumPayChannelType.ERRORBILLFILL.getValue());
			save.setCreateTime(now);
			FNRecordPayChannelService.save(save);
		} else {
			FnRecordPayChannel save = new FnRecordPayChannel();
			save.setId(UUID.randomUUID().toString());
			save.setRecordPayID(id);
			save.setPayType(EnumPayType.PREPAY.getValue());
			save.setChannelType(EnumPayChannelType.WECHATNORMALPAY.getValue());
			save.setCreateTime(now);
			FNRecordPayChannelService.save(save);
		}
		{
			// 保存账单扩展字段
			FnOrderExtend save = new FnOrderExtend();
			save.setOrderId(record.getOrderId());
			save.setTenantId(record.getTenantId());
			save.setOperateType(EnumPrePayOrReturn.PREPAY.getValue());
			save.setData(value);
			save.setRemainData(payAfter);
			save.setChargeType(billingType);
			save.setOperateTime(now);
			FNOrderIdService.save(save);
		}

		{// 充值记录扩展字段
			FnRecordPrePayExtend save = new FnRecordPrePayExtend();
			save.setId(id);
			save.setPrePaySystemCode(record.getSystemCode());
			FnCommonPrePaySystemParam systemParam = (FnCommonPrePaySystemParam) systemCodeMap
					.get(record.getSystemCode());
			save.setSource(Integer.valueOf(systemParam.getId()));
			FNRecordPrePayExtendService.save(save);
		}
		{// 更新跨系统远程充值记录FnRecordPrePayForOtherSystem
			FnRecordPrePayForOtherSystem update = new FnRecordPrePayForOtherSystem();
			update.setStatus(EnumPrePayStatus.PROCESSE.getValue());
			update.setOperateTime(new Date());
			FNRecordPrePayForOtherSystemService.update(record, update);
		}
		{
			// 发送短信
			if ((boolean) ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendPayMessage)) {
			String perPaySuccessTemplate = (String) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_PerPaySuccessTemplate);
			String messageSignature = (String) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_MessageSignature);
			FNChargeMessageService.paySendMessage(saveObj, payAfter, tenant, perPaySuccessTemplate, messageSignature);
			}
		}
	}

	public double add(double value1, double value2) {
		BigDecimal b1 = new BigDecimal(Double.valueOf(value1).toString());
		BigDecimal b2 = new BigDecimal(Double.valueOf(value2).toString());
		return b1.add(b2).doubleValue();
	}

	public Double div(double d1, double d2, int len) {// 进行除法运算
		BigDecimal b1 = new BigDecimal(d1);
		BigDecimal b2 = new BigDecimal(d2);
		return b1.divide(b2, len, BigDecimal.ROUND_HALF_UP).doubleValue();
	}

	@Override
	public void processFail(FnRecordPrePayForOtherSystem record, Map<String, FnCommonPrePaySystemParam> systemCodeMap)
			throws Exception {
		FnRecordPrePayForOtherSystem update = new FnRecordPrePayForOtherSystem();
		update.setStatus(EnumPrePayStatus.FAIL.getValue());
		update.setOperateTime(new Date());
		FNRecordPrePayForOtherSystemService.update(record, update);// 修改为充值失败状态
		{// 异常账单记录
			FnRecordPrePayErrorOrder errorOrder = FNRecordPrePayErrorOrderService.queryByOrderId(record.getOrderId());
			if (errorOrder == null) {// 新增
				FnRecordPrePayErrorOrder save = new FnRecordPrePayErrorOrder();
				save.setOrderId(record.getOrderId());
				save.setBuildingId(record.getBuildingId());
				save.setBuildingName(record.getBuildingName());
				save.setBodyType(record.getType());
				save.setTenantId(record.getTenantId());
				save.setTenantName(record.getTenantName());
				save.setBodyCode(record.getCode());
				save.setEnergyTypeId(record.getEnergyTypeId());
				save.setOrderTime(record.getCreateTime());
				save.setMoney(record.getMoney());
				save.setAmount(record.getAmount());
				save.setSystemCode(record.getSystemCode());
				save.setUserId(record.getUserId());
				save.setUserName(record.getUserName());
				save.setStatus(EnumErrorOrderStatus.NoProcess.getValue());
				save.setOperateTime(record.getOperateTime());
				save.setAmountUnit(
						record.getMoney() == null ? UnitUtil.getCumulantUnit(record.getEnergyTypeId()) : "元");
				save.setSystemName(systemCodeMap.get(record.getSystemCode()).getSystemName());
				FNRecordPrePayErrorOrderService.save(save);
			} else {// 更新
				FnRecordPrePayErrorOrder updateErrorOrder = new FnRecordPrePayErrorOrder();
				updateErrorOrder.setStatus(EnumErrorOrderStatus.NoProcess.getValue());
				updateErrorOrder.setOperateTime(new Date());
				FNRecordPrePayErrorOrderService.update(errorOrder, updateErrorOrder);
			}

		}
	}
}
