package com.persagy.finein.fnmanage.alarm.thread;

import com.persagy.ac.service.AcBuildingService;
import com.persagy.core.constant.SchemaConstant;
import com.persagy.core.constant.SystemConstant;
import com.persagy.core.thread.BaseThread;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.ac.AcBuilding;
import com.persagy.ems.pojo.finein.FnAlarm;
import com.persagy.ems.pojo.finein.FnAlarmType;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.util.HttpUtils;
import com.persagy.finein.enumeration.EnumAlarmStatus;
import com.persagy.finein.fnmanage.alarm.handler.FNTGlobalAlarmPushHandler;
import com.persagy.finein.service.FNAlarmPushStatusService;
import com.persagy.finein.service.FNAlarmService;
import com.persagy.finein.service.FNAlarmTypeService;
import com.persagy.finein.service.FNSysParamValueService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * Description:推送全局报警 Company: Persagy
 * 
 * <AUTHOR>
 * @version 1.0
 * @since: 2019年6月12日: 上午11:57:17 Update By 邵泓博 2019年6月12日: 上午11:57:17
 */
@Component("FNTGlobalAlarmPushThread")
public class FNTGlobalAlarmPushThread extends BaseThread {

	private static boolean CONSTANT_THREAD_IS_OPEN = true;

	private static int CONSTANT_SLEEP = 60;

	private static long CONSTANT_QUERY_LIMIT = 100;// 处理报警每次查询100 条

	private static String Global_Alarm_Url = "http://*************:8080/";// 推送全局报警路径

	private static String Push_Global_Alarm_Url = "/global-alarm-service/alarm/create";// 推送全局报警路径

	private static String Query_Global_Alarm_Url = "global-alarm-service/alarm/query";// 查询全局报警路径

	private static String Update_Global_Alarm_Url = "/global-alarm-service/alarm/update";// 更新全局报警路径

	private static String Push_Global_Alarm_System_Id = "Business000002";// 系统编码

	private static Logger log = Logger.getLogger(FNTGlobalAlarmPushThread.class);

	@SuppressWarnings("rawtypes")
	private static Map<String, Map> Global_Alarm_Type_Map = new HashMap<>();

	@Resource(name = "FNAlarmService")
	private FNAlarmService FNAlarmService;

	@Resource(name = "FNAlarmPushStatusService")
	private FNAlarmPushStatusService FNAlarmPushStatusService;

	@Resource(name = "FNSysParamValueService")
	private FNSysParamValueService FNSysParamValueService;

	@Resource(name = "AcBuildingService")
	private AcBuildingService AcBuildingService;

	@Resource(name = "FNAlarmTypeService")
	private FNAlarmTypeService FNAlarmTypeService;

	@Resource(name = "FNTGlobalAlarmPushHandler")
	private FNTGlobalAlarmPushHandler FNTGlobalAlarmPushHandler;

	private static boolean IsSysParamValueInited = false;

	protected final SimpleDateFormat standard = new SimpleDateFormat("yyyyMMddHHmmss");

	@PostConstruct
	private void init() {
		setName("FNTAlarmPushThread");
	}

	@Override
	protected void business() throws Exception {

		this.initSysParamValue();
		try {
			if (!CONSTANT_THREAD_IS_OPEN) {
				this.setStop(true);
				log.error("【租户管理】租户全局报警推送线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}
			this.process();
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}

	}

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void initSysParamValue() {
		if (IsSysParamValueInited) {
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_GlobalAlarmPushThreadIsOpen);
			if (threadIsOpen != null) {
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Integer sleep = (Integer) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_GlobalAlarmPushThreadSleepSecond);
			if (sleep != null) {
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}
		try {
			String url = (String) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_GlobalAlarmPushUrl);
			if (url != null) {
				Global_Alarm_Url = url;
			}
		} catch (Exception e1) {
		}
		try {
			String systemId = (String) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_GlobalAlarmPushSystemId);
			if (systemId != null) {
				Push_Global_Alarm_System_Id = systemId;
			}
		} catch (Exception e1) {
		}
		URL url = SchemaConstant.class.getResource("/config/GlobalAlarmTypeConfig.json");
		try {
			Map<String, Map> map = SystemConstant.jsonMapper.readValue(url, Map.class);
			Global_Alarm_Type_Map.putAll(map);
		} catch (Exception e) {
			e.printStackTrace();
		}
		IsSysParamValueInited = true;
		log.error("【租户管理】租户全局报警推送线程开始运行。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}

	private void process() throws Exception {
		List<AcBuilding> list = AcBuildingService.query(new AcBuilding());
		if (list != null) {
			Map<String, FnAlarmType> alarmTypeMap = FNAlarmTypeService.queryMap();
			for (AcBuilding acBuilding : list) {
				processBuilding(acBuilding, alarmTypeMap);
			}
		}

	}

	/**
	 * Description:
	 * 
	 * @param acBuilding
	 *            void
	 * <AUTHOR>
	 * @param alarmTypeMap
	 * @throws Exception
	 * @since 2019年6月17日: 下午4:11:13 Update By 邵泓博 2019年6月17日: 下午4:11:13
	 */

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void processBuilding(AcBuilding acBuilding, Map<String, FnAlarmType> alarmTypeMap) throws Exception {
		List<FnAlarm> alarmList = FNAlarmService.queryGlobalAlarmWaitPush(acBuilding.getId(), CONSTANT_QUERY_LIMIT);
		if (alarmList != null && alarmList.size() > 0) {
			// 查询全局报警
			Map<String, Object> map = new HashMap<>();
			List<String> projectIds = new ArrayList<String>();
			projectIds.add(acBuilding.getId());
			List<String> systemIds = new ArrayList<String>();
			systemIds.add(Push_Global_Alarm_System_Id);
			List<String> statuses = new ArrayList<String>();
			statuses.add("unrecovered");
			map.put("projectIds", projectIds);
			map.put("systemIds", systemIds);
			map.put("statuses", statuses);
			String postJson = HttpUtils.postJson(Global_Alarm_Url + Query_Global_Alarm_Url,
					SystemConstant.jsonMapper.writeValueAsString(map));
			Map result = SystemConstant.jsonMapper.readValue(postJson, Map.class);
			if (!"success".equals((String) result.get("result"))) {
				throw new Exception((String) result.get("ResultMsg"));
			}
			Map<String, String> globalIdMap = new HashMap<String, String>();
			List<Map> globalAlarmList = (List) result.get("content");
			if (globalAlarmList != null && globalAlarmList.size() > 0) {
				for (Map gobalAlarm : globalAlarmList) {
					globalIdMap.put((String) gobalAlarm.get("objectId"), (String) gobalAlarm.get("id"));
				}
			}
			for (FnAlarm alarm : alarmList) {
				try {
					String alarmTypeId = alarm.getAlarmTypeId();
					Map dtoGlobalAlarmType = Global_Alarm_Type_Map.get(alarmTypeId);
					if (dtoGlobalAlarmType == null) {
						log.error("【租户管理】租户全局报警推送线程:全局报警没有此报警类型,alarmTypeId:" + alarm.getAlarmTypeId());
						continue;
					}
					if (alarm.getStatus() == EnumAlarmStatus.WeiHuiHu.getValue().intValue()) {// 新增报警
						addAlarm(alarmTypeMap, acBuilding, alarm, dtoGlobalAlarmType);
					} else {// 更新报警状态
						updateAlarmStatus(globalIdMap, alarm, alarmTypeId);
					}
				} catch (Exception e) {
					log.error("【租户管理】租户全局报警推送线程:处理报警:" + alarm.getId() + e.getMessage());
				}
			}
		}
	}

	private void updateAlarmStatus(Map<String, String> globalIdMap, FnAlarm alarm, String alarmTypeId)
			throws Exception {
		String tenantId = alarm.getTenantId();
		String alarmPositionId = alarm.getAlarmPositionId();
		String key = buildKey(tenantId, alarmTypeId, alarmPositionId);
		String gobalAlarmId = globalIdMap.get(key);
		if (gobalAlarmId != null) {
			Map<String, Object> hashMap = new HashMap<>();
			hashMap.put("id", gobalAlarmId);
			hashMap.put("status",
					alarm.getStatus() == EnumAlarmStatus.YiGuoQi.getValue().intValue() ? "timeout" : "recovered");
			FNTGlobalAlarmPushHandler.updateGlobalAlarm(Global_Alarm_Url + Update_Global_Alarm_Url, hashMap,
					alarm.getId());
		}
	}

	/**
	 * Description: void
	 * 
	 * <AUTHOR>
	 * @throws Exception
	 * @since 2019年6月19日: 下午5:35:27 Update By 邵泓博 2019年6月19日: 下午5:35:27
	 */

	@SuppressWarnings({ "unchecked", "rawtypes" })
	private void addAlarm(Map<String, FnAlarmType> alarmTypeMap, AcBuilding acBuilding, FnAlarm alarm,
			Map<String, Map> dtoGlobalAlarmType) throws Exception {
		String alarmTypeId = alarm.getAlarmTypeId();
		FnAlarmType fnAlarmType = alarmTypeMap.get(alarmTypeId);
		if (fnAlarmType == null) {
			log.error("【租户管理】租户全局报警推送线程:报警没有此报警类型,alarmTypeId:" + alarm.getAlarmTypeId());
			return;
		}
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append("租户(").append(alarm.getTenantId()).append("_").append(alarm.getAlarmPositionName());
		stringBuilder.append(")").append(fnAlarmType.getName());
		Map jsonParams = new HashMap<>();
		jsonParams.put("systemId", Push_Global_Alarm_System_Id);
		jsonParams.put("projectId", alarm.getBuildingId());
		jsonParams.put("projectName", acBuilding.getName());
		jsonParams.put("typeId", dtoGlobalAlarmType.get("id"));
		jsonParams.put("content", stringBuilder.toString());
		jsonParams.put("level", "B");
		jsonParams.put("createTime", standard.format(alarm.getCreateTime()));
		jsonParams.put("objectId",alarm.getTenantId() + "_" + alarm.getAlarmTypeId() + "_" + alarm.getAlarmPositionId());
		jsonParams.put("objectName", alarm.getAlarmPositionName());
		FNTGlobalAlarmPushHandler.addGlobalAlarm(Global_Alarm_Url + Push_Global_Alarm_Url, jsonParams, alarm.getId());
	}

	/**
	 * Description:
	 * 
	 * @param tenantId
	 * @param alarmTypeId
	 * @param alarmPositionId
	 * @return String
	 * <AUTHOR>
	 * @since 2019年6月17日: 下午5:09:10 Update By 邵泓博 2019年6月17日: 下午5:09:10
	 */

	private String buildKey(String tenantId, String alarmTypeId, String alarmPositionId) {
		StringBuilder stringBuilder = new StringBuilder();
		stringBuilder.append(tenantId).append("_").append(alarmTypeId).append("_").append(alarmPositionId);
		return stringBuilder.toString();
	}
}
