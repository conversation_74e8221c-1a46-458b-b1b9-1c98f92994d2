package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnRoom;
import com.persagy.finein.service.FNRoomService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：物业管理
 * 接口名：通用-获取房间列表
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntCommonRoomListController extends BaseController {

    @Resource(name = "FNRoomService")
    private FNRoomService fnRoomService;

    @RequestMapping("FNTCommonRoomListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonRoomList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String)dto.get("buildingId");
            String floorId = (String)dto.get("floorId");

            if(buildingId == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            List<Object> contentObj = new ArrayList<Object>();
            Map<String,Object> contentMap = new HashMap<String,Object>();

            FnRoom query = new FnRoom();
            query.setBuildingId(buildingId);
            query.setSort("floorId", EMSOrder.Asc);
            query.setSort("code", EMSOrder.Asc);
            if(floorId != null){
                query.setFloorId(floorId.trim());
            }

            contentMap.put("roomList", fnRoomService.query(query));

            contentObj.add(contentMap);
            content.addAll(contentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTCommonRoomListService");
        }
    }


}
