package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.service.FNBuildingService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理
 * 接口名：通用-获取建筑列表
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntCommonBuildingListController {

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @RequestMapping("FNTCommonBuildingListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonBuildingList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            //查询尚格云All建筑
            Building queryBuilding = new Building();
            queryBuilding.setSort("id", EMSOrder.Asc);

            List<Building> buildings = fnBuildingService.queryList(queryBuilding);
            if(buildings != null){
                content.addAll(buildings);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTCommonBuildingListService");
        }
    }


}
