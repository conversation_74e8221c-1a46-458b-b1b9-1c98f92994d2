package com.persagy.finein.fnmanage.compute.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantData;
import com.persagy.ems.pojo.finein.FnTenantMeterData;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.compute.handler.FNTComputeTenantMeterAvgHandler;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月22日 下午12:58:25

* 说明:计算最近*天平均值线程
*/
@Component("FNTComputeTenantMeterAvgHandler")
public class FNTComputeTenantMeterAvgHandlerImpl extends CoreServiceImpl implements FNTComputeTenantMeterAvgHandler{

	@Resource(name = "FNTenantPayTypeService")
	private FNTenantPayTypeService FNTenantPayTypeService;
	
	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;
	
	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;
	
	@Resource(name = "FNTenantDataService")
	private FNTenantDataService FNTenantDataService;
	
	@Resource(name = "FNTenantMeterDataService")
	private FNTenantMeterDataService FNTenantMeterDataService;
	
	@Resource(name = "FNTenantMeterAvgService")
	private FNTenantMeterAvgService FNTenantMeterAvgService;
	
	@Override
	public void processBuilding(Building building,long days) throws Exception{
		//查询建筑下所有租户
		List<FnTenant> tenantList = FNTenantService.queryListByValidStatus(building.getId(), EnumValidStatus.VALID);
		if(tenantList != null){
			for(FnTenant tenant : tenantList){
				if(tenant.getStatus().intValue() == EnumTenantStatus.ACTIVATED.getValue().intValue()){//只计算已激活
					this.processTenant(building, tenant,days);
				}
			}
		}
	}
	
	@Override	
	public void processTenant(Building building,FnTenant tenant,long days) throws Exception{
		Date timeTo = DateUtils.truncate(new Date(), Calendar.DATE);
		Date timeFrom = DateUtils.addDays(timeTo, -Long.valueOf(days).intValue());
		if(tenant.getActiveTime().getTime()>timeFrom.getTime()){//租户能耗应该从激活时间开始计算
			timeFrom=tenant.getActiveTime();
		}
		//查询租户
//		if("ZHBH_1013".equals(tenant.getId())){
//			System.out.println("--");
//		}
		Map<String,FnTenantPayType> payTypeMap = FNTenantPayTypeService.queryPayTypeMap(tenant.getId());
		for(Map.Entry<String, FnTenantPayType> entry : payTypeMap.entrySet()){
			{//能耗
				List<FnTenantData> dataList = FNTenantDataService.queryListGteLt(building.getId(), tenant.getId(), EnumTimeType.T2, entry.getKey(), timeFrom, timeTo, EnumEnergyMoney.Energy);
				int dataCount = 0;
				Double total = null;
				if(dataList != null && dataList.size() > 0){
					for(FnTenantData tenantData : dataList){
						if(tenantData.getData() != null){
							dataCount++;
							total = total == null ? tenantData.getData() : total + tenantData.getData();
						}
					}
				}
				Double avg = null;
				if(total != null && dataCount > 0){
					avg = total.doubleValue() / dataCount;
				}
				if(avg != null){
					this.FNTenantMeterAvgService.save(building.getId(),tenant.getId(), entry.getKey(), EnumPayBodyType.TENANT, tenant.getId(), EnumEnergyMoney.Energy, timeFrom, timeTo, dataCount, avg);
				}
			}
			{//费用
				List<FnTenantData> dataList = FNTenantDataService.queryListGteLt(building.getId(), tenant.getId(), EnumTimeType.T2, entry.getKey(), timeFrom, timeTo, EnumEnergyMoney.Money);
				int dataCount = 0;
				Double total = null;
				if(dataList != null && dataList.size() > 0){
					for(FnTenantData tenantData : dataList){
						if(tenantData.getData() != null){
							dataCount++;
							total = total == null ? tenantData.getData() : total + tenantData.getData();
						}
					}
				}
				Double avg = null;
				if(total != null && dataCount > 0){
					avg = total.doubleValue() / dataCount;
				}
				if(avg != null){
					this.FNTenantMeterAvgService.save(building.getId(),tenant.getId(), entry.getKey(), EnumPayBodyType.TENANT, tenant.getId(), EnumEnergyMoney.Money, timeFrom, timeTo, dataCount, avg);
				}
			}
			List<DTOMeter> meterList = FNMeterService.queryMeterList(tenant.getId(), entry.getKey());
			if(meterList != null){
				for(DTOMeter meter : meterList){
					Integer cumulantFunctionId = FunctionTypeUtil.getCumulantFunctionId(meter.getEnergyTypeId());
					
					{
						List<FnTenantMeterData> dataList = FNTenantMeterDataService.queryListGteLt(building.getId(), tenant.getId(), meter.getMeterId(), cumulantFunctionId, entry.getKey(), EnumTimeType.T2, timeFrom, timeTo, EnumEnergyMoney.Energy);
						
						int dataCount = 0;
						Double total = null;
						if(dataList != null && dataList.size() > 0){
							for(FnTenantMeterData tenantMeterData : dataList){
								if(tenantMeterData.getData() != null){
									dataCount++;
									total = total == null ? tenantMeterData.getData() : total + tenantMeterData.getData();
								}
							}
						}
						Double avg = null;
						if(total != null && dataCount > 0){
							avg = total.doubleValue() / dataCount;
						}
						
						if(avg != null){
							this.FNTenantMeterAvgService.save(building.getId(),tenant.getId(), entry.getKey(), EnumPayBodyType.METER, meter.getMeterId(), EnumEnergyMoney.Energy, timeFrom, timeTo, dataCount, avg);
						}
					}
					{
						List<FnTenantMeterData> dataList = FNTenantMeterDataService.queryListGteLt(building.getId(), tenant.getId(), meter.getMeterId(), cumulantFunctionId, entry.getKey(), EnumTimeType.T2, timeFrom, timeTo, EnumEnergyMoney.Money);
						
						int dataCount = 0;
						Double total = null;
						if(dataList != null && dataList.size() > 0){
							for(FnTenantMeterData tenantMeterData : dataList){
								if(tenantMeterData.getData() != null){
									dataCount++;
									total = total == null ? tenantMeterData.getData() : total + tenantMeterData.getData();
								}
							}
						}
						Double avg = null;
						if(total != null && dataCount > 0){
							avg = total.doubleValue() / dataCount;
						}
						if(avg != null){
							this.FNTenantMeterAvgService.save(building.getId(),tenant.getId(), entry.getKey(), EnumPayBodyType.METER, meter.getMeterId(), EnumEnergyMoney.Money, timeFrom, timeTo, dataCount, avg);
						}
					}
				}
			}
		}
	}

}

