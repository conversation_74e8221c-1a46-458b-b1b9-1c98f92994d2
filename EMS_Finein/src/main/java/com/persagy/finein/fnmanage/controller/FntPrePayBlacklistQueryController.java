package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.finein.FnRemoteRechargeStatus;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNBuildingService;
import com.persagy.finein.service.FNRemoteRechargeStatusService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：自动发送短信设置
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntPrePayBlacklistQueryController extends BaseController {

    @Resource(name = "FNRemoteRechargeStatusService")
    private FNRemoteRechargeStatusService fnRemoteRechargeStatusService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @RequestMapping("FNTPrePayBlacklistQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult prePayBlacklistQuery(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            FnRemoteRechargeStatus query = new FnRemoteRechargeStatus();
            query.setBuildingId(buildingId);
            query.setRemoteRechargeStatus(EnumYesNo.NO.getValue());
            List<FnRemoteRechargeStatus> list = fnRemoteRechargeStatusService.query(query);
            List<Object> contentList = new ArrayList<Object>();
            if (list != null && list.size() > 0) {
                Map<String, FnTenant> tenantMap = fnTenantService.queryMap();
                Map<String, Building> buildingMap = fnBuildingService.query();
                for (FnRemoteRechargeStatus blacklist : list) {
                    FnTenant fnTenant = tenantMap.get(blacklist.getTenantId());
                    Building building = buildingMap.get(blacklist.getBuildingId());
                    if (fnTenant == null || building == null) {
                        continue;
                    }
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("buildingId", blacklist.getBuildingId());
                    map.put("buildingName", building.getName());
                    map.put("tenantId", blacklist.getTenantId());
                    map.put("tenantName", fnTenant.getName());
                    contentList.add(map);
                }
            }
            content.add(contentList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPrePayBlacklistQueryService");
        }
    }


}
