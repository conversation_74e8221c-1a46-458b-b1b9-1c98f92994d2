package com.persagy.finein.fnmanage.compute.thread;

import com.persagy.core.thread.BaseThread;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.fnmanage.compute.handler.FNTComputeRemainDaysHandler;
import com.persagy.finein.service.FNBuildingService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月22日 下午12:58:25

* 说明:计算租户仪表剩余使用天数线程
*/
@Component("FNTComputeRemainDaysThread")
public class FNTComputeRemainDaysThread extends BaseThread{

	private static boolean CONSTANT_THREAD_IS_OPEN = true;
	private static long CONSTANT_SLEEP = 60;
	private static long CONSTANT_TOLERATERSECOND = 1200;
	
	
	private static Logger log = Logger.getLogger(FNTComputeRemainDaysThread.class);
	
	@Resource(name = "FNBuildingService")
	private FNBuildingService FNBuildingService;
	
	@Resource(name = "FNTComputeRemainDaysHandler")
	private FNTComputeRemainDaysHandler FNTComputeRemainDaysHandler;

	private static boolean IsSysParamValueInited = false;
	
	@Override
	protected void business() throws Exception {
		
		try {
			this.initSysParamValue();
			
			if(!CONSTANT_THREAD_IS_OPEN){
				this.setStop(true);
				log.error("【租户管理】计算租户仪表剩余使用天数线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}
			
			this.process();
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}
	}
	
	private void initSysParamValue(){
		if(IsSysParamValueInited){
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_ComputeRemainDaysThreadIsOpen);
			if(threadIsOpen != null){
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Long sleep = (Long)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_ComputeRemainDaysThreadSleepSecond);
			if(sleep != null){
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}
		try {
			Long toleraterSecond = (Long)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_ComputeRemainDaysThreadTolerateSecond);
			if(toleraterSecond != null){
				CONSTANT_TOLERATERSECOND = toleraterSecond;
			}
		} catch (Exception e1) {
		}
		
		IsSysParamValueInited = true;
		log.error("【租户管理】计算租户仪表剩余使用天数线程开始运行。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}
	
	private void process(){
		try {
			try {
				//1.查询所有建筑
				List<Building> buildingList = FNBuildingService.queryList(new Building());
				if(buildingList != null && buildingList.size() > 0){
					for(Building building : buildingList){
						this.FNTComputeRemainDaysHandler.processBuilding(building, CONSTANT_TOLERATERSECOND);
					}
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}

