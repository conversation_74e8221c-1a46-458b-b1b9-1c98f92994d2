package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.EnumPayStatus;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.fnmanage.param.handler.FNTTenantParamPostPayHandler;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：批量操作-后付费账单结算-保存
 *
 * <AUTHOR>
 * */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntBatchPostPayBillingSaveController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @Resource(name = "FNOrderIdService")
    private FNOrderIdService fnOrderIdService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNPriceTemplateService")
    private FNPriceTemplateService fnPriceTemplateService;

    @Resource(name = "FNRecordPostClearingMeterService")
    private FNRecordPostClearingMeterService fnRecordPostClearingMeterService;

    @Resource(name = "FNRecordPostClearingPayService")
    private FNRecordPostClearingPayService fnRecordPostClearingPayService;

    @Resource(name = "FNTTenantParamPostPayHandler")
    private FNTTenantParamPostPayHandler fntTenantParamPostPayHandler;


    @RequestMapping("FNTBatchPostPayBillingSaveService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchPostPayBillingSave(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String) dto.get("energyTypeId");
            String userId = this.getParamUserId(dto);
            List<Object> tenantList = (List<Object>) dto.get("tenantList");


            if (energyTypeId == null
                    || userId == null
                    || tenantList == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            DTOUser dtoUser = fnUserService.queryUserByUserId(userId);
            if (tenantList.size() > 0) {
                List<String> tenantIdList = new ArrayList<>();
                for (int i = 0; i < tenantList.size(); i++) {
                    Map<String, Object> tenantObj = (Map<String, Object>) tenantList.get(i);
                    String tenantId = (String) tenantObj.get("tenantId");
                    tenantIdList.add(tenantId);
                }

                List<FnTenant> tenantEntityList = fnTenantService.queryListByIds(tenantIdList);
                Map<String, FnTenant> tenantEntityMap = new HashMap<>();
                for (FnTenant tenant : tenantEntityList) {
                    tenantEntityMap.put(tenant.getId(), tenant);
                }
                Map<String, Building> buildingMap = fnBuildingService.query();

                List<FnPriceTemplate> priceTemplateList = fnPriceTemplateService.queryList();
                Map<String, FnPriceTemplate> priceTemplateMap = new HashMap<>();
                if (priceTemplateList != null) {
                    for (FnPriceTemplate priceTemplate : priceTemplateList) {
                        priceTemplateMap.put(priceTemplate.getId(), priceTemplate);
                    }
                }

                List<FnRecordPostClearingMeter> meterClearingRecordList = new ArrayList<>();
                List<FnRecordPostClearingPay> tenantClearingRecordList = new ArrayList<>();

                Map<FnTenant, String> refreshMap = new HashMap<>();

                for (int i = 0; i < tenantList.size(); i++) {
                    Map<String, Object> resultObj = new HashMap<>();
                    Map<String, Object> tenantObj = (Map<String, Object>) tenantList.get(i);
                    String tenantId = (String) tenantObj.get("tenantId");
                    FnTenant fnTenant = tenantEntityMap.get(tenantId);
                    String buildingId = (String) tenantObj.get("buildingId");
                    Building building = buildingMap.get(buildingId);
                    String lastClearingTime = (String) tenantObj.get("lastClearingTime");
                    String currentClearingTime = (String) tenantObj.get("currentClearingTime");
                    Double currentBillingEnergy = DoubleFormatUtil.Instance().getDoubleData(tenantObj.get("currentBillingEnergy"));
                    Double currentBillingMoney = DoubleFormatUtil.Instance().getDoubleData(tenantObj.get("currentBillingMoney"));
                    String priceTemplateId = (String) tenantObj.get("priceTemplateId");

                    List<Object> meterList = (List<Object>) tenantObj.get("meterList");

                    //解析仪表
                    Date now = new Date();
                    String orderId = fnOrderIdService.queryOrderId(EnumPayType.POSTPAY.getValue(), DateUtils.truncate(now, Calendar.DATE));
                    if (meterList != null) {
                        Double meterEnergy = null;
                        Double meterMoney = null;
                        for (Object meterObj : meterList) {
                            Map<String, Object> meterMap = (Map<String, Object>) meterObj;

                            String meterId = (String) meterMap.get("meterId");
                            Integer meterType = (Integer) meterMap.get("meterType");
                            String meterLastClearingTime = (String) meterMap.get("lastClearingTime");
                            List<Object> functionList = (List<Object>) meterMap.get("functionList");

                            JSONObject extendObj = new JSONObject();
                            JSONArray functionArray = new JSONArray();
                            extendObj.put("functionList", functionArray);
                            if (functionList != null && functionList.size() > 0) {
                                for (Object functionObj : functionList) {
                                    JSONObject functionExtendObj = new JSONObject();
                                    Map<String, Object> functionMap = (Map<String, Object>) functionObj;
                                    String type = (String) functionMap.get("type");
                                    Integer functionId = (Integer) functionMap.get("functionId");
                                    Double lastBillingData = DoubleFormatUtil.Instance().getDoubleData(functionMap.get("lastClearingData"));
                                    Double currentBillingData = DoubleFormatUtil.Instance().getDoubleData(functionMap.get("currentClearingData"));
                                    Double diffData = DoubleFormatUtil.Instance().getDoubleData(functionMap.get("diffData"));
                                    Double money = DoubleFormatUtil.Instance().getDoubleData(functionMap.get("money"));
                                    if (diffData != null) {
                                        meterEnergy = meterEnergy == null ? diffData : meterEnergy + diffData;
                                    }
                                    if (money != null) {
                                        meterMoney = meterMoney == null ? money : meterMoney + money;
                                    }
                                    functionExtendObj.put("type", type);
                                    functionExtendObj.put("functionId", Long.valueOf(functionId));
                                    functionExtendObj.put("lastData", lastBillingData);
                                    functionExtendObj.put("currentData", currentBillingData);
                                    functionExtendObj.put("diffData", diffData);
                                    functionExtendObj.put("money", money);
                                    functionArray.add(functionExtendObj);
                                }
                            }
                            FnPriceTemplate priceTemplate = priceTemplateMap.get(priceTemplateId);
                            FnRecordPostClearingMeter meterRecord = new FnRecordPostClearingMeter();
                            meterRecord.setId(UUID.randomUUID().toString());
                            meterRecord.setBuildingId(fnTenant.getBuildingId());
                            meterRecord.setEnergyTypeId(energyTypeId);
                            meterRecord.setBuildingName(building.getName());
                            meterRecord.setTenantId(fnTenant.getId());
                            meterRecord.setTenantName(fnTenant.getName());
                            meterRecord.setOrderId(orderId);
                            meterRecord.setMeterId(meterId);
                            meterRecord.setMeterType(meterType);
                            meterRecord.setPriceTemplateId(priceTemplate.getId());
                            meterRecord.setPriceTemplateName(priceTemplate.getName());
                            meterRecord.setPriceTemplateContent(priceTemplate.getContent());
                            meterRecord.setPriceTemplateType(priceTemplate.getType());
                            meterRecord.setExtend(extendObj.toString());
                            String lastTime = meterLastClearingTime.substring(0, 10);
                            String endTime = currentClearingTime.substring(0, 10);
                            meterRecord.setLastClearingTime(standard.parse(meterLastClearingTime));
                            meterRecord.setCurrentClearingTime(standard.parse(currentClearingTime));
                            meterRecord.setOrderTime(lastTime + "~" + endTime);
                            meterRecord.setCurrentSumEnergy(meterEnergy);
                            meterRecord.setCurrentSumEnergyUnit(UnitUtil.getCumulantUnit(energyTypeId));
                            meterRecord.setMoney(meterMoney);
                            meterRecord.setRoomIds(fnTenant.getRoomCodes());
                            meterRecord.setCreateTime(now);
                            meterClearingRecordList.add(meterRecord);
                        }
                    }
                    //处理租户
                    FnRecordPostClearingPay tenantRecord = new FnRecordPostClearingPay();
                    tenantRecord.setId(UUID.randomUUID().toString());
                    tenantRecord.setBuildingId(fnTenant.getBuildingId());
                    tenantRecord.setBuildingName(building.getName());
                    tenantRecord.setTenantId(tenantId);
                    tenantRecord.setEnergyTypeId(energyTypeId);
                    tenantRecord.setTenantName(fnTenant.getName());
                    tenantRecord.setOrderId(orderId);
                    String lastTime = lastClearingTime.substring(0, 10);
                    String endTime = currentClearingTime.substring(0, 10);
                    tenantRecord.setLastClearingTime(standard.parse(lastClearingTime));
                    tenantRecord.setCurrentClearingTime(standard.parse(currentClearingTime));
                    tenantRecord.setOrderTime(lastTime + "~" + endTime);
                    tenantRecord.setCurrentSumEnergy(currentBillingEnergy);
                    tenantRecord.setCurrentSumEnergyUnit(UnitUtil.getCumulantUnit(energyTypeId));
                    tenantRecord.setMoney(currentBillingMoney);
                    tenantRecord.setPayStatus(EnumPayStatus.NO.getValue());
                    tenantRecord.setUserId(userId);
                    tenantRecord.setUserName(dtoUser.getUserName());
                    tenantRecord.setRoomIds(fnTenant.getRoomCodes());
                    tenantRecord.setCreateTime(now);
                    tenantClearingRecordList.add(tenantRecord);
                    resultObj.put("tenantId", tenantId);
                    resultObj.put("orderId", orderId);
                    content.add(resultObj);

                    refreshMap.put(fnTenant, energyTypeId);
                }
                fnRecordPostClearingMeterService.save(meterClearingRecordList);
                fnRecordPostClearingPayService.save(tenantClearingRecordList);

                for (Map.Entry<FnTenant, String> entry : refreshMap.entrySet()) {
                    //刷新后会费参数计算
                    Building building = new Building();
                    building.setId(entry.getKey().getBuildingId());
                    FnTenantPayType tenantPayType = new FnTenantPayType();
                    tenantPayType.setEnergyTypeId(entry.getValue());
                    fntTenantParamPostPayHandler.handle(building, entry.getKey(), tenantPayType);
                    System.out.println("刷新后付费列表参数:" + entry.getKey().getId() + ":" + entry.getValue());
                }
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTBatchPostPayBillingSaveService");
        }
    }
}
