package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.TimeDataUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantData;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.service.FNTenantDataService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：批量操作-后付费账单结算-明细
 *
 * <AUTHOR>
 * */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntBatchPostPayBillingDetailController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantDataService")
    private FNTenantDataService fnTenantDataService;


    @RequestMapping("FNTBatchPostPayBillingDetailService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchPostPayBillingDetail(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String)dto.get("energyTypeId");
            String tenantId = (String)dto.get("tenantId");
            String timeFrom = (String)dto.get("timeFrom");
            String timeTo = (String)dto.get("timeTo");


            if(energyTypeId == null
                    || tenantId == null
                    || timeFrom == null
                    || timeTo == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnTenant fnTenant = fnTenantService.queryOne(tenantId);
            Date from = standard.parse(timeFrom);
            Date to = standard.parse(timeTo);

            Map<String,Double> energyMap = TimeDataUtil.getTimeDataMap(from, to, EnumTimeType.T2);
            Map<String,Double> moneyMap = new LinkedHashMap<>(energyMap);

            {
                List<FnTenantData> dataList = fnTenantDataService.queryListGteLt(fnTenant.getBuildingId(), tenantId, EnumTimeType.T2, energyTypeId, from, to, EnumEnergyMoney.Energy);
                if(dataList != null){
                    for(FnTenantData fnTenantData : dataList){
                        String key = standard.format(fnTenantData.getTimeFrom());
                        if(energyMap.containsKey(key)){
                            energyMap.put(key, fnTenantData.getData());
                        }
                    }
                }
            }
            {
                List<FnTenantData> dataList = fnTenantDataService.queryListGteLt(fnTenant.getBuildingId(), tenantId, EnumTimeType.T2, energyTypeId, from, to, EnumEnergyMoney.Money);
                if(dataList != null){
                    for(FnTenantData fnTenantData : dataList){
                        String key = standard.format(fnTenantData.getTimeFrom());
                        if(moneyMap.containsKey(key)){
                            moneyMap.put(key, fnTenantData.getData());
                        }
                    }
                }
            }

            String energyUnit = UnitUtil.getCumulantUnit(energyTypeId);
            Map<String,Object> contentMap = new HashMap<String, Object>();
            contentMap.put("energyUnit", energyUnit);
            List<Object> dataList = new ArrayList<>();
            contentMap.put("dataList", dataList);

            Double totalEnergy = null;
            Double totalMoney = null;
            for(Map.Entry<String, Double> entry : energyMap.entrySet()){
                String key = entry.getKey();
                Double energy = entry.getValue();
                Double money = moneyMap.get(key);
                if(energy != null){
                    totalEnergy = totalEnergy == null ? energy : totalEnergy + energy;
                }
                if(money != null){
                    totalMoney = totalMoney == null ? money : totalMoney + money;
                }
                Map<String,Object> dataObj = new HashMap<String, Object>();
                dataObj.put("time", key);
                dataObj.put("dayEnergy", energy);
                dataObj.put("totalEnergy", totalEnergy);
                dataObj.put("dayMoney", money);
                dataObj.put("totalMoney", totalMoney);
                dataList.add(dataObj);
            }
            content.add(contentMap);

            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTBatchPostPayBillingDetailService");
        }
    }
}
