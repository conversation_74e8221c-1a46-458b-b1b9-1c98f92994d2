package com.persagy.finein.fnmanage.thread;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.persagy.core.thread.BaseThread;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnChargeMessage;
import com.persagy.ems.sms.business.ISmsHandler;
import com.persagy.ems.sms.business.impl.DefaultSmsHandler;
import com.persagy.ems.sms.client.MissionResultInfo;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumMessageSendStatus;
import com.persagy.finein.service.FNChargeMessageService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Date;
import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月18日 下午7:56:04
 *  update  2019年10月31日 xsg 添加黑名单
 * 说明: 发送消息线程
 */
@Component("FNTMessageSendThread")
public class FNTMessageSendThread extends BaseThread {

	private static boolean CONSTANT_THREAD_IS_OPEN = true;
	private static int CONSTANT_LIMIT = 50;
	private static long CONSTANT_OVERDUE_TIME = 86400;
	private static int CONSTANT_SLEEP = 30;
	private static int CONSTANT_MAX_TRY_TIMES = 3;
	private static String CONSTANT_SERVER = "http://202.85.214.57:8087/service/sms/api.asmx";
	private static String CONSTANT_USER = "博锐尚格";
	private static String CONSTANT_PASSWORD = "persagy_2014";

	private static ISmsHandler smsHandler;

	@Resource(name = "FNChargeMessageService")
	private FNChargeMessageService FNChargeMessageService;

	private static Logger log = Logger.getLogger(FNTMessageSendThread.class);

	private static boolean IsSysParamValueInited = false;

	@Override
	protected void business() throws Exception {

		try {
			this.initSysParamValue();

			if (!CONSTANT_THREAD_IS_OPEN) {
				this.setStop(true);
				log.error("【租户管理】租户发送消息线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}
			if (smsHandler == null) {
				smsHandler = new DefaultSmsHandler();
			}

			this.process();
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}
	}

	private void initSysParamValue() {
		if (IsSysParamValueInited) {
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_MessageSendThreadIsOpen);
			if (threadIsOpen != null) {
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Integer limit = (Integer) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_MessageSendQueryLimit);
			if (limit != null) {
				CONSTANT_LIMIT = limit;
			}
		} catch (Exception e1) {
		}
		try {
			Long overdueTime = (Long) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_MessageSendOverdueTimeSecond);
			if (overdueTime != null) {
				CONSTANT_OVERDUE_TIME = overdueTime;
			}
		} catch (Exception e1) {
		}
		try {
			Integer sleep = (Integer) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_MessageSendThreadSleepSecond);
			if (sleep != null) {
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}
		try {
			Integer maxTryTimes = (Integer) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_MessageSendMaxTryTimes);
			if (maxTryTimes != null) {
				CONSTANT_MAX_TRY_TIMES = maxTryTimes;
			}
		} catch (Exception e1) {
		}
		try {
			String server = (String) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_MessageSendServer);
			if (server != null) {
				CONSTANT_SERVER = server;
			}
		} catch (Exception e1) {
		}
		try {
			String user = (String) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_MessageSendUser);
			if (user != null) {
				CONSTANT_USER = user;
			}
		} catch (Exception e1) {
		}

		IsSysParamValueInited = true;
		log.error("【租户管理】租户发送消息线程开始计算。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}

	private void process() {
		try {
			List<FnChargeMessage> messageList = FNChargeMessageService.queryMessageList(EnumMessageSendStatus.WAIT_SEND,
					CONSTANT_LIMIT);
			if (messageList == null || messageList.size() == 0) {
				return;
			}
			Date now = new Date();
			for (FnChargeMessage message : messageList) {
				//处理黑名单
				List<String> phoneBlackList = this.phoneBlackList();
				if (phoneBlackList.size()>0 && phoneBlackList!=null){
					if (phoneBlackList.contains(message.getContactMobile())) {
						log.error("手机号在黑名单中：" + message.getContactMobile());
						FNChargeMessageService.sendBlack(message.getId());
						continue;
					}
				}
				// 处理过期
				if (now.getTime() - message.getCreateTime().getTime() > CONSTANT_OVERDUE_TIME * 1000) {
					FNChargeMessageService.sendOverdue(message.getId());
					continue;
				}

				// 处理发送
				int tryTimes = 0;
				boolean isSuccess = false;
				String exceptionReason = "";
				while (tryTimes < CONSTANT_MAX_TRY_TIMES) {
					try {
						MissionResultInfo missionResultInfo = smsHandler.sendSms(CONSTANT_SERVER, CONSTANT_USER,
								CONSTANT_PASSWORD, message.getContent(), message.getContactMobile(), "");
						if ("0".equals(missionResultInfo.getStatus().getValue())) {// 成功
							isSuccess = true;
							break;
						} else {
							exceptionReason = missionResultInfo.getStatus().getValue();
						}
					} catch (Exception e) {
						e.printStackTrace();
						exceptionReason = "-1";
					}
					tryTimes++;
				}
				if (isSuccess) {
					FNChargeMessageService.sendSuccess(message.getId(), tryTimes);
				} else {
					FNChargeMessageService.sendFailure(message.getId(), exceptionReason, tryTimes);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private List<String> phoneBlackList(){
		try {
			String filePath = null;
			URL url = FNTMessageSendThread.class.getResource("/config/phoneBlackList.json");
			if (url == null) {
				filePath = System.getProperty("user.dir") + "/config/phoneBlackList.json";
			} else {
				filePath = url.getPath();
			}
			File file = new File(filePath);
			ObjectMapper mapper = new ObjectMapper();
			String stringJsonNode = mapper.readTree(file).get("phone").toString();
			return mapper.readValue(stringJsonNode, new TypeReference<List<String>>(){});
		} catch (IOException e) {
			e.printStackTrace();
		}
		return null;
	}
}
