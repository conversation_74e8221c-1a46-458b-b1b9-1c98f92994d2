package com.persagy.finein.fnmanage.compute.thread;

import com.persagy.core.thread.BaseThread;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.fnmanage.compute.handler.FNTComputeTotalPowerHandler;
import com.persagy.finein.service.FNBuildingService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月22日 下午12:58:25
 * 
 * 说明:租户总功率计算线程
 */
@Component("FNTComputeTotalPowerThread")
public class FNTComputeTotalPowerThread extends BaseThread {

	private static boolean CONSTANT_THREAD_IS_OPEN = true;
	private static long CONSTANT_SLEEP = 300;
	private static Integer CONSTANT_DELAY_SECOND = 1800;

	private static Logger log = Logger.getLogger(FNTComputeTotalPowerThread.class);

	@Resource(name = "FNBuildingService")
	private FNBuildingService FNBuildingService;

	@Resource(name = "FNTComputeTotalPowerHandler")
	private FNTComputeTotalPowerHandler FNTComputeTotalPowerHandler;

	private static boolean IsSysParamValueInited = false;

	@Override
	protected void business() throws Exception {
		try {
			this.initSysParamValue();

			if (!CONSTANT_THREAD_IS_OPEN) {
				this.setStop(true);
				log.error("【租户管理】租户最大功率统计线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}

			this.process();
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}
	}

	private void initSysParamValue() {
		if (IsSysParamValueInited) {
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_ComputeMaxPowerThreadIsOpen);
			if (threadIsOpen != null) {
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Integer sleep = (Integer) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_ComputeMaxPowerThreadSleepSecond);
			if (sleep != null) {
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}
		try {
			Integer delay = (Integer) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_ComputeMaxPowerThreadDelaySecond);
			if (delay != null) {
				CONSTANT_DELAY_SECOND = delay;
			}
		} catch (Exception e1) {
		}

		IsSysParamValueInited = true;
		log.error("【租户管理】租户最大功率统计线程开始运行。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}

	@Transactional(propagation = Propagation.NOT_SUPPORTED)
	private void process() {
		try {
			// 1.查询所有建筑
			List<Building> buildingList = FNBuildingService.queryList(new Building());
			if (buildingList != null && buildingList.size() > 0) {
				for (Building building : buildingList) {
					this.FNTComputeTotalPowerHandler.processBuilding(building, CONSTANT_DELAY_SECOND);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
