package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;
import com.persagy.ems.pojo.finein.FnAlarmType;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.service.FNAlarmLimitGlobalService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 报警-设置报警门限
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntAlarmLimitUpdateController extends BaseController {

    @Resource(name = "FNAlarmLimitGlobalService")
    private FNAlarmLimitGlobalService fnAlarmLimitGlobalService;

    private static final Map<String,String> EnergyTypeAlarmTypeMap = new HashMap<String,String>();

    static{
        EnergyTypeAlarmTypeMap.put(FineinConstant.EnergyType.Dian, FineinConstant.AlarmType.DIANFEIYONGBUZU);
        EnergyTypeAlarmTypeMap.put(FineinConstant.EnergyType.Shui, FineinConstant.AlarmType.SHUIFEIYONGBUZU);
        EnergyTypeAlarmTypeMap.put(FineinConstant.EnergyType.ReShui, FineinConstant.AlarmType.RESHUIFEIYONGBUZU);
        EnergyTypeAlarmTypeMap.put(FineinConstant.EnergyType.RanQi, FineinConstant.AlarmType.RANQIFEIYONGBUZU);
    }

    @RequestMapping("FNTAlarmLimitUpdateService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation= Propagation.REQUIRED,rollbackFor=Exception.class)
    public InterfaceResult alarmLimitUpdate(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            List<Object> alarmLimitList = (List<Object>)dto.get("alarmLimitList");

            if(dto.get("alarmLimitList") == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Map<String, FnAlarmType> alarmTypeMap = new HashMap<String,FnAlarmType>();

            for(FnAlarmType alarmType : ConstantDBBaseData.AlarmTypeList){
                alarmTypeMap.put(alarmType.getId(), alarmType);
            }

            List<FnAlarmLimitGlobal> globalList = fnAlarmLimitGlobalService.queryList();
            Map<String,FnAlarmLimitGlobal> globalMap = new HashMap<String,FnAlarmLimitGlobal>();
            if(globalList != null && globalList.size() > 0){
                for(FnAlarmLimitGlobal obj : globalList){
                    globalMap.put(obj.getAlarmTypeId(), obj);
                }
            }

            for(int i = 0;i< alarmLimitList.size();i++){
                Map<String,Object> alarmLimitMap = (Map<String,Object>)alarmLimitList.get(i);
                String alarmTypeId = (String)alarmLimitMap.get("alarmTypeId");
                Double limitValue = DoubleFormatUtil.Instance().getDoubleData(alarmLimitMap.get("limitValue"));

                if(globalMap.containsKey(alarmTypeId)){//更新
                    FnAlarmLimitGlobal query = new FnAlarmLimitGlobal();
                    query.setId(globalMap.get(alarmTypeId).getId());

                    FnAlarmLimitGlobal update = new FnAlarmLimitGlobal();
                    update.setLimitValue(limitValue);
                    fnAlarmLimitGlobalService.update(query, update);
                }else{
                    FnAlarmLimitGlobal saveObj = new FnAlarmLimitGlobal();
                    saveObj.setId(UUID.randomUUID().toString());
                    saveObj.setAlarmTypeId(alarmTypeId);

                    saveObj.setParentAlarmTypeId(alarmTypeMap.get(alarmTypeId).getParentId());
                    saveObj.setTreeId(alarmTypeMap.get(alarmTypeId).getTreeId());

                    saveObj.setLimitValue(limitValue);
                    saveObj.setIsOpen(1);
                    fnAlarmLimitGlobalService.save(saveObj);
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTAlarmLimitUpdateService");
        }
    }
}
