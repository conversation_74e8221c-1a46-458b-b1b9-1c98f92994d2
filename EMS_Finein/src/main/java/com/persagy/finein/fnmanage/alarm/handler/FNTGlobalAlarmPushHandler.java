package com.persagy.finein.fnmanage.alarm.handler;

import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年10月21日 下午1:45:10
 * 
 * 说明:
 */
public interface FNTGlobalAlarmPushHandler {

	/**
	 * Description:同步全局报警
	 * 
	 * @param addList
	 *            void
	 * <AUTHOR>
	 * @throws Exception 
	 * @since 2019年6月17日: 下午5:47:09 Update By 邵泓博 2019年6月17日: 下午5:47:09
	 */

	@SuppressWarnings("rawtypes")
	public void addGlobalAlarm(String url, Map param, String alarmId) throws Exception;


	 /**
	 * Description:
	 * @param global_Alarm_Url
	 * @param updateStatusList
	 * @param alarmIdList void
	 * <AUTHOR>
	 * @throws Exception
	 * @since 2019年6月17日: 下午5:55:23
	 * Update By 邵泓博 2019年6月17日: 下午5:55:23
	 */

	public void updateGlobalAlarm(String global_Alarm_Url, Map param, String alarmId) throws Exception;

}
