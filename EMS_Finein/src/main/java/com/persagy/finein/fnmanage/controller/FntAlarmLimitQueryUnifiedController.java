package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.fnmonitor.controller.FnmAlarmLimitController;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntAlarmLimitQueryUnifiedController extends BaseController {


    /**
     * 此处作记录，老项目中调用FNMAlarmLimitListService 新项目调用 FNMAlarmLimitListController
     * 达到同样的效果
     */
    @Resource(name = "fnmAlarmLimitController")
    private FnmAlarmLimitController fnmAlarmLimitController;

    @RequestMapping("FNTAlarmLimitQueryUnifiedService")
    @ResponseBody
    public InterfaceResult alarmLimitQueryUnified(@RequestParam(value="jsonString") String jsonString) {
        try {
            InterfaceResult interfaceResult = fnmAlarmLimitController.alarmLimitList(jsonString);
            return Result.SUCCESS(interfaceResult.getContent());
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTAlarmLimitQueryUnifiedService");
        }
    }
}
