package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnRecordPrePayErrorOrder;
import com.persagy.ems.pojo.finein.FnRecordPrePayForOtherSystem;
import com.persagy.finein.enumeration.EnumErrorOrderStatus;
import com.persagy.finein.service.FNRecordPrePayErrorOrderService;
import com.persagy.finein.service.FNRecordPrePayForOtherSystemService;
import com.persagy.finein.service.FNUserService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：异常账单设置
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntErrorOrderSetController extends BaseController {

    @Resource(name = "FNRecordPrePayForOtherSystemService")
    private FNRecordPrePayForOtherSystemService fnRecordPrePayForOtherSystemService;

    @Resource(name = "FNRecordPrePayErrorOrderService")
    private FNRecordPrePayErrorOrderService fnRecordPrePayErrorOrderService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @RequestMapping("FNTErrorOrderSetService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult errorOrderSet(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String orderId = (String) dto.get("orderId");
            Integer type = (Integer) dto.get("type");
            String userId = this.getParamUserId(dto);
            if (orderId == null || type == null || userId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            if (type == 0) {
                {// 远程充值记录状态修改
                    FnRecordPrePayForOtherSystem query = new FnRecordPrePayForOtherSystem();
                    query.setOrderId(orderId);
                    FnRecordPrePayForOtherSystem update = new FnRecordPrePayForOtherSystem();
                    update.setStatus(type);
                    update.setOperateTime(new Date());
                    fnRecordPrePayForOtherSystemService.update(query, update);
                }
            }
            {// 异常账单状态修改
                FnRecordPrePayErrorOrder query = new FnRecordPrePayErrorOrder();
                query.setOrderId(orderId);
                FnRecordPrePayErrorOrder update = new FnRecordPrePayErrorOrder();
                update.setStatus(
                        type == 0 ? EnumErrorOrderStatus.PrePayAgain.getValue() : EnumErrorOrderStatus.Close.getValue());
                update.setOperateTime(new Date());
                DTOUser user = fnUserService.queryUserByUserId(userId);
                update.setOperateUserId(user.getUserId());
                update.setOperateUserName(user.getUserName());
                fnRecordPrePayErrorOrderService.update(query, update);
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTErrorOrderSetService");
        }
    }


}
