package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOTenantDetails;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnPriceTemplate;
import com.persagy.ems.pojo.finein.FnRemoteRechargeStatus;
import com.persagy.ems.pojo.finein.FnTenantPrePayMeterParam;
import com.persagy.finein.service.FNRemoteRechargeStatusService;
import com.persagy.finein.service.FNTenantPrePayMeterParamService;
import com.persagy.finein.service.FNTenantPriceService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：租户-租户详情
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntCommonTenantInfoByMeterIdController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantPrePayMeterParamService")
    private FNTenantPrePayMeterParamService fnTenantPrePayMeterParamService;

    @Resource(name = "FNRemoteRechargeStatusService")
    private FNRemoteRechargeStatusService fnRemoteRechargeStatusService;

    @Resource(name = "FNTenantPriceService")
    private FNTenantPriceService fnTenantPriceService;


    @RequestMapping("FNTCommonTenantInfoByMeterIdService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonTenantInfoByMeterId(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String meterId = (String) dto.get("meterId");

            if (meterId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull("meterId"));
            }

            List<DTOTenantDetails> tenantList = fnTenantService.queryByMeterId(meterId);
            if (tenantList != null) {
                for (int i = 0; i < tenantList.size(); i++) {
                    DTOTenantDetails dtoTenantDetails = tenantList.get(i);

                    {// 查询租户微信充值是否被限制
                        FnRemoteRechargeStatus query = new FnRemoteRechargeStatus();
                        query.setTenantId(dtoTenantDetails.getTenantId());
                        List<FnRemoteRechargeStatus> list = fnRemoteRechargeStatusService.query(query);
                        if (list != null && list.size() > 0) {
                            dtoTenantDetails.setRemoteRechargeStatus(list.get(0).getRemoteRechargeStatus());
                        } else {
                            dtoTenantDetails.setRemoteRechargeStatus(1);
                        }
                    }
                    FnTenantPrePayMeterParam meterPreParam = fnTenantPrePayMeterParamService.queryMeterPreParam(
                            dtoTenantDetails.getTenantId(), meterId, dtoTenantDetails.getEnergyTypeId());
                    if (meterPreParam != null) {
                        String remainDays = meterPreParam.getRemainDays();
                        if (remainDays != null) {
                            String[] split = remainDays.split("~");
                            dtoTenantDetails.setRemainMinDays(Integer.valueOf(split[0]));
                            dtoTenantDetails.setRemainMaxDays(Integer.valueOf(split[1]));
                        }
                        dtoTenantDetails.setIsAlarm(meterPreParam.getIsAlarm());
                        dtoTenantDetails.setRemainData(meterPreParam.getRemainData());
                    }
                    //租户价格信息
                    {
                        FnPriceTemplate priceTemplate = fnTenantPriceService.query(dtoTenantDetails.getTenantId(),dtoTenantDetails.getEnergyTypeId());
                        dtoTenantDetails.setPriceId(priceTemplate.getId());
                        dtoTenantDetails.setPriceEnergyTypeId(priceTemplate.getEnergyTypeId());
                        dtoTenantDetails.setPriceName(priceTemplate.getName());
                        dtoTenantDetails.setPriceCreateUserId(priceTemplate.getCreateUserId());
                        dtoTenantDetails.setPriceCreateTime(priceTemplate.getCreateTime());
                        dtoTenantDetails.setPriceType(priceTemplate.getType());
                        dtoTenantDetails.setPriceContent(priceTemplate.getContent());
                        dtoTenantDetails.setPriceLastUpdateTime(priceTemplate.getLastUpdateTime());
                        dtoTenantDetails.setPriceIsValid(priceTemplate.getIsValid());
                        dtoTenantDetails.setPriceInvalidTime(priceTemplate.getInvalidTime());
                        dtoTenantDetails.setPriceLastUpdateUserId(priceTemplate.getLastUpdateUserId());
                    }
                }
            }
            Map<String, Object> hashMap = new HashMap<String, Object>();
            hashMap.put("tenantList", tenantList);
            content.add(hashMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTCommonTenantInfoByMeterIdService");
        }
    }


}
