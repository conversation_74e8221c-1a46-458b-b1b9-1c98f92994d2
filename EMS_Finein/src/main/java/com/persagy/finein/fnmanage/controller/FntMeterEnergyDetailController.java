package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.dictionary.meter.DictionaryFunction;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.ems.pojo.meterdata.MeterData;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumMeterType;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.service.FNMeterDataService;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNProjectService;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：物业监控
 * 接口名：租户详情-获取仪表列表
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntMeterEnergyDetailController extends BaseController {

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNMeterDataService")
    private FNMeterDataService fnMeterDataService;

    @Resource(name = "FNProjectService")
    private FNProjectService fnProjectService;

    @RequestMapping("FNTMeterEnergyDetailService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult meterEnergyDetail(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String meterId = (String)dto.get("meterId");
            String energyTypeId = (String)dto.get("energyTypeId");
            String timeFrom = (String)dto.get("timeFrom");
            String timeTo = (String)dto.get("timeTo");
            Integer density = (Integer)dto.get("density");//0 15分钟 1时  2日   4月   5年

            if(meterId == null || timeFrom == null || timeTo == null || density == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Date from = standard.parse(timeFrom);
            Date to = standard.parse(timeTo);

            int timeType = EnumTimeType.T1.getValue();
            if(density.intValue() == 0){
                timeType = EnumTimeType.T0.getValue();
            }else if(density.intValue() == 1){
                timeType = EnumTimeType.T1.getValue();
            }else{
                timeType = EnumTimeType.T2.getValue();
            }
            Map<String,Object> contentMap = new HashMap<String,Object>();
            contentMap.put("unit", UnitUtil.getCumulantUnit(energyTypeId));
            List<Object> resultFunctionList = new ArrayList<Object>();
            contentMap.put("functionList", resultFunctionList);

            //查询仪表协议,对应累计量功能号
            Map<String,Double> dateMap = this.getDateMap(from, to, timeType);
            Map<Integer,Map<String,Double>> allFunctionDataMap = new LinkedHashMap<>();
            Project project = fnProjectService.queryProject();
            if(project != null){
                List<Integer> functionList = new ArrayList<>();
                FnMeter fnMeter = fnMeterService.queryMeterById(meterId);
                Double ct = 1.0;
                JSONObject extendObj = null;
                try {
                    extendObj = (JSONObject) JSONValue.parse(fnMeter.getExtend());
                    Double tempCt = DoubleFormatUtil.Instance().getDoubleData(extendObj.get("ljlIsCt"));
                    if(tempCt != null && tempCt.intValue() == 1){
                        ct = fnMeter.getRadio();
                    }
                } catch (Exception e) {
                }
                if(fnMeter == null){
                    functionList.add(FunctionTypeUtil.getCumulantFunctionId(energyTypeId));
                }else{
                    if(fnMeter.getProtocolId() == null || ConstantDBBaseData.ProtocolMap.get(fnMeter.getProtocolId()) == null){
                        functionList.add(FunctionTypeUtil.getCumulantFunctionId(energyTypeId));
                    }else{
                        if(fnMeter.getMeterType().intValue() == EnumMeterType.Common.getValue().intValue()){
                            functionList.add(FunctionTypeUtil.getCumulantFunctionId(energyTypeId));
                        }else{
                            if(fnMeter.getEnergyTypeId().equals(FineinConstant.EnergyType.Dian)){
                                functionList.addAll(FunctionTypeUtil.getCumulantDianMultiple().values());
                            }else{
                                functionList.add(FunctionTypeUtil.getCumulantFunctionId(energyTypeId));
                            }
                        }
                    }
                }
                if(functionList.size() > 0){
                    for(Integer functionId : functionList){
                        Map<String,Double> newDataMap = new LinkedHashMap<>(dateMap);
                        allFunctionDataMap.put(functionId, newDataMap);
                        List<MeterData> meterDataList = fnMeterDataService.queryMeterDataGteLt(project.getId(), meterId, functionId,timeType, from, to);
                        if(meterDataList != null){
                            for(MeterData data : meterDataList){
                                String time = standard.format(data.getReceivetime());
                                if(newDataMap.containsKey(time)){
                                    if(data.getData() != null){
                                        newDataMap.put(time, data.getData() * ct);
                                    }
                                }
                            }
                        }
                    }
                }
            }


            for(Map.Entry<Integer,Map<String,Double>> entry : allFunctionDataMap.entrySet()){
                Map<String,Object> functionObj = new LinkedHashMap<>();
                functionObj.put("functionId", entry.getKey());
                DictionaryFunction dictionaryFunction = ConstantDBBaseData.DictionaryFunctionMap.get(entry.getKey()+"");
                functionObj.put("functionName", dictionaryFunction == null ? "" : dictionaryFunction.getName());
                List<Object> dataList = new ArrayList<>();
                functionObj.put("dataList", dataList);

                for(Map.Entry<String, Double> subEntrgy : entry.getValue().entrySet()){
                    Map<String,Object> map = new HashMap<String,Object>();
                    map.put("x", subEntrgy.getKey());
                    map.put("y", subEntrgy.getValue());
                    dataList.add(map);
                }
                resultFunctionList.add(functionObj);
            }

            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTMeterEnergyDetailService");
        }
    }

    public Map<String,Double> getDateMap(Date timeFrom,Date timeTo,int timeType){
        Map<String,Double> result = new LinkedHashMap<String,Double>();
        Calendar c = Calendar.getInstance();
        c.setTime(timeFrom);
        while(c.getTime().getTime() < timeTo.getTime()){
            result.put(standard.format(c.getTime()), null);
            if(timeType == EnumTimeType.T0.getValue().intValue()){
                c.add(Calendar.MINUTE, 15);
            }else if(timeType == EnumTimeType.T1.getValue().intValue()){
                c.add(Calendar.HOUR, 1);
            }else if(timeType == EnumTimeType.T2.getValue().intValue()){
                c.add(Calendar.DATE, 1);
            }else{
                c.add(Calendar.HOUR, 1);
            }
        }
        return result;
    }

}
