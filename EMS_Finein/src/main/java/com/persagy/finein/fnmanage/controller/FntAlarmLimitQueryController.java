package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;
import com.persagy.finein.service.FNAlarmLimitGlobalService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 报警-查询报警门限
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntAlarmLimitQueryController extends BaseController {

    @Resource(name = "FNAlarmLimitGlobalService")
    private FNAlarmLimitGlobalService fnAlarmLimitGlobalService;


    private static final Map<String,String> EnergyTypeAlarmTypeMap = new LinkedHashMap<String,String>();

    static{
        EnergyTypeAlarmTypeMap.put(FineinConstant.EnergyType.Dian, FineinConstant.AlarmType.DIANFEIYONGBUZU);
        EnergyTypeAlarmTypeMap.put(FineinConstant.EnergyType.Shui, FineinConstant.AlarmType.SHUIFEIYONGBUZU);
        EnergyTypeAlarmTypeMap.put(FineinConstant.EnergyType.ReShui, FineinConstant.AlarmType.RESHUIFEIYONGBUZU);
        EnergyTypeAlarmTypeMap.put(FineinConstant.EnergyType.RanQi, FineinConstant.AlarmType.RANQIFEIYONGBUZU);
    }

    @RequestMapping("FNTAlarmLimitQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult alarmLimitQuery(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            List<FnAlarmLimitGlobal> globalList = fnAlarmLimitGlobalService.queryList();
            Map<String,FnAlarmLimitGlobal> globalMap = new HashMap<String,FnAlarmLimitGlobal>();
            if(globalList != null && globalList.size() > 0){
                for(FnAlarmLimitGlobal obj : globalList){
                    globalMap.put(obj.getAlarmTypeId(), obj);
                }
            }

            List<Object> typeList = new ArrayList<Object>();
            for(Map.Entry<String, String> entry : EnergyTypeAlarmTypeMap.entrySet()){

                Map<String,Object> typeObj = new HashMap<String,Object>();
                typeObj.put("alarmTypeId", entry.getValue());
                typeObj.put("limitValue", globalMap.containsKey(entry.getValue()) ? globalMap.get(entry.getValue()).getLimitValue() : null);

                typeList.add(typeObj);
            }

            content.addAll(typeList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTAlarmLimitQueryService");
        }
    }
}
