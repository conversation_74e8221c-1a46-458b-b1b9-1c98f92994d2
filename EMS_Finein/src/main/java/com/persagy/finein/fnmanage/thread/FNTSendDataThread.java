package com.persagy.finein.fnmanage.thread;

import com.persagy.communication.entity.Packet;
import com.persagy.communication.mina.udp.client.UDPClientManager;
import com.persagy.communication.util.IClientManager;
import com.persagy.core.thread.BaseThread;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnSendData;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.finein.communication.util.PackageUtil;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumOriginalDataType;
import com.persagy.finein.service.FNProjectService;
import com.persagy.finein.service.FNSendDataService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月18日 下午7:56:04

* 说明: 发送数据线程
*/
@Component("FNTSendDataThread")
public class FNTSendDataThread extends BaseThread{

	private static boolean CONSTANT_THREAD_IS_OPEN = true;
	private static int CONSTANT_SLEEP = 30;
	
	private static String CONSTANT_SendDataCumulantClientIp = "127.0.0.1";
	private static int CONSTANT_SendDataCumulantClientPort = 8280;
	private static String CONSTANT_SendDataCumulantServerIp = "127.0.0.1";
	private static int CONSTANT_SendDataCumulantServerPort = 80;
	private static String CONSTANT_SendDataInstantClientIp = "127.0.0.1";
	private static int CONSTANT_SendDataInstantClientPort = 8281;
	private static String CONSTANT_SendDataInstantServerIp = "127.0.0.1";
	private static int CONSTANT_SendDataInstantServerPort = 8887;
	
	private static int CONSTANT_SendDataMaxSize = 1000;
	private static boolean CONSTANT_SendDataIsSeparate = false;
	private static String CONSTANT_SendDataSeparateBegin = "(";
	private static String CONSTANT_SendDataSeparateEnd = ")";
	
	
	private static Logger log = Logger.getLogger(FNTSendDataThread.class);
	
	private static boolean IsSysParamValueInited = false;
	private static boolean IsSendClientInited = false;
	
	private static IClientManager CumulantClientManager = null;
	private static IClientManager InstantClientManager = null;
	
	private static int QueryLimit = 200;//每次查询条数限制
	private static int PackageTimeOut = 30000;//返回数据30秒超时
	
	@Resource(name="FNSendDataService")
	private FNSendDataService FNSendDataService;

	@Resource(name="FNProjectService")
	private FNProjectService FNProjectService;
	
	@Override
	protected void business() throws Exception {
		try {
			this.initSysParamValue();
			
			if(!CONSTANT_THREAD_IS_OPEN){
				this.setStop(true);
				log.error("【租户管理】租户发送数据线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}
			this.initSendClient();
			
			this.process();
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}
	}
	
	private void initSysParamValue(){
		if(IsSysParamValueInited){
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataThreadIsOpen);
			if(threadIsOpen != null){
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Integer sleep = (Integer)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataThreadSleepSecond);
			if(sleep != null){
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}
		try {
			String SendDataCumulantClientIp = (String)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataCumulantClientIp);
			if(SendDataCumulantClientIp != null){
				CONSTANT_SendDataCumulantClientIp = SendDataCumulantClientIp;
			}
		} catch (Exception e1) {
		}
		try {
			Integer SendDataCumulantClientPort = (Integer)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataCumulantClientPort);
			if(SendDataCumulantClientPort != null){
				CONSTANT_SendDataCumulantClientPort = SendDataCumulantClientPort;
			}
		} catch (Exception e1) {
		}
		try {
			String SendDataCumulantServerIp = (String)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataCumulantServerIp);
			if(SendDataCumulantServerIp != null){
				CONSTANT_SendDataCumulantServerIp = SendDataCumulantServerIp;
			}
		} catch (Exception e1) {
		}
		try {
			Integer SendDataCumulantServerPort = (Integer)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataCumulantServerPort);
			if(SendDataCumulantServerPort != null){
				CONSTANT_SendDataCumulantServerPort = SendDataCumulantServerPort;
			}
		} catch (Exception e1) {
		}
		try {
			String SendDataInstantClientIp = (String)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataInstantClientIp);
			if(SendDataInstantClientIp != null){
				CONSTANT_SendDataInstantClientIp = SendDataInstantClientIp;
			}
		} catch (Exception e1) {
		}
		try {
			Integer SendDataInstantClientPort = (Integer)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataInstantClientPort);
			if(SendDataInstantClientPort != null){
				CONSTANT_SendDataInstantClientPort = SendDataInstantClientPort;
			}
		} catch (Exception e1) {
		}
		try {
			String SendDataInstantServerIp = (String)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataInstantServerIp);
			if(SendDataInstantServerIp != null){
				CONSTANT_SendDataInstantServerIp = SendDataInstantServerIp;
			}
		} catch (Exception e1) {
		}
		try {
			Integer SendDataInstantServerPort = (Integer)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataInstantServerPort);
			if(SendDataInstantServerPort != null){
				CONSTANT_SendDataInstantServerPort = SendDataInstantServerPort;
			}
		} catch (Exception e1) {
		}
		try {
			Integer SendDataMaxSize = (Integer)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataMaxSize);
			if(SendDataMaxSize != null){
				CONSTANT_SendDataMaxSize = SendDataMaxSize;
			}
		} catch (Exception e1) {
		}
		try {
			Boolean SendDataIsSeparate = (Boolean)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataIsSeparate);
			if(SendDataIsSeparate != null){
				CONSTANT_SendDataIsSeparate = SendDataIsSeparate;
			}
		} catch (Exception e1) {
		}
		try {
			String SendDataSeparateBegin = (String)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataSeparateBegin);
			if(SendDataSeparateBegin != null){
				CONSTANT_SendDataSeparateBegin = SendDataSeparateBegin;
			}
		} catch (Exception e1) {
		}
		try {
			String SendDataSeparateEnd = (String)ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendDataSeparateEnd);
			if(SendDataSeparateEnd != null){
				CONSTANT_SendDataSeparateEnd = SendDataSeparateEnd;
			}
		} catch (Exception e1) {
		}
		
		IsSysParamValueInited = true;
		log.error("【租户管理】租户发送数据线程开始。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}
	
	private void initSendClient(){
		if(IsSendClientInited){
			return;
		}
		
		if(CONSTANT_SendDataIsSeparate){
			CumulantClientManager = new UDPClientManager(CONSTANT_SendDataCumulantClientIp, CONSTANT_SendDataCumulantClientPort, CONSTANT_SendDataCumulantServerIp, CONSTANT_SendDataCumulantServerPort,
					"utf-8", CONSTANT_SendDataMaxSize, (byte)CONSTANT_SendDataSeparateBegin.charAt(0), (byte)CONSTANT_SendDataSeparateEnd.charAt(0));
			InstantClientManager = new UDPClientManager(CONSTANT_SendDataInstantClientIp, CONSTANT_SendDataInstantClientPort, CONSTANT_SendDataInstantServerIp, CONSTANT_SendDataInstantServerPort,
					"utf-8", CONSTANT_SendDataMaxSize, (byte)CONSTANT_SendDataSeparateBegin.charAt(0), (byte)CONSTANT_SendDataSeparateEnd.charAt(0));
		}else{
			CumulantClientManager = new UDPClientManager(CONSTANT_SendDataCumulantClientIp, CONSTANT_SendDataCumulantClientPort, CONSTANT_SendDataCumulantServerIp, CONSTANT_SendDataCumulantServerPort,
					"utf-8", CONSTANT_SendDataMaxSize);
			InstantClientManager = new UDPClientManager(CONSTANT_SendDataInstantClientIp, CONSTANT_SendDataInstantClientPort, CONSTANT_SendDataInstantServerIp, CONSTANT_SendDataInstantServerPort,
					"utf-8", CONSTANT_SendDataMaxSize);
		}
		
		CumulantClientManager.Start();
		InstantClientManager.Start();
		
		IsSendClientInited = true;
	}
	
	private void process(){
		try {
			Project project = FNProjectService.queryProject();
			while(true){
				try {
					int count=0;
					long start = System.currentTimeMillis();//
					List<FnSendData> dataList = FNSendDataService.queryList(project.getId(), QueryLimit);
					if(dataList == null || dataList.size() == 0){
						break;
					}
					for(FnSendData sendData : dataList){
						count++;
//						System.out.println("-------------转发数据：仪表编码："+sendData.getMeterId()+",功能号："+sendData.getFunctionId()+"数据值："+sendData.getData());
						try {
							long packageId = 1;
							if(sendData.getDataType().intValue() == EnumOriginalDataType.MonthData.getDataType().intValue()){
								packageId = PackageUtil.GenerateCalPackageId();
							}else{
								packageId = PackageUtil.GenerateInsPackageId();
							}
									
							String myPackage = PackageUtil.Instance().Package(sendData.getBuildingId(), sendData, packageId);
//							System.err.println("-------------转发数据："+myPackage);
							
							Packet packet = null;
							
							if(sendData.getDataType().intValue() == EnumOriginalDataType.MonthData.getDataType().intValue()){
								long send = System.currentTimeMillis();
								CumulantClientManager.AppendToSend(new Packet(myPackage));
								for(int i=0;i<PackageTimeOut;i++){
									packet = CumulantClientManager.PopRece();
									if(packet != null){
										break;
									}else{
										Thread.sleep(1);
									}
								}
								long send2 = System.currentTimeMillis();
								if(packet == null){
									Thread.sleep(1);
									break;
								}
								System.out.println("--------------------转发单条数据时间："+(send2-send)+"--------------");
								boolean result = PackageUtil.Instance().Parse(packet.packetString, packageId);

								System.out.println("---------------------转发数据packetString："+packet.packetString);
								System.out.println("---------------------转发数据packageId："+packageId);
								System.out.println("---------------------转发数据结果："+result);
								
								if(result){
									FNSendDataService.removeById(sendData.getId());
								}else{
									Thread.sleep(1);
									break;
								}
							}else{
								InstantClientManager.AppendToSend(new Packet(myPackage));
								FNSendDataService.removeById(sendData.getId());
								Thread.sleep(1);
							}
						} catch (Exception e) {
							e.printStackTrace();
						}
					}
//					long end = System.currentTimeMillis();
//					System.out.println("------------------转发"+count+"条数据共计时间："+(end-start)+"------------");
					Thread.sleep(100);
				} catch (Exception e) {
					Thread.sleep(1 * 1000);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}

