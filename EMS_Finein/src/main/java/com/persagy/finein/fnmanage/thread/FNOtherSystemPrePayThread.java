package com.persagy.finein.fnmanage.thread;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.thread.BaseThread;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnCommonPrePaySystemParam;
import com.persagy.ems.pojo.finein.FnRecordPrePayForOtherSystem;
import com.persagy.finein.communication.exception.MeterSetException;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumPrePayStatus;
import com.persagy.finein.service.FNCommonPrePaySystemParamService;
import com.persagy.finein.service.FNRecordPrePayForOtherSystemService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 说明: 处理远程充值线程
 */
@Component("FNOtherSystemPrePayThread")
public class FNOtherSystemPrePayThread extends BaseThread {

	private static boolean CONSTANT_THREAD_IS_OPEN = true;

	private static int CONSTANT_SLEEP = 10;

	private static int SPACE_SECOND = 10;

	private static boolean IsSysParamValueInited = false;

	private static Double minMoney = 1.0;

	private static Map<String, FnCommonPrePaySystemParam> systemCodeMap = new HashMap<String, FnCommonPrePaySystemParam>();

	private static Logger log = Logger.getLogger(FNOtherSystemPrePayThread.class);

	@Resource(name = "FNOtherSystemPrePayHandler")
	private com.persagy.finein.fnmanage.thread.handler.FNOtherSystemPrePayHandler FNOtherSystemPrePayHandler;

	@Resource(name = "FNCommonPrePaySystemParamService")
	private FNCommonPrePaySystemParamService FNCommonPrePaySystemParamService;

	@Resource(name = "FNRecordPrePayForOtherSystemService")
	private FNRecordPrePayForOtherSystemService FNRecordPrePayForOtherSystemService;

	@Override
	public void business() throws Exception {
		try {
			this.initSysParamValue();
			if (!CONSTANT_THREAD_IS_OPEN) {
				this.setStop(true);
				log.error("【租户管理】远程充值处理线程停止。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
				return;
			}
			FnRecordPrePayForOtherSystem query = new FnRecordPrePayForOtherSystem();
			query.setStatus(EnumPrePayStatus.NO_PROCESSE.getValue());
			query.setSort("createTime", EMSOrder.Asc);
			List<FnRecordPrePayForOtherSystem> list = FNRecordPrePayForOtherSystemService.query(query);
			if (list != null && list.size() > 0) {
				for (FnRecordPrePayForOtherSystem record : list) {
					Long ProcessPrePayCount = (Long) ConstantDBBaseData.SysParamValueMap
							.get(FineinConstant.SysParamValueKey.Id_ProcessPrePayCount);
					while (ProcessPrePayCount > 0) {// 尝试充值十次
						try {
							if (FNOtherSystemPrePayHandler.processRecord(record, minMoney, systemCodeMap)) {
								break;
							}
						} catch (MeterSetException e) {
							e.printStackTrace();
						} catch (Exception e) {
							e.printStackTrace();
						}
						ProcessPrePayCount--;
						Thread.sleep(SPACE_SECOND * 1000);
						if (ProcessPrePayCount == 0) {// 充值失败
							FNOtherSystemPrePayHandler.processFail(record, systemCodeMap);
							break;
						}
					}
				}
			}
			Thread.sleep(CONSTANT_SLEEP * 1000);
		} catch (Exception e) {
			e.printStackTrace();
			try {
				Thread.sleep(CONSTANT_SLEEP * 1000);
			} catch (Exception e1) {
			}
		}
	}

	private void initSysParamValue() throws Exception {

		if (IsSysParamValueInited) {
			return;
		}
		try {
			Boolean threadIsOpen = (Boolean) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_FNOtherSystemPrePayThreadIsOpen);
			if (threadIsOpen != null) {
				CONSTANT_THREAD_IS_OPEN = threadIsOpen;
			}
		} catch (Exception e1) {
		}
		try {
			Integer sleep = (Integer) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_FNOtherSystemPrePayThreadSleepSecond);
			if (sleep != null) {
				CONSTANT_SLEEP = sleep;
			}
		} catch (Exception e1) {
		}
		try {
			Double money = (Double) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_FNOtherSystemPrePayMaxMoney);
			if (money != null) {
				minMoney = money;
			}
		} catch (Exception e1) {
		}

		try {
			Integer spaceSecond = (Integer) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_FNOtherSystemPrePaySpaceSecond);
			if (spaceSecond != null) {
				SPACE_SECOND = spaceSecond;
				log.error("【租户管理】远程充值处理单次充值间隔为" + spaceSecond + "秒。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
			}
		} catch (Exception e1) {
		}

		List<FnCommonPrePaySystemParam> list = FNCommonPrePaySystemParamService.query(new FnCommonPrePaySystemParam());
		if (list != null && list.size() > 0) {
			for (FnCommonPrePaySystemParam fnCommonPrePaySystemParam : list) {
				systemCodeMap.put(fnCommonPrePaySystemParam.getSystemCode(), fnCommonPrePaySystemParam);
			}
		}
		IsSysParamValueInited = true;
		log.error("【租户管理】远程充值处理线程开始。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。。");
	}

}
