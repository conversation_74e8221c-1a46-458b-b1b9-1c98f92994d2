package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.MD5Tools;
import com.persagy.ems.pojo.finein.FnRecordPrePayForOtherSystem;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.service.FNRecordPrePayForOtherSystemService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：跨系统远程充值记录查询
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntCommonPrePayRecordController extends BaseController {

    @Resource(name = "FNRecordPrePayForOtherSystemService")
    private FNRecordPrePayForOtherSystemService fnRecordPrePayForOtherSystemService;

    @RequestMapping("FNTCommonPrePayRecordService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonPrePayRecord(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String systemCode = (String)dto.get("systemCode");
            String serialNumber = (String)dto.get("serialNumber");
            String buildingId = (String)dto.get("buildingId");
            String tenantId = (String)dto.get("tenantId");
            String energyTypeId = (String)dto.get("energyTypeId");
            String timeFrom = (String)dto.get("timeFrom");
            String timeTo = (String)dto.get("timeTo");

            if(tenantId == null || energyTypeId == null
                    || timeFrom == null
                    || buildingId == null
                    || systemCode == null
                    || serialNumber == null
                    || timeTo == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            //验证序列号
            String str = FineinConstant.systemSerialNumberMap.get(systemCode);
            if(str==null){
                throw new Exception("序列号不存在,请先生成序列号");
            }else{
                String[] split = str.split("_");
                StringBuffer stringBuffer = new StringBuffer();
                StringBuffer append = stringBuffer.append(split[0]).append(split[1]);
                if(!serialNumber.equals(MD5Tools.MD5(append.toString()))){
                    throw new Exception("序列号或密钥不匹配");
                }
                if(new Date().getTime()-Long.parseLong(split[2])>60*5*1000){
                    throw new Exception("序列号已过期");
                }
            }
            List<Object> objList = new ArrayList<Object>();
            FnRecordPrePayForOtherSystem query = new FnRecordPrePayForOtherSystem();
            query.setSystemCode(systemCode);
            query.setBuildingId(buildingId);
            query.setTenantId(tenantId);
            query.setEnergyTypeId(energyTypeId);
            query.setSpecialOperation("operateTime", SpecialOperator.$gte, timeFrom);
            query.setSpecialOperation("operateTime", SpecialOperator.$lt, timeTo);

            List<FnRecordPrePayForOtherSystem> list = fnRecordPrePayForOtherSystemService.query(query);
            if(list!=null&&list.size()>0){
                for (FnRecordPrePayForOtherSystem record : list) {
                    Double value;
                    int billingType;
                    if(record.getAmount()==null){
                        value=record.getMoney();
                        billingType= EnumPrepayChargeType.Qian.getValue();
                    }else{
                        value=record.getAmount();
                        billingType=EnumPrepayChargeType.Liang.getValue();
                    }
                    Map<String,Object> obj = new HashMap<String,Object>();
                    obj.put("orderId", record.getOrderId());
                    obj.put("tenantId", record.getTenantId());
                    obj.put("type", record.getType());
                    obj.put("code", record.getCode());
                    obj.put("energyTypeId", record.getEnergyTypeId());
                    obj.put("value", value);
                    obj.put("billingType", billingType);
                    obj.put("status", record.getStatus());
                    obj.put("userId", record.getUserId());
                    obj.put("userName", record.getUserName());
                    objList.add(obj);
                }
            }
            content.addAll(objList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTCommonPrePayRecordService");
        }
    }


}
