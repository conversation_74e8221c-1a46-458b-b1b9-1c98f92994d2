package com.persagy.finein.fnmanage.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnOrderExtend;
import com.persagy.ems.pojo.finein.FnRecordReturn;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 项目名：租户管理 接口名：预付费退費-退费
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntReturnPayController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNOrderIdService")
    private FNOrderIdService fnOrderIdService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @Resource(name = "FNRecordReturnService")
    private FNRecordReturnService fnRecordReturnService;

    @Resource(name = "FNTenantBackPayRecordService")
    private FNTenantBackPayRecordService fnTenantBackPayRecordService;

    @Resource(name = "FNOrderExtendService")
    private FNOrderExtendService fnOrderExtendService;

    public static Map<String,Date> timeMap=new HashMap<String,Date>();


    @RequestMapping("FNTReturnPayService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult returnPay(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String) dto.get("energyTypeId");
            Integer billingType = (Integer) dto.get("billingType");
            String tenantId = (String) dto.get("tenantId");
            String meterId = (String) dto.get("meterId");
            Integer prePayType = (Integer) dto.get("prePayType");
            Double value = DoubleFormatUtil.Instance().getDoubleData(dto.get("value"));
            Double remainData = DoubleFormatUtil.Instance().getDoubleData(dto.get("remainData"));
            String userId = this.getParamUserId(dto);

            if (tenantId == null || energyTypeId == null || value == null || billingType == null || prePayType == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            Map<String, Object> contentObj = new HashMap<String, Object>();
            if (prePayType == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
                this.processTenantPay(contentObj, tenantId, energyTypeId, billingType, value, userId, remainData);
            } else {
                processMeterPay(contentObj, tenantId, meterId,energyTypeId, billingType, value, userId);
            }
            content.add(contentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTReturnPayService");
        }
    }

    private void processMeterPay(Map<String,Object> contentObj,String tenantId, String meterId,String energyTypeId,Integer billingType,Double value,String userId) throws Exception{
        Map<String,Object> map = new HashMap<String,Object>();
        DTOUser user = fnUserService.queryUserByUserId(userId);
        map.put("user", user);
        map.put("tenantId", tenantId);
        //查询租户
        FnTenant fnTenant = fnTenantService.queryOne(tenantId);
        if(fnTenant == null){
            throw new Exception("租户不存在:"+tenantId);
        }
        map.put("tenantName", fnTenant.getName());
        map.put("value", value);
        Building building = fnBuildingService.query(fnTenant.getBuildingId());
        if(building == null){
            throw new Exception("租户所属建筑不存在:"+fnTenant.getBuildingId());
        }

        FnMeter fnMeter = fnMeterService.queryMeterById(meterId);
        if(fnMeter == null){
            throw new Exception("仪表不存在:"+meterId);
        }
        Date now = new Date();
        Date lastPayTime = timeMap.get(tenantId+"_"+meterId);
        if((now.getTime()-(lastPayTime==null?0:lastPayTime.getTime()))<1000){//退费时间少于1秒
            return;
        }
        //判断仪表是否计算变比
        Double radio = 1.0;
        try {
            Long syjIsCt = (Long) ((JSONObject) JSONValue.parse(fnMeter.getExtend())).get("syjIsCt");
            if (syjIsCt != null && syjIsCt.intValue() == 1) {
                radio = fnMeter.getRadio();
            }
        } catch (Exception e) {
        }
        ICommunication communication = (ICommunication) SystemConstant.context.getBean(fnMeter.getProtocolId());
        Integer result=0;
        Double div = div(value,radio,7);
        try {
            result = communication.returnPay(fnMeter, div.floatValue(),map);
        }catch (Exception e) {
        }
        contentObj.put("result", result);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void processTenantPay(Map<String, Object> contentObj, String tenantId, String energyTypeId,
                                  Integer billingType, Double value, String userId, Double remainData) throws Exception {
        // 查询租户
        FnTenant fnTenant = fnTenantService.queryOne(tenantId);
        if (fnTenant == null) {
            throw new Exception("租户不存在:" + tenantId);
        }

        Building building = fnBuildingService.query(fnTenant.getBuildingId());
        if (building == null) {
            throw new Exception("租户所属建筑不存在:" + fnTenant.getBuildingId());
        }

        Date now = new Date();
        Date lastPayTime = timeMap.get(tenantId+"_"+energyTypeId);
        if((now.getTime()-(lastPayTime==null?0:lastPayTime.getTime()))<1000){//退费时间少于1秒
            return;
        }
        {// 插入退费记录
            FnRecordReturn saveObj = new FnRecordReturn();
            saveObj.setId(UUID.randomUUID().toString());

            saveObj.setBuildingId(fnTenant.getBuildingId());
            saveObj.setBuildingName(building.getName());
            saveObj.setType(EnumPayBodyType.TENANT.getValue());
            saveObj.setTenantId(tenantId);
            saveObj.setTenantName(fnTenant.getName());
            saveObj.setCode(tenantId);
            saveObj.setName(fnTenant.getName());
            saveObj.setEnergyTypeId(energyTypeId);
            String orderId = fnOrderIdService.queryOrderId(EnumPayType.PREPAY.getValue(), now);
            saveObj.setOrderId(orderId);
            if (billingType.intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
                saveObj.setMoney(DoubleFormatUtil.Instance().doubleFormat(value, 5L));
            } else if (billingType.intValue() == EnumPrepayChargeType.Liang.getValue().intValue()) {
                saveObj.setAmount(DoubleFormatUtil.Instance().doubleFormat(value, 5L));
            } else {
                throw new Exception("未知的billingType:" + billingType);
            }
            String unit = UnitUtil.getCumulantUnit(energyTypeId);
            saveObj.setAmountUnit(unit);
            saveObj.setOperateTime(now);
            saveObj.setUserId(userId);
            DTOUser user = fnUserService.queryUserByUserId(userId);
            saveObj.setUserId(userId);
            saveObj.setUserName(user == null ? null : user.getUserName());
            saveObj.setCreateTime(now);

            fnRecordReturnService.save(saveObj);
            {// 查询
                FnOrderExtend query = new FnOrderExtend();
                query.setTenantId(tenantId);
                FnOrderExtend orderExtend = fnOrderExtendService.queryLastOrderExtend(query);
                if (orderExtend != null) {
                    remainData = orderExtend.getRemainData();
                }
            }
            // 账单扩展表
            FnOrderExtend save = new FnOrderExtend();
            save.setData(DoubleFormatUtil.Instance().doubleFormat(value, 5L));
            save.setOrderId(orderId);
            double sub = this.sub(remainData, value);
            save.setRemainData(sub);
            save.setTenantId(tenantId);
            save.setData(DoubleFormatUtil.Instance().doubleFormat(value, 5L));
            save.setChargeType(billingType);
            save.setOperateTime(now);
            save.setOperateType(EnumPrePayOrReturn.RETURN.getValue());
            fnOrderExtendService.save(save);
        }

        {
            double sub = this.sub(0.0, value);
            Date timeFrom = new Date(now.getTime() / (FineinConstant.Time.Minute_15) * (FineinConstant.Time.Minute_15));
            fnTenantBackPayRecordService.saveData(building.getId(), tenantId, energyTypeId, EnumTimeType.T0,
                    EnumPrepayChargeType.valueOf(billingType), timeFrom, sub);
        }
        timeMap.put(tenantId+"_"+energyTypeId, now);
        contentObj.put("result", 0);
    }

    public double sub(double value1, double value2) {//减法
        BigDecimal b1 = new BigDecimal(Double.valueOf(value1).toString());
        BigDecimal b2 = new BigDecimal(Double.valueOf(value2).toString());
        return b1.subtract(b2).doubleValue();
    }

    public double add(double value1, double value2) {//加法
        BigDecimal b1 = new BigDecimal(Double.valueOf(value1).toString());
        BigDecimal b2 = new BigDecimal(Double.valueOf(value2).toString());
        return b1.add(b2).doubleValue();
    }

    public  Double div(double d1,double d2,int len) {// 进行除法运算
        BigDecimal b1 = new BigDecimal(d1);
        BigDecimal b2 = new BigDecimal(d2);
        return b1.divide(b2,len,BigDecimal.ROUND_HALF_UP).doubleValue();
    }
}
