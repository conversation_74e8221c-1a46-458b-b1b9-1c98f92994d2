package com.persagy.finein.fnmanage.controller;

import com.persagy.ac.service.AcSystemPermissionService;
import com.persagy.ac.service.AcSystemUserPermissionService;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.ac.AcSystemPermission;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：通用-查询当前用户权限
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FntCommonUserPermissionController extends BaseController {

    @Resource(name = "AcSystemUserPermissionService")
    private AcSystemUserPermissionService acSystemUserPermissionService;

    @Resource(name = "AcSystemPermissionService")
    private AcSystemPermissionService acSystemPermissionService;

    @RequestMapping("FNTCommonUserPermissionService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonUserPermission(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String permissionId = (String) dto.get("permissionId");
            List<Map<String, Object>> contentList = new ArrayList<>();
            Map user = (Map) dto.get("puser");
            // 查询所有权限
            AcSystemPermission query = new AcSystemPermission();
            query.setProductId("Finein");
            List<AcSystemPermission> permissionList = acSystemPermissionService.query(query);
            if ("persagyAdmin".equals((String) user.get("name"))) {// 最高权限
                for (AcSystemPermission acSystemPermission : permissionList) {
                    if (permissionId != null) {
                        if (!permissionId.equals(acSystemPermission.getId())) {
                            continue;
                        }
                    }
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("permissionId", acSystemPermission.getId());
                    map.put("permissionName", acSystemPermission.getName());
                    map.put("systemName", acSystemPermission.getProductId());
                    map.put("isHave", 1);
                    contentList.add(map);
                }
                content.addAll(contentList);
                return Result.SUCCESS(content);
            } else {
                Map<String, AcSystemPermission> permissionMap = acSystemUserPermissionService
                        .queryByUserId((String) user.get("id"));
                for (AcSystemPermission acSystemPermission : permissionList) {
                    if (permissionId != null) {
                        if (!permissionId.equals(acSystemPermission.getId())) {
                            continue;
                        }
                    }
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("permissionId", acSystemPermission.getId());
                    map.put("permissionName", acSystemPermission.getName());
                    map.put("systemName", acSystemPermission.getProductId());
                    if (permissionMap.containsKey(acSystemPermission.getId())) {
                        map.put("isHave", 1);// 有
                    } else {
                        map.put("isHave", 0);
                    }
                    contentList.add(map);
                }
            }
            content.addAll(contentList);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTCommonUserPermissionService");
        }
    }


}
