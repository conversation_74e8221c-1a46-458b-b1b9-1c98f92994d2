package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.finein.FnEnergyType;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.service.FNEnergyTypeService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理
 * 接口名：通用-查询能源类型
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntCommonEnergyTypeListController extends BaseController {

    @Resource(name = "FNEnergyTypeService")
    private FNEnergyTypeService fnEnergyTypeService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @RequestMapping("FNTCommonEnergyTypeListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonEnergyTypeList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId =(String)dto.get("tenantId");
            if(tenantId==null||"".equals(tenantId)){
                content.addAll(fnEnergyTypeService.queryList(EnumValidStatus.VALID, EMSOrder.Asc));
            }else{
                FnTenant fnTenant = fnTenantService.queryOne(tenantId);
                String energyTypeIds = fnTenant.getEnergyTypeIds();
                String[] energyTypeIdList = energyTypeIds.split(",");
                for(FnEnergyType fnEnergyType : ConstantDBBaseData.EnergyTypeList){
                    for (String energyTypeId : energyTypeIdList) {
                        if(energyTypeId.equals(fnEnergyType.getId())){
                            content.add(fnEnergyType);
                        }
                    }
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTCommonEnergyTypeListService");
        }
    }


}
