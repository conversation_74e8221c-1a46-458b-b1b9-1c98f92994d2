package com.persagy.finein.fnmanage.alarm.handler;

import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.dictionary.Building;

import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月21日 下午1:45:10

* 说明:
*/
public interface FNTAlarmProcessTenantHandler {
	
	public void handle(Building building, FnTenant tenant, Map<String, FnAlarmLimitGlobal> globalAlarmLimitMap, Integer dataSpaceSecond) throws Exception;
}

