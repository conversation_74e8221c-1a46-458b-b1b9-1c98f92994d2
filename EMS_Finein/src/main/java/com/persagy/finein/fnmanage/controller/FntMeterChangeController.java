package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnRecordMeterChange;
import com.persagy.finein.service.FNRecordMeterChangeService;
import com.persagy.finein.service.FNUserService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：租户管理-换表记录
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntMeterChangeController extends BaseController {

    @Resource(name = "FNRecordMeterChangeService")
    private FNRecordMeterChangeService fnRecordMeterChangeService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @RequestMapping("FNTMeterChangeService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation= Propagation.REQUIRED,rollbackFor=Exception.class)
    public InterfaceResult meterChange(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String meterId = (String) dto.get("meterId");
            String energyTypeId = (String) dto.get("energyTypeId");
            String changeTime = (String) dto.get("changeTime");
            String userId = this.getParamUserId(dto);

            if (meterId == null || energyTypeId == null || changeTime == null || userId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            Map<String,Object> contentObj = new HashMap<>();
            content.add(contentObj);
            Date change = standard.parse(changeTime);
            if(change.getTime() > new Date().getTime()){
                contentObj.put("result", 2);
                return Result.SUCCESS(content);
            }

            Date lastChangeTime = fnRecordMeterChangeService.queryLastChangeTime(meterId, energyTypeId);
            if(lastChangeTime != null && lastChangeTime.getTime() >= change.getTime()){
                contentObj.put("result", 1);
                return Result.SUCCESS(content);
            }

            FnRecordMeterChange saveObj = new FnRecordMeterChange();
            saveObj.setId(UUID.randomUUID().toString());
            saveObj.setMeterId(meterId);
            saveObj.setChangeTime(standard.parse(changeTime));
            saveObj.setCreateTime(new Date());
            saveObj.setEnergyTypeId(energyTypeId);
            saveObj.setUserId(userId);
            DTOUser dtoUser = fnUserService.queryUserByUserId(userId);
            if(dtoUser != null){
                saveObj.setUserName(dtoUser.getUserName());

            }
            fnRecordMeterChangeService.save(saveObj);
            contentObj.put("result", 0);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTMeterChangeService");
        }
    }


}
