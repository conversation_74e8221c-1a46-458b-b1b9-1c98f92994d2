package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.TimeDataUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantData;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.service.FNTenantDataService;
import com.persagy.finein.service.FNTenantService;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：能耗费用报表-能耗费用报表查询（单租户）
 * <AUTHOR>
 */

@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntBuildingEnergyMoneyController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantDataService")
    private FNTenantDataService fnTenantDataService;


    @RequestMapping("FNTBuildingEnergyMoneyService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult buildingEnergyMoney(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String energyTypeId = (String) dto.get("energyTypeId");
            Integer dataType = (Integer) dto.get("dataType");
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");

            if (dataType==null||energyTypeId == null || buildingId == null || timeFrom == null || timeTo == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            Date from = standard.parse(timeFrom);
            Date to = standard.parse(timeTo);
            List<FnTenant> tenantList = fnTenantService.queryListByValidStatus(buildingId, EnumValidStatus.VALID);
            if(tenantList==null||tenantList.size()==0){
                throw new Exception("建筑id不存在:"+buildingId);
            }
            Map<String, Double> dataMap = TimeDataUtil.getTimeDataMap(from, to, EnumTimeType.T4);
            for (FnTenant fnTenant : tenantList) {
                processTenant(buildingId, energyTypeId, dataType, from, to, dataMap, fnTenant);
            }
            List<Object> dataList = new ArrayList<Object>();
            for (Map.Entry<String, Double> entry : dataMap.entrySet()) {
                Map<String,Object> map = new HashMap<String,Object>();
                map.put("time", entry.getKey());
                map.put("data", entry.getValue()==null?0.0:entry.getValue().doubleValue());
                dataList.add(map);
            }
            String unit = UnitUtil.getCumulantUnit(energyTypeId);
            if(dataType== EnumEnergyMoney.Money.getValue().intValue()){
                unit="元";
            }
            Map<String, Object> contentMap = new HashMap<String, Object>();
            contentMap.put("dataUnit", unit);
            contentMap.put("buildingId", buildingId);
            contentMap.put("dataList", dataList);
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTBuildingEnergyMoneyService");
        }
    }

    private void processTenant(String buildingId, String energyTypeId, Integer dataType, Date from, Date to,
                               Map<String, Double> dataMap, FnTenant fnTenant) throws Exception {
        List<FnTenantData> list = fnTenantDataService.queryListGteLt(buildingId, fnTenant.getId(), EnumTimeType.T2, energyTypeId, from, to, EnumEnergyMoney.valueOf(dataType));
        if(list!=null&&list.size()>0){
            for (FnTenantData fnTenantData : list) {
                if (fnTenantData.getData() != null) {
                    if (fnTenantData.getTimeFrom().getTime() < fnTenant.getActiveTime().getTime()) {// 小于激活时间，不统计
                        continue;
                    }
                    Date date = DateUtils.truncate(fnTenantData.getTimeFrom(), Calendar.MONTH);
                    dataMap.put(standard.format(date), dataMap.get(standard.format(date))==null?fnTenantData.getData():dataMap.get(standard.format(date))+fnTenantData.getData());
                }
            }
        }
    }

}
