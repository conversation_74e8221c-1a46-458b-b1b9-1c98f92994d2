package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumPrePayType;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNTenantPayTypeService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 租户仪表
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntTenantMeterController extends BaseController {


    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;


    /**
     * 租户仪表查询
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantMeterQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantMeterQuery(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");
            String energyTypeId = (String) dto.get("energyTypeId");
            if (tenantId == null || energyTypeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            List<Map<String, Object>> list = new ArrayList<>();
            FnTenantPayType payType = fnTenantPayTypeService.query(tenantId, energyTypeId);
            if (payType.getPayType() == EnumPayType.PREPAY.getValue().intValue()
                    && !payType.getPrePayType().equals(EnumPrePayType.ONLINE_TENANTPAY.getValue())) {
                List<DTOMeter> meterList = fnMeterService.queryMeterList(tenantId, energyTypeId);
                if (meterList != null) {
                    for (DTOMeter dtoMeter : meterList) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("meterId", dtoMeter.getMeterId());
                        list.add(map);
                    }
                }
            }
            content.add(list);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTTenantMeterQueryService");
        }
    }

    /**
     * 租户仪表列表
     * @param jsonString
     * @return
     */
    @RequestMapping("FNTTenantMeterListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult tenantMeterList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String)dto.get("tenantId");
            String energyTypeId = (String)dto.get("energyTypeId");

            if(tenantId == null || energyTypeId == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            List<DTOMeter> meterList = fnMeterService.queryMeterList(tenantId, energyTypeId);
            if(meterList != null){
                content.addAll(meterList);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTTenantMeterListService");
        }
    }
}
