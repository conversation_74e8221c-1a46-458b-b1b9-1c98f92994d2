package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.FnTenantPrePayMeterParam;
import com.persagy.ems.pojo.finein.FnTenantPrePayParam;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.core.util.PathUtil;
import com.persagy.finein.enumeration.EnumPrePayType;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.service.*;
import org.apache.poi.hssf.usermodel.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;

/**
 * 项目名：租户管理 接口名：批量操作-剩余量/金额报表（查询+下载）
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "unchecked", "rawtypes" })
public class FntBatchRemainGridController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNTenantPrePayParamService")
    private FNTenantPrePayParamService fnTenantPrePayParamService;

    @Resource(name = "FNTenantPrePayMeterParamService")
    private FNTenantPrePayMeterParamService fnTenantPrePayMeterParamService;

    @Resource(name = "PathUtil")
    private PathUtil pathUtil;

    @Resource(name = "FNFileResourceService")
    private FNFileResourceService fnFileResourceService;

    @RequestMapping("FNTBatchRemainGridService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult batchRemainGrid(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String) dto.get("energyTypeId");
            Integer isDownload = (Integer) dto.get("isDownload");
            List<String> tenantIdList = (List<String>) dto.get("tenantList");
            if (tenantIdList == null || energyTypeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            Integer remainType = (Integer) dto.get("remainType");
            Map<String, Object> contentMap = new HashMap<>();
            contentMap.put("energyUnit", UnitUtil.getCumulantUnit(energyTypeId));
            contentMap.put("energyTypeName", EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId));
            List<Object> tenantList = new ArrayList<>();
            contentMap.put("tenantList", tenantList);
            String gridTime = standard.format(new Date(new Date().getTime() / (FineinConstant.Time.Minute_15) * (FineinConstant.Time.Minute_15)));
            contentMap.put("gridTime", gridTime.substring(0, 16));
            List<FnTenant> fnTenantList = new ArrayList<FnTenant>();
            fnTenantList = fnTenantService.queryListByIds(tenantIdList);
//		for (String id : tenantIdList) {
//			FnTenant tenant = FNTenantService.queryOne(id);
//			if(tenant!=null){
//				fnTenantList.add(tenant);
//			}
//		}
            Map<String, List<Map<String, Object>>> meterMap = fnMeterService.queryMeter(tenantIdList, energyTypeId);
            if (fnTenantList != null) {
                for (FnTenant fnTenant : fnTenantList) {
                    List<Map<String, Object>> meterList = meterMap.get(fnTenant.getId());
                    FnTenantPayType tenantPayType = fnTenantPayTypeService.query(fnTenant.getId(), energyTypeId);
                    Integer prePayType = tenantPayType.getPrePayType();
                    if (EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue() == prePayType) {
                        Map<String, Object> objectMap = new HashMap<>();
                        FnTenantPrePayParam tenantPreParam = fnTenantPrePayParamService
                                .queryTenantPreParam(fnTenant.getId(), energyTypeId);
                        objectMap.put("tenantId", fnTenant.getId());
                        objectMap.put("tenantName", fnTenant.getName());
                        objectMap.put("roomCode", fnTenant.getRoomCodes());
                        objectMap.put("prePayType", prePayType);
                        StringBuffer stringBuffer = new StringBuffer();
                        for (Map<String, Object> meter : meterList) {
                            String address = (String) meter.get("address");
                            if (stringBuffer.length() == 0) {
                                stringBuffer.append(address);
                            } else {
                                stringBuffer.append(",").append(address);
                            }
                        }
                        objectMap.put("address", stringBuffer.toString());


                        if (remainType == -1) {
                            if (tenantPayType.getPrepayChargeType() == EnumPrepayChargeType.Liang.getValue().intValue()) {
                                objectMap.put("remainEnergy",
                                        tenantPreParam == null ? null : tenantPreParam.getRemainData());
                                objectMap.put("remainMoney", null);
                                objectMap.put("remainDays", tenantPreParam == null ? null : tenantPreParam.getRemainDays());
                            } else {
                                objectMap.put("remainMoney",
                                        tenantPreParam == null ? null : tenantPreParam.getRemainData());
                                objectMap.put("remainEnergy", null);
                                objectMap.put("remainDays", tenantPreParam == null ? null : tenantPreParam.getRemainDays());
                            }
                        } else if (remainType == EnumPrepayChargeType.Liang.getValue().intValue()) {
                            objectMap.put("remainEnergy", tenantPreParam == null ? null : tenantPreParam.getRemainData());
                            objectMap.put("remainDays", tenantPreParam == null ? null : tenantPreParam.getRemainDays());
                        } else if (remainType == EnumPrepayChargeType.Qian.getValue().intValue()) {
                            objectMap.put("remainMoney", tenantPreParam == null ? null : tenantPreParam.getRemainData());
                            objectMap.put("remainDays", tenantPreParam == null ? null : tenantPreParam.getRemainDays());
                        }
                        tenantList.add(objectMap);
                    } else {
                        for (Map<String, Object> meter : meterList) {
                            Map<String, Object> objectMap = new HashMap<>();
                            objectMap.put("tenantId", fnTenant.getId());
                            objectMap.put("tenantName", fnTenant.getName());
                            objectMap.put("roomCode", meter.get("roomCode"));
                            objectMap.put("prePayType", prePayType);
                            objectMap.put("address", meter.get("address"));
                            FnTenantPrePayMeterParam tenantPreMeterParam = fnTenantPrePayMeterParamService
                                    .queryMeterPreParam(fnTenant.getId(), (String) meter.get("meterId"), energyTypeId);
                            if (remainType == -1) {
                                if (tenantPayType.getPrepayChargeType() == EnumPrepayChargeType.Liang.getValue()
                                        .intValue()) {
                                    objectMap.put("remainEnergy",
                                            tenantPreMeterParam == null ? null : tenantPreMeterParam.getRemainData());
                                    objectMap.put("remainMoney", "--");
                                    objectMap.put("remainDays", tenantPreMeterParam == null ? null : tenantPreMeterParam.getRemainDays());
                                } else {
                                    objectMap.put("remainMoney",
                                            tenantPreMeterParam == null ? null : tenantPreMeterParam.getRemainData());
                                    objectMap.put("remainEnergy", null);
                                    objectMap.put("remainDays", tenantPreMeterParam == null ? null : tenantPreMeterParam.getRemainDays());
                                }
                            } else if (remainType == EnumPrepayChargeType.Liang.getValue().intValue()) {
                                objectMap.put("remainEnergy",
                                        tenantPreMeterParam == null ? null : tenantPreMeterParam.getRemainData());
                                objectMap.put("remainDays", tenantPreMeterParam == null ? null : tenantPreMeterParam.getRemainDays());
                            } else if (remainType == EnumPrepayChargeType.Qian.getValue().intValue()) {
                                objectMap.put("remainMoney",
                                        tenantPreMeterParam == null ? null : tenantPreMeterParam.getRemainData());
                                objectMap.put("remainDays", tenantPreMeterParam == null ? null : tenantPreMeterParam.getRemainDays());
                            }
                            tenantList.add(objectMap);
                        }
                    }
                }
            }
            if (isDownload == 0) {
                content.add(contentMap);
            } else {
                String subdirectory = pathUtil.getTempDownloadSubDir();
                String path = pathUtil.getPath();
                String fileDir = Paths.get(path, subdirectory).toString();
                File file = new File(fileDir);
                if (!file.exists()) {
                    file.mkdirs();
                }
                FileResource fileResource = new FileResource();
                String id = UUID.randomUUID().toString();
                fileResource.setId(id);
                StringBuffer stringBuffer = new StringBuffer();
                if (remainType == -1) {
                    stringBuffer.append("剩余天数报表");
                } else if (remainType == EnumPrepayChargeType.Liang.getValue().intValue()) {
                    stringBuffer.append("剩余量报表");
                } else if (remainType == EnumPrepayChargeType.Qian.getValue().intValue()) {
                    stringBuffer.append("剩余金额报表");
                }
                String replace = gridTime.replace(":", "：");
                stringBuffer.append("-").append(EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId)).append("-").append(replace);
                fileResource.setName(stringBuffer.toString());
                fileResource.setSubdirectory(subdirectory);
                fileResource.setSuffix("xls");
                this.buildExcel(path, subdirectory, id, contentMap);
                fnFileResourceService.save(fileResource);
                Map<String, Object> newContentObj = new HashMap<>();
                newContentObj.put("id", id);
                content.add(newContentObj);
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNTBatchRemainGridService");
        }
    }

    @SuppressWarnings("deprecation")
    private void buildExcel(String path, String subdirectory, String id, Map<String, Object> contentMap) {
        HSSFWorkbook wb = new HSSFWorkbook();
        FileOutputStream fout = null;
        HSSFSheet sheet = wb.createSheet("数据");
        HSSFCellStyle style = wb.createCellStyle();
        style.setAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        String energyUnit = (String) contentMap.get("energyUnit");

        List<Map<String, Object>> tenantList = (List<Map<String, Object>>) contentMap.get("tenantList");

        // 创建标题
        HSSFRow row0 = sheet.createRow((short) 0);
        {
            HSSFCell cell = row0.createCell((short) 0);
            cell.setCellStyle(style);
            cell.setCellValue("租户编号");
        }
        {
            HSSFCell cell = row0.createCell((short) 1);
            cell.setCellStyle(style);
            cell.setCellValue("租户名称");
        }

        {
            HSSFCell cell = row0.createCell((short) 2);
            cell.setCellStyle(style);
            cell.setCellValue("房间编号");
        }


        {
            HSSFCell cell = row0.createCell((short) 3);
            cell.setCellStyle(style);
            cell.setCellValue("仪表地址");
        }

        {
            HSSFCell cell = row0.createCell((short) 4);
            cell.setCellStyle(style);
            cell.setCellValue("剩余金额(元)");
        }
        {
            HSSFCell cell = row0.createCell((short) 5);
            cell.setCellStyle(style);
            cell.setCellValue("剩余量" + energyUnit);
        }
        {
            HSSFCell cell = row0.createCell((short) 6);
            cell.setCellStyle(style);
            cell.setCellValue("剩余天数");
        }

        if (tenantList != null) {
            int currentRow = 1;
            for (int i = 0; i < tenantList.size(); i++) {
                Map<String, Object> tenant = (Map<String, Object>) tenantList.get(i);
                String tenantId = (String) tenant.get("tenantId");
                String tenantName = (String) tenant.get("tenantName");
                String roomIds = (String) tenant.get("roomCode");
                String address = (String) tenant.get("address");
                Double remainMoney = DoubleFormatUtil.Instance().getDoubleData_00(tenant.get("remainMoney"));
                Double remainEnergy = DoubleFormatUtil.Instance().getDoubleData_00(tenant.get("remainEnergy"));
                String remainDays = (String) tenant.get("remainDays");

                HSSFRow row = sheet.createRow((short) currentRow);
                {
                    HSSFCell cell = row.createCell((short) (0));
                    cell.setCellValue(tenantId == null ? " " : tenantId);
                }
                {
                    HSSFCell cell = row.createCell((short) (1));
                    cell.setCellValue(tenantName == null ? " " : tenantName);
                }
                {
                    HSSFCell cell = row.createCell((short) (2));
                    cell.setCellValue(roomIds == null ? "--" : roomIds);
                }

                {
                    HSSFCell cell = row.createCell((short) (3));
                    cell.setCellValue("".equals(address) ? "--" : address);
                }
                {
                    HSSFCell cell = row.createCell((short) (4));
                    cell.setCellValue(remainMoney == null ? 0 : remainMoney);
                }
                {
                    HSSFCell cell = row.createCell((short) (5));
                    cell.setCellValue(remainEnergy == null ? 0 : remainEnergy);
                }
                {
                    HSSFCell cell = row.createCell((short) (6));
                    cell.setCellValue(remainDays == null ? "--" : remainDays + "");
                }
                currentRow++;
            }
        }
        String newFilePath = Paths.get(path, subdirectory, id).toString();
        try {
            fout = new FileOutputStream(newFilePath);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fout != null) {
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
    }

}
