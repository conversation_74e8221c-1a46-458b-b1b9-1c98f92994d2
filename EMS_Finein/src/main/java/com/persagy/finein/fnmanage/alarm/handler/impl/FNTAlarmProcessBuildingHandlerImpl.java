package com.persagy.finein.fnmanage.alarm.handler.impl;

import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.fnmanage.alarm.handler.FNTAlarmProcessBuildingHandler;
import com.persagy.finein.fnmanage.alarm.handler.FNTAlarmProcessTenantHandler;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月21日 下午1:45:10

* 说明:
*/
@Component("FNTAlarmProcessBuildingHandler")
public class FNTAlarmProcessBuildingHandlerImpl extends CoreServiceImpl implements FNTAlarmProcessBuildingHandler{
	
	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;
	
	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNTAlarmProcessTenantHandler")
	private FNTAlarmProcessTenantHandler FNTAlarmProcessTenantHandler;
	
	
	public void handle(Building building,Map<String,FnAlarmLimitGlobal> globalAlarmLimitMap,Integer dataSpaceSecond) throws Exception{
		//查询建筑下所有租户
		List<FnTenant> tenantList = FNTenantService.queryListByValidStatus(building.getId(), EnumValidStatus.VALID);
		if(tenantList == null || tenantList.size() == 0){
			return;
		}
		for(FnTenant tenant : tenantList){
			this.FNTAlarmProcessTenantHandler.handle(building, tenant, globalAlarmLimitMap,dataSpaceSecond);
		}
	}

}

