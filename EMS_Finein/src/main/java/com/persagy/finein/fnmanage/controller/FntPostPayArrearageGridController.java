package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.TimeDataUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnPriceTemplate;
import com.persagy.ems.pojo.finein.FnRecordPostClearingPay;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantData;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.service.FNRecordPostClearingPayService;
import com.persagy.finein.service.FNTenantDataService;
import com.persagy.finein.service.FNTenantPriceService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理
 * 接口名：后付费-单租户欠费账单（查询+下载）
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntPostPayArrearageGridController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantPriceService")
    private FNTenantPriceService fnTenantPriceService;

    @Resource(name = "FNRecordPostClearingPayService")
    private FNRecordPostClearingPayService fnRecordPostClearingPayService;

    @Resource(name = "FNTenantDataService")
    private FNTenantDataService fnTenantDataService;


    @RequestMapping("FNTPostPayArrearageGridService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult postPayArrearageGrid(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String)dto.get("buildingId");
            String tenantId = (String)dto.get("tenantId");
            String energyTypeId = (String)dto.get("energyTypeId");
            String orderId = (String)dto.get("orderId");

            if(tenantId == null || energyTypeId == null
                    || buildingId == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnTenant fnTenant = fnTenantService.queryOne(tenantId);
            String energyUnit = UnitUtil.getCumulantUnit(energyTypeId);
            String energyTypeName = EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId);
            Map<String,Object> contentMap = new HashMap<String, Object>();
            contentMap.put("energyUnit", energyUnit);
            contentMap.put("energyTypeName", energyTypeName);
            contentMap.put("tenantId", tenantId);
            contentMap.put("tenantName", fnTenant.getName());

            FnPriceTemplate fnPriceTemplate = fnTenantPriceService.query(tenantId, energyTypeId);
            contentMap.put("priceTemplateId", fnPriceTemplate.getId());
            contentMap.put("priceTemplateName", fnPriceTemplate.getName());

            Double totalMoney = null;
            Double remainMoney = null;

            List<Object> orderList = new ArrayList<Object>();
            contentMap.put("orderList", orderList);

            List<FnRecordPostClearingPay> recordList = fnRecordPostClearingPayService.queryNotPayRecord(buildingId, tenantId, energyTypeId);
            if(recordList != null){
                for(FnRecordPostClearingPay record : recordList){
                    Map<String,Object> map = new HashMap<String,Object>();
                    if(orderId != null && !"".equals(orderId.trim()) && !record.getOrderId().equals(orderId)){
                        continue;
                    }
                    map.put("orderTime", record.getOrderTime());
                    map.put("orderId", record.getOrderId());
                    map.put("money", record.getMoney());
                    map.put("amount", record.getCurrentSumEnergy());
                    List<Object> orderDetail = new ArrayList<Object>();
                    map.put("orderDetail", orderDetail);
                    String[] timeArray = record.getOrderTime().split("~");
                    Date orderTimeFrom = standard.parse(timeArray[0]+" 00:00:00");
                    Date orderTimeTo = standard.parse(timeArray[1]+" 00:00:00");

                    if(record.getMoney() != null){
                        totalMoney = totalMoney == null ? record.getMoney() : totalMoney + record.getMoney();
                    }

                    Map<String,Double> timeDataMap = TimeDataUtil.getTimeDataMap(orderTimeFrom, orderTimeTo, EnumTimeType.T2);

                    List<FnTenantData> energyList = fnTenantDataService.queryListGteLt(buildingId, tenantId, EnumTimeType.T2, energyTypeId, orderTimeFrom, orderTimeTo, EnumEnergyMoney.Energy);
                    List<FnTenantData> moneyList = fnTenantDataService.queryListGteLt(buildingId, tenantId, EnumTimeType.T2, energyTypeId, orderTimeFrom, orderTimeTo, EnumEnergyMoney.Money);

                    Map<String,Double> energyMap = new HashMap<String,Double>();
                    Map<String,Double> moneyMap = new HashMap<String,Double>();

                    if(energyList != null){
                        for(FnTenantData tenantSum : energyList){
                            if(tenantSum.getData() != null){
                                energyMap.put(standard.format(tenantSum.getTimeFrom()), tenantSum.getData());
                            }
                        }
                    }
                    if(moneyList != null){
                        for(FnTenantData tenantSum : moneyList){
                            if(tenantSum.getData() != null){
                                moneyMap.put(standard.format(tenantSum.getTimeFrom()), tenantSum.getData());
                            }
                        }
                    }
                    Double sumEnergy = null;
                    Double sumMoney = null;
                    for(Map.Entry<String, Double> entry : timeDataMap.entrySet()){
                        Map<String,Object> detailMap = new HashMap<String,Object>();
                        detailMap.put("time", entry.getKey().substring(0,10));
                        Double energy = energyMap.get(entry.getKey());
                        if(energy != null){
                            sumEnergy = sumEnergy == null ?  energy : sumEnergy + energy;
                        }
                        Double money = moneyMap.get(entry.getKey());
                        if(money != null){
                            sumMoney = sumMoney == null ? money : sumMoney + money;
                        }
                        detailMap.put("dayEnergy", energy);
                        detailMap.put("totalEnergy", sumEnergy);
                        detailMap.put("totalMoney", sumMoney);

                        orderDetail.add(detailMap);
                    }
                    orderList.add(map);
                }
            }

            contentMap.put("totalMoney", totalMoney);
            Date lastClearingDate = fnRecordPostClearingPayService.queryLastClearingDate(buildingId, tenantId, energyTypeId);
            if(lastClearingDate != null){
                List<FnTenantData> remainMoneyList = fnTenantDataService.queryListGte(buildingId, tenantId, EnumTimeType.T2, energyTypeId, lastClearingDate, EnumEnergyMoney.Money);
                if(remainMoneyList != null){
                    for(FnTenantData entity : remainMoneyList){
                        if(entity.getData() != null){
                            remainMoney = remainMoney == null ? entity.getData() : remainMoney + entity.getData();
                        }
                    }
                }
            }

            contentMap.put("remainMoney", remainMoney);
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPostPayArrearageGridService");
        }
    }


}
