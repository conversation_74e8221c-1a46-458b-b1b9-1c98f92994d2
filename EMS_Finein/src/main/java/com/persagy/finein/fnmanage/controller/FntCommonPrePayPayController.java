package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.MD5Tools;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * 项目名：租户管理 接口名：通用充值接口
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntCommonPrePayPayController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNOrderIdService")
    private FNOrderIdService fnOrderIdService;

    @Resource(name = "FNRecordPrePayService")
    private FNRecordPrePayService fnRecordPrePayService;

    @Resource(name = "FNTenantBackPayRecordService")
    private FNTenantBackPayRecordService fnTenantBackPayRecordService;

    @Resource(name = "FNRecordPrePayExtendService")
    private FNRecordPrePayExtendService fnRecordPrePayExtendService;

    @Resource(name = "FNRecordPrePayForOtherSystemService")
    private FNRecordPrePayForOtherSystemService fnRecordPrePayForOtherSystemService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNCommonPrePaySystemParamService")
    private FNCommonPrePaySystemParamService fnCommonPrePaySystemParamService;

    @Resource(name = "FNChargeMessageService")
    private FNChargeMessageService fnChargeMessageService;

    @Resource(name = "FNTenantPrePayParamService")
    private FNTenantPrePayParamService fnTenantPrePayParamService;

    public static Map<String, Date> timeMap = new HashMap<String, Date>();


    @RequestMapping("FNTCommonPrePayPayService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult commonPrePayPay(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String systemCode = (String) dto.get("systemCode");
            String buildingId = (String) dto.get("buildingId");
            String tenantId = (String) dto.get("tenantId");
            String meterId = (String) dto.get("meterId");
            String energyTypeId = (String) dto.get("energyTypeId");
            String serialNumber = (String) dto.get("serialNumber");
            String userId = (String) dto.get("userId");
            String userName = (String) dto.get("userName");
            String orderId = (String) dto.get("orderId");
            String orderTime = (String) dto.get("orderTime");
            Double value = DoubleFormatUtil.Instance().getDoubleData(dto.get("value"));
            Double remainData = DoubleFormatUtil.Instance().getDoubleData(dto.get("remainData"));

            if (systemCode == null || buildingId == null || tenantId == null || energyTypeId == null || serialNumber == null
                    || userId == null || value == null || userName == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            // 验证序列号
            String str = FineinConstant.systemSerialNumberMap.get(systemCode);
            if (str == null) {
                throw new Exception("序列号不存在,请先生成序列号");
            } else {
                String[] split = str.split("_");
                StringBuffer stringBuffer = new StringBuffer();
                StringBuffer append = stringBuffer.append(split[0]).append(split[1]);
                if (!serialNumber.equals(MD5Tools.MD5(append.toString()))) {
                    throw new Exception("序列号或密钥不匹配");
                }
                if (new Date().getTime() - Long.parseLong(split[2]) > 60 * 5 * 1000) {
                    throw new Exception("序列号已过期");
                }
            }

            FnTenant queryTenant = fnTenantService.queryTenant(buildingId, tenantId, EnumTenantStatus.ACTIVATED,
                    EnumValidStatus.VALID);
            if (queryTenant == null) {
                throw new Exception("查询信息错误");
            }

            FnTenantPayType tenantPayType = fnTenantPayTypeService.query(tenantId, energyTypeId);
            if (tenantPayType == null) {
                throw new Exception("未找到租户的付费类型");
            }
            Map<String, Object> contentObj = new HashMap<String, Object>();
            if (tenantPayType.getPrePayType() == EnumPrePayType.ONLINE_METERPAY.getValue().intValue()) {
                if (meterId == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull("meterId"));
                }
                List<DTOMeter> meterList = fnMeterService.queryMeterList(tenantId, energyTypeId);
                // 判断仪表id
                boolean flag = true;
                if (meterList != null && meterList.size() > 0) {
                    for (DTOMeter dtoMeter : meterList) {
                        if (dtoMeter.getMeterId().equals(meterId)) {
                            flag = false;
                        }
                    }
                }
                if (flag) {
                    throw new Exception("租户仪表不对应");
                }
                this.processMeterPay(contentObj, systemCode, queryTenant, meterId, energyTypeId,
                        tenantPayType.getPrepayChargeType(), value, userId, userName, orderId, orderTime);
            } else if (tenantPayType.getPrePayType() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {

                this.processTenantPay(contentObj, systemCode, queryTenant, energyTypeId,
                        tenantPayType.getPrepayChargeType(), value, userId, remainData, userName, orderId, orderTime);
            } else {
                throw new Exception("不知道的prePayType:" + tenantPayType.getPrePayType());
            }
            contentObj.put("prePayType", tenantPayType.getPrePayType());
            content.add(contentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTCommonPrePayPayService");
        }
    }


    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void processTenantPay(Map<String, Object> contentObj, String systemCode, FnTenant fnTenant,
                                  String energyTypeId, Integer billingType, Double value, String userId, Double remainData, String userName,
                                  String orderId, String orderTime) throws Exception {

        Building building = fnBuildingService.query(fnTenant.getBuildingId());
        Date now = new Date();
        Date lastPayTime = timeMap.get(fnTenant.getId() + "_" + energyTypeId);
        if ((now.getTime() - (lastPayTime == null ? 0 : lastPayTime.getTime())) < 1000) {// 充值时间少于1秒
            return;
        }
        if (orderId == null) {
            orderId = fnOrderIdService.queryOrderId(EnumPayType.PREPAY.getValue(), now);
        }
        String id = UUID.randomUUID().toString();
        // 插入充值记录
        FnRecordPrePay saveObj = new FnRecordPrePay();
        saveObj.setId(id);
        saveObj.setBuildingId(fnTenant.getBuildingId());
        saveObj.setBuildingName(building.getName());
        saveObj.setType(EnumPayBodyType.TENANT.getValue());
        saveObj.setTenantId(fnTenant.getId());
        saveObj.setTenantName(fnTenant.getName());
        saveObj.setCode(fnTenant.getId());
        saveObj.setName(fnTenant.getName());
        saveObj.setEnergyTypeId(energyTypeId);
        saveObj.setOrderId(orderId);
        if (billingType.intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
            saveObj.setMoney(DoubleFormatUtil.Instance().doubleFormat(value, 5L));
        } else if (billingType.intValue() == EnumPrepayChargeType.Liang.getValue().intValue()) {
            saveObj.setAmount(DoubleFormatUtil.Instance().doubleFormat(value, 5L));
        } else {
            throw new Exception("未知的billingType:" + billingType);
        }
        String unit = UnitUtil.getCumulantUnit(energyTypeId);
        saveObj.setAmountUnit(unit);
        saveObj.setOperateTime(now);
        saveObj.setUserId(userId);
        saveObj.setUserId(userId);
        saveObj.setUserName(userName);
        saveObj.setCreateTime(orderTime == null ? now : standard.parse(orderTime));
        fnRecordPrePayService.save(saveObj);

        FnTenantPrePayParam tenantPreParam = fnTenantPrePayParamService.queryTenantPreParam(fnTenant.getId(),
                energyTypeId);
        if (tenantPreParam != null && tenantPreParam.getRemainData() != null) {
            remainData = add(tenantPreParam.getRemainData(), value);
        }

        {// 插入账单
            FnOrderExtend save = new FnOrderExtend();
            save.setTenantId(fnTenant.getId());
            save.setOrderId(orderId);
            save.setOperateType(EnumPrePayOrReturn.PREPAY.getValue());
            save.setData(value);
            save.setRemainData(remainData == null ? 0.0 : remainData);
            save.setChargeType(billingType);
            save.setOperateTime(now);
            fnOrderIdService.save(save);
        }

        {// 充值记录扩展字段
            FnRecordPrePayExtend save = new FnRecordPrePayExtend();
            save.setId(id);
            save.setPrePaySystemCode(systemCode);
            FnCommonPrePaySystemParam query = new FnCommonPrePaySystemParam();
            query.setSystemCode(systemCode);
            List<FnCommonPrePaySystemParam> list = fnCommonPrePaySystemParamService.query(query);
            if (list != null && list.size() > 0) {
                save.setSource(Integer.valueOf(list.get(0).getId()));
            }
            fnRecordPrePayExtendService.save(save);
        }
        {// 远程充值记录
            FnRecordPrePayForOtherSystem saveR = new FnRecordPrePayForOtherSystem();
            saveR.setId(UUID.randomUUID().toString());
            saveR.setSystemCode(systemCode);
            saveR.setBuildingId(fnTenant.getBuildingId());
            saveR.setBuildingName(building.getName());
            saveR.setType(EnumPayBodyType.TENANT.getValue());
            saveR.setTenantId(fnTenant.getId());
            saveR.setTenantName(fnTenant.getName());
            saveR.setCode(fnTenant.getId());
            saveR.setName(fnTenant.getName());
            saveR.setEnergyTypeId(energyTypeId);
            saveR.setOrderId(orderId);
            if (billingType.intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
                saveObj.setMoney(DoubleFormatUtil.Instance().doubleFormat(value, 5L));
            } else if (billingType.intValue() == EnumPrepayChargeType.Liang.getValue().intValue()) {
                saveObj.setAmount(DoubleFormatUtil.Instance().doubleFormat(value, 5L));
            } else {
                throw new Exception("未知的billingType:" + billingType);
            }
            saveR.setAmountUnit(unit);
            saveR.setOperateTime(now);
            saveR.setUserId(userId);
            saveR.setUserName(userName);
            saveR.setCreateTime(now);
            saveR.setStatus(EnumPrePayStatus.PROCESSE.getValue());
            fnRecordPrePayForOtherSystemService.save(saveR);
        }
        {
            Date timeFrom = new Date(now.getTime() / (FineinConstant.Time.Minute_15) * (FineinConstant.Time.Minute_15));
            fnTenantBackPayRecordService.saveData(building.getId(), fnTenant.getId(), energyTypeId, EnumTimeType.T0,
                    EnumPrepayChargeType.valueOf(billingType), timeFrom, value);
        }

        {// 发送充值记录
            if ((boolean) ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendPayMessage)) {
                String perPaySuccessTemplate = (String) ConstantDBBaseData.SysParamValueMap
                        .get(FineinConstant.SysParamValueKey.Id_PerPaySuccessTemplate);
                String messageSignature = (String) ConstantDBBaseData.SysParamValueMap
                        .get(FineinConstant.SysParamValueKey.Id_MessageSignature);
                fnChargeMessageService.paySendMessage(saveObj, remainData, fnTenant, perPaySuccessTemplate,
                        messageSignature);
            }
        }
        timeMap.put(fnTenant.getId() + "_" + energyTypeId, now);
        contentObj.put("result", 0);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void processMeterPay(Map<String, Object> contentObj, String systemCode, FnTenant fnTenant, String meterId,
                                 String energyTypeId, Integer billingType, Double value, String userId, String userName, String orderId,
                                 String orderTime) throws Exception {

        Date now = new Date();
        if (orderId == null) {// 本地生成账单编号
            orderId = fnOrderIdService.queryOrderId(EnumPayType.PREPAY.getValue(), now);
        }
        Building building = fnBuildingService.query(fnTenant.getBuildingId());
        {// 远程充值记录
            FnRecordPrePayForOtherSystem saveObj = new FnRecordPrePayForOtherSystem();
            saveObj.setId(UUID.randomUUID().toString());
            saveObj.setBuildingId(fnTenant.getBuildingId());
            saveObj.setBuildingName(building.getName());
            saveObj.setType(EnumPayBodyType.METER.getValue());
            saveObj.setTenantId(fnTenant.getId());
            saveObj.setTenantName(fnTenant.getName());
            saveObj.setCode(meterId);
            saveObj.setName(meterId);
            saveObj.setEnergyTypeId(energyTypeId);
            saveObj.setOrderId(orderId);
            if (billingType.intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
                saveObj.setMoney(DoubleFormatUtil.Instance().doubleFormat(value, 5L));
            } else if (billingType.intValue() == EnumPrepayChargeType.Liang.getValue().intValue()) {
                saveObj.setAmount(DoubleFormatUtil.Instance().doubleFormat(value, 5L));
            } else {
                throw new Exception("未知的billingType:" + billingType);
            }
            String unit = UnitUtil.getCumulantUnit(energyTypeId);
            saveObj.setAmountUnit(unit);
            saveObj.setOperateTime(now);
            saveObj.setUserId(userId);
            saveObj.setUserName(userName);
            saveObj.setCreateTime(orderTime == null ? new Date() : standard.parse(orderTime));
            saveObj.setStatus(EnumPrePayStatus.NO_PROCESSE.getValue());
            saveObj.setSystemCode(systemCode);
            fnRecordPrePayForOtherSystemService.save(saveObj);
        }
        contentObj.put("orderId", orderId);
    }

    public double add(double value1, double value2) {
        BigDecimal b1 = new BigDecimal(Double.valueOf(value1).toString());
        BigDecimal b2 = new BigDecimal(Double.valueOf(value2).toString());
        return b1.add(b2).doubleValue();
    }

    public Double div(double d1, double d2, int len) {// 进行除法运算
        BigDecimal b1 = new BigDecimal(d1);
        BigDecimal b2 = new BigDecimal(d2);
        return b1.divide(b2, len, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

}
