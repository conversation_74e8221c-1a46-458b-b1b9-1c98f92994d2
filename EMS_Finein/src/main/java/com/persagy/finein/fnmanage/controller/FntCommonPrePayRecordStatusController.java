package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnRecordForOtherSystemError;
import com.persagy.ems.pojo.finein.FnRecordPrePayForOtherSystem;
import com.persagy.finein.enumeration.EnumPrePayStatus;
import com.persagy.finein.service.FNRecordForOtherSystemErrorService;
import com.persagy.finein.service.FNRecordPrePayForOtherSystemService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：跨系统远程充值记录查询
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntCommonPrePayRecordStatusController extends BaseController {

    @Resource(name = "FNRecordPrePayForOtherSystemService")
    private FNRecordPrePayForOtherSystemService fnRecordPrePayForOtherSystemService;

    @Resource(name = "FNRecordForOtherSystemErrorService")
    private FNRecordForOtherSystemErrorService fnRecordForOtherSystemErrorService;

    @RequestMapping("FNTCommonPrePayRecordStatusService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult commonPrePayRecordStatus(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String orderId = (String) dto.get("orderId");

            if (buildingId == null||orderId==null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Map<String, Object> obj = new HashMap<String, Object>();
            FnRecordPrePayForOtherSystem query = new FnRecordPrePayForOtherSystem();
            query.setBuildingId(buildingId);
            query.setOrderId(orderId);
            List<FnRecordPrePayForOtherSystem> list = fnRecordPrePayForOtherSystemService.query(query);
            if (list != null && list.size() > 0) {
                obj.put("orderId", list.get(0).getOrderId());
                obj.put("status", list.get(0).getStatus());
                if(list.get(0).getStatus()== EnumPrePayStatus.FAIL.getValue().intValue()){
                    FnRecordForOtherSystemError error = new FnRecordForOtherSystemError();
                    error.setId(list.get(0).getId());
                    List<FnRecordForOtherSystemError> errorList = fnRecordForOtherSystemErrorService.query(error);
                    if(errorList!=null&&errorList.size()>0){
                        obj.put("reason", errorList.get(0).getErrorMessage());
                    }
                }
            }
            content.add(obj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTCommonPrePayRecordStatusService");
        }
    }


}
