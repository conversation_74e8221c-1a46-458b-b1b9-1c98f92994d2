package com.persagy.finein.fnmanage.compute.handler;

import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.dictionary.Building;

/**
* 作者:shaohongbo

* 时间:2018年07月23日 

* 说明:租户、仪表功率统计值计算线程
*/
public interface FNTComputePowerStatHandler{

	public void processBuilding(Building building) throws Exception;
	
	public void processTenant(Building building, FnTenant tenant) throws Exception;
}

