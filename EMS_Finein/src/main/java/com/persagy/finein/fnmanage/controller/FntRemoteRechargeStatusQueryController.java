package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnRemoteRechargeStatus;
import com.persagy.ems.pojo.finein.FnTenantFlag;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNRemoteRechargeStatusService;
import com.persagy.finein.service.FNTenantFlagService;
import com.persagy.finein.service.FNUserService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 项目名：租户管理 接口名：租户-租户详情
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntRemoteRechargeStatusQueryController extends BaseController {

    @Resource(name = "FNRemoteRechargeStatusService")
    private FNRemoteRechargeStatusService fnRemoteRechargeStatusService;

    @Resource(name = "FNTenantFlagService")
    private FNTenantFlagService fnTenantFlagService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @RequestMapping("FNTRemoteRechargeStatusQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult remoteRechargeStatusQuery(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String tenantId = (String) dto.get("tenantId");
            String systemCode = "1";

            if (tenantId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            HashMap<String, Object> obj = new HashMap<String, Object>();
            FnRemoteRechargeStatus query = new FnRemoteRechargeStatus();
            query.setBuildingId(buildingId);
            query.setTenantId(tenantId);
            query.setSystemCode(systemCode);
            List<FnRemoteRechargeStatus> list = fnRemoteRechargeStatusService.query(query);
            if (list != null && list.size() > 0) {
                obj.put("remoteRechargeStatus", list.get(0).getRemoteRechargeStatus());

            } else {
                FnTenantFlag queryFlag = new FnTenantFlag();
                queryFlag.setBuildingId(buildingId);
                queryFlag.setTenantId(tenantId);
                List<FnTenantFlag> tenantFlagList = fnTenantFlagService.query(queryFlag);
                if (tenantFlagList == null || tenantFlagList.size() == 0) {
                    throw new Exception("该租户全码不存在：" + tenantId);
                }

                String userId = this.getParamUserId(dto);
                DTOUser user = fnUserService.queryUserByUserId(userId);
                query.setRemoteRechargeStatus(EnumYesNo.YES.getValue());
                query.setTenantFlag(tenantFlagList.get(0).getTenantFlag());
                query.setBuildingId(tenantFlagList.get(0).getBuildingId());
                query.setUserId(user.getUserId());
                query.setSystemCode(systemCode);
                query.setUserName(user.getUserName());
                query.setLastTime(new Date());
                fnRemoteRechargeStatusService.save(query);
                obj.put("remoteRechargeStatus", query.getRemoteRechargeStatus());
            }
            content.add(obj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTRemoteRechargeStatusQueryService");
        }
    }


}
