package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.finein.FnMessageBlackList;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantFlag;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.service.FNBuildingService;
import com.persagy.finein.service.FNMessageBlackListService;
import com.persagy.finein.service.FNTenantFlagService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：自动发送短信设置
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntMessageBlacklistSaveController extends BaseController {

    @Resource(name = "FNMessageBlackListService")
    private FNMessageBlackListService fnMessageBlackListService;

    @Resource(name = "FNTenantFlagService")
    private FNTenantFlagService fnTenantFlagService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;


    @RequestMapping("FNTMessageBlacklistSaveService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult messageBlacklistSave(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            List<Map<String, Object>> list = (List<Map<String, Object>>) dto.get("list");
            List<FnMessageBlackList> saveList = new ArrayList<FnMessageBlackList>();
            if (list != null) {
                Map<String, Building> buildingMap = fnBuildingService.query();
                Map<String, FnTenantFlag> flagMap = fnTenantFlagService.queryFlag();
                Map<String, FnTenant> tenantMap = fnTenantService.queryMap();
                Map puser = (Map) dto.get("puser");
                String id = (String) puser.get("id");
                String name = (String) puser.get("showName");
                Date now = new Date();
                for (Map<String, Object> map : list) {
                    FnMessageBlackList save = new FnMessageBlackList();
                    save.setBuildingId((String) map.get("buildingId"));
                    Building building = buildingMap.get((String) map.get("buildingId"));
                    if (building == null) {
                        throw new Exception("建筑不存在:" + (String) map.get("buildingId"));
                    }
                    save.setBuildingName(building.getName());
                    FnTenantFlag fnTenantFlag = flagMap.get((String) map.get("tenantId"));
                    String tenantId = (String) map.get("tenantId");
                    FnTenant tenant = tenantMap.get(tenantId);
                    if (tenant == null) {
                        throw new Exception("租户不存在:" + (String) map.get("tenantId"));
                    }
                    if (fnTenantFlag == null) {
                        throw new Exception("住户全码不存在:" + (String) map.get("tenantId"));
                    }
                    save.setTenantFlag(fnTenantFlag.getTenantFlag());
                    save.setTenantId((String) map.get("tenantId"));
                    save.setTenantName(tenant.getName());
                    save.setUserId(id);
                    save.setUserName(name);
                    save.setLastTime(now);
                    saveList.add(save);
                }
            }
            fnMessageBlackListService.remove(new FnMessageBlackList());
            fnMessageBlackListService.save(saveList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTMessageBlacklistSaveService");
        }
    }


}
