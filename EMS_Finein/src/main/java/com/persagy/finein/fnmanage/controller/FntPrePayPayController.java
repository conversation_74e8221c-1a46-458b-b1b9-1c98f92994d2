package com.persagy.finein.fnmanage.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.apache.log4j.Logger;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 项目名：租户管理 接口名：预付费充值-充值
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntPrePayPayController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNOrderIdService")
    private FNOrderIdService fnOrderIdService;

    @Resource(name = "FNUserService")
    private FNUserService fnUserService;

    @Resource(name = "FNRecordPrePayService")
    private FNRecordPrePayService fnRecordPrePayService;

    @Resource(name = "FNTenantBackPayRecordService")
    private FNTenantBackPayRecordService fnTenantBackPayRecordService;

    @Resource(name = "FNBeiDianReturnRecordService")
    private FNBeiDianReturnRecordService fnBeiDianReturnRecordService;

    @Resource(name = "FNChargeMessageService")
    private FNChargeMessageService fnChargeMessageService;


    private static Logger log = Logger.getLogger(FntPrePayPayController.class);

    private SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static Map<String, Date> timeMap = new HashMap<String, Date>();



    @RequestMapping("FNTPrePayPayService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult prePayPay(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");
            String meterId = (String) dto.get("meterId");
            String energyTypeId = (String) dto.get("energyTypeId");
            Integer prePayType = (Integer) dto.get("prePayType");
            Integer billingType = (Integer) dto.get("billingType");
            Double value = DoubleFormatUtil.Instance().getDoubleData(dto.get("value"));
            Double remainData = DoubleFormatUtil.Instance().getDoubleData(dto.get("remainData"));
            String userId = this.getParamUserId(dto);

            if (tenantId == null || energyTypeId == null || prePayType == null || value == null || billingType == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("tenantId", tenantId);
            jsonObject.put("meterId", meterId);
            jsonObject.put("energyTypeId", energyTypeId);
            jsonObject.put("prePayType", prePayType.longValue());
            jsonObject.put("billingType", billingType.longValue());
            jsonObject.put("value", value);
            jsonObject.put("userId", userId);
            log.error("监控并发问题日志:*******" + standard.format(new Date()) + "******* 参数" + jsonObject.toString());

            Map<String, Object> contentObj = new HashMap<String, Object>();
            if (prePayType.intValue() == EnumPrePayType.ONLINE_METERPAY.getValue().intValue()) {
                if (meterId == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull("meterId"));
                }
                this.processMeterPay(contentObj, tenantId, meterId, energyTypeId, billingType, value, userId);
            } else if (prePayType.intValue() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
                this.processTenantPay(contentObj, tenantId, energyTypeId, billingType, value, userId, remainData);
            } else {
                throw new Exception("不知道的prePayType:" + prePayType);
            }
            content.add(contentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPrePayPayService");
        }
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void processTenantPay(Map<String, Object> contentObj, String tenantId, String energyTypeId,
                                  Integer billingType, Double value, String userId, Double remainData) throws Exception {

        // 查询租户
        FnTenant fnTenant = fnTenantService.queryOne(tenantId);
        if (fnTenant == null) {
            throw new Exception("租户不存在:" + tenantId);
        }

        Building building = fnBuildingService.query(fnTenant.getBuildingId());
        if (building == null) {
            throw new Exception("租户所属建筑不存在:" + fnTenant.getBuildingId());
        }
        Date now = new Date();
        Date lastPayTime = timeMap.get(tenantId + "_" + energyTypeId);
        if ((now.getTime() - (lastPayTime == null ? 0 : lastPayTime.getTime())) < 1000) {// 充值时间少于1秒
            return;
        }
        String orderId = fnOrderIdService.queryOrderId(EnumPayType.PREPAY.getValue(), now);
        // 插入充值记录
        FnRecordPrePay saveObj = new FnRecordPrePay();
        saveObj.setId(UUID.randomUUID().toString());

        saveObj.setBuildingId(fnTenant.getBuildingId());
        saveObj.setBuildingName(building.getName());
        saveObj.setType(EnumPayBodyType.TENANT.getValue());
        saveObj.setTenantId(tenantId);
        saveObj.setTenantName(fnTenant.getName());
        saveObj.setCode(tenantId);
        saveObj.setName(fnTenant.getName());
        saveObj.setEnergyTypeId(energyTypeId);
        saveObj.setOrderId(orderId);
        if (billingType.intValue() == EnumPrepayChargeType.Qian.getValue().intValue()) {
            saveObj.setMoney(DoubleFormatUtil.Instance().doubleFormat(value, 5L));
        } else if (billingType.intValue() == EnumPrepayChargeType.Liang.getValue().intValue()) {
            saveObj.setAmount(DoubleFormatUtil.Instance().doubleFormat(value, 5L));
        } else {
            throw new Exception("未知的billingType:" + billingType);
        }
        String unit = UnitUtil.getCumulantUnit(energyTypeId);
        saveObj.setAmountUnit(unit);
        saveObj.setOperateTime(now);
        saveObj.setUserId(userId);
        DTOUser user = fnUserService.queryUserByUserId(userId);
        saveObj.setUserId(user.getUserId());
        saveObj.setUserName(user == null ? null : user.getUserName());
        saveObj.setCreateTime(now);

        fnRecordPrePayService.save(saveObj);

        {// 插入账单
            FnOrderExtend save = new FnOrderExtend();
            save.setTenantId(tenantId);
            save.setOrderId(orderId);
            save.setOperateType(EnumPrePayOrReturn.PREPAY.getValue());
            save.setData(value);
            save.setRemainData(add(remainData == null ? 0.0 : remainData, value));
            save.setChargeType(billingType);
            save.setOperateTime(now);
            fnOrderIdService.save(save);
        }
        {
            Date timeFrom = new Date(now.getTime() / (FineinConstant.Time.Minute_15) * (FineinConstant.Time.Minute_15));
            fnTenantBackPayRecordService.saveData(building.getId(), tenantId, energyTypeId, EnumTimeType.T0,
                    EnumPrepayChargeType.valueOf(billingType), timeFrom, value);
        }
        {// 发送充值记录
            if ((boolean) ConstantDBBaseData.SysParamValueMap.get(FineinConstant.SysParamValueKey.Id_SendPayMessage)) {
                String perPaySuccessTemplate = (String) ConstantDBBaseData.SysParamValueMap
                        .get(FineinConstant.SysParamValueKey.Id_PerPaySuccessTemplate);
                String messageSignature = (String) ConstantDBBaseData.SysParamValueMap
                        .get(FineinConstant.SysParamValueKey.Id_MessageSignature);
                fnChargeMessageService.paySendMessage(saveObj, add(remainData == null ? 0.0 : remainData, value), fnTenant, perPaySuccessTemplate,
                        messageSignature);
            }
        }
        timeMap.put(tenantId + "_" + energyTypeId, now);

        contentObj.put("result", 0);
    }

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    private void processMeterPay(Map<String, Object> contentObj, String tenantId, String meterId, String energyTypeId,
                                 Integer billingType, Double value, String userId) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        DTOUser user = fnUserService.queryUserByUserId(userId);
        map.put("user", user);
        map.put("tenantId", tenantId);
        // 查询租户
        FnTenant fnTenant = fnTenantService.queryOne(tenantId);
        if (fnTenant == null) {
            throw new Exception("租户不存在:" + tenantId);
        }
        map.put("tenantName", fnTenant.getName());
        map.put("value", value);
        Building building = fnBuildingService.query(fnTenant.getBuildingId());
        if (building == null) {
            throw new Exception("租户所属建筑不存在:" + fnTenant.getBuildingId());
        }

        FnMeter fnMeter = fnMeterService.queryMeterById(meterId);
        if (fnMeter == null) {
            throw new Exception("仪表不存在:" + meterId);
        }
        if (fnMeter.getProtocolId().equals("DI_C_01_Y_Q_001")) {// 北电仪表
            contentObj.put("isBeiDian", EnumYesNo.YES.getValue());
            Double deductData = 0.0;
            FnBeiDianReturnRecord query = new FnBeiDianReturnRecord();
            query.setMeterId(meterId);
            List<FnBeiDianReturnRecord> list = fnBeiDianReturnRecordService.query(query);
            if (list != null && list.size() > 0) {
                if (value > list.get(0).getMoney()) {
                    value = value - list.get(0).getMoney();
                    deductData = list.get(0).getMoney();
                } else {
                    deductData = value;
                    value = (double) 0;
                }
            }
            contentObj.put("realPayData", value);
            contentObj.put("deductData", deductData);
        }
        if (value == 0) {// 不用充值
            return;
        }
        Date now = new Date();
        Date lastPayTime = timeMap.get(tenantId + "_" + meterId);
        if ((now.getTime() - (lastPayTime == null ? 0 : lastPayTime.getTime())) < 60000) {// 充值时间少于60秒
            timeMap.put(tenantId + "_" + meterId, now);
            return;
        }
        // 判断仪表是否计算变比
        Double radio = 1.0;
        try {
            Long syjIsCt = (Long) ((JSONObject) JSONValue.parse(fnMeter.getExtend())).get("syjIsCt");
            if (syjIsCt != null && syjIsCt.intValue() == 1) {
                radio = fnMeter.getRadio();
            }
        } catch (Exception e) {
        }
        ICommunication communication = (ICommunication) SystemConstant.context.getBean(fnMeter.getProtocolId());
        Double div = div(value, radio, 7);
        try {
            communication.pay(fnMeter, div.floatValue(), map);
        } catch (Exception e) {
            e.printStackTrace();
        }
        timeMap.put(tenantId + "_" + meterId, now);
        contentObj.put("result", 0);
    }

    public double add(double value1, double value2) {
        BigDecimal b1 = new BigDecimal(Double.valueOf(value1).toString());
        BigDecimal b2 = new BigDecimal(Double.valueOf(value2).toString());
        return b1.add(b2).doubleValue();
    }

    public Double div(double d1, double d2, int len) {// 进行除法运算
        BigDecimal b1 = new BigDecimal(d1);
        BigDecimal b2 = new BigDecimal(d2);
        return b1.divide(b2, len, BigDecimal.ROUND_HALF_UP).doubleValue();
    }
}
