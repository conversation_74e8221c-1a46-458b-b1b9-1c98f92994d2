package com.persagy.finein.fnmanage.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnProtocol;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNMeterService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理
 * 接口名：查询仪表支持的设置功能
 *
 * <AUTHOR>
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntMeterSetFunctionController extends BaseController {

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @RequestMapping("FNTMeterSetFunctionService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult meterSetFunction(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String meterId = (String) dto.get("meterId");
            if (meterId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            FnMeter meter = fnMeterService.queryMeterById(meterId);
            if (meter == null) {
                throw new Exception("仪表不存在");
            }
            Map<String,Object> objectMap = new HashMap<String,Object>();
            String protocolId = meter.getProtocolId();
            Map<String, FnProtocol> protocolmap = ConstantDBBaseData.ProtocolMap;
            FnProtocol fnProtocol = protocolmap.get(protocolId);
            String extend = fnProtocol.getExtend();
            if(extend==null||"".equals(extend)){
                objectMap.put("status", EnumYesNo.NO.getValue());
                objectMap.put("reason", "该仪表不支持设置功能");
            }else{
                Map map = SystemConstant.jsonMapper.readValue(extend, Map.class);
                objectMap.put("status", EnumYesNo.YES.getValue());
                map.put("meterType", meter.getMeterType());
                objectMap.put("result", map);
            }
            content.add(objectMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTMeterSetFunctionService");
        }
    }


}
