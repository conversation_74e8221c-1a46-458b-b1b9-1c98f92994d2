package com.persagy.finein.fnmanage.param.handler.impl;

import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.FnTenantPostPayParam;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.EnumAmountTypeLimit;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.fnmanage.param.handler.FNTTenantParamPostPayHandler;
import com.persagy.finein.service.FNRecordPostClearingPayService;
import com.persagy.finein.service.FNTenantDataService;
import com.persagy.finein.service.FNTenantPostPayParamService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月21日 下午1:45:10

* 说明:
*/
@Component("FNTTenantParamPostPayHandler")
public class FNTTenantParamPostPayHandlerImpl implements FNTTenantParamPostPayHandler{
	
	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;
	
	@Resource(name = "FNTenantPostPayParamService")
	private FNTenantPostPayParamService FNTenantPostPayParamService;
	
	@Resource(name = "FNRecordPostClearingPayService")
	private FNRecordPostClearingPayService FNRecordPostClearingPayService;
	
	@Resource(name = "FNTenantDataService")
	private FNTenantDataService FNTenantDataService;
	
	public synchronized void handle(Building building,FnTenant tenant,FnTenantPayType tenantPayType) throws Exception{
		
		FnTenantPostPayParam query = new FnTenantPostPayParam();
		query.setBuildingId(tenant.getBuildingId());
		query.setTenantId(tenant.getId());
		query.setEnergyTypeId(tenantPayType.getEnergyTypeId());
		
		FnTenantPostPayParam param = (FnTenantPostPayParam)FNTenantPostPayParamService.queryObject(query);
		
		FnTenantPostPayParam update = new FnTenantPostPayParam();
		
		Date lastClearingTime = FNRecordPostClearingPayService.queryLastClearingTime(tenant.getId(), tenantPayType.getEnergyTypeId(), tenant.getActiveTime());
		Map<String,Object> billingMap = FNRecordPostClearingPayService.queryBilling(building.getId(), tenant.getId(), tenantPayType.getEnergyTypeId());
		Double noClearingEnergy = FNTenantDataService.queryDataGte(building.getId(), tenant.getId(), EnumTimeType.T2, tenantPayType.getEnergyTypeId(), lastClearingTime, EnumEnergyMoney.Energy);
		Double noClearingMoney = FNTenantDataService.queryDataGte(building.getId(), tenant.getId(), EnumTimeType.T2, tenantPayType.getEnergyTypeId(), lastClearingTime, EnumEnergyMoney.Money);
		Integer orderCount = (Integer)billingMap.get("orderCount");
		Double billingEnergy = DoubleFormatUtil.Instance().getDoubleData(billingMap.get("totalEnergy"));
		Double billingMoney = DoubleFormatUtil.Instance().getDoubleData(billingMap.get("totalMoney"));
		update.setLastClearingTime(lastClearingTime);
		update.setNoPayOrderCount(orderCount);
		update.setBillingEnergy(billingEnergy);
		update.setBillingMoney(billingMoney);
		update.setNoBillingEnergy(noClearingEnergy);
		update.setNoBillingMoney(noClearingMoney);
		update.setLastUpdateTime(new Date());
		
		Date now = new Date();
		long diff = now.getTime() - lastClearingTime.getTime();
		
		if(diff >= FineinConstant.Time.Day_1 * 30 * 6){
			update.setNoBillingType(EnumAmountTypeLimit.Over_Six_Month.getValue());
		}else if(diff >= FineinConstant.Time.Day_1 * 30 * 3 && diff < FineinConstant.Time.Day_1 * 30 * 6){
			update.setNoBillingType(EnumAmountTypeLimit.Over_Three_Month.getValue());
		}else if(diff >= FineinConstant.Time.Day_1 * 30 * 1 && diff < FineinConstant.Time.Day_1 * 30 * 3){
			update.setNoBillingType(EnumAmountTypeLimit.Over_One_Month.getValue());
		}else{
			update.setNoBillingType(EnumAmountTypeLimit.Less_One_Month.getValue());
		}
		
		if(param == null){//保存
			update.setBuildingId(tenant.getBuildingId());
			update.setTenantId(tenant.getId());
			update.setEnergyTypeId(tenantPayType.getEnergyTypeId());
			
			FNTenantPostPayParamService.save(update);
		}else{//更新
			//设置null
			if(update.getLastClearingTime() == null){
				update.setSpecialOperation("lastClearingTime", SpecialOperator.$exists, false);
			}
			if(update.getNoBillingEnergy() == null){
				update.setSpecialOperation("noBillingEnergy", SpecialOperator.$exists, false);
			}
			if(update.getNoBillingMoney() == null){
				update.setSpecialOperation("noBillingMoney", SpecialOperator.$exists, false);
			}
			if(update.getNoPayOrderCount() == null){
				update.setSpecialOperation("noPayOrderCount", SpecialOperator.$exists, false);
			}
			if(update.getBillingEnergy() == null){
				update.setSpecialOperation("billingEnergy", SpecialOperator.$exists, false);
			}
			if(update.getBillingMoney() == null){
				update.setSpecialOperation("billingMoney", SpecialOperator.$exists, false);
			}
			FNTenantPostPayParamService.update(query,update);
		}
	}
}

