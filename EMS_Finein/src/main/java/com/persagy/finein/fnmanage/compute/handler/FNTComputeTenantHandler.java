package com.persagy.finein.fnmanage.compute.handler;

import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.pojo.finein.FnPriceTemplate;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.ems.pojo.finein.dictionary.Project;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月22日 下午12:58:25

* 说明:租户计算线程
*/
public interface FNTComputeTenantHandler{
	
	public void process();

	public void processBuilding(Project project, Building building) throws Exception;

	public void processTenant(Project project, Building building, FnTenant tenant, Map<String, Map<String, Double>> buildingDataMap) throws Exception;

	public void processTenantBackPay(Project project, Building building, FnTenant tenant, Date timeFrom, Date timeTo, String energyTypeId, int prePayChargeType) throws Exception;

	public void processTenantData(Building building, FnTenant tenant, String energyTypeId, Date timeFrom, Date timeTo, List<DTOMeter> meterList, Map<String, Map<String, Double>> buildingDataMap) throws Exception;

	public Map<String,Object> processMeterCommon(Project project, Building building, FnTenant tenant, DTOMeter meter, String energyTypeId, FnPriceTemplate priceTemplate) throws Exception;

	public Map<String,Object> processMeterMultiple(Project project, Building building, FnTenant tenant, DTOMeter meter, int jFunctionId,
                                                   int fFunctionId, int gFunctionId, int pFunctionId,
                                                   String energyTypeId, FnPriceTemplate priceTemplate) throws Exception;

	public Map<String,Object> processMeterFunctionCommon(Project project, Building building, FnTenant tenant, DTOMeter meter, String energyTypeId, int srcFunctionId, int destFunctionId, Double price, boolean isOnlyComputeMoeny) throws Exception;
	
//	public void processTenantMeterStat(String buildingId,String tenantId,String meterId,String energyTypeId,EnumTimeType timeType,int functionId,EnumEnergyMoney energyMoney,Date timeFrom,Double data) throws Exception;
//	
//	public void processTenantStat(String buildingId,String tenantId,String energyTypeId,EnumTimeType timeType,EnumEnergyMoney energyMoney,Date timeFrom,Double data) throws Exception;
	
}

