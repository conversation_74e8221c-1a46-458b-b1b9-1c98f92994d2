package com.persagy.finein.fnmanage.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.finein.FnRemoteRechargeStatus;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：自动发送短信设置
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FntPrePayBlacklistTenantListController extends BaseController {

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @Resource(name = "FNRemoteRechargeStatusService")
    private FNRemoteRechargeStatusService fnRemoteRechargeStatusService;


    @RequestMapping("FNTPrePayBlacklistTenantListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public InterfaceResult prePayBlacklistTenantList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String like = (String) dto.get("like");
            if (like != null) {
                like = like.trim().toLowerCase();
            }
            Map<String, FnRemoteRechargeStatus> blackListMap = fnRemoteRechargeStatusService.queryMap(buildingId,
                    EnumYesNo.NO);
            Map<String, Building> buildingMap = fnBuildingService.query();
            List<Object> list = new ArrayList<>();
            List<FnTenant> tenantList = fnTenantService.queryTenantLikeByBuildingId(buildingId, like);
            if (tenantList != null && tenantList.size() > 0) {
                for (FnTenant fnTenant : tenantList) {
                    if (blackListMap.containsKey(fnTenant.getId())) {
                        continue;
                    }
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("tenantId", fnTenant.getId());
                    map.put("tenantName", fnTenant.getName());
                    map.put("buildingName", buildingMap.get(fnTenant.getBuildingId()).getName());
                    map.put("buildingId", fnTenant.getBuildingId());
                    list.add(map);
                }
            }
            content.add(list);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTPrePayBlacklistTenantListService");
        }
    }


}
