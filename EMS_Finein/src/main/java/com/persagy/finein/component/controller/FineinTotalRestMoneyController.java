package com.persagy.finein.component.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPrePayParam;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.enumeration.EnumTenantStatus;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.service.FNProjectService;
import com.persagy.finein.service.FNTenantPrePayParamService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FineinTotalRestMoneyController extends BaseController {

    @Resource(name = "FNProjectService")
    private FNProjectService fnProjectService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantPrePayParamService")
    private FNTenantPrePayParamService fnTenantPrePayParamService;

    @RequestMapping("FineinTotalRestMoneyService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult fineinTotalRestMoney(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String)dto.get("energyTypeId");
            if(energyTypeId == null){
                energyTypeId = FineinConstant.EnergyType.Dian;
            }

            Project project = fnProjectService.queryProject();

            if(project == null){
                throw new Exception("project不能为空");
            }
            Integer chargeType = null;
            Double totalRemain = null;
            List<FnTenant> tenantList = fnTenantService.queryListByTenantValidStatus(project.getId(), EnumTenantStatus.ACTIVATED, EnumValidStatus.VALID);
            if(tenantList != null && tenantList.size() > 0){
                Map<String, FnTenantPrePayParam> paramMap = fnTenantPrePayParamService.queryTenantPreParamByBuilding(project.getId(), energyTypeId);
                for(FnTenant tenant : tenantList){
                    FnTenantPrePayParam param = paramMap.get(tenant.getId());
                    if(param == null){
                        continue;
                    }

                    if(chargeType == null && param.getPrepayChargeType() != null){
                        chargeType = param.getPrepayChargeType();
                    }

                    if(chargeType != null && chargeType.intValue() == param.getPrepayChargeType()){
                        if(param.getRemainData() != null){
                            totalRemain = totalRemain == null ? param.getRemainData() : param.getRemainData() + totalRemain;
                        }
                    }
                }
            }

            Boolean ifMoney = null;
            if(chargeType != null && chargeType.intValue() == EnumPrepayChargeType.Qian.getValue()){
                ifMoney = true;
            }else if(chargeType != null && chargeType.intValue() == EnumPrepayChargeType.Liang.getValue()){
                ifMoney = false;
            }

            Map<String,Object> contentMap = new HashMap<String, Object>();
            contentMap.put("ifMoney", ifMoney );
            contentMap.put("restData", totalRemain );
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FineinTotalRestMoneyService");
        }
    }
}
