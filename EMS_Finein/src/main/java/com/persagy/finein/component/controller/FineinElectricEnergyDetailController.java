package com.persagy.finein.component.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumTenantStatus;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FineinElectricEnergyDetailController extends BaseController {

    @Resource(name = "FNProjectService")
    private FNProjectService fnProjectService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNTenantPrePayParamService")
    private FNTenantPrePayParamService fnTenantPrePayParamService;

    @Resource(name = "FNTenantPostPayParamService")
    private FNTenantPostPayParamService fnTenantPostPayParamService;


    @RequestMapping("FineinElectricEnergyDetail")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult fineinElectricEnergyDetail(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Project project = fnProjectService.queryProject();

            if(project == null){
                throw new Exception("project不能为空");
            }

            List<FnTenant> tenantList = fnTenantService.queryListByTenantValidStatus(project.getId(), EnumTenantStatus.ACTIVATED, EnumValidStatus.VALID);
            Map<String,Map<String, FnTenantPayType>> tenantPayTypeMap = fnTenantPayTypeService.queryByBuildingId(project.getId());
            Map<String, FnTenantPrePayParam> prePayParamMap = fnTenantPrePayParamService.queryTenantPreParamByBuilding(project.getId(), FineinConstant.EnergyType.Dian);
            Map<String, FnTenantPostPayParam> postPayParamMap = fnTenantPostPayParamService.queryTenantPostParamByBuilding(project.getId(), FineinConstant.EnergyType.Dian);


            for(FnTenantType tenantType : ConstantDBBaseData.TenantTypeList){
                Map<String,Object> contentMap = new HashMap<String, Object>();
                contentMap.put("industryName", tenantType.getName());
                List<Object> dataList = new ArrayList<>();
                contentMap.put("data", dataList);
                content.add(contentMap);

                if(tenantList != null && tenantList.size() > 0){
                    for(FnTenant tenant : tenantList){
                        if(!tenant.getTenantTypeId().equals(tenantType.getId())){
                            continue;
                        }
                        if(!tenantPayTypeMap.containsKey(tenant.getId())){
                            continue;
                        }
                        if(!tenantPayTypeMap.get(tenant.getId()).containsKey(FineinConstant.EnergyType.Dian)){
                            continue;
                        }
                        Map<String,Object> tenantMap = new HashMap<>();
                        tenantMap.put("tenementId", tenant.getId());
                        tenantMap.put("name", tenant.getName());
                        tenantMap.put("code", tenant.getTenantTypeId());

                        FnTenantPayType tenantPayType = tenantPayTypeMap.get(tenant.getId()).get(FineinConstant.EnergyType.Dian);
                        if(tenantPayType.getPayType().intValue() == EnumPayType.POSTPAY.getValue()){//后付费
                            tenantMap.put("payType", EnumPayType.POSTPAY.getValue());
                            FnTenantPostPayParam param = postPayParamMap.get(tenant.getId());
                            tenantMap.put("dayUp", null);
                            tenantMap.put("dayDown", null);
                            tenantMap.put("lessData", null);
                            tenantMap.put("ownMoney", param == null ? null : param.getBillingEnergy());
                            Integer noBillingOrderCount = null;
                            try {
                                noBillingOrderCount = param.getNoPayOrderCount();
                            } catch (Exception e) {
                            }

                            tenantMap.put("ifNomarl", noBillingOrderCount == null || noBillingOrderCount.intValue() == 0 ? false : true);
                        }else{
                            tenantMap.put("payType", EnumPayType.PREPAY.getValue());
                            FnTenantPrePayParam param = prePayParamMap.get(tenant.getId());
                            String remainDays = null;
                            Integer minDays = null;
                            Integer maxDays = null;
                            try {
                                remainDays = param.getRemainDays();
                                String[] remainDaysArray = remainDays.split("~");
                                minDays = Integer.parseInt(remainDaysArray[0]);
                                maxDays = Integer.parseInt(remainDaysArray[1]);
                            } catch (Exception e) {
                            }
                            tenantMap.put("dayUp", maxDays);
                            tenantMap.put("dayDown", minDays);
                            tenantMap.put("lessData", param == null ? null : param.getRemainData());
                            tenantMap.put("ownMoney", null);
                            boolean ifNomarl = true;
                            if(param.getIsAlarm() != null && param.getIsAlarm().intValue() == EnumYesNo.YES.getValue()){
                                ifNomarl = false;
                            }
                            tenantMap.put("ifNormal", ifNomarl);
                        }
                        dataList.add(tenantMap);
                    }
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FineinElectricEnergyDetail");
        }
    }
}
