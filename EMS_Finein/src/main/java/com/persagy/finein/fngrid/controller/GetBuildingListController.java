package com.persagy.finein.fngrid.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.service.FNBuildingService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class GetBuildingListController extends BaseController {

    @Resource(name = "FNBuildingService")
    private FNBuildingService fnBuildingService;

    @RequestMapping("getBuildList")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult getBuildingList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Building buildingQuery = new Building();
            buildingQuery.setSort("id", EMSOrder.Asc);
            for(Building temp : fnBuildingService.queryList(buildingQuery)){
                Map resultInner = new LinkedHashMap<>();
                resultInner.put("id", temp.getId());
                resultInner.put("name", temp.getName());
                resultInner.put("main", false);
//			resultInner.put("parentId",);
                resultInner.put("level",1);
                content.add(resultInner);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"GetBuildingList");
        }
    }
}
