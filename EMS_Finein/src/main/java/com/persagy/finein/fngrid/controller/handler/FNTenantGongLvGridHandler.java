package com.persagy.finein.fngrid.controller.handler;

import java.io.File;
import java.util.Map;

/**
 * Description:   
 * Company: Persagy 
 * <AUTHOR> 
 * @version 1.0
 * @since: 2019年9月12日: 下午12:00:29
 * Update By 邵泓博 2019年9月12日: 下午12:00:29
 */

public interface FNTenantGongLvGridHandler {



	
	 /**
	 * Description: 
	 * @param tempf
	 * @param resourceId
	 * @param path
	 * @param dto
	 * @param tenantListMap void
	 * <AUTHOR>
	 * @return 
	 * @throws Exception 
	 * @since 2019年9月28日: 下午2:11:56
	 * Update By 邵泓博 2019年9月28日: 下午2:11:56
	 */ 
	File process(String buildingId, File tempf, String resourceId, String path, Map dto) throws Exception;

}
