package com.persagy.finein.fngrid.controller.handler.impl;

import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnEnergyType;
import com.persagy.ems.pojo.finein.FnProtocolFunction;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.meterdata.MeterData;
import com.persagy.ems.pojo.originaldata.MonthData;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumPrePayType;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.fngrid.controller.handler.FNGGridCommonHandler;
import com.persagy.finein.service.FNMeterDataService;
import com.persagy.finein.service.FNOriginalDataService;
import com.persagy.finein.service.FNTenantPayTypeService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


@Service("FNGGridCommonHandler")
public class FNGGridCommonHandlerImpl implements FNGGridCommonHandler{
	
	@Resource(name ="FNMeterDataService")
	private FNMeterDataService FNMeterDataService;
	
	@Resource(name ="FNOriginalDataService")
	private FNOriginalDataService FNOriginalDataService;
	
	@Resource(name ="FNTenantPayTypeService")
	private FNTenantPayTypeService FNTenantPayTypeService;
	
	@Resource(name ="FNTenantService")
	private FNTenantService FNTenantService;
	

	
	public  Double queryMeterData(String buildingId, String meterId, Integer functionId, Date timeFrom,EnumTimeType timeType)
			throws Exception {
		MeterData data = FNMeterDataService.queryMeterDataEqual(buildingId, meterId,
				functionId, timeType.getValue().intValue(), timeFrom);
		if (data != null && data.getData() != null) {
			return data.getData();
		} else {// 查询原始数据
			Date from = new Date(timeFrom.getTime() - FineinConstant.Time.Minute_15 * 2);
			Date to = new Date(timeFrom.getTime() + FineinConstant.Time.Minute_15 * 2);
			MonthData monthData = FNOriginalDataService.queryLastMonthDataGteLte(buildingId,
					meterId, functionId, from, to);
			if (monthData != null && monthData.getData() != null) {
				return monthData.getData();
			}
		}
		return null;
	}
	
	
	public static String getFunctionName(String str) {
		String name = null;
		switch (str) {
		case "J":
			name = "尖段";
			break;
		case "F":
			name = "峰段";
			break;
		case "G":
			name = "谷段";
			break;
		case "P":
			name = "平段";
			break;
		default:
			break;
		}
		return name;
	}
	
	
	public void processTenantPrePayList(String buildingId, List<FnTenant> fnTenantList,
			Map<String, List<FnTenant>> tenantListMap) throws Exception {
		Map<String, Map<String, FnTenantPayType>> tenantPayTypeMap = FNTenantPayTypeService
				.queryByBuildingId(buildingId);
		for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
			String ruanKey = energyType.getId() + "-" + 0;
			String biaoKey = energyType.getId() + "-" + 1;
			for (FnTenant fnTenant : fnTenantList) {
				Map<String, FnTenantPayType> tenantPayType = tenantPayTypeMap.get(fnTenant.getId());
				if (tenantPayType == null) {
					continue;
				}
				FnTenantPayType fnTenantPayType = tenantPayType.get(energyType.getId());
				if (fnTenantPayType == null) {
					continue;
				}
				if (fnTenantPayType.getPayType() == EnumPayType.PREPAY.getValue().intValue()) {// 预付费
					if (fnTenantPayType.getPrePayType() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
						if (tenantListMap.get(ruanKey) == null) {
							tenantListMap.put(ruanKey, new ArrayList<FnTenant>());
						}
						tenantListMap.get(ruanKey).add(fnTenant);
					} else {
						if (tenantListMap.get(biaoKey) == null) {
							tenantListMap.put(biaoKey, new ArrayList<FnTenant>());
						}
						tenantListMap.get(biaoKey).add(fnTenant);

					}
				}
			}
		}
	}


	
	 /**
	 *(non-Javadoc)
	 * @see FNGGridCommonHandler#processTenanPayTypetList(String, List, Map)
	 */
	 
	@Override
	public void processTenanPayTypetList(String buildingId, List<FnTenant> fnTenantList,
			Map<String, List<FnTenant>> tenantListMap) throws Exception {

		for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
			String prePayKey = energyType.getId() + "-" + 0;
			String postKey = energyType.getId() + "-" + 1;
			Map<String, EnumPayType> tenantPayTypeMap = FNTenantService.queryTenantPayType(buildingId,
					energyType.getId());
			for (FnTenant fnTenant : fnTenantList) {
				EnumPayType payType = tenantPayTypeMap.get(fnTenant.getId());
				if (payType == null) {
					continue;
				}
				if (payType.getValue().intValue() == EnumPayType.PREPAY.getValue().intValue()) {// 预付费
					if (tenantListMap.get(prePayKey) == null) {
						tenantListMap.put(prePayKey, new ArrayList<FnTenant>());
					}
					tenantListMap.get(prePayKey).add(fnTenant);
				} else {
					// 预付费
					if (tenantListMap.get(postKey) == null) {
						tenantListMap.put(postKey, new ArrayList<FnTenant>());
					}
					tenantListMap.get(postKey).add(fnTenant);
				}
			}
		}
	
	}
	
	public Integer queryCumulantFunctionId(String protocolId, String energyTypeId, int functionId) {
		List<FnProtocolFunction> functionList = ConstantDBBaseData.ProtocolFunctionMap.get(protocolId);
		if (functionList != null) {
			for (FnProtocolFunction protocolFunction : functionList) {
				if (protocolFunction.getFunctionId().intValue() == functionId) {
					return functionId;
				}
			}
		}
		return null;
	}
}
