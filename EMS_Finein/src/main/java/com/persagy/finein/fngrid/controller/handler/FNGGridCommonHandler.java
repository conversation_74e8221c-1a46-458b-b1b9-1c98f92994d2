package com.persagy.finein.fngrid.controller.handler;

import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.finein.enumeration.EnumTimeType;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Description:   
 * Company: Persagy 
 * <AUTHOR> 
 * @version 1.0
 * @since: 2019年9月12日: 下午12:00:29
 * Update By 邵泓博 2019年9月12日: 下午12:00:29
 */

public interface FNGGridCommonHandler {
	
	public  Double queryMeterData(String buildingId, String meterId, Integer functionId, Date timeFrom, EnumTimeType timeType)
			throws Exception;
	
	public void processTenantPrePayList(String buildingId, List<FnTenant> fnTenantList,
                                        Map<String, List<FnTenant>> tenantListMap) throws Exception;
	
	public void processTenanPayTypetList(String buildingId, List<FnTenant> fnTenantList,
                                         Map<String, List<FnTenant>> tenantListMap) throws Exception;
	
	public Integer queryCumulantFunctionId(String protocolId, String energyTypeId, int functionId);
}
