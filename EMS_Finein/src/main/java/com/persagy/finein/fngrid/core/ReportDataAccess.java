package com.persagy.finein.fngrid.core;

import com.pbsage.ems.excel.data.DoubleTimeObject;
import com.pbsage.ems.excel.data.MyDataAccess;
import com.pbsage.ems.excel.var.VarObject;
import com.persagy.core.component.JsonObjectMapper;
import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dictionary.dto.DictionaryDynamic;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.pojo.ac.AcBuilding;
import com.persagy.ems.pojo.dictionary.meter.DictionaryFunction;
import com.persagy.ems.pojo.meterdata.MeterData;
import com.persagy.ems.pojo.originaldata.MonthData;
import com.persagy.ems.pojo.originaldata.StatData;
import com.persagy.ems.pojo.servicedata.ServiceData;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.beans.PropertyDescriptor;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 
 * 功能描述 
 * @类型名称 ReportDataAccess
 * @创建者 wanghailong
 * @创建时间 2017年4月13日 下午3:49:19
 * @修改者 wanghailong
 * @修改时间 2017年4月13日 下午3:49:19
 * @邮箱 <EMAIL>  
 * @修改描述
 */

@Component("ReportDataAccess")
public class ReportDataAccess extends MyDataAccess {

	@Resource(name = "objectMapper")
	protected JsonObjectMapper objectMapper;

	@Resource(name = "coreService")
	protected CoreService coreService;
	
	@Override
    public String[] GetAllVariableType() {
        String[] variableTypes = super.GetAllVariableType();
        List<String> list = new ArrayList<>(Arrays.asList(variableTypes));
        list.add("DTe");
        list.add("DTp");
        String[] s = new String[list.size()]; 
        return list.toArray(s);
    }

    @Override
    public void ParseVar(String varSplit, VarObject result) {
    	if (varSplit.startsWith("DTe")) {
    		result.datatype = varSplit.substring(0, 3);
    		result.signMap.put("DTe", varSplit.substring(3));
    	}else if(varSplit.startsWith("DTp")) {
    		result.datatype = varSplit.substring(0, 3);
    		result.signMap.put("DTp", varSplit.substring(3));
    	}
        super.ParseVar(varSplit, result);
    }
	
	
	@SuppressWarnings("rawtypes")
	@Override
	public List<DoubleTimeObject> GetData(String datatype, Map<String, String> signMap, int timeType, Date timeFrom, Date timeTo) {
		List<DoubleTimeObject> result = new ArrayList<DoubleTimeObject>();
		String buildingId = signMap.get("Building");
		//查询能耗数据
		if(datatype.equals("Mr")){
			//仪表原始数据
			String functionIds = signMap.get("Meter");
			String sign = null;
			Integer funcid = null;
			if(functionIds.contains(".") && !"".equals(functionIds) && null != functionIds){
				int temp = functionIds.indexOf(".");
				sign = functionIds.substring(0, temp);
				String temp1 = functionIds.substring(temp+1, functionIds.length());
				funcid = Integer.valueOf(temp1);
			}else{
				sign = signMap.get("Meter");
				funcid = null ==signMap.get("Function") ?10101:new Integer(signMap.get("Function"));
			}
			try {
				
				DictionaryFunction df = new DictionaryFunction();
				df.setId(funcid.toString());
				df = coreService.query(df).get(0);
				String remark = df.getRemark();
				String entityName = null;
				if(!"".equals(remark) && null != remark){
					Map map = SystemConstant.jsonMapper.readValue(remark, Map.class);
					entityName = (String) map.get("entityName");
				}
				
				if("monthdata".equals(entityName)){
					MonthData monthData = new MonthData();
					monthData.setBuildingForContainer(buildingId);
//					monthData.setSign(signMap.get("Meter"));
//					monthData.setFuncid(null ==signMap.get("Function") ?10101:new Integer(signMap.get("Function")));
					monthData.setSign(sign);
					monthData.setFuncid(funcid);
					monthData.setSpecialOperation("receivetime", SpecialOperator.$gte, timeFrom);
					monthData.setSpecialOperation("receivetime", SpecialOperator.$lt, timeTo);
					try {
						List<MonthData> monthDataList = coreService.query(monthData);
						this.buildResult(result, monthDataList, "data", "receivetime");
					} catch (Exception e) {
						throw new RuntimeException(e);
					}
				}else if("electriccurrentdata".equals(entityName)){
					StatData statData = new StatData();
					statData.setBuildingForContainer(buildingId);
					statData.setValueType(1);//取实测数据
					statData.setTimeType(3);//取小时数据
					statData.setSign(sign);
					statData.setFuncId(funcid);
					statData.setSpecialOperation("countTime", SpecialOperator.$gte, timeFrom);
					statData.setSpecialOperation("countTime", SpecialOperator.$lt, timeTo);
					try {
						List<StatData> monthDataList = coreService.query(statData);
						this.buildResult(result, monthDataList, "data", "countTime");
					} catch (Exception e) {
						throw new RuntimeException(e);
					}
				}
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}else if(datatype.equals("Mo")){
			//仪表分精度数据
			DictionaryDynamic interfaceObject = new DictionaryDynamic();
			interfaceObject.setCategory("MT");
			interfaceObject.setElementId(signMap.get("Meter"));
			interfaceObject.setCode(null ==signMap.get("Function") ?"10101":signMap.get("Function"));
			
			MeterData meterData = new MeterData();
			meterData.setBuildingForContainer(buildingId);
			meterData.setSplitTimeType(timeType);
			meterData.setSign(signMap.get("Meter"));
			meterData.setFuncid(null ==signMap.get("Function") ?10101:new Integer(signMap.get("Function")));
			meterData.setSpecialOperation("receivetime", SpecialOperator.$gte, timeFrom);
			meterData.setSpecialOperation("receivetime", SpecialOperator.$lt, timeTo);
			try {
				List<MeterData> meterDataList = coreService.query(meterData);
				this.buildResult(result, meterDataList, "data", "receivetime");
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}else if(datatype.equals("Ms")){
			//仪表能耗数据
			DictionaryDynamic interfaceObject = new DictionaryDynamic();
			interfaceObject.setCategory("MT");
			interfaceObject.setElementId(signMap.get("Meter"));
			interfaceObject.setCode(null ==signMap.get("Function") ?"10101":signMap.get("Function"));
			
			ServiceData serviceData = new ServiceData();
			serviceData.setBuildingForContainer(buildingId);
			serviceData.setSplitTimeType(timeType);
			serviceData.setSign(signMap.get("Meter"));
			serviceData.setFuncid(null ==signMap.get("Function") ?10101:new Integer(signMap.get("Function")));
			serviceData.setSpecialOperation("timefrom", SpecialOperator.$gte, timeFrom);
			serviceData.setSpecialOperation("timefrom", SpecialOperator.$lt, timeTo);
			try {
				List<ServiceData> meterDataList = coreService.query(serviceData);
				this.buildResult(result, meterDataList, "data", "timefrom");
			} catch (Exception e) {
				throw new RuntimeException(e);
			}
		}
		return result;
	}

	@Override
	public String GetAttribute(String type, Map<String, String> signMap, String attribute) {
		if (type.equals("Expression") && attribute.equals("name")) {
			String Expression = signMap.get("Expression");
			String[] splits = Expression.split("=");
			if (splits.length > 1) {
				return splits[0];
			}
		}
		// 查询对象的名称
		String attrName = "未知";
		try {
			if (type.equals("building")) {
				AcBuilding building = new AcBuilding();
				building.setId(signMap.get("Building"));
				List<AcBuilding> buildingList = coreService.query(building);
				if (buildingList.size() > 0) {
					building = (AcBuilding) buildingList.get(0);
					attrName = building.getName();
				}
			}else if (type.equals("Meter")) {
				attrName = signMap.get("Meter");
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		return attrName;
	}
	
	@SuppressWarnings("rawtypes")
	private void buildResult(List<DoubleTimeObject> result, List dataList, String dataField, String timeFromField) throws Exception {
		for(Object obj : dataList){
			DoubleTimeObject dto = new DoubleTimeObject();
			result.add(dto);
			if(obj instanceof Map){
				Map map = (Map)obj;
				Object dataObject = map.get(dataField);
				if(dataObject!=null){
					if(dataObject instanceof Double){
						dto.data = (double) dataObject;
					}else{
						dto.data = new Double(dataObject.toString());
					}
				}
				Object timeFromObject = map.get(timeFromField);
				if(timeFromObject!=null){
					if(timeFromObject instanceof Date){
						dto.timefrom = (Date) timeFromObject;
					}else if(timeFromObject instanceof String && !"".equals(timeFromObject)){
						dto.timefrom = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse((String)timeFromObject);
					}
				}
			}else{
				PropertyDescriptor pd = new PropertyDescriptor(dataField,obj.getClass());
				Class<?> classType = pd.getPropertyType();
				Object value = pd.getReadMethod().invoke(obj);
				if(classType == Double.class){
					dto.data = (Double)value;
				}else{
					dto.data = new Double(value.toString());
				}
				
				pd = new PropertyDescriptor(timeFromField,obj.getClass());
				classType = pd.getPropertyType();
				value = pd.getReadMethod().invoke(obj);
				if(classType == Date.class){
					dto.timefrom = (Date) value;
				}else if(classType == String.class && !"".equals(value)){
					dto.timefrom = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse((String)value);
				}
			}
		}
	}
}
