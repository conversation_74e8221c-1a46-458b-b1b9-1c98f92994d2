package com.persagy.finein.fngrid.controller.handler.impl;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.dto.DTORoomMeter;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnRecordPrePay;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantBackPayRecord;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.enumeration.EnumPayBodyType;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.fngrid.controller.handler.FNGGridCommonHandler;
import com.persagy.finein.fngrid.controller.handler.FNPrePayGridHandler;
import com.persagy.finein.service.FNRecordPrePayService;
import com.persagy.finein.service.FNRoomService;
import com.persagy.finein.service.FNTenantBackPayRecordService;
import com.persagy.finein.service.FNTenantService;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;


@Service("FNPrePayGridHandler")
public class FNPrePayGridHandlerImpl implements FNPrePayGridHandler{

	@Resource(name = "FNGGridCommonHandler")
	private FNGGridCommonHandler FNGGridCommonHandler;
	
	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;
	
	@Resource(name = "FNTenantBackPayRecordService")
	private FNTenantBackPayRecordService FNTenantBackPayRecordService;
	
	@Resource(name = "FNRecordPrePayService")
	private FNRecordPrePayService FNRecordPrePayService;
	
	@Resource(name = "FNRoomService")
	private FNRoomService FNRoomService;
	
	protected final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	@Override
	public File process(String buildingId,File tempf, String resourceId, String path, Map dto) throws Exception {
		
		List<FnTenant> fnTenantList = FNTenantService.queryListByValidStatus(buildingId, EnumValidStatus.VALID);
		
		Map<String, List<FnTenant>> tenantListMap = new HashMap<String, List<FnTenant>>();
		FNGGridCommonHandler.processTenantPrePayList(buildingId, fnTenantList, tenantListMap);
		
	
		// String buildingName = (String) dto.get("buildingName");
		String timeFrom = (String) dto.get("startTime");
		String timeTo = (String) dto.get("endTime");
		Date from = standard.parse(timeFrom);
		Date to = standard.parse(timeTo);

		FileInputStream is = new FileInputStream(tempf);// 创建文件流
		HSSFWorkbook wb = new HSSFWorkbook(is);// 加载文件流
		FileOutputStream fout = null;

		for (Entry<String, List<FnTenant>> payMap : tenantListMap.entrySet()) {
			Double moneySum = 0.0;
			Double amountSum = 0.0;
			String key = payMap.getKey();
			String[] split = key.split("-");
			String energyTypeId = split[0];
			String energyTypeName = EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId);
			String energyUnit = UnitUtil.getCumulantUnit(energyTypeId);
			Integer prePayType = Integer.valueOf(split[1]);
			List<FnTenant> list = payMap.getValue();
			List<Object> tenantObjList = new ArrayList<>();
			if (prePayType == 0) {// 軟扣
				Map<String, List<FnRecordPrePay>> tenantRecordMap = new HashMap<>();
				for (FnTenant fnTenant : list) {
					List<FnRecordPrePay> recordList = new ArrayList<FnRecordPrePay>();
					// 查询租户仪表上次计算时间
					FnTenantBackPayRecord query = new FnTenantBackPayRecord();
					query.setTenantId(fnTenant.getId());
					query.setIsProcessed(EnumYesNo.YES.getValue().intValue());
					query.setEnergyTypeId(energyTypeId);
					query.setSort("lastUpdateTime", EMSOrder.Desc);
					query.setLimit((long) 1);
					List<FnTenantBackPayRecord> lastRecordList = FNTenantBackPayRecordService.query(query);
					if (lastRecordList != null && lastRecordList.size() > 0) {
						Date time = lastRecordList.get(0).getLastUpdateTime();
						if (time.getTime() >= standard.parse(timeTo).getTime()) {
							to = standard.parse(timeTo);
						} else {
							to = time;
						}
						List<FnRecordPrePay> recordPrePayList = FNRecordPrePayService
								.queryTenantPrePayRecord(fnTenant.getId(), energyTypeId, from, to);
						if (recordPrePayList != null && recordPrePayList.size() > 0) {
							recordList.addAll(recordPrePayList);
						}
					}

					if (recordList != null) {
						for (FnRecordPrePay recordPrePay : recordList) {
							if (!tenantRecordMap.containsKey(recordPrePay.getTenantId())) {
								tenantRecordMap.put(recordPrePay.getTenantId(), new ArrayList<FnRecordPrePay>());
							}
							tenantRecordMap.get(recordPrePay.getTenantId()).add(recordPrePay);
						}
					}
				}
				// Map<String, List<FnRecordPrePay>> tenantRecordMap = new
				// HashMap<>();
				if (list != null) {
					for (FnTenant tenant : list) {
						Map<String, Object> tenantMap = new HashMap<>();
						tenantMap.put("tenantId", tenant.getId());
						tenantMap.put("tenantName", tenant.getName());
						tenantMap.put("roomIds", tenant.getRoomCodes());
						tenantObjList.add(tenantMap);
						List<Object> orderList = new ArrayList<>();
						tenantMap.put("orderList", orderList);
						Double tenantMoneySum = 0.0;// 充值钱汇总
						Double tenantAmountSum = 0.0;// 充值量汇总
						List<FnRecordPrePay> recordPrePayList = tenantRecordMap.get(tenant.getId());
						if (recordPrePayList != null) {
							for (FnRecordPrePay recordPrePay : recordPrePayList) {
								Map<String, Object> recordMap = new HashMap<>();
								recordMap.put("payTime", standard.format(recordPrePay.getOperateTime()));
								recordMap.put("orderId", recordPrePay.getOrderId());
								recordMap.put("money", recordPrePay.getMoney());
								recordMap.put("amount", recordPrePay.getAmount());
								recordMap.put("userName", recordPrePay.getUserName());
								orderList.add(recordMap);

								moneySum += (recordPrePay.getMoney() == null ? 0.0 : recordPrePay.getMoney());
								tenantMoneySum += (recordPrePay.getMoney() == null ? 0.0 : recordPrePay.getMoney());
								amountSum += (recordPrePay.getAmount() == null ? 0.0 : recordPrePay.getAmount());
								tenantAmountSum += (recordPrePay.getAmount() == null ? 0.0 : recordPrePay.getAmount());
							}
						}
						tenantMap.put("tenantMoneySum", tenantMoneySum);
						tenantMap.put("tenantAmountSum", tenantAmountSum);
					}
				}
			} else {
				for (FnTenant fnTenant : list) {
					int orderCount = 0;
					Map<String, Object> tenantMap = new HashMap<>();
					tenantMap.put("tenantId", fnTenant.getId());
					tenantMap.put("tenantName", fnTenant.getName());
					List<Map<String, Object>> roomList = new ArrayList<>();
					tenantMap.put("roomList", roomList);
					tenantObjList.add(tenantMap);
					List<DTORoomMeter> DTORoomMeterList = FNRoomService.queryRoomListByTenantId(fnTenant.getId(),
							energyTypeId);
					Double tenantMoneySum = 0.0;// 充值钱汇总
					Double tenantAmountSum = 0.0;// 充值量汇总
					if (DTORoomMeterList != null) {
						for (DTORoomMeter dtoRoomMeter : DTORoomMeterList) {
							int roomOrderCount = 0;
							List<DTOMeter> meterList = dtoRoomMeter.getMeterList();
							if (meterList != null) {
								Map<String, Object> roomMap = new HashMap<>();
								roomList.add(roomMap);
								roomMap.put("roomCode", dtoRoomMeter.getRoomCode());
								List<Object> orderList = new ArrayList<>();
								roomMap.put("orderList", orderList);
								for (DTOMeter dtoMeter : meterList) {
									List<FnRecordPrePay> recordList = new ArrayList<FnRecordPrePay>();
									recordList = FNRecordPrePayService.queryTenantPrePayRecord(fnTenant.getId(),
											EnumPayBodyType.METER, energyTypeId, dtoMeter.getMeterId(), from, to);
									if (recordList != null) {
										for (FnRecordPrePay fnRecordPrePay : recordList) {
											Map<String, Object> recordMap = new HashMap<>();
											String extend = dtoMeter.getExtend();
											Map<String, Object> extendMap = SystemConstant.jsonMapper.readValue(extend,
													Map.class);
											recordMap.put("address", (String) extendMap.get("address"));
											recordMap.put("payTime", standard.format(fnRecordPrePay.getOperateTime()));
											recordMap.put("orderId", fnRecordPrePay.getOrderId());
											recordMap.put("money", fnRecordPrePay.getMoney());
											recordMap.put("amount", fnRecordPrePay.getAmount());
											recordMap.put("userName", fnRecordPrePay.getUserName());
											orderList.add(recordMap);
											// 账单充值量汇总
											amountSum += (fnRecordPrePay.getAmount() == null ? 0.0
													: fnRecordPrePay.getAmount());
											tenantAmountSum += (fnRecordPrePay.getAmount() == null ? 0.0
													: fnRecordPrePay.getAmount());
											moneySum += (fnRecordPrePay.getMoney() == null ? 0.0
													: fnRecordPrePay.getMoney());
											tenantMoneySum += (fnRecordPrePay.getMoney() == null ? 0.0
													: fnRecordPrePay.getMoney());
										}
										orderCount += recordList.size();// 充值记录汇总条数
										roomOrderCount += recordList.size();// 每个房间下充值记录条数
									}
								}
								roomMap.put("roomOrderCount", roomOrderCount);
							}
						}
					}
					tenantMap.put("orderCount", orderCount);
					tenantMap.put("tenantMoneySum", tenantMoneySum);
					tenantMap.put("tenantAmountSum", tenantAmountSum);
				}

			}
			Map<String, Object> contentObj = new HashMap<>();
			contentObj.put("energyUnit", energyUnit);
			contentObj.put("energyTypeName", energyTypeName);
			contentObj.put("tenantList", tenantObjList);
			contentObj.put("moneySum", moneySum);
			contentObj.put("amountSum", amountSum);
			this.buildPrePayGridExcel(energyTypeId, prePayType, contentObj, wb);
		}
		try {
			fout = new FileOutputStream(path);
			wb.write(fout);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (fout != null) {
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if (wb != null) {
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
		}
		return new File(path);
	}
	
	private void buildPrePayGridExcel(String energyTypeId, Integer prePayType, Map<String, Object> contentObj,
			HSSFWorkbook wb) {
		// String energyUnit = (String) contentObj.get("energyUnit");
		// String energyTypeName = (String) contentObj.get("energyTypeName");
		Double moneySum = (Double) contentObj.get("moneySum");
		Double amountSum = (Double) contentObj.get("amountSum");

		List<Map<String, Object>> tenantList = (List<Map<String, Object>>) contentObj.get("tenantList");
		String energyType = EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId);
		if (prePayType == 0) {
			processRuanKou(wb, moneySum, amountSum, tenantList, energyType);
		} else {
			processBiaoKou(wb, moneySum, amountSum, tenantList, energyType);
		}

	}
	
	@SuppressWarnings("deprecation")
	private void processRuanKou(HSSFWorkbook wb, Double moneySum, Double amountSum,
			List<Map<String, Object>> tenantList, String energyType) {
		HSSFSheet sheet = wb.getSheet(energyType + "-" + "软扣");
		if (tenantList != null) {
			int rowCount = 1;
			for (int i = 0; i < tenantList.size(); i++) {
				HSSFRow row = sheet.createRow((short) rowCount);
				Map<String, Object> tenant = (Map<String, Object>) tenantList.get(i);
				String tenantId = (String) tenant.get("tenantId");

				String tenantName = (String) tenant.get("tenantName");
				String roomIds = (String) tenant.get("roomIds");
				List<Object> orderList = (List<Object>) tenant.get("orderList");
				if (orderList != null && orderList.size() > 0) {
					{
						sheet.addMergedRegion(new CellRangeAddress(rowCount, rowCount + orderList.size() - 1, 0, 0));
						sheet.addMergedRegion(new CellRangeAddress(rowCount, rowCount + orderList.size() - 1, 1, 1));
						sheet.addMergedRegion(new CellRangeAddress(rowCount, rowCount + orderList.size() - 1, 2, 2));
					}
					{
						HSSFCell cell = row.createCell((short) (0));
						cell.setCellValue(tenantName == null ? " " : tenantName);
					}
					{
						HSSFCell cell = row.createCell((short) (1));
						cell.setCellValue(tenantId == null ? " " : tenantId);
					}

					{
						HSSFCell cell = row.createCell((short) (2));
						cell.setCellValue(roomIds == null ? " " : roomIds);
					}

					for (int j = 0; j < orderList.size(); j++) {
						if (j != 0) {
							row = sheet.createRow((short) rowCount + j);
						}
						Map<String, Object> orderMap = (Map<String, Object>) orderList.get(j);
						String payTime = (String) orderMap.get("payTime");
						String orderId = (String) orderMap.get("orderId");
						Double amount = DoubleFormatUtil.Instance().getDoubleData_00(orderMap.get("amount"));
						Double money = DoubleFormatUtil.Instance().getDoubleData_00(orderMap.get("money"));
						String userName = (String) orderMap.get("userName");
						{
							HSSFCell cell = row.createCell((short) (3));
							cell.setCellValue(payTime == null ? "--" : payTime);
						}
						{
							HSSFCell cell = row.createCell((short) (4));
							cell.setCellValue(orderId == null ? "--" : orderId);
						}
						{
							HSSFCell cell = row.createCell((short) (5));
							cell.setCellValue(money == null ? 0 : money);
						}
						{
							HSSFCell cell = row.createCell((short) (6));
							cell.setCellValue(amount == null ? 0 : amount);
						}
						{
							HSSFCell cell = row.createCell((short) (7));
							cell.setCellValue(userName == null ? "--" : userName);
						}
					}
					rowCount += orderList.size();
				} else {
					{
						HSSFCell cell = row.createCell((short) (0));
						cell.setCellValue(tenantName == null ? " " : tenantName);
					}
					{
						HSSFCell cell = row.createCell((short) (1));
						cell.setCellValue(tenantId == null ? " " : tenantId);
					}
					{
						HSSFCell cell = row.createCell((short) (2));
						cell.setCellValue(roomIds == null ? " " : roomIds);
					}
					{
						HSSFCell cell = row.createCell((short) (3));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (4));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (5));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (6));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (7));
						cell.setCellValue("--");
					}
					rowCount++;
				}
				HSSFRow row2 = sheet.createRow((short) rowCount);// 按租户充值汇总
				{
					HSSFCell cell = row2.createCell((short) (0));
					cell.setCellValue(tenantName + "合计充值");
				}
				{
					HSSFCell cell = row2.createCell((short) (1));
					cell.setCellValue("--");
				}
				{
					HSSFCell cell = row2.createCell((short) (2));
					cell.setCellValue("--");
				}
				{
					HSSFCell cell = row2.createCell((short) (3));
					cell.setCellValue("--");
				}
				{
					HSSFCell cell = row2.createCell((short) (4));
					cell.setCellValue("--");
				}
				{
					HSSFCell cell = row2.createCell((short) (6));
					cell.setCellValue((double) tenant.get("tenantAmountSum"));
				}
				{
					HSSFCell cell = row2.createCell((short) (5));
					cell.setCellValue((double) tenant.get("tenantMoneySum"));
				}
				{
					HSSFCell cell = row2.createCell((short) (7));
					cell.setCellValue("--");
				}
				rowCount++;
			}
			HSSFRow row3 = sheet.createRow((short) rowCount);// 按所选租户充值汇总
			{
				HSSFCell cell = row3.createCell((short) (0));
				cell.setCellValue("所选租户合计充值");
			}
			{
				HSSFCell cell = row3.createCell((short) (1));
				cell.setCellValue("--");
			}
			{
				HSSFCell cell = row3.createCell((short) (2));
				cell.setCellValue("--");
			}
			{
				HSSFCell cell = row3.createCell((short) (3));
				cell.setCellValue("--");
			}
			{
				HSSFCell cell = row3.createCell((short) (4));
				cell.setCellValue("--");
			}
			{
				HSSFCell cell = row3.createCell((short) (6));
				cell.setCellValue(amountSum);
			}
			{
				HSSFCell cell = row3.createCell((short) (5));
				cell.setCellValue(moneySum);
			}
			{
				HSSFCell cell = row3.createCell((short) (7));
				cell.setCellValue("--");
			}
		}
	}

	@SuppressWarnings("deprecation")
	private void processBiaoKou(HSSFWorkbook wb, Double moneySum, Double amountSum,
			List<Map<String, Object>> tenantList, String energyType) {
		HSSFSheet sheet = wb.getSheet(energyType + "-" + "表扣");
		int rowCount = 1;
		for (int i = 0; i < tenantList.size(); i++) {
			Map<String, Object> tenant = (Map<String, Object>) tenantList.get(i);
			String tenantId = (String) tenant.get("tenantId");
			int orderCount = (int) tenant.get("orderCount");
			String tenantName = (String) tenant.get("tenantName");
			List<Object> roomList = (List<Object>) tenant.get("roomList");
			HSSFRow row = sheet.createRow((short) rowCount);
			{
				HSSFCell cell = row.createCell((short) (0));
				cell.setCellValue(tenantName == null ? " " : tenantName);
			}
			{
				HSSFCell cell = row.createCell((short) (1));
				cell.setCellValue(tenantId == null ? " " : tenantId);
			}
			if (roomList != null && roomList.size() > 0) {
				if (orderCount > 0) {
					sheet.addMergedRegion(new CellRangeAddress(rowCount, rowCount + orderCount - 1, 0, 0));
					sheet.addMergedRegion(new CellRangeAddress(rowCount, rowCount + orderCount - 1, 1, 1));
				} else {
					sheet.addMergedRegion(new CellRangeAddress(rowCount, rowCount + roomList.size() - 1, 0, 0));
					sheet.addMergedRegion(new CellRangeAddress(rowCount, rowCount + roomList.size() - 1, 1, 1));
				}

				for (int j = 0; j < roomList.size(); j++) {
					if (j != 0) {
						row = sheet.createRow((short) rowCount);
					}
					Map<String, Object> roomMap = (Map<String, Object>) roomList.get(j);
					String roomCode = (String) roomMap.get("roomCode");
					int roomOrderCount = (int) roomMap.get("roomOrderCount");
					if (roomOrderCount > 0) {
						sheet.addMergedRegion(new CellRangeAddress(rowCount, rowCount + roomOrderCount - 1, 2, 2));
					}
					List<Map<String, Object>> orderList = (List<Map<String, Object>>) roomMap.get("orderList");
					if (orderList != null && orderList.size() != 0) {
						for (int k = 0; k < orderList.size(); k++) {
							if (k != 0) {
								row = sheet.createRow((short) rowCount);
							}
							Map<String, Object> map = orderList.get(k);
							Double amount = DoubleFormatUtil.Instance().getDoubleData_00(map.get("amount"));
							Double money = DoubleFormatUtil.Instance().getDoubleData_00(map.get("money"));
							String userName = (String) map.get("userName");
							String orderId = (String) map.get("orderId");
							String payTime = (String) map.get("payTime");
							String address = (String) map.get("address");

							{
								HSSFCell cell = row.createCell((short) (2));
								cell.setCellValue(roomCode == null ? "--" : roomCode);
							}
							{
								HSSFCell cell = row.createCell((short) (3));
								cell.setCellValue(address == null ? "--" : address);
							}
							{
								HSSFCell cell = row.createCell((short) (4));
								cell.setCellValue(payTime == null ? "--" : payTime);
							}
							{
								HSSFCell cell = row.createCell((short) (5));
								cell.setCellValue(orderId == null ? "--" : orderId);
							}
							{
								HSSFCell cell = row.createCell((short) (6));
								cell.setCellValue(money == null ? 0 : money);
							}
							{
								HSSFCell cell = row.createCell((short) (7));
								cell.setCellValue(amount == null ? 0 : amount);
							}
							{
								HSSFCell cell = row.createCell((short) (8));
								cell.setCellValue(userName == null ? "--" : userName);
							}
							rowCount++;
						}
					} else {
						{
							HSSFCell cell = row.createCell((short) (2));
							cell.setCellValue(roomCode == null ? "--" : roomCode);
						}

						{
							HSSFCell cell = row.createCell((short) (3));
							cell.setCellValue("--");
						}
						{
							HSSFCell cell = row.createCell((short) (4));
							cell.setCellValue("--");
						}
						{
							HSSFCell cell = row.createCell((short) (5));
							cell.setCellValue("--");
						}
						{
							HSSFCell cell = row.createCell((short) (6));
							cell.setCellValue("--");
						}
						{
							HSSFCell cell = row.createCell((short) (7));
							cell.setCellValue("--");
						}
						{
							HSSFCell cell = row.createCell((short) (8));
							cell.setCellValue("--");
						}
						rowCount++;
					}

				}
				HSSFRow row2 = sheet.createRow((short) rowCount);
				{
					HSSFCell cell = row2.createCell((short) (0));
					cell.setCellValue(tenantName + "合计充值");
				}
				{
					HSSFCell cell = row2.createCell((short) (1));
					cell.setCellValue("--");
				}
				{
					HSSFCell cell = row2.createCell((short) (2));
					cell.setCellValue("--");
				}
				{
					HSSFCell cell = row2.createCell((short) (3));
					cell.setCellValue("--");
				}
				{
					HSSFCell cell = row2.createCell((short) (4));
					cell.setCellValue("--");
				}
				{
					HSSFCell cell = row2.createCell((short) (5));
					cell.setCellValue("--");
				}

				{
					HSSFCell cell = row2.createCell((short) (7));
					cell.setCellValue((double) tenant.get("tenantAmountSum"));
				}
				{
					HSSFCell cell = row2.createCell((short) (6));
					cell.setCellValue((double) tenant.get("tenantMoneySum"));
				}
				{
					HSSFCell cell = row2.createCell((short) (8));
					cell.setCellValue("--");
				}
				rowCount++;
			}
			HSSFRow row3 = sheet.createRow((short) rowCount);
			{
				HSSFCell cell = row3.createCell((short) (0));
				cell.setCellValue("所选租户合计充值");
			}
			{
				HSSFCell cell = row3.createCell((short) (1));
				cell.setCellValue("--");
			}
			{
				HSSFCell cell = row3.createCell((short) (2));
				cell.setCellValue("--");
			}
			{
				HSSFCell cell = row3.createCell((short) (3));
				cell.setCellValue("--");
			}
			{
				HSSFCell cell = row3.createCell((short) (4));
				cell.setCellValue("--");
			}
			{
				HSSFCell cell = row3.createCell((short) (5));
				cell.setCellValue("--");
			}
			{
				HSSFCell cell = row3.createCell((short) (7));
				cell.setCellValue(amountSum);
			}
			{
				HSSFCell cell = row3.createCell((short) (6));
				cell.setCellValue(moneySum);
			}
			{
				HSSFCell cell = row3.createCell((short) (8));
				cell.setCellValue("--");
			}

		}
	}
}
