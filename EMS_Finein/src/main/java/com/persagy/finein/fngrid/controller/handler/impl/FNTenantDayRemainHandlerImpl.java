package com.persagy.finein.fngrid.controller.handler.impl;

import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.dto.DTORoomMeter;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumPrePayType;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.fngrid.controller.handler.FNGGridCommonHandler;
import com.persagy.finein.fngrid.controller.handler.FNTenantDayRemainHandler;
import com.persagy.finein.service.*;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;

@Service("FNTenantDayEnergyHandler")
public class FNTenantDayRemainHandlerImpl implements FNTenantDayRemainHandler {

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;

	@Resource(name = "FNMeterDataService")
	private FNMeterDataService FNMeterDataService;

	@Resource(name = "FNRoomService")
	private FNRoomService FNRoomService;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNTenantMeterDataService")
	private FNTenantMeterDataService FNTenantMeterDataService;

	@Resource(name = "FNGGridCommonHandler")
	private FNGGridCommonHandler FNGGridCommonHandler;

	@Resource(name = "FNTenantPayTypeService")
	private FNTenantPayTypeService FNTenantPayTypeService;

	@Resource(name = "FNServiceDataService")
	private FNServiceDataService FNServiceDataService;

	private SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	@SuppressWarnings("rawtypes")
	@Override
	public File process(String buildingId, File tempf, String resourceId, String path, Map dto) throws Exception {

		List<FnTenant> fnTenantList = FNTenantService.queryListByValidStatus(buildingId, EnumValidStatus.VALID);
		Map<String, FnTenantPayType> tenant_PayType = FNTenantPayTypeService.queryTenantAndPayType(buildingId,
				FineinConstant.EnergyType.Dian);

		String timeFrom = (String) dto.get("startTime");
		Date from = standard.parse(timeFrom);
		File file = new File(path);

		FileInputStream is = new FileInputStream(tempf);// 创建文件流
		HSSFWorkbook wb = new HSSFWorkbook(is);// 加载文件流
		FileOutputStream fout = null;
		List<Map<String, Object>> result = new ArrayList<>();
		for (FnTenant fnTenant : fnTenantList) {
			FnTenantPayType fnTenantPayType = tenant_PayType.get(fnTenant.getId());
			if (fnTenantPayType == null) {
				continue;
			}
			if (fnTenantPayType.getPayType() == EnumPayType.POSTPAY.getValue().intValue()) {
				continue;
			}
			if (fnTenantPayType.getPrePayType() == EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue()) {
				continue;
			}
			if("1#1F备用1".equals(fnTenant.getId())){
				System.out.println();
			}
			List<DTORoomMeter> roomMeterList = FNRoomService.queryRoomListByTenantId(fnTenant.getId(),
					FineinConstant.EnergyType.Dian);
			// from = tenant.getActiveTime();

			processTenant(FineinConstant.EnergyType.Dian, result, roomMeterList, fnTenant, from);

		}
		this.buildGridExcel(result, wb);
		try {
			fout = new FileOutputStream(file);
			wb.write(fout);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (fout != null) {
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if (wb != null) {
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
		}
		return file;
	}

	/**
	 * Description:
	 * 
	 * @param energyTypeId
	 * @param payType
	 * @param result
	 * @param wb
	 *            void
	 * <AUTHOR>
	 * @since 2019年9月12日: 下午3:26:31 Update By 邵泓博 2019年9月12日: 下午3:26:31
	 */
	@SuppressWarnings({ "deprecation", "unchecked", "unused" })
	private void buildGridExcel(List<Map<String, Object>> result, HSSFWorkbook wb) {
		// 能耗费用报表-电-2017.02~2017.09
		HSSFSheet sheet = wb.getSheetAt(0);
		if (result != null) {
			int currentRow = 1;
			for (int i = 0; i < result.size(); i++) {
				Map<String, Object> tenant = (Map<String, Object>) result.get(i);
				Integer count = (Integer) tenant.get("count");
				String tenantId = (String) tenant.get("tenantId");
				String tenantName = (String) tenant.get("tenantName");
				String floorId = (String) tenant.get("floorId");
				String roomCodes = (String) tenant.get("roomCodes");
				Date time2 = (Date) tenant.get("time");
				List<Map<String, Object>> meterList = (List<Map<String, Object>>) tenant.get("meterList");
				HSSFRow row = sheet.createRow((short) currentRow);

				{
					HSSFCell cell = row.createCell((short) (0));
					cell.setCellValue(tenantId == null ? " " : tenantId);
				}
				{
					HSSFCell cell = row.createCell((short) (1));
					cell.setCellValue(tenantName == null ? " " : tenantName);
				}
				{
					HSSFCell cell = row.createCell((short) (2));
					cell.setCellValue(roomCodes == null ? " " : roomCodes);
				}
				if (meterList != null && meterList.size() > 0) {
					if (count > 0) {
						sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + count - 1, 0, 0));
						sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + count - 1, 1, 1));
						sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + count - 1, 2, 2));
					}
					for (int j = 0; j < meterList.size(); j++) {
						if (j != 0) {
							row = sheet.createRow((short) currentRow);
						}
						Map<String, Object> meterMap = (Map<String, Object>) meterList.get(j);
						Double price = (Double) meterMap.get("price");
						String meterId = (String) meterMap.get("meterId");
						Double ct = (Double) meterMap.get("ct");
						String serverIp = (String) meterMap.get("serverIp");
						Double kkValue = (Double) meterMap.get("kkValue");
						String address = (String) meterMap.get("address");
						Double remainData = (Double) meterMap.get("remainData");
						Double leiji = (Double) meterMap.get("leiji");
						Integer paulele = (Integer) meterMap.get("paulele");
						{
							HSSFCell cell = row.createCell((short) (3));
							cell.setCellValue(kkValue == null ? 0.0 : kkValue);
						}
						{
							HSSFCell cell = row.createCell((short) (4));
							cell.setCellValue(ct == null ? 0.0 : ct);
						}
						{
							HSSFCell cell = row.createCell((short) (5));
							cell.setCellValue(price == null ? 0.0 : price);
						}

						{
							HSSFCell cell = row.createCell((short) (6));
							cell.setCellValue(paulele == null ? "--" : paulele == 0 ? "非保电" : "保电");
						}
						{
							HSSFCell cell = row.createCell((short) (7));
							cell.setCellValue(serverIp == null ? "--" : serverIp);
						}
						{
							HSSFCell cell = row.createCell((short) (8));
							cell.setCellValue(address == null ? "--" : address + "");
						}
						{
							HSSFCell cell = row.createCell((short) (9));
							cell.setCellValue(meterId == null ? "--" : meterId);
						}
						{
							HSSFCell cell = row.createCell((short) (10));
							cell.setCellValue(DoubleFormatUtil.Instance().getDoubleData_00(remainData));
						}
						{
							HSSFCell cell = row.createCell((short) (11));
							cell.setCellValue(DoubleFormatUtil.Instance().getDoubleData_00(leiji));
						}
						currentRow++;
					}
				} else {

					{
						HSSFCell cell = row.createCell((short) (3));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (4));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (5));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (6));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (7));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (8));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (9));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (10));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (11));
						cell.setCellValue("--");
					}
					
					currentRow++;
				}
			}
		}
	}

	@SuppressWarnings("unused")
	private void processTenant(String energyTypeId, List<Map<String, Object>> result, List<DTORoomMeter> roomMeterList,
			FnTenant tenant, Date from) throws Exception {
		if (roomMeterList != null && roomMeterList.size() > 0) {
			Map<String, Object> contentObject = new HashMap<>();
			contentObject.put("tenantId", tenant.getId());
			contentObject.put("tenantName", tenant.getName());
			contentObject.put("floorId", "");
			contentObject.put("time", from);
			contentObject.put("roomCodes", tenant.getRoomCodes());
			// 租户下所有仪表的读数总条数
			Integer count = 0;
			// contentObject.put("roomCodes", fnTenant.getRoomCodes());
			List<Map<String, Object>> meterList = new ArrayList<>();
			contentObject.put("meterList", meterList);
			for (DTORoomMeter roomMeter : roomMeterList) {
				// 租户房间下所有仪表的读数总条数
				// 房间下所有的仪表
				List<DTOMeter> dTOMeterList = roomMeter.getMeterList();
				if (dTOMeterList != null) {
					for (DTOMeter dtoMeter : dTOMeterList) {
						Map<String, Object> meterMap = new HashMap<String, Object>();
						Integer meterType = dtoMeter.getMeterType();
						meterMap.put("meterId", dtoMeter.getMeterId());
						meterMap.put("ct", dtoMeter.getRadio());
						meterMap.put("serverIp", dtoMeter.getServerIp());
						{

							// 保电状态
							Double paulele = FNGGridCommonHandler.queryMeterData(tenant.getBuildingId(),
									dtoMeter.getMeterId(), FineinConstant.FuntionTypeId.Instant_BaoDian_Dian, from,
									EnumTimeType.T0);
							meterMap.put("paulele", paulele == null ? null : paulele.intValue());
						}
						// 仪表读数是否乘以倍率
						JSONObject extendObj = (JSONObject) JSONValue.parse(dtoMeter.getExtend());
						Double kkvalue = (Double) extendObj.get("kkValue");
						String address = (String) extendObj.get("address");
						meterMap.put("kkValue", kkvalue);
						meterMap.put("address", address);
						List<Map<String, Object>> priceList = new ArrayList<>();
						String protocolId = dtoMeter.getProtocolId();

						{// 单价
							Integer priceFunctionId = FunctionTypeUtil.getPriceFunctionId(dtoMeter.getEnergyTypeId());
							Double data = FNGGridCommonHandler.queryMeterData(tenant.getBuildingId(),
									dtoMeter.getMeterId(), priceFunctionId, from, EnumTimeType.T0);
							meterMap.put("price", data);
						}
						{// 倍率
							Double data = FNGGridCommonHandler.queryMeterData(tenant.getBuildingId(),
									dtoMeter.getMeterId(), FineinConstant.FuntionTypeId.Instant_BeiLv_Dian, from,
									EnumTimeType.T0);
							meterMap.put("ct", data);
						}
						{// 剩余
							Long ljlIsCt = (Long) extendObj.get("sylIsCt");
							Double radio = 1.0;
							if (ljlIsCt != null && ljlIsCt.intValue() == 1) {
								radio = dtoMeter.getRadio();
							}
							Double data = FNGGridCommonHandler.queryMeterData(tenant.getBuildingId(),
									dtoMeter.getMeterId(), FineinConstant.FuntionTypeId.Instant_ShengYuJinE_Dian, from,
									EnumTimeType.T0);
							meterMap.put("remainData", data != null ? data * radio : 0.0);
						}

						{// 累计量
							Long ljlIsCt = (Long) extendObj.get("ljlIsCt");
							Double radio = 1.0;
							if (ljlIsCt != null && ljlIsCt.intValue() == 1) {
								radio = dtoMeter.getRadio();
							}
							int cumulantFunctionId = FunctionTypeUtil.getCumulantFunctionId(energyTypeId);
							Double data = FNGGridCommonHandler.queryMeterData(tenant.getBuildingId(),
									dtoMeter.getMeterId(), cumulantFunctionId, from, EnumTimeType.T0);
							meterMap.put("leiji", data != null ? data * radio : 0.0);
						}
						// 仪表所对应的读数总条数
						count++;
						meterList.add(meterMap);
					}
				}
			}
			contentObject.put("count", count);
			result.add(contentObject);
		}
	}

}
