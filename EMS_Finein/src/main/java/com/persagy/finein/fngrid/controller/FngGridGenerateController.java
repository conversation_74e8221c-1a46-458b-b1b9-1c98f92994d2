package com.persagy.finein.fngrid.controller;

import com.pbsage.ems.excel.data.MyDataAccess;
import com.pbsage.ems.excel.data.MyLog;
import com.pbsage.ems.excel.util.WorkbookObject;
import com.pbsage.ems.excel.util.WorkbookUtil;
import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.DictionaryUtil;
import com.persagy.ems.pojo.finein.FnGridTemplate;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.fngrid.controller.handler.*;
import com.persagy.finein.fngrid.excel.encapsulation.ExcelConvert;
import org.apache.commons.io.FileUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 *  用于根据报表模板生成Excel报表 功能描述
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FngGridGenerateController extends BaseController {

    @Resource(name = "FNTenantHourlyRemainDataHandler")
    private FNTenantHourlyRemainDataHandler fnTenantHourlyRemainDataHandler;

    @Resource(name = "FNPrePayGridHandler")
    private FNPrePayGridHandler fnPrePayGridHandler;

    @Resource(name = "FNPostPayRecordGridHandler")
    private FNPostPayRecordGridHandler fnPostPayRecordGridHandler;

    @Resource(name = "FNEnergyMoneyGridHandler")
    private FNEnergyMoneyGridHandler fnEnergyMoneyGridHandler;

    @Resource(name = "FNRemainDataGridHandler")
    private FNRemainDataGridHandler fnRemainDataGridHandler;

    @Resource(name = "FNTenantGongLvGridHandler")
    private FNTenantGongLvGridHandler fnTenantGongLvGridHandler;

    @Resource(name = "FNMeterDataGridHandler")
    private FNMeterDataGridHandler fnMeterDataGridHandler;

    @Resource(name = "FNTenantMeterGongLvGridHandler")
    private FNTenantMeterGongLvGridHandler fnTenantMeterGongLvGridHandler;

    @Resource(name = "FNDuiBiChaGridHandler")
    private FNDuiBiChaGridHandler fnDuiBiChaGridHandler;

    @Resource(name = "FNTenantDayEnergyHandler")
    private FNTenantDayRemainHandler fnTenantDayRemainHandler;

    @Resource(name = "FNEnergyDayGridHandler")
    private FNEnergyDayGridHandler fnEnergyDayGridHandler;

    @RequestMapping("FNGGridGenerateService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult gridGenerate(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String templateId = (String) dto.get("tempId");
            if (templateId == null || "".equals(templateId)) {
                throw new Exception("tempId不能为空！");
            }
            String startTime = (String) dto.get("startTime");
            if (startTime == null || "".equals(startTime)) {
                throw new Exception("startTime不能为空！");
            }
            String name = (String) dto.get("name");
            List<String> buildList = (List) dto.get("build");

            FnGridTemplate reportTemp = new FnGridTemplate();
            reportTemp.setId(templateId);
            List tempList = coreDao.query(reportTemp);
            if (tempList.size() == 0) {
                throw new Exception("根据tempId没有查到报表模板信息！");
            }

            reportTemp = (FnGridTemplate) tempList.get(0);


            String filePath = null;
            String resource = reportTemp.getResourceId();
            String fileStorageDictionary = DictionaryUtil.getFileStorageDictionary();

            String subdirectory = null;
            if (reportTemp.getIsSystem() == 1) {// 系统模板
                subdirectory = File.separator + "reportPrint" + File.separator + "filemanager";
                filePath = buildPath(filePath, resource);
            } else {
                if (resource == null || "".equals(resource)) {
                    throw new Exception("报表模板信息中没有模板资源resource");
                }
                // 获取报表模板
                FileResource tempResource = new FileResource();
                tempResource.setId(resource);
                tempResource = coreDao.query(tempResource).get(0);
                filePath = fileStorageDictionary + tempResource.getSubdirectory() + "/" + resource;
                subdirectory = tempResource.getSubdirectory();
            }
            byte[] bytes = null;
            byte[] outbytes = null;
            File tempf = new File(filePath);
            String resourceId = UUID.randomUUID().toString();
            if (reportTemp.getIsSystem() == 1) {// 系统模板
                if (buildList == null || buildList.size() == 0) {
                    throw new Exception("建筑不能为空");
                }
                String buildingId = buildList.get(0);
                String path = fileStorageDictionary + subdirectory + File.separator + resourceId;
                File file = new File(fileStorageDictionary + subdirectory);
                if (!file.exists()) {
                    file.mkdirs();
                }
                switch (resource) {
                    case "PrePayRecordGrid":
                        tempf = fnPrePayGridHandler.process(buildingId,tempf, resourceId, path, dto);
                        break;
                    case "PostPayRecordGrid":
                        tempf = fnPostPayRecordGridHandler.process(buildingId,tempf, resourceId, path, dto);
                        break;
                    case "EnergyMoneyGrid":
                        tempf = fnEnergyMoneyGridHandler.process(buildingId,tempf, resourceId, path, dto);
                        break;
                    case "RemainDataGrid":
                        tempf = fnRemainDataGridHandler.process(buildingId,tempf, resourceId, path, dto);
//				processTenantPrePayList(buildingId, fnTenantList, tenantListMap);
                        break;
                    case "MeterDataGrid":
                        tempf = fnMeterDataGridHandler.process(buildingId,tempf, resourceId, path, dto);
                        break;
                    case "TenantGongLvGrid":
                        tempf= fnTenantGongLvGridHandler.process(buildingId, tempf, resourceId, path, dto);
                        break;
                    case "TenantMeterGongLvGrid":
                        tempf = fnTenantMeterGongLvGridHandler.process(buildingId, tempf, resourceId, path, dto);
                        break;
                    case "PrePayMeterDuiBiChaGrid":
                        tempf = fnDuiBiChaGridHandler.process(buildingId, tempf, resourceId, path, dto);
                        break;
                    case "HourlyRemainDataGrid":
                        tempf = fnTenantHourlyRemainDataHandler.process(buildingId,tempf, resourceId, path, dto);
                        break;
                    case "TenantDayRemainGrid":
                        tempf = fnTenantDayRemainHandler.process(buildingId,tempf, resourceId, path, dto);
                        break;
                    case "TenantDayEnergyGrid":
                        tempf = fnEnergyDayGridHandler.process(buildingId,tempf, resourceId, path, dto);
                        break;
                    default:
                        break;
                }
                outbytes = FileUtils.readFileToByteArray(tempf);
            } else {
                bytes = FileUtils.readFileToByteArray(tempf);
                outbytes = generateReport(dto, bytes);
            }

            FileResource reportResource = new FileResource();
            reportResource.setId(resourceId);
            reportResource.setName(name);
            reportResource.setSuffix(".xls");
            reportResource.setSubdirectory(subdirectory);
            coreDao.save(reportResource);

            String reportPath = fileStorageDictionary + subdirectory + "/" + reportResource.getId();
            File reportf = new File(reportPath);
            FileUtils.writeByteArrayToFile(reportf, outbytes);

            UUID uuid = UUID.randomUUID();
            String tempPath = File.separator + uuid.toString().replaceAll("-", "");
            Map<String, File> fileMap = ExcelConvert.convertExcel2Html(reportPath,
                    fileStorageDictionary + subdirectory + tempPath);

            String readFileToString = "";
            List<Map> fileList = new ArrayList<>();
            for (String id : fileMap.keySet()) {
                Map temp = new HashMap<>();
                File path = fileMap.get(id);
                readFileToString = FileUtils.readFileToString(path, "UTF-8");
                int firstIndex = readFileToString.indexOf("<html");
                int lastIndex = readFileToString.lastIndexOf("</html>") + "</html>".length();
                readFileToString = readFileToString.substring(firstIndex, lastIndex);
                temp.put("sheetName", id);
                temp.put("html", readFileToString);
                fileList.add(temp);
            }
            Map result = new HashMap();
            result.put("id", reportResource.getId());
            result.put("name", name);
            result.put("fileHtml", fileList);
            content.add(result);

            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNGGridGenerateService");
        }
    }

    private String buildPath(String filePath, String resource) {
        switch (resource) {
            case "EnergyMoneyGrid":
                filePath = this.getClass().getResource("/").getPath() + File.separator + "reportPrint" + File.separator
                        + "template" + File.separator + "EnergyMoneyGrid.xls";
                break;
            case "PrePayRecordGrid":
                filePath = this.getClass().getResource("/").getPath() + File.separator + "reportPrint" + File.separator
                        + "template" + File.separator + "PrePayRecordGrid.xls";
                break;
            case "PostPayRecordGrid":
                filePath = this.getClass().getResource("/").getPath() + File.separator + "reportPrint" + File.separator
                        + "template" + File.separator + "PostPayRecordGrid.xls";
                break;
            case "MeterDataGrid":
                filePath = this.getClass().getResource("/").getPath() + File.separator + "reportPrint" + File.separator
                        + "template" + File.separator + "MeterDataGrid.xls";
                break;
            case "RemainDataGrid":
                filePath = this.getClass().getResource("/").getPath() + File.separator + "reportPrint" + File.separator
                        + "template" + File.separator + "RemainDataGrid.xls";
                break;
            case "TenantGongLvGrid":
                filePath = this.getClass().getResource("/").getPath() + File.separator + "reportPrint" + File.separator
                        + "template" + File.separator + "TenantGongLvGrid.xls";
                break;
            case "TenantMeterGongLvGrid":
                filePath = this.getClass().getResource("/").getPath() + File.separator + "reportPrint" + File.separator
                        + "template" + File.separator + "TenantMeterGongLvGrid.xls";
                break;
            case "PrePayMeterDuiBiChaGrid":
                filePath = this.getClass().getResource("/").getPath() + File.separator + "reportPrint" + File.separator
                        + "template" + File.separator + "PrePayMeterDuiBiChaGrid.xls";
                break;
            case "TenantDayRemainGrid":
                filePath = this.getClass().getResource("/").getPath() + File.separator + "reportPrint" + File.separator
                        + "template" + File.separator + "TenantDayRemainGrid.xls";
                break;
            case "TenantDayEnergyGrid":
                filePath = this.getClass().getResource("/").getPath() + File.separator + "reportPrint" + File.separator
                        + "template" + File.separator + "TenantDayEnergyGrid.xls";
                break;
            default:
                break;
        }
        return filePath;
    }

    /**
     * 报表生成
     *
     * @param dto
     * @param bytes
     * @return
     * @throws MyExcelException
     * @throws IOException
     */
    public byte[] generateReport(Map dto, byte[] bytes) throws Exception {
        WorkbookObject wo = new WorkbookObject(bytes);
        List<String> buildlist = (List<String>) dto.get("build");
        List<String> itemlist = (List<String>) dto.get("item");
        List<String> meterlist = (List<String>) dto.get("meter");

        String startTime = (String) dto.get("startTime");
        String endTime = (String) dto.get("endTime");
        // "E1"电、 "E2"冷、 "E3"热、"E4"水、"E5"气、"E6"风
        String energytype = (String) dto.get("Energytype");
        String fineincode = (String) dto.get("Fineincode");

        if (null != energytype && !"".equals(energytype)) {
            wo.signMap.put("Energytype", energytype);
        }

        if (null != fineincode && !"".equals(fineincode)) {
            wo.signMap.put("Fineincode", fineincode);
        }

        SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (null != buildlist && buildlist.size() > 0) {// 前台只能选择一个建筑
            wo.signMap.put("Building", buildlist.get(0));
        }
        if (null != itemlist && itemlist.size() > 0) {
            String[] energyitems = new String[itemlist.size()];
            for (int i = 0; i < itemlist.size(); i++) {
                // 分项id中“-”与后面的操作冲突，临时用“~”替换，报表读取数据前再转换回来
                energyitems[i] = itemlist.get(i).replace("-", "AXXB");
            }
            wo.context.variableMap.put("Energyitems", energyitems);
        }
        if (null != meterlist && meterlist.size() > 0) {
            String[] meters = meterlist.toArray(new String[meterlist.size()]);
            wo.context.variableMap.put("Meters", meters);
        }
        // wo.signMap.put("Building", "4101020001");
        // wo.context.variableMap.put("Energyitems", new String[] {
        // "A1-1-001"});
        // wo.context.variableMap.put("Meters", new String[] { "6003","6012" });
        if (null != startTime && !"".equals(startTime)) {
            wo.dateFrom = standard.parse(startTime);
        }
        if (null != endTime && !"".equals(endTime)) {
            wo.dateTo = standard.parse(endTime);
        }
        wo.dataAccess = (MyDataAccess) SystemConstant.context.getBean("ReportDataAccess");
        wo.log = new MyLog();

        WorkbookUtil.SortVariable(wo);
        WorkbookUtil.Process(wo);

        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        wo.workbook.write(byteArrayOutputStream);
        return byteArrayOutputStream.toByteArray();
    }
}
