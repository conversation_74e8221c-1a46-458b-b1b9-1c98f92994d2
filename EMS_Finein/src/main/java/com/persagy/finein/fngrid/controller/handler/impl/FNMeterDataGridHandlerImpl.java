package com.persagy.finein.fngrid.controller.handler.impl;

import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.dto.DTORoomMeter;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fngrid.controller.handler.FNGGridCommonHandler;
import com.persagy.finein.fngrid.controller.handler.FNMeterDataGridHandler;
import com.persagy.finein.service.FNRoomService;
import com.persagy.finein.service.FNTenantService;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;

@Service("FNMeterDataGridHandler")
public class FNMeterDataGridHandlerImpl implements FNMeterDataGridHandler{
	@Resource(name = "FNGGridCommonHandler")
	private FNGGridCommonHandler FNGGridCommonHandler;
	
	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;
	
	@Resource(name = "FNRoomService")
	private FNRoomService FNRoomService;
	
	protected final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	@Override
	public File process(String buildingId,File tempf, String resourceId, String path, Map dto) throws Exception {
		List<FnTenant> fnTenantList = FNTenantService.queryListByValidStatus(buildingId, EnumValidStatus.VALID);
		
		Map<String, List<FnTenant>> tenantListMap=new HashMap<>();
		
		FNGGridCommonHandler.processTenanPayTypetList(buildingId, fnTenantList, tenantListMap);

		String timeFrom = (String) dto.get("startTime");
		Date from = standard.parse(timeFrom);
		File file = new File(path);

		FileInputStream is = new FileInputStream(tempf);// 创建文件流
		HSSFWorkbook wb = new HSSFWorkbook(is);// 加载文件流
		FileOutputStream fout = null;

		for (Entry<String, List<FnTenant>> payMap : tenantListMap.entrySet()) {
			String key = payMap.getKey();
			String[] split = key.split("-");
			String energyTypeId = split[0];
			EnumPayType payType = EnumPayType.valueOf(Integer.valueOf(split[1]));
			List<FnTenant> list = payMap.getValue();
			List<Map<String, Object>> result = new ArrayList<>();
			for (FnTenant tenant : list) {
				List<DTORoomMeter> roomMeterList = FNRoomService.queryRoomListByTenantId(tenant.getId(), energyTypeId);
				// from = tenant.getActiveTime();
				processTenant(energyTypeId, result, roomMeterList, tenant, from);
			}
			this.buildMeterDataGridExcel(energyTypeId, payType, result, wb);
		}
		try {
			fout = new FileOutputStream(file);
			wb.write(fout);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (fout != null) {
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if (wb != null) {
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
		}
		return file;
	
	
	}
	/**
	 * 仪表读数报表处理租户
	 * 
	 * @param energyTypeId
	 * @param result
	 * @param roomMeterList
	 * @param tenant
	 * @param from
	 * @throws Exception
	 */
	private void processTenant(String energyTypeId, List<Map<String, Object>> result, List<DTORoomMeter> roomMeterList,
			FnTenant tenant, Date from) throws Exception {

		if (roomMeterList != null && roomMeterList.size() > 0) {
			Map<String, Object> contentObject = new HashMap<>();
			contentObject.put("tenantId", tenant.getId());
			contentObject.put("tenantName", tenant.getName());
			contentObject.put("time", from);
			// 租户下所有仪表的读数总条数
			Integer count = 0;
			// contentObject.put("roomCodes", fnTenant.getRoomCodes());
			List<Map<String, Object>> roomList = new ArrayList<>();
			contentObject.put("roomList", roomList);
			for (DTORoomMeter roomMeter : roomMeterList) {
				// 租户房间下所有仪表的读数总条数
				int orderCount = 0;
				Map<String, Object> roomMap = new HashMap<String, Object>();
				List<Map<String, Object>> meterList = new ArrayList<>();
				roomList.add(roomMap);
				roomMap.put("roomCode", roomMeter.getRoomCode());
				roomMap.put("meterList", meterList);
				// 房间下所有的仪表
				List<DTOMeter> dTOMeterList = roomMeter.getMeterList();
				if (dTOMeterList != null) {
					for (DTOMeter dtoMeter : dTOMeterList) {
						Map<String, Object> meterMap = new HashMap<String, Object>();
						Integer meterType = dtoMeter.getMeterType();
						meterMap.put("meterId", dtoMeter.getMeterId());
						meterMap.put("energyTypeId", dtoMeter.getEnergyTypeId());
						meterMap.put("energyTypeName",
								EnergyTypeUtil.queryEnergyTypeNameById(dtoMeter.getEnergyTypeId()));
						meterMap.put("meterType", dtoMeter.getMeterType());
						meterMap.put("energyUnit", UnitUtil.getCumulantUnit(dtoMeter.getEnergyTypeId()));
						meterMap.put("ct", dtoMeter.getRadio());
						// 仪表读数是否乘以倍率
						JSONObject extendObj = (JSONObject) JSONValue.parse(dtoMeter.getExtend());
						Long ljlIsCt = (Long) extendObj.get("ljlIsCt");
						Double radio = 1.0;
						if (ljlIsCt != null && ljlIsCt.intValue() == 1) {
							radio = dtoMeter.getRadio();
						}
						List<Map<String, Object>> list = new ArrayList<>();
						if (meterType == EnumMeterType.Common.getValue().intValue()) {// 普通表
							Map<String, Object> dataMap = new HashMap<>();
							String protocolId = dtoMeter.getProtocolId();
							Integer cumulantFunctionId = FunctionTypeUtil
									.getCumulantFunctionId(dtoMeter.getEnergyTypeId());
							Integer functionId = FNGGridCommonHandler.queryCumulantFunctionId(protocolId, energyTypeId,
									cumulantFunctionId);
							if (functionId != null) {
								dataMap.put("type", "L");
								Double data = FNGGridCommonHandler.queryMeterData(tenant.getBuildingId(), dtoMeter.getMeterId(), functionId, from,EnumTimeType.T2);
								dataMap.put("value", data == null ? null : data * radio);
								dataMap.put("isCt", ljlIsCt == null ? null : ljlIsCt.intValue());
								if (dtoMeter.getEnergyTypeId().equals(FineinConstant.EnergyType.Dian)) {
									dataMap.put("name", "正向有功电能");
								} else {
									dataMap.put("name", "");
								}
							}
							list.add(dataMap);
						} else {// 多费率
							Map<String, Integer> functionMap = FunctionTypeUtil.getCumulantDianMultiple();
							for (Entry<String, Integer> entry : functionMap.entrySet()) {
								Integer functionId = FNGGridCommonHandler.queryCumulantFunctionId(dtoMeter.getProtocolId(),
										energyTypeId, entry.getValue());
								if (functionId == null) {
									continue;
								}
								Double data = FNGGridCommonHandler.queryMeterData(tenant.getBuildingId(), dtoMeter.getMeterId(), functionId, from,EnumTimeType.T2);
								Map<String, Object> hashMap = new HashMap<>();
								hashMap.put("type", entry.getKey());
								String name = FNGGridCommonHandlerImpl.getFunctionName(entry.getKey());
								hashMap.put("name", name);
								hashMap.put("value", data == null ? null : data * radio);
								hashMap.put("isCt", ljlIsCt.intValue());
								list.add(hashMap);
							}
						}
						meterMap.put("list", list);
						// 仪表所对应的读数总条数
						meterMap.put("meterOrderCount", list.size());
						count += list.size();
						orderCount += list.size();
						meterList.add(meterMap);
					}
				}
				roomMap.put("orderCount", orderCount);
			}
			contentObject.put("count", count);
			result.add(contentObject);
		}

	}
	
	/**
	 * 生成仪表读数报表
	 * 
	 * @param energyTypeId
	 * @param payType
	 * @param result
	 * @param wb
	 */
	@SuppressWarnings("deprecation")
	private void buildMeterDataGridExcel(String energyTypeId, EnumPayType payType, List<Map<String, Object>> result,
			HSSFWorkbook wb) {
		// 能耗费用报表-电-2017.02~2017.09
		String energyType = EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId);
		HSSFSheet sheet = wb.getSheet(energyType + "-" + payType.getView());

		if (result != null) {
			int currentRow = 1;
			for (int i = 0; i < result.size(); i++) {
				Map<String, Object> tenant = (Map<String, Object>) result.get(i);
				Integer count = (Integer) tenant.get("count");
				String tenantId = (String) tenant.get("tenantId");
				String tenantName = (String) tenant.get("tenantName");
				Date time2 = (Date) tenant.get("time");
				List<Map<String, Object>> roomList = (List<Map<String, Object>>) tenant.get("roomList");
				HSSFRow row = sheet.createRow((short) currentRow);
				{
					HSSFCell cell = row.createCell((short) (0));
					cell.setCellValue(tenantId == null ? " " : tenantId);
				}
				{
					HSSFCell cell = row.createCell((short) (1));
					cell.setCellValue(tenantName == null ? " " : tenantName);
				}
				{
					HSSFCell cell = row.createCell((short) (2));
					cell.setCellValue(time2 == null ? " " : standard.format(time2));
				}

				if (roomList != null && roomList.size() > 0) {
					if (count > 0) {
						sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + count - 1, 0, 0));
						sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + count - 1, 1, 1));
						sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + count - 1, 2, 2));
					}
					for (int j = 0; j < roomList.size(); j++) {
						if (j != 0) {
							row = sheet.createRow((short) currentRow);
						}
						Map<String, Object> roomMap = (Map<String, Object>) roomList.get(j);
						String roomCode = (String) roomMap.get("roomCode");
						int orderCount = (int) roomMap.get("orderCount");
						if (orderCount > 0) {
							sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + orderCount - 1, 3, 3));
						}
						List<Map<String, Object>> meterList = (List<Map<String, Object>>) roomMap.get("meterList");

						if (meterList != null && meterList.size() > 0) {
							for (int k = 0; k < meterList.size(); k++) {
								Map<String, Object> meterMap = meterList.get(k);
								String meterId = (String) meterMap.get("meterId");
								String energyTypeName = (String) meterMap.get("energyTypeName");
								String energyUnit = (String) meterMap.get("energyUnit");
								int meterOrderCount = (int) meterMap.get("meterOrderCount");
								if (meterOrderCount > 0) {
									sheet.addMergedRegion(
											new CellRangeAddress(currentRow, currentRow + meterOrderCount - 1, 4, 4));
								}
								Double ct = (Double) meterMap.get("ct");
								List<Map<String, Object>> list = (List<Map<String, Object>>) meterMap.get("list");
								if (k != 0) {
									row = sheet.createRow((short) currentRow);
								}
								for (int z = 0; z < list.size(); z++) {
									if (z != 0) {
										row = sheet.createRow((short) currentRow);
									}
									{
										HSSFCell cell = row.createCell((short) (3));
										cell.setCellValue(roomCode == null ? "--" : roomCode);
									}
									{
										HSSFCell cell = row.createCell((short) (4));
										cell.setCellValue(meterId == null ? "--" : meterId);
									}
									{
										HSSFCell cell = row.createCell((short) (5));
										cell.setCellValue(energyTypeName == null ? "--" : energyTypeName);
									}

									{
										HSSFCell cell = row.createCell((short) (6));
										cell.setCellValue(ct == null ? "--" : ct + "");
									}
									{
										HSSFCell cell = row.createCell((short) (7));
										Double data = DoubleFormatUtil.Instance()
												.getDoubleData_00(list.get(z).get("value"));
										switch ((String) list.get(z).get("type")) {
										case "L":
											cell.setCellValue(data == null ? "--" : data + "(" + energyUnit + ")");
											break;
										case "J":
											cell.setCellValue(
													data == null ? "尖段:" : "尖段:" + data + "(" + energyUnit + ")");
											break;
										case "F":
											cell.setCellValue(
													data == null ? "峰段:" : "峰段:" + data + "(" + energyUnit + ")");
											break;
										case "G":
											cell.setCellValue(
													data == null ? "谷段:" : "谷段:" + data + "(" + energyUnit + ")");
											break;
										case "P":
											cell.setCellValue(
													data == null ? "平段:" : "平段:" + data + "(" + energyUnit + ")");
											break;
										default:
											break;
										}
									}
									{
										HSSFCell cell = row.createCell((short) (8));
										Integer isCt = (Integer) list.get(z).get("isCt");
										cell.setCellValue(isCt == null ? "--" : EnumYesNo.valueOf(isCt).getView());
									}
									currentRow++;
								}
							}
						} else {
							{
								HSSFCell cell = row.createCell((short) (3));
								cell.setCellValue("--");
							}
							{
								HSSFCell cell = row.createCell((short) (4));
								cell.setCellValue("--");
							}
							{
								HSSFCell cell = row.createCell((short) (5));
								cell.setCellValue("--");
							}
							{
								HSSFCell cell = row.createCell((short) (6));
								cell.setCellValue("--");
							}
							{
								HSSFCell cell = row.createCell((short) (7));
								cell.setCellValue("--");
							}
							{
								HSSFCell cell = row.createCell((short) (8));
								cell.setCellValue("--");
							}
							currentRow++;
						}
					}
				}
			}
		}
	}
}
