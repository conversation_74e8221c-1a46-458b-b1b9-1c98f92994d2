package com.persagy.finein.fngrid.controller.handler.impl;

import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnRecordPostPay;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.fngrid.controller.handler.FNGGridCommonHandler;
import com.persagy.finein.fngrid.controller.handler.FNPostPayRecordGridHandler;
import com.persagy.finein.service.FNRecordPostPayService;
import com.persagy.finein.service.FNTenantService;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;


@Service("FNPostPayRecordGridHandler")
public class FNPostPayRecordGridHandlerImpl implements FNPostPayRecordGridHandler{
	@Resource(name = "FNGGridCommonHandler")
	private FNGGridCommonHandler FNGGridCommonHandler;
	
	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;
	
	@Resource(name = "FNRecordPostPayService")
	private FNRecordPostPayService FNRecordPostPayService;

	protected final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	@Override
	public File process(String buildingId,File tempf, String resourceId, String path, Map dto) throws Exception {
		List<FnTenant> fnTenantList = FNTenantService.queryListByValidStatus(buildingId, EnumValidStatus.VALID);
		
		Map<String, List<FnTenant>> tenantListMap=new HashMap<>();
		
		FNGGridCommonHandler.processTenanPayTypetList(buildingId, fnTenantList, tenantListMap);
		// String buildingName = (String) dto.get("buildingName");
		String timeFrom = (String) dto.get("startTime");
		String timeTo = (String) dto.get("endTime");
		Date from = standard.parse(timeFrom);
		Date to = standard.parse(timeTo);

		File file = new File(path);
		// if (!file.exists()) {
		// file.mkdirs();
		// }

		FileInputStream is = new FileInputStream(tempf);// 创建文件流
		HSSFWorkbook wb = new HSSFWorkbook(is);// 加载文件流
		FileOutputStream fout = null;

		for (Entry<String, List<FnTenant>> payMap : tenantListMap.entrySet()) {
			String key = payMap.getKey();
			String[] split = key.split("-");
			String energyTypeId = split[0];
			String energyUnit = UnitUtil.getCumulantUnit(energyTypeId);
			String energyTypeName = EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId);
			EnumPayType payType = EnumPayType.valueOf(Integer.valueOf(split[1]));
			if (payType.getValue().intValue() == 0) {
				continue;
			}
			List<FnTenant> list = payMap.getValue();
			List<String> tenantIdList = new ArrayList<String>();
			for (FnTenant tenant : list) {
				tenantIdList.add(tenant.getId());
			}
			List<FnRecordPostPay> recordList = FNRecordPostPayService.queryRecord(tenantIdList, energyTypeId, from, to);
			Map<String, List<FnRecordPostPay>> tenantRecordMap = new HashMap<>();
			if (recordList != null) {
				for (FnRecordPostPay recordPostPay : recordList) {
					if (!tenantRecordMap.containsKey(recordPostPay.getTenantId())) {
						tenantRecordMap.put(recordPostPay.getTenantId(), new ArrayList<FnRecordPostPay>());
					}
					tenantRecordMap.get(recordPostPay.getTenantId()).add(recordPostPay);
				}
			}
			List<Object> tenantObjList = new ArrayList<>();
			for (FnTenant tenant : list) {
				Map<String, Object> tenantMap = new HashMap<>();
				tenantMap.put("tenantId", tenant.getId());
				tenantMap.put("tenantName", tenant.getName());
				tenantMap.put("roomIds", tenant.getRoomCodes());
				tenantObjList.add(tenantMap);
				List<Object> orderList = new ArrayList<>();
				tenantMap.put("orderList", orderList);
				List<FnRecordPostPay> recordPostPayList = tenantRecordMap.get(tenant.getId());
				if (recordPostPayList != null) {
					for (FnRecordPostPay recordPostPay : recordPostPayList) {
						Map<String, Object> recordMap = new HashMap<>();
						recordMap.put("payTime", standard.format(recordPostPay.getOperateTime()));
						recordMap.put("orderTime", recordPostPay.getOrderTime());
						recordMap.put("orderId", recordPostPay.getOrderId());
						recordMap.put("money", recordPostPay.getMoney());
						recordMap.put("userName", recordPostPay.getUserName());
						orderList.add(recordMap);
					}
				}

			}
			Map<String, Object> contentObj = new HashMap<>();
			contentObj.put("energyUnit", energyUnit);
			contentObj.put("energyTypeName", energyTypeName);
			contentObj.put("tenantList", tenantObjList);
			this.buildPostPayBillGridExcel(energyTypeId, payType, contentObj, wb);
		}
		try {
			fout = new FileOutputStream(path);
			wb.write(fout);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (fout != null) {
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if (wb != null) {
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
		}
		return file;
	
	}
	@SuppressWarnings("deprecation")
	private void buildPostPayBillGridExcel(String energyTypeId, EnumPayType payType, Map<String, Object> contentObj,
			HSSFWorkbook wb) {
		String energyType = EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId);
		HSSFSheet sheet = wb.getSheet(energyType);
		// sheet.addMergedRegion(new CellRangeAddress(0, 1, 0, 0));// 一行-二行 第一列
		// sheet.addMergedRegion(new CellRangeAddress(0, 1, 1, 1));// 一行-二行 第二列
		// sheet.addMergedRegion(new CellRangeAddress(2, 2, 0, 1));// 合计
		List<Map<String, Object>> tenantList = (List<Map<String, Object>>) contentObj.get("tenantList");
		if (tenantList != null) {
			int currentRow = 1;
			for (int i = 0; i < tenantList.size(); i++) {
				Map<String, Object> tenant = (Map<String, Object>) tenantList.get(i);
				String tenantId = (String) tenant.get("tenantId");
				String tenantName = (String) tenant.get("tenantName");
				String roomIds = (String) tenant.get("roomIds");
				List<Object> orderList = (List<Object>) tenant.get("orderList");

				HSSFRow row = sheet.createRow((short) currentRow);
				{
					HSSFCell cell = row.createCell((short) (0));
					cell.setCellValue(tenantName == null ? " " : tenantName);
				}
				{
					HSSFCell cell = row.createCell((short) (1));
					cell.setCellValue(tenantId == null ? " " : tenantId);
				}
				{
					HSSFCell cell = row.createCell((short) (2));
					cell.setCellValue(roomIds == null ? " " : roomIds);
				}

				if (orderList != null && orderList.size() > 0) {
					sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + orderList.size() - 1, 0, 0));
					sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + orderList.size() - 1, 1, 1));
					sheet.addMergedRegion(new CellRangeAddress(currentRow, currentRow + orderList.size() - 1, 2, 2));

					for (int j = 0; j < orderList.size(); j++) {
						if (j != 0) {
							row = sheet.createRow((short) currentRow + j);
						}
						Map<String, Object> orderMap = (Map<String, Object>) orderList.get(j);
						String payTime = (String) orderMap.get("payTime");
						String orderTime = (String) orderMap.get("orderTime");
						String orderId = (String) orderMap.get("orderId");
						Double money = DoubleFormatUtil.Instance().getDoubleData_00(orderMap.get("money"));
						String userName = (String) orderMap.get("userName");
						{
							HSSFCell cell = row.createCell((short) (3));
							cell.setCellValue(payTime == null ? "--" : payTime);
						}
						{
							HSSFCell cell = row.createCell((short) (4));
							cell.setCellValue(orderTime == null ? "--" : orderTime);
						}
						{
							HSSFCell cell = row.createCell((short) (5));
							cell.setCellValue(orderId == null ? "--" : orderId);
						}
						{
							HSSFCell cell = row.createCell((short) (6));
							cell.setCellValue(money == null ? "--" : money + "");
						}
						{
							HSSFCell cell = row.createCell((short) (7));
							cell.setCellValue(userName == null ? "--" : userName);
						}
					}
					currentRow += orderList.size();
				} else {
					{
						HSSFCell cell = row.createCell((short) (3));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (4));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (5));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (6));
						cell.setCellValue("--");
					}
					{
						HSSFCell cell = row.createCell((short) (7));
						cell.setCellValue("--");
					}
					currentRow++;
				}
			}
		}
	}
}
