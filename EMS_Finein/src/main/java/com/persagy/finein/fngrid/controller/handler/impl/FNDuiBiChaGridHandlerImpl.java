package com.persagy.finein.fngrid.controller.handler.impl;

import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.finein.common.util.TimeDataUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnProtocol;
import com.persagy.ems.pojo.finein.FnTenantMeterData;
import com.persagy.ems.pojo.servicedata.ServiceData;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fngrid.controller.handler.FNDuiBiChaGridHandler;
import com.persagy.finein.fngrid.controller.handler.FNGGridCommonHandler;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNServiceDataService;
import com.persagy.finein.service.FNTenantMeterDataService;
import com.persagy.finein.service.FNTenantService;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;


@Service("FNDuiBiChaGridHandler")
public class FNDuiBiChaGridHandlerImpl implements FNDuiBiChaGridHandler{

	@Resource(name = "FNGGridCommonHandler")
	private FNGGridCommonHandler FNGGridCommonHandler;
	
	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;
	
	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;
	
	@Resource(name = "FNTenantMeterDataService")
	private FNTenantMeterDataService FNTenantMeterDataService;
	
	@Resource(name = "FNServiceDataService")
	private FNServiceDataService FNServiceDataService;
	
	protected final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public File process(String buildingId,File tempf, String resourceId, String path, Map dto) throws Exception {
		String timeFrom = (String) dto.get("startTime");
		String timeTo = (String) dto.get("endTime");
		Date from = standard.parse(timeFrom);
		Date to = standard.parse(timeTo);
		FileInputStream is = new FileInputStream(tempf);// 创建文件流
		HSSFWorkbook wb = new HSSFWorkbook(is);// 加载文件流
		FileOutputStream fout = null;
		Map<String, List> dataMap = new HashMap<>();
		Map<String, FnMeter> meterMap = FNMeterService.queryMap();
		Map<String, List<String>> meterTenantMap = FNMeterService.queryTenant(buildingId);
		Map<String, Double> timeMap = null;
		timeMap = TimeDataUtil.getTimeDataMap(from, to, EnumStatTimeType.Day_1);
		for (Entry<String, FnMeter> entry : meterMap.entrySet()) {
			FnMeter meter = entry.getValue();
			String protocolId = meter.getProtocolId();
			FnProtocol fnProtocol = ConstantDBBaseData.ProtocolMap.get(protocolId);
			Integer destCumulantFunctionId = FunctionTypeUtil.getCumulantFunctionId(meter.getEnergyTypeId());
			Integer cumulantFunctionId = FNGGridCommonHandler.queryCumulantFunctionId(meter.getProtocolId(), meter.getEnergyTypeId(),
					destCumulantFunctionId);
			if (cumulantFunctionId == null) {
				continue;
			}
			Map<String, Double> meterDataMap = new LinkedHashMap<>(timeMap);
			Map<String, Double> RuanJianDataMap = new LinkedHashMap<>(timeMap);
			if (fnProtocol.getPayType() == EnumPayType.PREPAY.getValue().intValue()) {// 预付费
				Integer billingMode = fnProtocol.getBillingMode();
				Integer shengYuFunction;
				EnumEnergyMoney energyMoney;
				if (billingMode == EnumPrepayChargeType.Qian.getValue().intValue()) {// 钱
					shengYuFunction = FunctionTypeUtil.getShengYuJinEFunctionId(meter.getEnergyTypeId());
					energyMoney = EnumEnergyMoney.Money;
				} else {
					shengYuFunction = FunctionTypeUtil.getShengYuLiangFunctionId(meter.getEnergyTypeId());
					energyMoney = EnumEnergyMoney.Energy;
				}
				{// 软件消耗
					List<String> tenantIds = meterTenantMap.get(meter.getId());
					if (tenantIds != null) {
						for (String tenantId : tenantIds) {
							List<FnTenantMeterData> meterDataList = FNTenantMeterDataService.queryListGteLt(buildingId,
									tenantId, meter.getId(), cumulantFunctionId, meter.getEnergyTypeId(),
									EnumTimeType.T2, from, to, energyMoney);
							for (FnTenantMeterData fnTenantMeterData : meterDataList) {
								String key = standard.format(fnTenantMeterData.getTimeFrom());
								if (RuanJianDataMap.get(key) == null) {
									RuanJianDataMap.put(key, fnTenantMeterData.getData());
								} else {
									RuanJianDataMap.put(key, RuanJianDataMap.get(key) + fnTenantMeterData.getData());
								}
							}
						}
					}
				}
				{// 仪表消耗
					List<ServiceData> meterDataList = FNServiceDataService.queryServiceDataGteLt(buildingId,
							meter.getId(), shengYuFunction, EnumTimeType.T2, from, to);
					for (ServiceData serviceData : meterDataList) {
						String key = standard.format(serviceData.getTimefrom());
						if (meterDataMap.get(key) == null) {
							meterDataMap.put(key, serviceData.getData());
						} else {
							meterDataMap.put(key, meterDataMap.get(key) + serviceData.getData());
						}
					}
				}
				{// 差值
					for (String key : timeMap.keySet()) {
						Double double1 = meterDataMap.get(key) == null ? 0.0 : meterDataMap.get(key);
						Double double2 = RuanJianDataMap.get(key) == null ? 0.0 : RuanJianDataMap.get(key);
						if (dataMap.get(entry.getKey()) == null) {
							dataMap.put(entry.getKey(), new ArrayList<Double>());
						}
						dataMap.get(entry.getKey()).add(double2 - double1);
					}
				}
			}
		}
		this.buildTenantDuiBiChaGridExcel(dataMap, timeMap, wb);
		try {
			fout = new FileOutputStream(path);
			wb.write(fout);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (fout != null) {
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if (wb != null) {
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
		}
		return new File(path);

	}
	
	@SuppressWarnings("deprecation")
	private void buildTenantDuiBiChaGridExcel(Map<String, List> dataMap, Map<String, Double> timeMap, HSSFWorkbook wb) {
		HSSFCellStyle style = wb.createCellStyle();
		style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
		style.setFillForegroundColor(HSSFColor.CORAL.index);
		HSSFSheet sheet = wb.getSheet("sheet");
		HSSFRow row0 = sheet.createRow((short) 0);
		HSSFCell cell;
		cell = row0.createCell((short) (0));
		cell.setCellValue("仪表编码");
		int count = 1;
		for (Entry<String, Double> entry : timeMap.entrySet()) {
			cell = row0.createCell((short) (count));
			cell.setCellValue(entry.getKey());
			count++;
		}
		int rowCount = 1;
		for (Entry<String, List> entry : dataMap.entrySet()) {
			String meterId = entry.getKey();
			List<Double> list = entry.getValue();
			HSSFRow row = sheet.createRow((short) rowCount);
			HSSFCell createCell = row.createCell(0);
			createCell.setCellValue(meterId);
			for (int i = 0; i < list.size(); i++) {
				HSSFCell createCell1 = row.createCell(i + 1);
				Double value = list.get(i);
				Double cha = (Double) ConstantDBBaseData.SysParamValueMap
						.get(FineinConstant.SysParamValueKey.Id_FNEnergyMoneyDuiBiCha);
				if (cha == null) {
					cha = 10.00;
				}
				if (value > cha || value < -cha) {
					createCell1.setCellStyle(style);
				}
				createCell1.setCellValue(list.get(i));
			}
			rowCount++;
		}

	}
}
