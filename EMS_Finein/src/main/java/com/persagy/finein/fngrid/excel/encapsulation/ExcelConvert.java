package com.persagy.finein.fngrid.excel.encapsulation;

import com.persagy.finein.fngrid.excel.conversion.ExcelToHtmlConverter;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.w3c.dom.Document;
import org.w3c.dom.Node;

import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.*;
import java.util.LinkedHashMap;
import java.util.Map;

public class ExcelConvert {
	
	public static Map<String,File> convertExcel2Html(String excelFilePath, String parentPath) throws Exception {
		
		Map<String,File> fileMap = new LinkedHashMap<String,File>(); 
		InputStream is = null;
		OutputStream out = null;
		StringWriter writer = null;
		
		//EXCEL文件（xls格式）
		File excelFile = new File(excelFilePath);
		File htmlFileParent = new File(parentPath);
		
		try {
			
			if (excelFile.exists()) {
				if (!htmlFileParent.exists()) {
					htmlFileParent.mkdirs();
				}
				
				//包装流
				is = new FileInputStream(excelFile);
				//Excel对象
				HSSFWorkbook workBook = new HSSFWorkbook(is);
				//Excel sheet个数
				Integer sheetCount = workBook.getNumberOfSheets();
				for(int i=0;i<sheetCount;i++){
					Document defalutDocument = DocumentBuilderFactory.newInstance().newDocumentBuilder().newDocument();
					
					ExcelToHtmlConverter converter = new ExcelToHtmlConverter(defalutDocument);
					
					//是否显示行号
					converter.setOutputRowNumbers(false);
					//是否显示列号
					converter.setOutputColumnHeaders(false);
					//是否显示内容为Sheet名称的标题
					converter.setOutputSheetHeader(false);
					
					//converter.setUseDivsToSpan(true);
					
					converter.processWorkbook(workBook,i);
					
					Document excelDoucument = converter.getDocument();
					Node style = excelDoucument.getElementsByTagName("style").item(0);
					style.setTextContent(style.getTextContent() + "td{min-width:100px;}" + "\n");
					
					writer = new StringWriter();
					Transformer serializer = TransformerFactory.newInstance().newTransformer();
					serializer.setOutputProperty(OutputKeys.ENCODING, "UTF-8");
					serializer.setOutputProperty(OutputKeys.INDENT, "yes");
					serializer.setOutputProperty(OutputKeys.METHOD, "html");
					serializer.transform(new DOMSource(excelDoucument), new StreamResult(writer));
					
					//HTML文件
					File htmlFile = new File(parentPath + File.separator + "sheet" + i + ".html");
					
					out = new FileOutputStream(htmlFile);
					out.write(writer.toString().getBytes("UTF-8"));
					out.flush();
					out.close();
					writer.close();
					
					fileMap.put(workBook.getSheetName(i), htmlFile);
				}
			}	
			
		}finally {
			try {
				if (is != null) {
					is.close();
				}
				if (out != null) {
					out.close();
				}
				if (writer != null) {
					writer.close();
				}
			} catch (Exception e) {
				throw e;
			}
		}
		return fileMap;
	}
	
}
