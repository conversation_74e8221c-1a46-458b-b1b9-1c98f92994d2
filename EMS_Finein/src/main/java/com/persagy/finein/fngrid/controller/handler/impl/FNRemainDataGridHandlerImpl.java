package com.persagy.finein.fngrid.controller.handler.impl;

import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.ems.pojo.finein.FnTenantPrePayMeterParam;
import com.persagy.ems.pojo.finein.FnTenantPrePayParam;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.enumeration.EnumPrePayType;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.fngrid.controller.handler.FNGGridCommonHandler;
import com.persagy.finein.fngrid.controller.handler.FNRemainDataGridHandler;
import com.persagy.finein.service.*;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;


@Service("FNRemainDataGridHandler")
public class FNRemainDataGridHandlerImpl implements FNRemainDataGridHandler {

	@Resource(name = "FNGGridCommonHandler")
	private FNGGridCommonHandler FNGGridCommonHandler;

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;
	
	@Resource(name = "FNTenantPayTypeService")
	private FNTenantPayTypeService FNTenantPayTypeService;
	
	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;
	
	@Resource(name = "FNTenantPrePayParamService")
	private FNTenantPrePayParamService FNTenantPrePayParamService;
	
	@Resource(name = "FNTenantPrePayMeterParamService")
	private FNTenantPrePayMeterParamService FNTenantPrePayMeterParamService;

	protected final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public File process(String buildingId, File tempf, String resourceId, String path, Map dto) throws Exception {

		List<FnTenant> fnTenantList = FNTenantService.queryListByValidStatus(buildingId, EnumValidStatus.VALID);

		Map<String, List<FnTenant>> tenantListMap = new HashMap<String, List<FnTenant>>();
		FNGGridCommonHandler.processTenantPrePayList(buildingId, fnTenantList, tenantListMap);


		// String buildingName = (String) dto.get("buildingName");
		// String timeFrom = (String) dto.get("startTime");
		// String timeTo = (String) dto.get("endTime");

		// Date from = standard.parse(timeFrom);
		File file = new File(path);
		// if (!file.exists()) {
		// file.mkdirs();
		// }

		FileInputStream is = new FileInputStream(tempf);// 创建文件流
		HSSFWorkbook wb = new HSSFWorkbook(is);// 加载文件流
		FileOutputStream fout = null;

		for (Entry<String, List<FnTenant>> payMap : tenantListMap.entrySet()) {
			String key = payMap.getKey();
			String[] split = key.split("-");
			String energyTypeId = split[0];
			Integer prePayType = Integer.valueOf(split[1]);
			List<FnTenant> list = payMap.getValue();
			List<String> tenantIdList = new ArrayList<String>();
			for (FnTenant tenant : list) {
				tenantIdList.add(tenant.getId());
			}
			List<Object> tenantList = new ArrayList<>();
			Map<String, List<Map<String, Object>>> meterMap = FNMeterService.queryMeter(tenantIdList, energyTypeId);
			for (FnTenant tenant : list) {
				List<Map<String, Object>> meterList = meterMap.get(tenant.getId());
				FnTenantPayType tenantPayType = FNTenantPayTypeService.query(tenant.getId(), energyTypeId);
				if (EnumPrePayType.ONLINE_TENANTPAY.getValue().intValue() == tenantPayType.getPrePayType()) {
					Map<String, Object> objectMap = new HashMap<>();
					FnTenantPrePayParam tenantPreParam = FNTenantPrePayParamService.queryTenantPreParam(tenant.getId(),
							energyTypeId);
					objectMap.put("tenantId", tenant.getId());
					objectMap.put("tenantName", tenant.getName());
					objectMap.put("roomCode", tenant.getRoomCodes());
					objectMap.put("prePayType", prePayType);
					StringBuffer stringBuffer = new StringBuffer();
					if (meterList == null) {
						continue;
					}
					for (Map<String, Object> meter : meterList) {
						String address = (String) meter.get("address");
						if (stringBuffer.length() == 0) {
							stringBuffer.append(address);
						} else {
							stringBuffer.append(",").append(address);
						}
					}
					objectMap.put("address", stringBuffer.toString());

					if (tenantPayType.getPrepayChargeType() == EnumPrepayChargeType.Liang.getValue().intValue()) {
						objectMap.put("remainEnergy", tenantPreParam == null ? null : tenantPreParam.getRemainData());
						objectMap.put("remainMoney", null);
						objectMap.put("remainDays", tenantPreParam == null ? null : tenantPreParam.getRemainDays());
					} else {
						objectMap.put("remainMoney", tenantPreParam == null ? null : tenantPreParam.getRemainData());
						objectMap.put("remainEnergy", null);
						objectMap.put("remainDays", tenantPreParam == null ? null : tenantPreParam.getRemainDays());
					}
					tenantList.add(objectMap);
				} else {
					if (meterList == null) {
						continue;
					}
					for (Map<String, Object> meter : meterList) {
						Map<String, Object> objectMap = new HashMap<>();
						objectMap.put("tenantId", tenant.getId());
						objectMap.put("tenantName", tenant.getName());
						objectMap.put("roomCode", meter.get("roomCode"));
						objectMap.put("prePayType", prePayType);
						objectMap.put("address", meter.get("address"));
						FnTenantPrePayMeterParam tenantPreMeterParam = FNTenantPrePayMeterParamService
								.queryMeterPreParam(tenant.getId(), (String) meter.get("meterId"), energyTypeId);
						if (tenantPayType.getPrepayChargeType() == EnumPrepayChargeType.Liang.getValue().intValue()) {
							objectMap.put("remainEnergy",
									tenantPreMeterParam == null ? null : tenantPreMeterParam.getRemainData());
							objectMap.put("remainMoney", "--");
							objectMap.put("remainDays",
									tenantPreMeterParam == null ? null : tenantPreMeterParam.getRemainDays());
						} else {
							objectMap.put("remainMoney",
									tenantPreMeterParam == null ? null : tenantPreMeterParam.getRemainData());
							objectMap.put("remainEnergy", null);
							objectMap.put("remainDays",
									tenantPreMeterParam == null ? null : tenantPreMeterParam.getRemainDays());
							tenantList.add(objectMap);
						}
					}
				}

				this.buildRemainGridExcel(energyTypeId, prePayType, tenantList, wb);
			}
			try {
				fout = new FileOutputStream(file);
				wb.write(fout);
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				if (fout != null) {
					try {
						fout.close();
					} catch (IOException e1) {
					}
				}
				if (wb != null) {
					try {
						wb.close();
					} catch (IOException e1) {
					}
				}
			}
		}
		return file;
	}
	@SuppressWarnings("deprecation")
	private void buildRemainGridExcel(String energyTypeId, Integer prePayType, List<Object> tenantList,
			HSSFWorkbook wb) {
		String energyType = EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId);
		String str = prePayType == 0 ? "软扣" : "表扣";
		HSSFSheet sheet = wb.getSheet(energyType + "-" + str);
		if (tenantList != null) {
			int currentRow = 1;
			for (int i = 0; i < tenantList.size(); i++) {
				Map<String, Object> tenant = (Map<String, Object>) tenantList.get(i);
				String tenantId = (String) tenant.get("tenantId");
				String tenantName = (String) tenant.get("tenantName");
				String roomIds = (String) tenant.get("roomCode");
				String address = (String) tenant.get("address");
				Double remainMoney = DoubleFormatUtil.Instance().getDoubleData_00(tenant.get("remainMoney"));
				Double remainEnergy = DoubleFormatUtil.Instance().getDoubleData_00(tenant.get("remainEnergy"));
				String remainDays = (String) tenant.get("remainDays");

				HSSFRow row = sheet.createRow((short) currentRow);
				{
					HSSFCell cell = row.createCell((short) (0));
					cell.setCellValue(tenantId == null ? " " : tenantId);
				}
				{
					HSSFCell cell = row.createCell((short) (1));
					cell.setCellValue(tenantName == null ? " " : tenantName);
				}
				{
					HSSFCell cell = row.createCell((short) (2));
					cell.setCellValue(roomIds == null ? "--" : roomIds);
				}

				{
					HSSFCell cell = row.createCell((short) (3));
					cell.setCellValue("".equals(address) ? "--" : address);
				}
				{
					HSSFCell cell = row.createCell((short) (4));
					cell.setCellValue(remainMoney == null ? 0 : remainMoney);
				}
				{
					HSSFCell cell = row.createCell((short) (5));
					cell.setCellValue(remainEnergy == null ? 0 : remainEnergy);
				}
				{
					HSSFCell cell = row.createCell((short) (6));
					cell.setCellValue(remainDays == null ? "--" : remainDays + "");
				}
				currentRow++;
			}
		}
	}
}
