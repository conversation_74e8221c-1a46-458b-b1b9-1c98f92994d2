package com.persagy.finein.fngrid.controller;

import com.pbsage.ems.excel.data.MyDataAccess;
import com.pbsage.ems.excel.data.MyLog;
import com.pbsage.ems.excel.util.VarValueDraw;
import com.pbsage.ems.excel.util.WorkbookDrawUtil;
import com.pbsage.ems.excel.util.WorkbookObject;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.DictionaryUtil;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnGridTemplate;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.service.FnGridTemplateService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 报表打印
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FngGridTempController extends BaseController {

    @Resource(name = "FnGridTemplateService")
    private FnGridTemplateService fnGridTemplateService;

    /**
     * 报表打印 -- 模板列表
     * @param jsonString
     * @return
     */
    @RequestMapping("FNGGridTempListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult gridTempList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Map user = (Map) dto.get("puser");
            List<Object> dataList = new ArrayList<Object>();
            FnGridTemplate query = new FnGridTemplate();
            if(!"persagyAdmin".equals(user.get("name"))){
                query.setValid(1);
            }
            List<FnGridTemplate> list = fnGridTemplateService.queryList(query);
            for (FnGridTemplate fnGridTemplate : list) {
                Map<String,Object> fnGridTemplateMap = new HashMap<String,Object>();
                fnGridTemplateMap.put("id", fnGridTemplate.getId());
                fnGridTemplateMap.put("name", fnGridTemplate.getName());
                fnGridTemplateMap.put("type", fnGridTemplate.getType());
                fnGridTemplateMap.put("include", fnGridTemplate.getInclude());
                fnGridTemplateMap.put("isSystem", fnGridTemplate.getIsSystem());
                fnGridTemplateMap.put("includeBuild", fnGridTemplate.getIsIncludeBuild()==0?false:true);
                fnGridTemplateMap.put("valid", fnGridTemplate.getValid());
                dataList.add(fnGridTemplateMap);
            }
            content.addAll(dataList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNGGridTempListService");
        }
    }

    /**
     * 报表打印 -- 保存模板
     * @param jsonString
     * @return
     */
    @RequestMapping("FNGGridTempSaveService")
    @ResponseBody
    public InterfaceResult gridTempSave(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            List resource = (List) dto.get("resource");

            // 报表名称
            String templateName = (String) dto.get("templateName");
            if (templateName == null || "".equals(templateName)) {
                throw new Exception("templateName不能为空！");
            }
            FileResource fileResource = new FileResource();
            if (resource != null) {
                for (int i = 0; i < resource.size(); i++) {
                    String id = (String) resource.get(i);
                    fileResource.setId(id);
                    fileResource = coreDao.query(fileResource).get(0);

                    String fileStorageDictionary = DictionaryUtil.getFileStorageDictionary();
                    String filePath = fileStorageDictionary + fileResource.getSubdirectory() + "/" + id;
                    // 获取模板文件
                    WorkbookObject wo = new WorkbookObject(filePath);
                    wo.dataAccess = new MyDataAccess();
                    wo.log = new MyLog();
                    // 解析excel模板中的设置项
                    VarValueDraw setItems = WorkbookDrawUtil.Draw(wo);

                    FnGridTemplate gridTemplate = new FnGridTemplate();
                    gridTemplate.setId(UUID.randomUUID().toString());
                    gridTemplate.setName(templateName);
                    gridTemplate.setType(getTemplateType(setItems));
                    gridTemplate.setIsIncludeBuild(setItems.buildingSign == true ? 1 : 0);
                    gridTemplate.setInclude(getTemplateInclude(setItems));
                    gridTemplate.setIsSystem(0);
                    // reportTemp.setCreateUser(userId);
                    gridTemplate.setCreateTime(currentTime);
                    gridTemplate.setValid(EnumValidStatus.VALID.getValue());
                    gridTemplate.setResourceId(id);
                    fnGridTemplateService.save(gridTemplate);
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNGGridTempSaveService");
        }
    }

    /**
     * 获取模板的包含项，即需要选的项
     *
     * @param varValue
     * @return
     */
    private String getTemplateInclude(VarValueDraw varValue) {
        String include = "";
        if (varValue.variableMap.containsKey("Energyitems")) {// 判断分项
            include += "1,";
        }
        if (varValue.variableMap.containsKey("Meters")) {// 判断支路/仪表
            include += "2,";
        }
        if (include.length() > 1) {
            include = include.substring(0, include.length() - 1);
        }
        return include;
    }

    /**
     * 获取模板的时间类型
     *
     * @param varValue
     * @return
     */
    private String getTemplateType(VarValueDraw varValue) {
        String timeType = "d";
        if (varValue.timeType == 3) {
            timeType = "w";
        } else if (varValue.timeType == 4) {
            timeType = "m";
        } else if (varValue.timeType == 5) {
            timeType = "y";
        }
        if (varValue.timeSpan) {
            timeType = timeType + timeType;
        }
        return timeType;
    }

    /**
     * 报表打印 -- 模板修改
     * @param jsonString
     * @return
     */
    @RequestMapping("FNGGridTempUpdateService")
    @ResponseBody
    public InterfaceResult gridTempUpdate(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String)dto.get("id");
            String name = (String)dto.get("name");
            if(id == null || name == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnGridTemplate queryCount = new FnGridTemplate();
            queryCount.setName(name);
            if(fnGridTemplateService.count(queryCount) > 0){
                throw new Exception("该名称已被使用，请修改");
            }


            FnGridTemplate query = new FnGridTemplate();
            query.setId(id);

            FnGridTemplate update = new FnGridTemplate();
            update.setName(name);
            update.setLastUpdateTime(new Date());

            fnGridTemplateService.update(query, update);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNGGridTempUpdateService");
        }
    }

    /**
     * 报表打印 -- 模板状态修改
     * @param jsonString
     * @return
     */
    @RequestMapping("FNGGridTempUpdateStatusService")
    @ResponseBody
    public InterfaceResult gridTempUpdateStatus(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String)dto.get("id");
            Integer status = (Integer)dto.get("status");
            if(id == null || status == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnGridTemplate query = new FnGridTemplate();
            query.setId(id);

            FnGridTemplate update = new FnGridTemplate();
            update.setValid(status);
            update.setLastUpdateTime(new Date());

            fnGridTemplateService.update(query, update);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNGGridTempUpdateStatusService");
        }
    }

    /**
     * 删除模板
     * @param jsonString
     * @return
     */
    @RequestMapping("FNGGridTempDeleteService")
    @ResponseBody
    public InterfaceResult gridTempDelete(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String)dto.get("id");
            if(id == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            FnGridTemplate query = new FnGridTemplate();
            query.setId(id);

            fnGridTemplateService.removeOne(query);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNGGridTempDeleteService");
        }
    }
}
