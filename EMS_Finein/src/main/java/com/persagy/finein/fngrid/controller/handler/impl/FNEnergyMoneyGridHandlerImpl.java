package com.persagy.finein.fngrid.controller.handler.impl;

import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantData;
import com.persagy.finein.core.util.EnergyTypeUtil;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fngrid.controller.handler.FNEnergyMoneyGridHandler;
import com.persagy.finein.fngrid.controller.handler.FNGGridCommonHandler;
import com.persagy.finein.service.FNTenantDataService;
import com.persagy.finein.service.FNTenantService;
import org.apache.commons.lang.time.DateUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;


@Service("FNEnergyMoneyGridHandler")
public class FNEnergyMoneyGridHandlerImpl implements FNEnergyMoneyGridHandler{

	@Resource(name = "FNGGridCommonHandler")
	private FNGGridCommonHandler FNGGridCommonHandler;
	
	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;
	
	@Resource(name = "FNTenantDataService")
	private FNTenantDataService FNTenantDataService;
	
	protected final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	@SuppressWarnings({ "unchecked", "rawtypes" })
	@Override
	public File process(String buildingId,File tempf, String resourceId, String path, Map dto) throws Exception {
		
		List<FnTenant> fnTenantList = FNTenantService.queryListByValidStatus(buildingId, EnumValidStatus.VALID);
		
		Map<String, List<FnTenant>> tenantListMap = new HashMap<String, List<FnTenant>>();
		FNGGridCommonHandler.processTenanPayTypetList(buildingId, fnTenantList, tenantListMap);
		
	

		// String buildingName = (String) dto.get("buildingName");
		String timeFrom = (String) dto.get("startTime");
		String timeTo = (String) dto.get("endTime");
		Date from = standard.parse(timeFrom);
		Date to = standard.parse(timeTo);

		FileInputStream is = new FileInputStream(tempf);// 创建文件流
		HSSFWorkbook wb = new HSSFWorkbook(is);// 加载文件流
		FileOutputStream fout = null;

		Map<String, Double> tempMap = new LinkedHashMap<>();
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(from);
		while (calendar.getTime().getTime() < to.getTime()) {
			Date time = DateUtils.truncate(calendar.getTime(), Calendar.MONTH);
			tempMap.put(standard.format(time), null);
			calendar.add(Calendar.MONTH, 1);
		}
		for (Entry<String, List<FnTenant>> payMap : tenantListMap.entrySet()) {
			Map<String, Double> energyMap = new LinkedHashMap<>(tempMap);
			Map<String, Double> moneyMap = new LinkedHashMap<>(tempMap);

			String key = payMap.getKey();
			String[] split = key.split("-");
			String energyTypeId = split[0];
			String energyUnit = UnitUtil.getCumulantUnit(energyTypeId);
			EnumPayType payType = EnumPayType.valueOf(Integer.valueOf(split[1]));

			List tenantObjList = new ArrayList<Map<String, Object>>();
			List<FnTenant> list = payMap.getValue();
			for (FnTenant tenant : list) {
				Map<String, Object> tenantMap = new HashMap<>();
				tenantMap.put("tenantId", tenant.getId());
				tenantMap.put("tenantName", tenant.getName());
				tenantObjList.add(tenantMap);
				Map<String, Double> newEnergyMap = new LinkedHashMap<>(tempMap);
				Map<String, Double> newMoneyMap = new LinkedHashMap<>(tempMap);
				Date newTimeFrom = from;
				Date newTimeTo = to;
				if (tenant.getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue()) {
					if (tenant.getLeaveTime() != null) {
						newTimeTo = new Date(tenant.getLeaveTime().getTime());
					}
				}
				{
					List<FnTenantData> tenantSumList = FNTenantDataService.queryListGteLt(tenant.getBuildingId(),
							tenant.getId(), EnumTimeType.T2, energyTypeId, newTimeFrom, newTimeTo,
							EnumEnergyMoney.Energy);
					if (tenantSumList != null) {
						for (FnTenantData tenantSum : tenantSumList) {
							if (tenantSum.getData() != null) {
								if (tenantSum.getTimeFrom().getTime() < tenant.getActiveTime().getTime()) {// 小于激活时间，不统计
									continue;
								}
								String month = standard.format(tenantSum.getTimeFrom()).substring(0, 7)
										+ "-01 00:00:00";
								if (newEnergyMap.get(month) == null) {
									newEnergyMap.put(month, tenantSum.getData());
								} else {
									newEnergyMap.put(month, newEnergyMap.get(month) + tenantSum.getData());
								}

								if (energyMap.get(month) == null) {
									energyMap.put(month, tenantSum.getData());
								} else {
									energyMap.put(month, energyMap.get(month) + tenantSum.getData());
								}
							}
						}
					}
				}
				{
					List<FnTenantData> tenantSumList = FNTenantDataService.queryListGteLt(tenant.getBuildingId(),
							tenant.getId(), EnumTimeType.T2, energyTypeId, newTimeFrom, newTimeTo,
							EnumEnergyMoney.Money);
					if (tenantSumList != null) {
						for (FnTenantData tenantSum : tenantSumList) {
							if (tenantSum.getData() != null) {
								if (tenantSum.getTimeFrom().getTime() < tenant.getActiveTime().getTime()) {// 小于激活时间，不统计
									continue;
								}
								String month = standard.format(tenantSum.getTimeFrom()).substring(0, 7)
										+ "-01 00:00:00";
								if (newMoneyMap.get(month) == null) {
									newMoneyMap.put(month, tenantSum.getData());
								} else {
									newMoneyMap.put(month, newMoneyMap.get(month) + tenantSum.getData());
								}

								if (moneyMap.get(month) == null) {
									moneyMap.put(month, tenantSum.getData());
								} else {
									moneyMap.put(month, moneyMap.get(month) + tenantSum.getData());
								}
							}
						}
					}
				}

				List<Object> dataList = new ArrayList<>();
				tenantMap.put("dataList", dataList);
				for (Entry<String, Double> entry : newEnergyMap.entrySet()) {
					Map<String, Object> map = new HashMap<>();
					map.put("time", entry.getKey());
					map.put("energy", entry.getValue());
					map.put("money", newMoneyMap.get(entry.getKey()));
					dataList.add(map);
				}
			}
			List<Object> timeList = new ArrayList<>();
			for (Entry<String, Double> entry : energyMap.entrySet()) {
				Map<String, Object> map = new HashMap<>();
				map.put("time", entry.getKey());
				map.put("energy", entry.getValue());
				map.put("money", moneyMap.get(entry.getKey()));
				timeList.add(map);
			}
			Map<String, Object> contentObj = new HashMap<>();
			contentObj.put("energyUnit", energyUnit);
			contentObj.put("timeList", timeList);
			contentObj.put("tenantList", tenantObjList);
			this.buildEnergyMoneyGridExcel(energyTypeId, payType, contentObj, wb);
		}
		try {
			fout = new FileOutputStream(path);
			wb.write(fout);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (fout != null) {
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if (wb != null) {
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
		}
		// log.error("***********创建能耗费用报表文件:" + path);
		return new File(path);
	
	}
	
	@SuppressWarnings({ "deprecation", "unchecked" })
	private void buildEnergyMoneyGridExcel(String energyTypeId, EnumPayType payType, Map<String, Object> contentObj,
			HSSFWorkbook wb) {
		// 能耗费用报表-电-2017.02~2017.09
		String energyType = EnergyTypeUtil.queryEnergyTypeNameById(energyTypeId);
		HSSFSheet sheet = wb.getSheet(energyType + "-" + payType.getView());
		String energyUnit = (String) contentObj.get("energyUnit");

		List<Map<String, Object>> timeList = (List<Map<String, Object>>) contentObj.get("timeList");
		List<Map<String, Object>> tenantList = (List<Map<String, Object>>) contentObj.get("tenantList");

		// 创建标题
		HSSFRow row0 = sheet.getRow((short) 0);
		HSSFRow row1 = sheet.getRow((short) 1);
		HSSFRow row2 = sheet.getRow((short) 2);
		for (int i = 0; i < timeList.size(); i++) {
			sheet.addMergedRegion(new CellRangeAddress(0, 0, 2 * i + 2, 2 * i + 1 + 2));//
			Map<String, Object> timeMap = (Map<String, Object>) timeList.get(i);
			String time = (String) timeMap.get("time");
			time = time.substring(0, 7).replace("-", ".");
			Double energy = DoubleFormatUtil.Instance().getDoubleData_00(timeMap.get("energy"));
			Double money = DoubleFormatUtil.Instance().getDoubleData_00(timeMap.get("money"));
			{
				HSSFCell cell = row0.createCell((short) (2 * i + 2));
				cell.setCellValue(time);
			}
			{
				HSSFCell cell = row1.createCell((short) (2 * i + 2));
				cell.setCellValue("能耗(" + energyUnit + ")");
			}
			{
				HSSFCell cell = row1.createCell((short) (2 * i) + 1 + 2);
				cell.setCellValue("费用(元)");
			}
			{
				HSSFCell cell = row2.createCell((short) (2 * i + 2));
				cell.setCellValue(energy == null ? 0 : energy);
			}
			{
				HSSFCell cell = row2.createCell((short) (2 * i) + 1 + 2);
				cell.setCellValue(money == null ? 0 : money);
			}
		}
		if (tenantList != null) {
			for (int i = 0; i < tenantList.size(); i++) {
				HSSFRow row = sheet.createRow((short) i + 3);
				Map<String, Object> tenant = (Map<String, Object>) tenantList.get(i);
				String tenantId = (String) tenant.get("tenantId");
				String tenantName = (String) tenant.get("tenantName");
				List<Object> dataList = (List<Object>) tenant.get("dataList");
				{
					HSSFCell cell = row.createCell((short) (0));
					cell.setCellValue(tenantId == null ? " " : tenantId);
				}
				{
					HSSFCell cell = row.createCell((short) (1));
					cell.setCellValue(tenantName == null ? " " : tenantName);
				}
				if (dataList != null) {
					for (int j = 0; j < dataList.size(); j++) {
						Map<String, Object> timeMap = (Map<String, Object>) dataList.get(j);
						Double energy = DoubleFormatUtil.Instance().getDoubleData_00(timeMap.get("energy"));
						Double money = DoubleFormatUtil.Instance().getDoubleData_00(timeMap.get("money"));
						{
							HSSFCell cell = row.createCell((short) (2 * j + 2));
							cell.setCellValue(energy == null ? 0 : energy);
						}
						{
							HSSFCell cell = row.createCell((short) (2 * j) + 1 + 2);
							cell.setCellValue(money == null ? 0 : money);
						}
					}
				}
			}
		}

	}
	
	
}
