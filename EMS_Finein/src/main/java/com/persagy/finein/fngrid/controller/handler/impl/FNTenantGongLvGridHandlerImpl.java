package com.persagy.finein.fngrid.controller.handler.impl;

import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantMeterPowerStat;
import com.persagy.finein.enumeration.EnumBodyType;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumTenantStatus;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.fngrid.controller.handler.FNGGridCommonHandler;
import com.persagy.finein.fngrid.controller.handler.FNTenantGongLvGridHandler;
import com.persagy.finein.service.FNTenantMeterPowerStatService;
import com.persagy.finein.service.FNTenantService;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;


@Service("FNTenantGongLvGridHandler")
public class FNTenantGongLvGridHandlerImpl implements FNTenantGongLvGridHandler {

    @Resource(name = "FNGGridCommonHandler")
    private FNGGridCommonHandler FNGGridCommonHandler;

    @Resource(name = "FNTenantService")
    private FNTenantService FNTenantService;

    @Resource(name = "FNTenantMeterPowerStatService")
    private FNTenantMeterPowerStatService FNTenantMeterPowerStatService;

    protected final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @SuppressWarnings({"unchecked", "rawtypes"})
    @Override
    public File process(String buildingId, File tempf, String resourceId, String path, Map dto) throws Exception {

        List<FnTenant> fnTenantList = FNTenantService.queryListByValidStatus(buildingId, EnumValidStatus.VALID);

        Map<String, List<FnTenant>> tenantListMap = new HashMap<String, List<FnTenant>>();
        FNGGridCommonHandler.processTenanPayTypetList(buildingId, fnTenantList, tenantListMap);


        // String buildingName = (String) dto.get("buildingName");
        String timeFrom = (String) dto.get("startTime");
        String timeTo = (String) dto.get("endTime");
        Date from = standard.parse(timeFrom);
        Date to = standard.parse(timeTo);

        FileInputStream is = new FileInputStream(tempf);// 创建文件流
        HSSFWorkbook wb = new HSSFWorkbook(is);// 加载文件流
        FileOutputStream fout = null;
        for (Entry<String, List<FnTenant>> payMap : tenantListMap.entrySet()) {
            String key = payMap.getKey();
            String[] split = key.split("-");
            String energyTypeId = split[0];
            if (!energyTypeId.equals(FineinConstant.EnergyType.Dian)) {
                continue;
            }
            List<Object> dataList = new ArrayList<>();
            EnumPayType payType = EnumPayType.valueOf(Integer.valueOf(split[1]));
            List tenantObjList = new ArrayList<Map<String, Object>>();
            List<FnTenant> list = payMap.getValue();
            for (FnTenant tenant : list) {
                Map<String, Object> tenantMap = new HashMap<>();
                tenantMap.put("tenantId", tenant.getId());
                tenantMap.put("tenantName", tenant.getName());
                tenantObjList.add(tenantMap);
                to = standard.parse(timeTo);
                if (tenant.getStatus().intValue() == EnumTenantStatus.RETURNED_LEASE.getValue()) {
                    if (tenant.getLeaveTime() != null) {
                        to = new Date(tenant.getLeaveTime().getTime());
                    }
                }
                {
                    System.out.println("查询租户：" + tenant.getId() + "的数据,建筑：" + tenant.getBuildingId() + ",类型：" + energyTypeId
                            + ",开始结束时间：" + standard.format(from) + "~" + standard.format(to));
                    List<FnTenantMeterPowerStat> tenantSumList = FNTenantMeterPowerStatService.queryListGteLte(
                            tenant.getBuildingId(), tenant.getId(), EnumBodyType.TENANT, energyTypeId, from, to);
                    System.out.println("数据：");
                    if (tenantSumList != null) {
                        for (FnTenantMeterPowerStat powerStat : tenantSumList) {
                            System.out.print(powerStat.toString());
                            Map<String, Object> map = new HashMap<>();
                            map.put("tenantId", powerStat.getTenantId());
                            map.put("tenantName", tenant.getName());
                            map.put("time", standard.format(powerStat.getTimeFrom()));
                            map.put("data", powerStat.getData());
                            map.put("countTime", standard.format(powerStat.getCountTime()));
                            dataList.add(map);
                        }
                    }
                }
            }
            this.buildTenantGongLvGridExcel(energyTypeId, payType, dataList, wb);
        }
        try {
            fout = new FileOutputStream(path);
            wb.write(fout);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (fout != null) {
                try {
                    fout.close();
                } catch (IOException e1) {
                }
            }
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e1) {
                }
            }
        }
        return new File(path);


    }

    @SuppressWarnings("deprecation")
    private void buildTenantGongLvGridExcel(String energyTypeId, EnumPayType payType, List<Object> dataList,
                                            HSSFWorkbook wb) {
        // 能耗费用报表-电-2017.02~2017.09
        HSSFSheet sheet = wb.getSheet(payType.getView());
        // 创建标题
        HSSFRow row0 = sheet.getRow((short) 0);
        String[] arr = {"租户编号", "租户名称", "峰值时间", "统计时间", "功率峰值"};
        for (int i = 0; i < arr.length; i++) {
            HSSFCell cell = row0.createCell((short) (i));
            cell.setCellValue(arr[i]);
        }
        if (dataList != null) {
            for (int i = 0; i < dataList.size(); i++) {
                Map<String, Object> map = (Map<String, Object>) dataList.get(i);
                String tenantId = (String) map.get("tenantId");
                String tenantName = (String) map.get("tenantName");
                // String meterId = (String) map.get("meterId");
                String timeFrom = (String) map.get("time");
                String countTime = (String) map.get("countTime");
                Double data = (Double) map.get("data");
                HSSFRow row = sheet.createRow((short) i + 1);
                {
                    HSSFCell cell = row.createCell((short) (0));
                    cell.setCellValue(tenantId == null ? " " : tenantId);
                }
                {
                    HSSFCell cell = row.createCell((short) (1));
                    cell.setCellValue(tenantName == null ? " " : tenantName);
                }
                {
                    HSSFCell cell = row.createCell((short) (2));
                    cell.setCellValue(timeFrom == null ? " " : timeFrom);
                }

                {
                    HSSFCell cell = row.createCell((short) (3));
                    cell.setCellValue(countTime == null ? " " : countTime);
                }
                {
                    HSSFCell cell = row.createCell((short) (4));
                    cell.setCellValue(data == null ? " " : data + "");
                }

            }
        }

    }


}
