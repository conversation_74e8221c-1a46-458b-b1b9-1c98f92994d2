package com.persagy.finein.custom.zhongdian.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnPriceTemplate;
import com.persagy.ems.pojo.finein.FnRoom;
import com.persagy.ems.pojo.finein.FnRoomMeter;
import com.persagy.finein.communication.exception.MeterSetException;
import com.persagy.finein.communication.interfaces.impl.DI_C_02_Y_Q_001.DI_C_02_Y_Q_001_Impl;
import com.persagy.finein.enumeration.EnumPaulEleStatus;
import com.persagy.finein.enumeration.EnumPriceDetail;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNRoomMeterService;
import com.persagy.finein.service.FNRoomService;
import com.persagy.finein.service.impl.FNPriceTemplateServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2024/12/31 9:54
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FncZdNewController extends BaseController {

    @Resource(name = "FNRoomService")
    private FNRoomService fnRoomService;

    @Resource(name = "FNRoomMeterService")
    private FNRoomMeterService fnRoomMeterService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNPriceTemplateService")
    private FNPriceTemplateServiceImpl fNPriceTemplateService;


    /**
     * 加密投退新 （武汉长江中心）
     * 传入参数：楼层，按照楼层一层一层投退，方便观察是否成功
     * 传入参数：仪表，控制单个仪表的设置
     * meterId 等于空时，folderId必传
     */
    @RequestMapping("zdNewUnSecretMultiple")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult zdNewUnSecretMultiple(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String folderId = (String) dto.get("folderId");
            String meterId = (String) dto.get("meterId");
            if (meterId == null) {
                if (folderId == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
            }
            if (folderId != null) {
                //查询楼层内的电表
                List<FnRoom> roomList = fnRoomService.queryListByFloorId(folderId);
                for (FnRoom fnRoom : roomList) {
                    FnRoomMeter query = new FnRoomMeter();
                    query.setRoomId(fnRoom.getId());
                    query.setEnergyTypeId("Dian");
                    List<FnRoomMeter> fnRoomMeters = fnRoomMeterService.query(query);
                    if (fnRoomMeters != null && !fnRoomMeters.isEmpty()) {
                        for (FnRoomMeter fnRoomMeter : fnRoomMeters) {
                            Map<String, Object> meterResultMap = new HashMap<>();
                            FnMeter meter = fnMeterService.queryMeterById(fnRoomMeter.getMeterId());
                            meterResultMap.put("roomCode", fnRoomMeter.getRoomCode());
                            meterResultMap.put("meterId", meter.getId());
                            try {
                                String protocolId = meter.getProtocolId();
                                if (!protocolId.equals("DI_C_02_Y_Q_001")) {
                                    continue;
                                }
                                DI_C_02_Y_Q_001_Impl communication = (DI_C_02_Y_Q_001_Impl) SystemConstant.context.getBean(protocolId);
                                boolean singleResult = communication.unSecret(meter);
                                if (singleResult) {
                                    meterResultMap.put("result", EnumYesNo.YES.getValue());
                                } else {
                                    meterResultMap.put("result", EnumYesNo.NO.getValue());
                                    meterResultMap.put("reason", "设置失败");
                                }
                            } catch (Exception e) {
                                meterResultMap.put("result", EnumYesNo.NO.getValue());
                                meterResultMap.put("reason", "设置失败:" + e.getMessage());
                            }
                            content.add(meterResultMap);
                        }
                    }
                }
            } else if (meterId != null) {
                Map<String, Object> meterResultMap = new HashMap<>();
                FnMeter meter = fnMeterService.queryMeterById(meterId);
                meterResultMap.put("meterId", meter.getId());
                try {
                    String protocolId = meter.getProtocolId();
                    if (!protocolId.equals("DI_C_02_Y_Q_001")) {
                        meterResultMap.put("result", EnumYesNo.NO.getValue());
                        meterResultMap.put("reason", "非DI_C_02_Y_Q_001电表");
                        content.add(meterResultMap);
                        return Result.SUCCESS(content);
                    }
                    DI_C_02_Y_Q_001_Impl communication = (DI_C_02_Y_Q_001_Impl) SystemConstant.context.getBean(protocolId);
                    boolean singleResult = communication.unSecret(meter);
                    if (singleResult) {
                        meterResultMap.put("result", EnumYesNo.YES.getValue());
                    } else {
                        meterResultMap.put("result", EnumYesNo.NO.getValue());
                        meterResultMap.put("reason", "设置失败");
                    }
                } catch (Exception e) {
                    meterResultMap.put("result", EnumYesNo.NO.getValue());
                    meterResultMap.put("reason", "设置失败:" + e.getMessage());
                }
                content.add(meterResultMap);
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "zdNewUnSecretMultiple");
        }
    }

    /**
     * 开启本地费控 （武汉长江中心现场电表未设置）
     * 传入参数：楼层，按照楼层一层一层开启，方便观察是否成功
     * 传入参数：仪表，控制单个仪表的设置
     * meterId 等于空时，folderId必传
     */
    @RequestMapping("zdNewOnLocalCostControlMultiple")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult zdNewOnLocalCostControlMultiple(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String folderId = (String) dto.get("folderId");
            String meterId = (String) dto.get("meterId");
            if (meterId == null) {
                if (folderId == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
            }
            if (folderId != null) {
                //查询楼层内的电表
                List<FnRoom> roomList = fnRoomService.queryListByFloorId(folderId);
                for (FnRoom fnRoom : roomList) {
                    FnRoomMeter query = new FnRoomMeter();
                    query.setRoomId(fnRoom.getId());
                    query.setEnergyTypeId("Dian");
                    List<FnRoomMeter> fnRoomMeters = fnRoomMeterService.query(query);
                    if (fnRoomMeters != null && !fnRoomMeters.isEmpty()) {
                        for (FnRoomMeter fnRoomMeter : fnRoomMeters) {
                            Map<String, Object> meterResultMap = new HashMap<>();
                            FnMeter meter = fnMeterService.queryMeterById(fnRoomMeter.getMeterId());
                            meterResultMap.put("roomCode", fnRoomMeter.getRoomCode());
                            meterResultMap.put("meterId", meter.getId());
                            try {
                                String protocolId = meter.getProtocolId();
                                if (!protocolId.equals("DI_C_02_Y_Q_001")) {
                                    continue;
                                }
                                DI_C_02_Y_Q_001_Impl communication = (DI_C_02_Y_Q_001_Impl) SystemConstant.context.getBean(protocolId);
                                boolean singleResult = communication.onLocalCostControl(meter);
                                if (singleResult) {
                                    meterResultMap.put("result", EnumYesNo.YES.getValue());
                                } else {
                                    meterResultMap.put("result", EnumYesNo.NO.getValue());
                                    meterResultMap.put("reason", "设置失败");
                                }
                            } catch (Exception e) {
                                meterResultMap.put("result", EnumYesNo.NO.getValue());
                                meterResultMap.put("reason", "设置失败:" + e.getMessage());
                            }
                            content.add(meterResultMap);
                        }
                    }
                }
            } else if (meterId != null) {
                Map<String, Object> meterResultMap = new HashMap<>();
                FnMeter meter = fnMeterService.queryMeterById(meterId);
                meterResultMap.put("meterId", meter.getId());
                try {
                    String protocolId = meter.getProtocolId();
                    if (!protocolId.equals("DI_C_02_Y_Q_001")) {
                        meterResultMap.put("result", EnumYesNo.NO.getValue());
                        meterResultMap.put("reason", "非DI_C_02_Y_Q_001电表");
                        content.add(meterResultMap);
                        return Result.SUCCESS(content);
                    }
                    DI_C_02_Y_Q_001_Impl communication = (DI_C_02_Y_Q_001_Impl) SystemConstant.context.getBean(protocolId);
                    boolean singleResult = communication.onLocalCostControl(meter);
                    if (singleResult) {
                        meterResultMap.put("result", EnumYesNo.YES.getValue());
                    } else {
                        meterResultMap.put("result", EnumYesNo.NO.getValue());
                        meterResultMap.put("reason", "设置失败");
                    }
                } catch (Exception e) {
                    meterResultMap.put("result", EnumYesNo.NO.getValue());
                    meterResultMap.put("reason", "设置失败:" + e.getMessage());
                }
                content.add(meterResultMap);
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "zdNewOnLocalCostControlMultiple");
        }
    }

    /**
     * 中电保电设置
     * 传入参数：楼层，按照楼层一层一层，方便观察是否成功
     * folderId：楼层id
     * status：on 开启，off 关闭
     */
    @RequestMapping("zdNewPaulEleSetting")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult zdNewPaulEleSetting(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String folderId = (String) dto.get("folderId");
            String status = (String) dto.get("status");
            if (status == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            if (folderId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            //查询楼层内的电表
            List<FnRoom> roomList = fnRoomService.queryListByFloorId(folderId);
            for (FnRoom fnRoom : roomList) {
                FnRoomMeter query = new FnRoomMeter();
                query.setRoomId(fnRoom.getId());
                query.setEnergyTypeId("Dian");
                List<FnRoomMeter> fnRoomMeters = fnRoomMeterService.query(query);
                if (fnRoomMeters != null && !fnRoomMeters.isEmpty()) {
                    for (FnRoomMeter fnRoomMeter : fnRoomMeters) {
                        Map<String, Object> meterResultMap = new HashMap<>();
                        FnMeter meter = fnMeterService.queryMeterById(fnRoomMeter.getMeterId());
                        meterResultMap.put("roomCode", fnRoomMeter.getRoomCode());
                        meterResultMap.put("meterId", meter.getId());
                        try {
                            String protocolId = meter.getProtocolId();
                            if (!protocolId.equals("DI_C_02_Y_Q_001")) {
                                continue;
                            }
                            DI_C_02_Y_Q_001_Impl communication = (DI_C_02_Y_Q_001_Impl) SystemConstant.context.getBean(protocolId);
                            boolean singleResult = false;
                            if (status.equals("on")) {
                                singleResult = communication.settingPaulEle(meter, EnumPaulEleStatus.PaulEle, new HashMap<String, Object>());
                            } else if (status.equals("off")) {
                                singleResult = communication.settingPaulEle(meter, EnumPaulEleStatus.UnPaulEle, new HashMap<String, Object>());
                            }
                            if (singleResult) {
                                meterResultMap.put("result", EnumYesNo.YES.getValue());
                            } else {
                                meterResultMap.put("result", EnumYesNo.NO.getValue());
                                meterResultMap.put("reason", "设置失败");
                            }
                        } catch (Exception e) {
                            meterResultMap.put("result", EnumYesNo.NO.getValue());
                            meterResultMap.put("reason", "设置失败:" + e.getMessage());
                        } catch (MeterSetException e) {
                            meterResultMap.put("status", EnumYesNo.NO.getValue());
                            meterResultMap.put("reason", "不支持此功能");
                        }
                        content.add(meterResultMap);
                    }
                }
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "zdNewPaulEleSetting");
        }
    }

    /**
     * 中电批量更新价格 只下发到电表中，不修改租户的电价方案，如果需要修改电价方案请在页面上批量修改，无仪表操作记录
     * 传入参数：楼层，按照楼层一层一层，方便观察是否成功
     * folderId：楼层id
     * priceId：价格方案ID
     */
    @RequestMapping("zdNewUpdatePrice")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult zdNewUpdatePrice(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String folderId = (String) dto.get("folderId");
            String priceId = (String) dto.get("priceId");
            if (priceId == null || folderId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            //查询电价模板
            FnPriceTemplate fnPriceTemplate = fNPriceTemplateService.query(priceId);
            if (fnPriceTemplate == null) {
                throw new Exception(priceId + "未查询到电价");
            }
            //查询楼层内的电表
            List<FnRoom> roomList = fnRoomService.queryListByFloorId(folderId);
            for (FnRoom fnRoom : roomList) {
                FnRoomMeter query = new FnRoomMeter();
                query.setRoomId(fnRoom.getId());
                query.setEnergyTypeId("Dian");
                List<FnRoomMeter> fnRoomMeters = fnRoomMeterService.query(query);
                if (fnRoomMeters != null && !fnRoomMeters.isEmpty()) {
                    for (FnRoomMeter fnRoomMeter : fnRoomMeters) {
                        Map<String, Object> meterResultMap = new HashMap<>();
                        FnMeter meter = fnMeterService.queryMeterById(fnRoomMeter.getMeterId());
                        meterResultMap.put("roomCode", fnRoomMeter.getRoomCode());
                        meterResultMap.put("meterId", meter.getId());
                        try {
                            String protocolId = meter.getProtocolId();
                            if (!protocolId.equals("DI_C_02_Y_Q_001")) {
                                continue;
                            }
                            DI_C_02_Y_Q_001_Impl communication = (DI_C_02_Y_Q_001_Impl) SystemConstant.context.getBean(protocolId);
                            double price = DoubleFormatUtil.Instance().getDoubleData(fnPriceTemplate.getContent());
                            boolean singleResult = communication.settingPrice(meter, EnumPriceDetail.L, price, new HashMap<>());
                            if (singleResult) {
                                meterResultMap.put("result", EnumYesNo.YES.getValue());
                            } else {
                                meterResultMap.put("result", EnumYesNo.NO.getValue());
                                meterResultMap.put("reason", "设置失败");
                            }
                        } catch (Exception e) {
                            meterResultMap.put("result", EnumYesNo.NO.getValue());
                            meterResultMap.put("reason", "设置失败:" + e.getMessage());
                        } catch (MeterSetException e) {
                            meterResultMap.put("status", EnumYesNo.NO.getValue());
                            meterResultMap.put("reason", "不支持此功能");
                        }
                        content.add(meterResultMap);
                    }
                }
            }

            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "zdNewUpdatePrice");
        }
    }

}
