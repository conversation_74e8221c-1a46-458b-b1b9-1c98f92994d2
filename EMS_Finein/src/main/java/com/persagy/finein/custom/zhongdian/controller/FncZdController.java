package com.persagy.finein.custom.zhongdian.controller;

import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.finein.communication.exception.MeterSetException;
import com.persagy.finein.communication.interfaces.ICommunication;
import com.persagy.finein.communication.interfaces.impl.DI_C_02_Y_Q_001.DI_C_02_Y_Q_001_Impl;
import com.persagy.finein.enumeration.EnumPaulEleStatus;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNMeterService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 中电定制接口
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FncZdController extends BaseController {

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;


    /**
     * 中电自定义保电
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCZDPaulEleService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult zdpaulele(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String)dto.get("energyTypeId");
            Integer isMultiple = (Integer)dto.get("isMultiple");
            List<String> meterList = (List<String>)dto.get("meterList");

            if(energyTypeId == null || isMultiple == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            if(isMultiple.intValue() == 1){
                List<FnMeter> fnMeterList = fnMeterService.queryMeterByEnergyTypeId(energyTypeId);
                if(fnMeterList != null && fnMeterList.size() > 0){
                    for(FnMeter meter : fnMeterList){
                        if(meter.getPayType().intValue() == EnumPayType.POSTPAY.getValue().intValue()){
                            continue;
                        }
                        Map<String,Object> meterMap = new HashMap<>();
                        try {
                            String protocolId = meter.getProtocolId();
                            if(!protocolId.equals("DI_C_02_Y_Q_001")){
                                continue;
                            }
                            meterMap.put("meterId", meter.getId());
                            DI_C_02_Y_Q_001_Impl communication = (DI_C_02_Y_Q_001_Impl) SystemConstant.context.getBean(protocolId);
                            boolean singleResult = communication.settingPaulEle(meter, EnumPaulEleStatus.PaulEle,new HashMap<String,Object>());
                            if(singleResult){
                                meterMap.put("result", EnumYesNo.YES.getValue());
                            }else{
                                meterMap.put("result", EnumYesNo.NO.getValue());
                                meterMap.put("reason", "设置失败");
                            }
                        } catch (MeterSetException e) {
                            meterMap.put("status", EnumYesNo.NO.getValue());
                            meterMap.put("reason", "不支持此功能");
                        } catch (Exception e) {
                            meterMap.put("status", EnumYesNo.NO.getValue());
                            meterMap.put("reason", "设置失败:"+e.getMessage());
                        }

                        content.add(meterMap);
                    }
                }
            }else{
                for(String meterId : meterList){
                    FnMeter meter = fnMeterService.queryMeterById(meterId);
                    if(meter.getPayType().intValue() == EnumPayType.POSTPAY.getValue().intValue()){
                        continue;
                    }
                    Map<String,Object> meterMap = new HashMap<>();
                    try {
                        String protocolId = meter.getProtocolId();
                        if(!protocolId.equals("DI_C_02_Y_Q_001")){
                            continue;
                        }
                        meterMap.put("meterId", meter.getId());
                        DI_C_02_Y_Q_001_Impl communication = (DI_C_02_Y_Q_001_Impl) SystemConstant.context.getBean(protocolId);
                        boolean singleResult = communication.settingPaulEle(meter, EnumPaulEleStatus.PaulEle,new HashMap<String,Object>());
                        if(singleResult){
                            meterMap.put("result", EnumYesNo.YES.getValue());
                        }else{
                            meterMap.put("result", EnumYesNo.NO.getValue());
                            meterMap.put("reason", "设置失败");
                        }
                    } catch (MeterSetException e) {
                        meterMap.put("status", EnumYesNo.NO.getValue());
                        meterMap.put("reason", "不支持此功能");
                    } catch (Exception e) {
                        meterMap.put("status", EnumYesNo.NO.getValue());
                        meterMap.put("reason", "设置失败:"+e.getMessage());
                    }
                    content.add(meterMap);
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCZDPaulEleService");
        }
    }

    /**
     * 中电自定义解除保电
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCZDUnPaulEleService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult zdUnPaulEle(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String)dto.get("energyTypeId");
            Integer isMultiple = (Integer)dto.get("isMultiple");
            List<String> meterList = (List<String>)dto.get("meterList");

            if(energyTypeId == null || isMultiple == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            if(isMultiple.intValue() == 1){
                List<FnMeter> fnMeterList = fnMeterService.queryMeterByEnergyTypeId(energyTypeId);
                if(fnMeterList != null && fnMeterList.size() > 0){
                    for(FnMeter meter : fnMeterList){
                        if(meter.getPayType().intValue() == EnumPayType.POSTPAY.getValue().intValue()){
                            continue;
                        }
                        Map<String,Object> meterMap = new HashMap<>();
                        try {
                            String protocolId = meter.getProtocolId();
                            if(!protocolId.equals("DI_C_02_Y_Q_001")){
                                continue;
                            }
                            meterMap.put("meterId", meter.getId());
                            DI_C_02_Y_Q_001_Impl communication = (DI_C_02_Y_Q_001_Impl)SystemConstant.context.getBean(protocolId);
                            boolean singleResult = communication.settingPaulEle(meter, EnumPaulEleStatus.UnPaulEle,new HashMap<String,Object>());
                            if(singleResult){
                                meterMap.put("result", EnumYesNo.YES.getValue());
                            }else{
                                meterMap.put("result", EnumYesNo.NO.getValue());
                                meterMap.put("reason", "设置失败");
                            }
                        } catch (MeterSetException e) {
                            meterMap.put("status", EnumYesNo.NO.getValue());
                            meterMap.put("reason", "不支持此功能");
                        } catch (Exception e) {
                            meterMap.put("status", EnumYesNo.NO.getValue());
                            meterMap.put("reason", "设置失败:"+e.getMessage());
                        }

                        content.add(meterMap);
                    }
                }
            }else{
                for(String meterId : meterList){
                    FnMeter meter = fnMeterService.queryMeterById(meterId);
                    if(meter.getPayType().intValue() == EnumPayType.POSTPAY.getValue().intValue()){
                        continue;
                    }
                    Map<String,Object> meterMap = new HashMap<>();
                    try {
                        String protocolId = meter.getProtocolId();
                        if(!protocolId.equals("DI_C_02_Y_Q_001")){
                            continue;
                        }
                        meterMap.put("meterId", meter.getId());
                        ICommunication communication = (ICommunication) SystemConstant.context.getBean(protocolId);
                        boolean singleResult = communication.settingPaulEle(meter, EnumPaulEleStatus.UnPaulEle,new HashMap<String,Object>());
                        if(singleResult){
                            meterMap.put("result", EnumYesNo.YES.getValue());
                        }else{
                            meterMap.put("result", EnumYesNo.NO.getValue());
                            meterMap.put("reason", "设置失败");
                        }
                    } catch (MeterSetException e) {
                        meterMap.put("status", EnumYesNo.NO.getValue());
                        meterMap.put("reason", "不支持此功能");
                    } catch (Exception e) {
                        meterMap.put("status", EnumYesNo.NO.getValue());
                        meterMap.put("reason", "设置失败:"+e.getMessage());
                    }
                    content.add(meterMap);
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCZDPaulEleService");
        }
    }

    /**
     * 中电自定义加密投退
     * @param jsonString
     * @return
     */
    @RequestMapping("FNCZDUnSecretMultipleService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult zdUnSecretMultiple(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String energyTypeId = (String)dto.get("energyTypeId");

            if(energyTypeId == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            List<FnMeter> meterList = fnMeterService.queryMeterByEnergyTypeId(energyTypeId);
            if(meterList != null && meterList.size() > 0){
                for(FnMeter meter : meterList){
                    if(meter.getPayType().intValue() == EnumPayType.POSTPAY.getValue().intValue()){
                        continue;
                    }
                    Map<String,Object> meterMap = new HashMap<>();
                    try {
                        String protocolId = meter.getProtocolId();
                        if(!protocolId.equals("DI_C_02_Y_Q_001")){
                            continue;
                        }
                        meterMap.put("meterId", meter.getId());
                        DI_C_02_Y_Q_001_Impl communication = (DI_C_02_Y_Q_001_Impl) SystemConstant.context.getBean(protocolId);
                        boolean singleResult = communication.unSecret(meter);
                        if(singleResult){
                            meterMap.put("result", EnumYesNo.YES.getValue());
                        }else{
                            meterMap.put("result", EnumYesNo.NO.getValue());
                            meterMap.put("reason", "设置失败");
                        }
                    } catch (Exception e) {
                        meterMap.put("status", EnumYesNo.NO.getValue());
                        meterMap.put("reason", "设置失败:"+e.getMessage());
                    }

                    content.add(meterMap);
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNCZDUnSecretMultipleService");
        }
    }
}
