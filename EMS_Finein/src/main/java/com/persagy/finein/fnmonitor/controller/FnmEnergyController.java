package com.persagy.finein.fnmonitor.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.*;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.ems.pojo.meterdata.MeterData;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.fnmanage.alarm.util.FNAlarmUtil;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 物业监控 -- 数据分析
 *
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FnmEnergyController extends BaseController {

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    @Resource(name = "FNTenantDataService")
    private FNTenantDataService fnTenantDataService;

    @Resource(name = "FNMeterDataService")
    private FNMeterDataService fnMeterDataService;

    @Resource(name = "FNAlarmUtil")
    private FNAlarmUtil fnAlarmUtil;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNProjectService")
    private FNProjectService fnProjectService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNTenantBackPayDataService")
    private FNTenantBackPayDataService fnTenantBackPayDataService;

    @Resource(name = "FNTenantMeterDataService")
    private FNTenantMeterDataService fnTenantMeterDataService;

    @Resource(name = "FNTenantMeterPowerStatService")
    private FNTenantMeterPowerStatService fnTenantMeterPowerStatService;

    @Resource(name = "FNTenantPrePayParamService")
    private FNTenantPrePayParamService fnTenantPrePayParamService;

    @Resource(name = "FNTenantPrePayMeterParamService")
    private FNTenantPrePayMeterParamService fnTenantPrePayMeterParamService;

    @Resource(name = "FNTenantMeterPowerService")
    private FNTenantMeterPowerService fnTenantMeterPowerService;

    @Resource(name = "FNEnergyTypeService")
    private FNEnergyTypeService fnEnergyTypeService;


    /**
     * 物业监控 -- 数据分析 -- 能耗char图
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMEnergyDataListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult energyDataList(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer density = (Integer) dto.get("density");// 1时 2日 4月 5年
            Integer doType = (Integer) dto.get("doType");// 0查看详情 1数据对比
            String startDate = (String) dto.get("startDate");
            String endDate = (String) dto.get("endDate");
            String tenantId = (String) dto.get("tenantId");
            String meterId = (String) dto.get("meterId");
            String typeId = (String) dto.get("typeId");

            if (density == null || doType == null || tenantId == null || startDate == null || endDate == null
                    || typeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Date timeFrom = standard.parse(startDate);
            Date timeTo = standard.parse(endDate);

            String[] paramArray = typeId.split("_");

            Map<String, Object> contentObj = new HashMap<String, Object>();
            FnMeter fnMeter = null;
            if (meterId != null && !"".equals(meterId.trim())) {
                fnMeter = fnMeterService.queryMeterById(meterId);
            }
            FnTenant fnTenant = fnTenantService.queryOne(tenantId);

            switch (paramArray[1]) {
                case "DianGongLv":
                    contentObj.put("unit", UnitUtil.getDataUnit(paramArray[0], paramArray[1], null));
                    if (doType == 0) {
                        this.processDianGongLv(contentObj, fnTenant, fnMeter, timeFrom, timeTo, doType);
                    } else {
                        this.processDianGongLvAnalysis(contentObj, fnTenant, fnMeter, timeFrom, timeTo);
                    }
                    break;
                case "FeiYong":
                    contentObj.put("unit", UnitUtil.getDataUnit(paramArray[0], paramArray[1], null));
                    this.processTenantData(contentObj, fnTenant, paramArray[0], timeFrom, timeTo, density,
                            EnumEnergyMoney.Money);
                    break;
                case "HaoDianLiang":
                    contentObj.put("unit", UnitUtil.getDataUnit(paramArray[0], paramArray[1], null));
                    this.processTenantData(contentObj, fnTenant, paramArray[0], timeFrom, timeTo, density,
                            EnumEnergyMoney.Energy);
                    break;
                case "HaoShuiLiang":
                    contentObj.put("unit", UnitUtil.getDataUnit(paramArray[0], paramArray[1], null));
                    this.processTenantData(contentObj, fnTenant, paramArray[0], timeFrom, timeTo, density,
                            EnumEnergyMoney.Energy);
                    break;
                case "HaoReShuiLiang":
                    contentObj.put("unit", UnitUtil.getDataUnit(paramArray[0], paramArray[1], null));
                    this.processTenantData(contentObj, fnTenant, paramArray[0], timeFrom, timeTo, density,
                            EnumEnergyMoney.Energy);
                    break;
                case "HaoRanQiLiang":
                    contentObj.put("unit", UnitUtil.getDataUnit(paramArray[0], paramArray[1], null));
                    this.processTenantData(contentObj, fnTenant, paramArray[0], timeFrom, timeTo, density,
                            EnumEnergyMoney.Energy);
                    break;
                case "ShengYuLiang":
                    this.processShengYuLiang(contentObj, fnTenant, paramArray[0], paramArray[1], fnMeter, timeFrom, timeTo,
                            density);
                    break;
                default:
                    break;
            }
            content.add(contentObj);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMEnergyDataListService");
        }
    }

    private void processDianGongLv(Map<String, Object> contentObj, FnTenant fnTenant, FnMeter fnMeter, Date timeFrom,
                                   Date timeTo, Integer doType) throws Exception {
        Double limitValue = fnAlarmUtil.queryAlarmLimit(fnTenant.getBuildingId(), fnTenant.getId(),
                FineinConstant.AlarmType.FUHELVGUOGAO);
        Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Minute_15);
        Double kkValue = null;
        try {
            JSONObject extendObj = (JSONObject) JSONValue.parse(fnMeter.getExtend());
            kkValue = DoubleFormatUtil.Instance().getDoubleData(extendObj.get("kkValue"));
        } catch (Exception e) {
            e.printStackTrace();
        }

        {// 总
            int cumulantFunctionId = FunctionTypeUtil.getCumulantFunctionId(fnMeter.getEnergyTypeId());
            Map<String, Double> tempTimeMap = new LinkedHashMap<>(timeMap);

            JSONObject extendObj = null;
            try {
                extendObj = (JSONObject) JSONValue.parse(fnMeter.getExtend());
            } catch (Exception e) {
                e.printStackTrace();
            }
            Integer isSanXiang = 0;
            try {
                isSanXiang = Integer.parseInt(extendObj.get("isSanXiang").toString());
            } catch (Exception e) {
                e.printStackTrace();
            }
            List<FnTenantMeterData> meterDataList = fnTenantMeterDataService.queryListGteLt(fnTenant.getBuildingId(),
                    fnTenant.getId(), fnMeter.getId(), cumulantFunctionId, fnMeter.getEnergyTypeId(), EnumTimeType.T0,
                    timeFrom, timeTo, EnumEnergyMoney.Energy);
            if (meterDataList != null) {
                for (FnTenantMeterData fnTenantMeterData : meterDataList) {
                    if (fnTenant.getActiveTime() != null
                            && fnTenantMeterData.getTimeFrom().getTime() < fnTenant.getActiveTime().getTime()) {
                        continue;
                    }
                    if (fnTenantMeterData.getData() != null) {
                        tempTimeMap.put(standard.format(fnTenantMeterData.getTimeFrom()),
                                fnTenantMeterData.getData() * 4);
                    }
                }
            }
            List<Object> dataList = new ArrayList<>();
            for (Map.Entry<String, Double> entry : tempTimeMap.entrySet()) {
                Map<String, Object> map = new HashMap<>();
                map.put("x", entry.getKey());
                map.put("y", entry.getValue() == null ? 0.0 : entry.getValue());//
                Double fuZaiLv = null;
                if (entry.getValue() != null && kkValue != null && kkValue.doubleValue() != 0.0) {
                    double baseLoad = kkValue.doubleValue() * 220 / 1000.0;
                    if (isSanXiang != null && isSanXiang.intValue() == 1) {
                        baseLoad *= 3;
                    }
                    fuZaiLv = entry.getValue().doubleValue() / baseLoad * 100;
                    map.put("z", fuZaiLv);
                } else {
                    map.put("z", 0.0);
                }
                if (fuZaiLv != null && limitValue != null && fuZaiLv > limitValue) {
                    map.put("a", true);
                } else {
                    map.put("a", false);
                }
                dataList.add(map);
            }
            contentObj.put("dataList", dataList);
            contentObj.put("aList", new ArrayList<Object>());
            contentObj.put("bList", new ArrayList<Object>());
            contentObj.put("cList", new ArrayList<Object>());
        }
    }

    private void processDianGongLvAnalysis(Map<String, Object> contentObj, FnTenant fnTenant, FnMeter fnMeter,
                                           Date timeFrom, Date timeTo) throws Exception {
        Map<String, Double> timeMap = null;
        timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Day_1);
        if (timeMap.size() > 7) {// 大于等于七天
            List<FnTenantMeterPowerStat> dataList = fnTenantMeterPowerStatService.queryListGteLte(
                    fnTenant.getBuildingId(), fnTenant.getId(), EnumBodyType.TENANT, FineinConstant.EnergyType.Dian,
                    timeFrom, timeTo);
            if (dataList != null && dataList.size() > 0) {
                for (FnTenantMeterPowerStat fnTenantData : dataList) {
                    if (fnTenant.getActiveTime() != null
                            && fnTenantData.getTimeFrom().getTime() < fnTenant.getActiveTime().getTime()) {
                        continue;
                    }
                    if (fnTenantData.getData() != null) {
                        timeMap.put(standard.format(DateUtils.truncate(fnTenantData.getTimeFrom(), Calendar.DATE)),
                                fnTenantData.getData());
                    }
                }
            }
        } else if (timeMap.size() <= 1 && timeMap.size() > 0) {
            timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Minute_15);
            List<FnTenantData> dataList = fnTenantDataService.queryListGteLte(fnTenant.getBuildingId(),
                    fnTenant.getId(), EnumTimeType.T0, FineinConstant.EnergyType.Dian, timeFrom, timeTo,
                    EnumEnergyMoney.Energy);
            if (dataList != null && dataList.size() > 0) {
                for (FnTenantData fnTenantData : dataList) {
                    if (fnTenant.getActiveTime() != null
                            && fnTenantData.getTimeFrom().getTime() < fnTenant.getActiveTime().getTime()) {
                        continue;
                    }
                    if (fnTenantData.getData() != null) {
                        timeMap.put(standard.format(fnTenantData.getTimeFrom()), fnTenantData.getData() * 4);
                    }
                }
            }
        } else {
            timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Hour_1);
            List<FnTenantData> dataList = fnTenantDataService.queryListGteLte(fnTenant.getBuildingId(),
                    fnTenant.getId(), EnumTimeType.T1, FineinConstant.EnergyType.Dian, timeFrom, timeTo,
                    EnumEnergyMoney.Energy);
            if (dataList != null && dataList.size() > 0) {
                for (FnTenantData fnTenantData : dataList) {
                    if (fnTenant.getActiveTime() != null
                            && fnTenantData.getTimeFrom().getTime() < fnTenant.getActiveTime().getTime()) {
                        continue;
                    }
                    if (fnTenantData.getData() != null) {
                        timeMap.put(standard.format(fnTenantData.getTimeFrom()), fnTenantData.getData());
                    }
                }
            }
        }
        {// 总
            List<Object> dataList = new ArrayList<>();
            for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
                Map<String, Object> map = new HashMap<>();
                map.put("x", entry.getKey());
                map.put("y", entry.getValue() == null ? 0.0 : entry.getValue());//
                dataList.add(map);
            }
            contentObj.put("dataList", dataList);
            // contentObj.put("aList",dataList);
        }

    }

    private void processTenantData(Map<String, Object> contentObj, FnTenant fnTenant, String energyTypeId,
                                   Date timeFrom, Date timeTo, Integer density, EnumEnergyMoney energyMoney) throws Exception {
        int timeTypeValue = density;
        if (density > 2) {
            timeTypeValue = 2;
        }
        Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.valueOf(density));

        List<FnTenantData> list = fnTenantDataService.queryListGteLt(fnTenant.getBuildingId(), fnTenant.getId(),
                EnumTimeType.valueOf(timeTypeValue), energyTypeId, timeFrom, timeTo, energyMoney);

        if (list != null) {
            for (FnTenantData data : list) {
                if (fnTenant.getActiveTime() != null
                        && data.getTimeFrom().getTime() < fnTenant.getActiveTime().getTime()) {
                    continue;
                }
                if (data.getData() != null) {
                    String key = null;
                    if (EnumTimeType.T4.getValue() == density.intValue()) {
                        key = standard.format(data.getTimeFrom()).substring(0, 7) + "-01 00:00:00";
                    } else if (EnumTimeType.T5.getValue() == density.intValue()) {
                        key = standard.format(data.getTimeFrom()).substring(0, 4) + "-01-01 00:00:00";
                    } else {
                        key = standard.format(data.getTimeFrom());
                    }
                    if (timeMap.get(key) == null) {
                        timeMap.put(key, data.getData());
                    } else {
                        timeMap.put(key, timeMap.get(key) + data.getData());
                    }
                }
            }
        }

        List<Object> dataList = new ArrayList<>();
        for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
            Map<String, Object> map = new HashMap<>();
            map.put("x", entry.getKey());
            map.put("y", entry.getValue());
            dataList.add(map);
        }
        contentObj.put("dataList", dataList);
    }

    private void  processShengYuLiang(Map<String, Object> contentObj, FnTenant fnTenant, String energyTypeId,
                                     String paramType, FnMeter fnMeter, Date timeFrom, Date timeTo, Integer density) throws Exception {
        Map<String, FnTenantPayType> payTypeMap = fnTenantPayTypeService.queryPayTypeMap(fnTenant.getId());
        FnTenantPayType tenantPayType = payTypeMap.get(energyTypeId);

        EnumPrepayChargeType prepayChargeType = EnumPrepayChargeType.valueOf(tenantPayType.getPrepayChargeType());
        contentObj.put("unit", UnitUtil.getDataUnit(energyTypeId, paramType, prepayChargeType));

        int timeTypeValue = density;
        if (density > 2) {
            timeTypeValue = 2;
        }
        Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo,
                EnumTimeType.valueOf(timeTypeValue));

        Project project = fnProjectService.queryProject();

        if (tenantPayType.getPayType().intValue() == EnumPayType.PREPAY.getValue()) {
            if (tenantPayType.getPrePayType().intValue() == EnumPrePayType.OFFLINE_METERPAY.getValue()
                    || tenantPayType.getPrePayType().intValue() == EnumPrePayType.ONLINE_METERPAY.getValue()) {// 表
                Double ct = 1.0;
                JSONObject extendObj = null;
                try {
                    extendObj = (JSONObject) JSONValue.parse(fnMeter.getExtend());
                    Double tempCt = DoubleFormatUtil.Instance().getDoubleData(extendObj.get("syjIsCt"));
                    if (tempCt != null && tempCt.intValue() == 1) {
                        ct = fnMeter.getRadio();
                    }
                } catch (Exception e) {
                }

                List<MeterData> list = null;
                if (tenantPayType.getPrepayChargeType() != null
                        && tenantPayType.getPrepayChargeType().intValue() == EnumPrepayChargeType.Qian.getValue()) {
                    int funcId = FunctionTypeUtil.getShengYuJinEFunctionId(energyTypeId);
                    list = fnMeterDataService.queryMeterDataGteLt(project.getId(), fnMeter.getId(), funcId,
                            timeTypeValue, timeFrom, timeTo);
                } else if (tenantPayType.getPrepayChargeType() != null
                        && tenantPayType.getPrepayChargeType().intValue() == EnumPrepayChargeType.Liang.getValue()) {
                    int funcId = FunctionTypeUtil.getShengYuLiangFunctionId(energyTypeId);
                    list = fnMeterDataService.queryMeterDataGteLt(project.getId(), fnMeter.getId(), funcId,
                            timeTypeValue, timeFrom, timeTo);
                }

                if (list != null) {
                    for (MeterData data : list) {
                        if (fnTenant.getActiveTime() != null
                                && data.getReceivetime().getTime() < fnTenant.getActiveTime().getTime()) {
                            continue;
                        }
                        if (data.getData() != null) {
                            String key = standard.format(data.getReceivetime());
                            timeMap.put(key, data.getData() * ct);
                        }
                    }
                }
            } else {// 租户
                if (tenantPayType.getPrepayChargeType() != null) {
                    List<FnTenantBackPayData> list = fnTenantBackPayDataService.queryDataGteLt(fnTenant.getBuildingId(),
                            fnTenant.getId(), energyTypeId, EnumTimeType.valueOf(timeTypeValue),
                            EnumPrepayChargeType.valueOf(tenantPayType.getPrepayChargeType()), timeFrom, timeTo);
                    if (list != null) {
                        for (FnTenantBackPayData data : list) {
                            if (fnTenant.getActiveTime() != null
                                    && data.getTimeFrom().getTime() < fnTenant.getActiveTime().getTime()) {
                                continue;
                            }
                            if (data.getData() != null) {
                                String key = standard.format(data.getTimeFrom());
                                timeMap.put(key, data.getData());
                            }
                        }
                    }
                }
            }
        }

        List<Object> dataList = new ArrayList<>();
        for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
            Map<String, Object> map = new HashMap<>();
            map.put("x", entry.getKey());
            map.put("y", entry.getValue());
            dataList.add(map);
        }
        contentObj.put("dataList", dataList);
    }

    /**
     * 物业监控 -- 数据分析 -- 租户能耗char图
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMEnergyInfoService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult energyInfo(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer density = (Integer) dto.get("density");// 1时 2日 4月
            String startDate = (String) dto.get("startDate");
            String endDate = (String) dto.get("endDate");
            String tenantId = (String) dto.get("tenantId");
            String meterId = (String) dto.get("meterId");
            String typeId = (String) dto.get("typeId");// Dian_ShengYuLiang

            if (density == null || endDate == null || startDate == null || typeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Date timeFrom = standard.parse(startDate);
            Date timeTo = standard.parse(endDate);

            String[] paramArray = typeId.split("_");

            Map<String, Object> contentObj = new HashMap<String, Object>();
            content.add(contentObj);

            FnMeter fnMeter = null;
            if (meterId != null && !"".equals(meterId.trim())) {
                fnMeter = fnMeterService.queryMeterById(meterId);
            }
            FnTenant fnTenant = fnTenantService.queryOne(tenantId);

            switch (paramArray[1]) {
                case "DianGongLv":
                    this.processDianGongLv(contentObj, fnTenant, fnMeter, timeFrom, timeTo, paramArray[0], paramArray[1],
                            density);
                    break;
                case "FeiYong":
                    this.procesFeiYong(contentObj, fnTenant, timeFrom, timeTo, paramArray[0], paramArray[1], density);
                    break;
                case "HaoDianLiang":
                    this.processHao_Liang(contentObj, fnTenant, timeFrom, timeTo, paramArray[0], paramArray[1], density);
                    break;
                case "HaoShuiLiang":
                    this.processHao_Liang(contentObj, fnTenant, timeFrom, timeTo, paramArray[0], paramArray[1], density);
                    break;
                case "HaoReShuiLiang":
                    this.processHao_Liang(contentObj, fnTenant, timeFrom, timeTo, paramArray[0], paramArray[1], density);
                    break;
                case "HaoRanQiLiang":
                    this.processHao_Liang(contentObj, fnTenant, timeFrom, timeTo, paramArray[0], paramArray[1], density);
                    break;
                case "ShengYuLiang":
                    this.processShengYuLiang(contentObj, fnTenant, fnMeter, timeFrom, timeTo, paramArray[0], paramArray[1],
                            density);
                    break;
                default:
                    throw new Exception("FNMEnergyInfoService typeId 无法识别:" + typeId);
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMEnergyInfoService");
        }
    }

    // 耗*量
    private void processHao_Liang(Map<String, Object> contentObj, FnTenant fnTenant, Date timeFrom, Date timeTo,
                                  String energyTypeId, String paramType, Integer density) throws Exception {
        Double data = fnTenantDataService.queryDataGteLt(fnTenant.getBuildingId(), fnTenant.getId(), EnumTimeType.T2,
                energyTypeId, timeFrom, timeTo, EnumEnergyMoney.Energy);
        Date yesterday = DateUtils.truncate(new Date(), Calendar.DATE);
        List<FnTenantData> historyDataList = fnTenantDataService.queryListGteLt(fnTenant.getBuildingId(),
                fnTenant.getId(), EnumTimeType.T2, energyTypeId, fnTenant.getActiveTime(), yesterday,
                EnumEnergyMoney.Energy);

        Map<String, Double> densityDataMap = new HashMap<>();
        if (historyDataList != null) {
            for (FnTenantData tenantData : historyDataList) {
                if (tenantData.getData() != null) {
                    if (density.intValue() == EnumTimeType.T1.getValue().intValue()) {// 日平均值
                        densityDataMap.put(standard.format(tenantData.getTimeFrom()), tenantData.getData());
                    } else if (density.intValue() == EnumTimeType.T2.getValue().intValue()) {// 月平均值
                        Date tenantDataTime = DateUtils.truncate(tenantData.getTimeFrom(), Calendar.MONTH);
                        Date to = DateUtils.truncate(DateUtils.addMonths(new Date(), -1), Calendar.MONTH);
                        Date from = DateUtils.truncate(DateUtils.addMonths(fnTenant.getActiveTime(), 1),
                                Calendar.MONTH);
                        if (tenantDataTime.getTime() >= from.getTime() && tenantDataTime.getTime() <= to.getTime()) {
                            String key = standard.format(tenantData.getTimeFrom()).substring(0, 7) + "-01 00:00:00";
                            if (!densityDataMap.containsKey(key)) {
                                densityDataMap.put(key, tenantData.getData());
                            } else {
                                densityDataMap.put(key, tenantData.getData() + densityDataMap.get(key));
                            }
                        }
                    } else if (density.intValue() == EnumTimeType.T4.getValue().intValue()) {// 年平均值
                        Date tenantDataTime = DateUtils.truncate(tenantData.getTimeFrom(), Calendar.YEAR);
                        Date to = DateUtils.truncate(DateUtils.addYears(new Date(), -1), Calendar.YEAR);
                        Date from = DateUtils.truncate(DateUtils.addYears(fnTenant.getActiveTime(), 1), Calendar.YEAR);
                        if (tenantDataTime.getTime() >= from.getTime() && tenantDataTime.getTime() <= to.getTime()) {
                            String key = standard.format(tenantData.getTimeFrom()).substring(0, 4) + "-01-01 00:00:00";
                            if (!densityDataMap.containsKey(key)) {
                                densityDataMap.put(key, tenantData.getData());
                            } else {
                                densityDataMap.put(key, tenantData.getData() + densityDataMap.get(key));
                            }
                        }
                    }
                }
            }
        }

        Double historySum = null;
        int historyCount = 0;
        for (Map.Entry<String, Double> entry : densityDataMap.entrySet()) {
            if (entry.getValue() != null) {
                historySum = historySum == null ? entry.getValue() : entry.getValue() + historySum;
                historyCount++;
            }
        }
        Double area = fnTenant.getArea();
        contentObj.put("data", data);
        contentObj.put("dataUnit", UnitUtil.getDataUnit(energyTypeId, paramType, EnumPrepayChargeType.Liang));
        if (historySum != null && historyCount > 0) {
            contentObj.put("historyAvg", historySum.doubleValue() / historyCount);
        } else {
            contentObj.put("historyAvg", null);
        }

        List<Date> tongQiDateList = getTongQiDateList(timeFrom, timeTo);
        Double tongQiData = fnTenantDataService.queryDataGteLt(fnTenant.getBuildingId(), fnTenant.getId(),
                EnumTimeType.T2, energyTypeId, tongQiDateList.get(0), tongQiDateList.get(1), EnumEnergyMoney.Energy);
        if (data != null && tongQiData != null && tongQiData.doubleValue() != 0.0) {
            contentObj.put("tongbiRatio", (data - tongQiData) / tongQiData.doubleValue() * 100);
        } else {
            contentObj.put("tongbiRatio", null);
        }

        if (data != null && area != null && area.doubleValue() != 0.0) {
            contentObj.put("areaAvg", data.doubleValue() / area.doubleValue());
        } else {
            contentObj.put("areaAvg", null);
        }
        contentObj.put("areaAvgUnit", UnitUtil.getAreaDataUnit(energyTypeId, paramType));
    }

    // 剩余量（预付费租户-仪表）
    private void processShengYuLiang(Map<String, Object> contentObj, FnTenant fnTenant, FnMeter fnMeter, Date timeFrom,
                                     Date timeTo, String energyTypeId, String paramType, Integer density) throws Exception {
        Integer type = 0;
        Integer isAlarm = 0;
        // 查询租户付费类型
        Map<String, FnTenantPayType> payTypeMap = fnTenantPayTypeService.queryPayTypeMap(fnTenant.getId());
        FnTenantPayType tenantPayType = payTypeMap.get(energyTypeId);
        if (tenantPayType == null || tenantPayType.getPayType().intValue() == EnumPayType.POSTPAY.getValue()) {
            contentObj.put("avgData", null);
            contentObj.put("maxData", null);
            contentObj.put("dataUnit", null);
            contentObj.put("remainData", null);
            contentObj.put("remainDays", null);
            contentObj.put("type", type);
            contentObj.put("isAlarm", isAlarm);
            return;
        }
        Date yesterday = DateUtils.truncate(new Date(), Calendar.DATE);
        Map<String, Double> densityDataMap = new HashMap<>();
        Double remainData = null;
        String remainDays = null;
        EnumPrepayChargeType prepayChargeType = EnumPrepayChargeType.valueOf(tenantPayType.getPrepayChargeType());
        contentObj.put("dataUnit", UnitUtil.getDataUnit(energyTypeId, paramType, prepayChargeType));

        EnumEnergyMoney energyMoney = null;
        if (prepayChargeType == null) {
            energyMoney = EnumEnergyMoney.Energy;
        } else if (prepayChargeType == EnumPrepayChargeType.Liang) {
            energyMoney = EnumEnergyMoney.Energy;
        } else if (prepayChargeType == EnumPrepayChargeType.Qian) {
            energyMoney = EnumEnergyMoney.Money;
            type = 1;
        } else {
            energyMoney = EnumEnergyMoney.Energy;
        }

        if (tenantPayType.getPrePayType().intValue() == EnumPrePayType.ONLINE_TENANTPAY.getValue()) {// 租户
            List<FnTenantData> historyDataList = fnTenantDataService.queryListGteLt(fnTenant.getBuildingId(),
                    fnTenant.getId(), EnumTimeType.T2, energyTypeId, fnTenant.getActiveTime(), yesterday, energyMoney);

            if (historyDataList != null) {
                for (FnTenantData tenantData : historyDataList) {
                    if (tenantData.getData() != null) {
                        if (density.intValue() == EnumTimeType.T1.getValue().intValue()) {// 日平均值
                            densityDataMap.put(standard.format(tenantData.getTimeFrom()), tenantData.getData());
                        } else if (density.intValue() == EnumTimeType.T2.getValue().intValue()) {// 月平均值
                            Date tenantDataTime = DateUtils.truncate(tenantData.getTimeFrom(), Calendar.MONTH);
                            Date to = DateUtils.truncate(DateUtils.addMonths(new Date(), -1), Calendar.MONTH);
                            Date from = DateUtils.truncate(DateUtils.addMonths(fnTenant.getActiveTime(), 1),
                                    Calendar.MONTH);
                            if (tenantDataTime.getTime() >= from.getTime()
                                    && tenantDataTime.getTime() <= to.getTime()) {
                                String key = standard.format(tenantData.getTimeFrom()).substring(0, 7) + "-01 00:00:00";
                                if (!densityDataMap.containsKey(key)) {
                                    densityDataMap.put(key, tenantData.getData());
                                } else {
                                    densityDataMap.put(key, tenantData.getData() + densityDataMap.get(key));
                                }
                            }
                        } else if (density.intValue() == EnumTimeType.T4.getValue().intValue()) {// 年平均值
                            Date tenantDataTime = DateUtils.truncate(tenantData.getTimeFrom(), Calendar.YEAR);
                            Date to = DateUtils.truncate(DateUtils.addYears(new Date(), -1), Calendar.YEAR);
                            Date from = DateUtils.truncate(DateUtils.addYears(fnTenant.getActiveTime(), 1),
                                    Calendar.YEAR);
                            if (tenantDataTime.getTime() >= from.getTime()
                                    && tenantDataTime.getTime() <= to.getTime()) {
                                String key = standard.format(tenantData.getTimeFrom()).substring(0, 4)
                                        + "-01-01 00:00:00";
                                if (!densityDataMap.containsKey(key)) {
                                    densityDataMap.put(key, tenantData.getData());
                                } else {
                                    densityDataMap.put(key, tenantData.getData() + densityDataMap.get(key));
                                }
                            }
                        }
                    }
                }
            }

            FnTenantPrePayParam prePayParam = fnTenantPrePayParamService.queryTenantPreParam(fnTenant.getId(),
                    energyTypeId);
            if (prePayParam != null) {
                remainData = prePayParam.getRemainData();
                remainDays = prePayParam.getRemainDays();
                isAlarm = prePayParam.getIsAlarm() == null ? 0 : prePayParam.getIsAlarm();
            }
        } else {
            if (fnMeter == null) {
                contentObj.put("avgData", null);
                contentObj.put("maxData", null);
                contentObj.put("dataUnit", null);
                contentObj.put("remainData", null);
                contentObj.put("remainDays", null);
                contentObj.put("type", type);
                contentObj.put("isAlarm", isAlarm);
                return;
            }

            int functionId = FunctionTypeUtil.getCumulantFunctionId(energyTypeId);
            List<FnTenantMeterData> historyDataList = fnTenantMeterDataService.queryListGteLt(fnTenant.getBuildingId(),
                    fnTenant.getId(), fnMeter.getId(), functionId, energyTypeId, EnumTimeType.T2,
                    fnTenant.getActiveTime(), yesterday, energyMoney);

            if (historyDataList != null) {
                for (FnTenantMeterData tenantData : historyDataList) {
                    if (tenantData.getData() != null) {
                        if (density.intValue() == EnumTimeType.T1.getValue().intValue()) {// 日平均值
                            densityDataMap.put(standard.format(tenantData.getTimeFrom()), tenantData.getData());
                        } else if (density.intValue() == EnumTimeType.T2.getValue().intValue()) {// 月平均值
                            Date tenantDataTime = DateUtils.truncate(tenantData.getTimeFrom(), Calendar.MONTH);
                            Date to = DateUtils.truncate(DateUtils.addMonths(new Date(), -1), Calendar.MONTH);
                            Date from = DateUtils.truncate(DateUtils.addMonths(fnTenant.getActiveTime(), 1),
                                    Calendar.MONTH);
                            if (tenantDataTime.getTime() >= from.getTime()
                                    && tenantDataTime.getTime() <= to.getTime()) {
                                String key = standard.format(tenantData.getTimeFrom()).substring(0, 7) + "-01 00:00:00";
                                if (!densityDataMap.containsKey(key)) {
                                    densityDataMap.put(key, tenantData.getData());
                                } else {
                                    densityDataMap.put(key, tenantData.getData() + densityDataMap.get(key));
                                }
                            }
                        } else if (density.intValue() == EnumTimeType.T4.getValue().intValue()) {// 年平均值
                            Date tenantDataTime = DateUtils.truncate(tenantData.getTimeFrom(), Calendar.YEAR);
                            Date to = DateUtils.truncate(DateUtils.addYears(new Date(), -1), Calendar.YEAR);
                            Date from = DateUtils.truncate(DateUtils.addYears(fnTenant.getActiveTime(), 1),
                                    Calendar.YEAR);
                            if (tenantDataTime.getTime() >= from.getTime()
                                    && tenantDataTime.getTime() <= to.getTime()) {
                                String key = standard.format(tenantData.getTimeFrom()).substring(0, 4)
                                        + "-01-01 00:00:00";
                                if (!densityDataMap.containsKey(key)) {
                                    densityDataMap.put(key, tenantData.getData());
                                } else {
                                    densityDataMap.put(key, tenantData.getData() + densityDataMap.get(key));
                                }
                            }
                        }
                    }
                }
            }
            FnTenantPrePayMeterParam prePayParam = fnTenantPrePayMeterParamService.queryMeterPreParam(fnTenant.getId(),
                    fnMeter.getId(), energyTypeId);

            if (prePayParam != null) {
                remainData = prePayParam.getRemainData();
                remainDays = prePayParam.getRemainDays();
                isAlarm = prePayParam.getIsAlarm() == null ? 0 : prePayParam.getIsAlarm();
            }
        }

        Double historySum = null;
        int historyCount = 0;
        Double maxData = null;
        for (Map.Entry<String, Double> entry : densityDataMap.entrySet()) {
            if (entry.getValue() != null) {
                historySum = historySum == null ? entry.getValue() : entry.getValue() + historySum;
                historyCount++;
                if (maxData == null || entry.getValue() > maxData) {
                    maxData = entry.getValue();
                }
            }
        }
        if (historySum != null && historyCount > 0) {
            contentObj.put("avgData", historySum.doubleValue() / historyCount);
        } else {
            contentObj.put("avgData", null);
        }
        contentObj.put("maxData", maxData);
        contentObj.put("remainData", remainData);
        contentObj.put("remainDays", remainDays);
        contentObj.put("type", type);
        contentObj.put("isAlarm", isAlarm);
    }

    // 费用
    private void procesFeiYong(Map<String, Object> contentObj, FnTenant fnTenant, Date timeFrom, Date timeTo,
                               String energyTypeId, String paramType, Integer density) throws Exception {
        Double data = fnTenantDataService.queryDataGteLt(fnTenant.getBuildingId(), fnTenant.getId(), EnumTimeType.T2,
                energyTypeId, timeFrom, timeTo, EnumEnergyMoney.Money);
        Date yesterday = DateUtils.truncate(new Date(), Calendar.DATE);
        List<FnTenantData> historyDataList = fnTenantDataService.queryListGteLt(fnTenant.getBuildingId(),
                fnTenant.getId(), EnumTimeType.T2, energyTypeId, fnTenant.getActiveTime(), yesterday,
                EnumEnergyMoney.Money);

        Map<String, Double> densityDataMap = new HashMap<>();
        if (historyDataList != null) {
            for (FnTenantData tenantData : historyDataList) {
                if (tenantData.getData() != null) {
                    if (density.intValue() == EnumTimeType.T1.getValue().intValue()) {// 日平均值
                        densityDataMap.put(standard.format(tenantData.getTimeFrom()), tenantData.getData());
                    } else if (density.intValue() == EnumTimeType.T2.getValue().intValue()) {// 月平均值
                        Date tenantDataTime = DateUtils.truncate(tenantData.getTimeFrom(), Calendar.MONTH);
                        Date to = DateUtils.truncate(DateUtils.addMonths(new Date(), -1), Calendar.MONTH);
                        Date from = DateUtils.truncate(DateUtils.addMonths(fnTenant.getActiveTime(), 1),
                                Calendar.MONTH);
                        if (tenantDataTime.getTime() >= from.getTime() && tenantDataTime.getTime() <= to.getTime()) {
                            String key = standard.format(tenantData.getTimeFrom()).substring(0, 7) + "-01 00:00:00";
                            if (!densityDataMap.containsKey(key)) {
                                densityDataMap.put(key, tenantData.getData());
                            } else {
                                densityDataMap.put(key, tenantData.getData() + densityDataMap.get(key));
                            }
                        }
                    } else if (density.intValue() == EnumTimeType.T4.getValue().intValue()) {// 年平均值
                        Date tenantDataTime = DateUtils.truncate(tenantData.getTimeFrom(), Calendar.YEAR);
                        Date to = DateUtils.truncate(DateUtils.addYears(new Date(), -1), Calendar.YEAR);
                        Date from = DateUtils.truncate(DateUtils.addYears(fnTenant.getActiveTime(), 1), Calendar.YEAR);
                        if (tenantDataTime.getTime() >= from.getTime() && tenantDataTime.getTime() <= to.getTime()) {
                            String key = standard.format(tenantData.getTimeFrom()).substring(0, 4) + "-01-01 00:00:00";
                            if (!densityDataMap.containsKey(key)) {
                                densityDataMap.put(key, tenantData.getData());
                            } else {
                                densityDataMap.put(key, tenantData.getData() + densityDataMap.get(key));
                            }
                        }
                    }
                }
            }
        }

        Double historySum = null;
        int historyCount = 0;
        for (Map.Entry<String, Double> entry : densityDataMap.entrySet()) {
            if (entry.getValue() != null) {
                historySum = historySum == null ? entry.getValue() : entry.getValue() + historySum;
                historyCount++;
            }
        }
        Double area = fnTenant.getArea();
        contentObj.put("data", data);
        if (historySum != null && historyCount > 0) {
            contentObj.put("historyAvg", historySum.doubleValue() / historyCount);
        } else {
            contentObj.put("historyAvg", null);
        }

        List<Date> tongQiDateList = getTongQiDateList(timeFrom, timeTo);
        Double tongQiData = fnTenantDataService.queryDataGteLt(fnTenant.getBuildingId(), fnTenant.getId(),
                EnumTimeType.T2, energyTypeId, tongQiDateList.get(0), tongQiDateList.get(1), EnumEnergyMoney.Money);
        if (data != null && tongQiData != null && tongQiData.doubleValue() != 0.0) {
            contentObj.put("tongbiRatio", (data - tongQiData) / tongQiData.doubleValue() * 100);
        } else {
            contentObj.put("tongbiRatio", null);
        }

        if (data != null && area != null && area.doubleValue() != 0.0) {
            contentObj.put("areaAvg", data.doubleValue() / area.doubleValue());
        } else {
            contentObj.put("areaAvg", null);
        }
    }

    // 电功率
    private void processDianGongLv(Map<String, Object> contentObj, FnTenant fnTenant, FnMeter fnMeter, Date timeFrom,
                                   Date timeTo, String energyTypeId, String paramType, Integer density) throws Exception {
        Double emptyValue = 0.0;
        JSONObject extendObj = null;
        try {
            extendObj = (JSONObject) JSONValue.parse(fnMeter.getExtend());
            emptyValue = DoubleFormatUtil.Instance().getDoubleData(extendObj.get("kkValue"));
        } catch (Exception e) {
        }
        int cumulantFunctionId = FunctionTypeUtil.getCumulantFunctionId(energyTypeId);
        Integer isSanXiang = 0;
        try {
            isSanXiang = Integer.parseInt(extendObj.get("isSanXiang").toString());
        } catch (Exception e) {
        }
        Double originalData = null;

        Date now = new Date();
        Date from = new Date(now.getTime() - FineinConstant.Time.Hour_1);
        FnTenantMeterData queryLastGteLt = fnTenantMeterDataService.queryLastGteLt(fnTenant.getBuildingId(),
                fnTenant.getId(), fnMeter.getId(), cumulantFunctionId, energyTypeId, EnumTimeType.T0, from, timeTo,
                EnumEnergyMoney.Energy);
        if (queryLastGteLt != null) {
            originalData = queryLastGteLt.getData() * 4;
        }
        Double historyMaxLoad = null;
        String historyMaxLoadTime = null;
        //查询最大值
        FnTenantMeterPower query = new FnTenantMeterPower();
        query.setBuildingId(fnTenant.getBuildingId());
        query.setTenantId(fnTenant.getId());
        query.setBodyType(EnumBodyType.METER.getValue());
        query.setBodyCode(fnMeter.getId());
        query.setDataType(EnumStatType.Max.getValue());
        List<FnTenantMeterPower> maxPowerList = fnTenantMeterPowerService.query(query);
        if (maxPowerList != null && maxPowerList.size() > 0) {
            historyMaxLoad = maxPowerList.get(0).getData();
            historyMaxLoadTime = standard.format(maxPowerList.get(0).getTimeFrom());
        }
        contentObj.put("historyMaxLoad", historyMaxLoad);
        contentObj.put("historyMaxLoadTime", historyMaxLoadTime);
        contentObj.put("emptyValue", emptyValue);
        contentObj.put("load", null);
        if (originalData != null) {
            Double gongLvData = null;
            gongLvData = originalData.doubleValue();
            if (gongLvData != null) {
                double baseLoad = emptyValue.doubleValue() * 220 / 1000.0;
                if (isSanXiang != null && isSanXiang.intValue() == 1) {
                    baseLoad *= 3;
                }
                double fuzailv = gongLvData.doubleValue() / baseLoad * 100;
                contentObj.put("load", fuzailv);
            }
        }
    }

    private List<Date> getTongQiDateList(Date timeFrom, Date timeTo) {
        Date newTimeFrom = DateUtils.addYears(timeFrom, -1);
        Date newTimeTo = DateUtils.addYears(timeTo, -1);
        List<Date> dateList = new ArrayList<>();
        dateList.add(newTimeFrom);
        dateList.add(newTimeTo);
        return dateList;
    }


    /**
     * 物业监控 -- 数据分析 -- 能耗类型列表
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMEnergyTypeListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult energyTypeList(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            if (buildingId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull("buildingId"));
            }

            List<FnEnergyType> energyTypeList = fnEnergyTypeService.queryList(EnumValidStatus.VALID, EMSOrder.Asc);

            List<Object> list = null;
            if (energyTypeList != null && energyTypeList.size() > 0) {
                list = new ArrayList<Object>();

                Map<String, Boolean> alarmMap = fnEnergyTypeService.queryEnergyTypeIsAlarm(buildingId, energyTypeList);

                for (FnEnergyType entity : energyTypeList) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("isAlarm", alarmMap.get(entity.getId()) == null ? false : alarmMap.get(entity.getId()));
                    map.put("typeId", entity.getId());
                    map.put("typeName", entity.getName());
                    list.add(map);
                }
            }

            if (list != null) {
                content.addAll(list);
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMEnergyTypeListService");
        }
    }

    /**
     * 物业监控 -- 数据分析 -- 能耗类型参数树
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMEnergyTypeTreeService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult energyTypeTree(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");
            Integer doType = (Integer) dto.get("doType");// 0：详情 1：对比

            if (doType == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            Map<String, FnTenantPayType> payTypeMap = new HashMap<>();
            if (doType.intValue() == 0) {// 租户编码不能为空
                if (tenantId == null) {
                    throw new Exception(ExceptionUtil.ParamIsNull());
                }
                payTypeMap = fnTenantPayTypeService.queryPayTypeMap(tenantId);
            }

            List<Object> contentList = new ArrayList<Object>();

            for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                Map<String, Object> contentObj = new HashMap<String, Object>();
                contentObj.put("id", energyType.getId());
                contentObj.put("name", energyType.getName());
                List<Object> subTypeList = new ArrayList<Object>();
                contentObj.put("subTypeList", subTypeList);
                if (doType.intValue() == 0) {
                    for (FnMonitorParam param : ConstantDBBaseData.MonitorParamList) {
                        if (energyType.getId().equals(param.getEnergyTypeId())
                                && payTypeMap.containsKey(param.getEnergyTypeId())) {
                            Map<String, Object> paramMap = new HashMap<String, Object>();
                            paramMap.put("id", param.getId());
                            paramMap.put("name", param.getName());
                            if (param.getName().contains("对比差")) {
                                continue;
                            }
                            if (payTypeMap.get(param.getEnergyTypeId()).getPayType().intValue() == EnumPayType.POSTPAY
                                    .getValue().intValue()) {
                                if (param.getName().contains("剩余量")) {
                                    continue;
                                }
                            }
                            if (payTypeMap.get(param.getEnergyTypeId()).getPayType().intValue() == EnumPayType.PREPAY
                                    .getValue().intValue()) {
                                if (param.getName().contains("费用")) {
                                    continue;
                                }
                            }
                            subTypeList.add(paramMap);
                        }
                    }
                } else {
                    for (FnMonitorParam param : ConstantDBBaseData.MonitorParamList) {
                        if (energyType.getId().equals(param.getEnergyTypeId())) {
                            Map<String, Object> paramMap = new HashMap<String, Object>();
                            paramMap.put("id", param.getId());
                            paramMap.put("name", param.getName());
                            if ("剩余量".equals(param.getName()) || "对比差".equals(param.getName())) {
                                continue;
                            }
                            subTypeList.add(paramMap);
                        }
                    }
                }
                if (subTypeList.size() > 0) {
                    contentList.add(contentObj);
                }
            }
            content.addAll(contentList);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMEnergyTypeTreeService");
        }
    }
}
