package com.persagy.finein.fnmonitor.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.ems.pojo.finein.FnEnergyType;
import com.persagy.ems.pojo.finein.FnMonitorParam;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FNMRoomEnergyTypeTreeController extends BaseController {

    @RequestMapping("FNMRoomEnergyTypeTreeService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult RoomEnergyTypeTree(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            List<Object> contentList = new ArrayList<Object>();

            for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                Map<String, Object> contentObj = new HashMap<String, Object>();
                contentObj.put("id", energyType.getId());
                contentObj.put("name", energyType.getName());
                List<Object> subTypeList = new ArrayList<Object>();
                contentObj.put("subTypeList", subTypeList);
                for (FnMonitorParam param : ConstantDBBaseData.MonitorParamList) {
                    if (energyType.getId().equals(param.getEnergyTypeId())) {
                        Map<String, Object> paramMap = new HashMap<String, Object>();
                        paramMap.put("id", param.getId());
                        paramMap.put("name", param.getName());
                        if ("剩余量".equals(param.getName()) || "费用".contains(param.getName())) {
                            continue;
                        }
                        subTypeList.add(paramMap);
                    }
                }

                if (subTypeList.size() > 0) {
                    contentList.add(contentObj);
                }
            }
            content.addAll(contentList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNMRoomEnergyTypeTreeService");
        }
    }

}
