package com.persagy.finein.fnmonitor.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnAlarm;
import com.persagy.ems.pojo.finein.FnAlarmType;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumAlarmPositionType;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNAlarmService;
import com.persagy.finein.service.FNAlarmTypeService;
import com.persagy.finein.service.FNTenantService;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 物业监控 -- 报警管理
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FnmAlarmController extends BaseController {

    @Resource(name = "FNAlarmService")
    private FNAlarmService fnAlarmService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNAlarmTypeService")
    private FNAlarmTypeService fnAlarmTypeService;


    /**
     * 物业监控 -- 报警管理 -- 报警列表
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMAlarmListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult alarmList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String parentId = (String) dto.get("id");
            String tenantId = (String) dto.get("tenantId");
            Integer status = (Integer) dto.get("status");
            String timeFrom = (String) dto.get("timeFrom");
            String timeTo = (String) dto.get("timeTo");
            Integer pageIndex = (Integer) dto.get("pageIndex");
            Integer pageSize = (Integer) dto.get("pageSize");

            if (buildingId == null || timeFrom == null || timeTo == null || pageIndex == null || pageSize == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Map<String, FnAlarmType> alarmTypeMap = new HashMap<String, FnAlarmType>();
            for (FnAlarmType fnAlarmType : ConstantDBBaseData.AlarmTypeList) {
                alarmTypeMap.put(fnAlarmType.getId(), fnAlarmType);
            }
            // 查询所有报警
            FnAlarm query = new FnAlarm();
            query.setBuildingId(buildingId);
            query.setStatus(status);
            query.setTenantId(tenantId);
            query.setParentAlarmTypeId(parentId);
            query.setSpecialOperation("createTime", SpecialOperator.$gte, standard.parse(timeFrom));
            query.setSpecialOperation("createTime", SpecialOperator.$lt, standard.parse(timeTo));
            query.setSort("createTime", EMSOrder.Desc);
            int count = fnAlarmService.count(query);

            Map<String, Object> contentObj = new HashMap<String, Object>();
            List<Object> list = new ArrayList<Object>();
            contentObj.put("count", count);
            contentObj.put("list", list);

            query.setSkip(Long.valueOf(pageIndex) * pageSize);
            query.setLimit(Long.valueOf(pageSize));
            List<FnAlarm> alarmList = fnAlarmService.query(query);
            if (alarmList != null && alarmList.size() > 0) {
                Map<String, FnTenant> tenantMap = fnTenantService.queryMap();
                for (FnAlarm fnAlarm : alarmList) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("alarmReason", alarmTypeMap.get(fnAlarm.getAlarmTypeId()).getName());
                    map.put("alarmId", fnAlarm.getId());
                    map.put("alarmParentType", alarmTypeMap.get(fnAlarm.getParentAlarmTypeId()).getName());
                    if (fnAlarm.getAlarmPositionType() == EnumAlarmPositionType.Meter.getValue().intValue()) {
                        FnTenant fnTenant = tenantMap.get(fnAlarm.getTenantId());
                        if (fnTenant != null) {
                            map.put("alarmPositionName", fnTenant.getName() + "(" + fnAlarm.getAlarmPositionId() + ")");
                        }
                        if ("ZHBJ_04".equals(fnAlarm.getParentAlarmTypeId())) {// 仪表数据中断报警
                            JSONObject extendObj = null;
                            try {
                                extendObj = (JSONObject) JSONValue.parse(fnAlarm.getExtend());
                            } catch (Exception e) {
                            }
                            map.put("interruptTime", extendObj == null || extendObj.get("interruptTime") == null ? null
                                    : extendObj.get("interruptTime") + "");
                        }
                    } else {
                        map.put("alarmPositionName", fnAlarm.getAlarmPositionName());
                    }
                    map.put("isLimit", alarmTypeMap.get(fnAlarm.getAlarmTypeId()).getIsLimit());
                    if (alarmTypeMap.get(fnAlarm.getAlarmTypeId()).getIsLimit() == EnumYesNo.YES.getValue().intValue()) {
                        map.put("limitValue", fnAlarm.getLimitValue());
                        map.put("currentValue", DoubleFormatUtil.Instance().doubleFormat(fnAlarm.getCurrentValue(), 2L));
                        map.put("unit", fnAlarm.getUnit());
                    }
                    map.put("alarmTime", fnAlarm.getAlarmTime());
                    map.put("status", fnAlarm.getStatus());
                    map.put("finishTime", fnAlarm.getFinishTime());
                    list.add(map);
                }
            }

            content.add(contentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNMAlarmListService");
        }
    }

    /**
     * 物业监控 -- 报警管理 -- 所有报警类型
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMAlarmParentLimitListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult alarmParentLimitList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            FnAlarmType query = new FnAlarmType();
            query.setParentId("");
            List<FnAlarmType> list = fnAlarmTypeService.query(query);
            Map<String,Object> map = new HashMap<String,Object>();
            map.put("id", "1");
            map.put("name", "全部");
            map.put("list", list);
            content.add(map);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNMAlarmParentLimitListService");
        }
    }
}
