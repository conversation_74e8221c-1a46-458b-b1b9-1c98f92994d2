package com.persagy.finein.fnmonitor.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOEnergyTypeMeter;
import com.persagy.ems.dto.DTORoom;
import com.persagy.ems.dto.DTOTenant;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 物业监控
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FnmFloorController extends BaseController {

    @Resource(name = "FNFloorService")
    private FNFloorService fnFloorService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantDataService")
    private FNTenantDataService fnTenantDataService;

    @Resource(name = "FNTenantPrePayParamService")
    private FNTenantPrePayParamService fnTenantPrePayParamService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNRoomService")
    private FNRoomService fnRoomService;

    @Resource(name = "FNMeterService")
    private FNMeterService fnMeterService;

    /**
     *  物业监控 -- 总览查看 -- 所有租户
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMFloorAlarmService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult floorAlarm(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");

            if (buildingId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull("buildingId"));
            }

            List<Object> contentObj = new ArrayList<Object>();
            List<Object> floorList = new ArrayList<Object>();
            Map<String, Object> contentMap = new HashMap<String, Object>();

            contentMap.put("floorList", floorList);

            int cell = 0;

            List<FnFloor> floorEntityList = fnFloorService.queryList(buildingId, EMSOrder.Desc);
            if (floorEntityList != null && floorEntityList.size() > 0) {

                List<DTOTenant> tenantList = fnTenantService.queryListByStatus(buildingId, EnumTenantStatus.ACTIVATED, EnumValidStatus.VALID);
                Map<String, List<DTOTenant>> floorTenantMap = new HashMap<String, List<DTOTenant>>();
                if (tenantList != null) {
                    for (DTOTenant dtoTenant : tenantList) {
                        if (dtoTenant.getFloorList() != null) {
                            for (String floorId : dtoTenant.getFloorList()) {
                                if (!floorTenantMap.containsKey(floorId)) {
                                    floorTenantMap.put(floorId, new ArrayList<DTOTenant>());
                                }
                                floorTenantMap.get(floorId).add(dtoTenant);
                            }
                        }
                    }
                }

                for (Map.Entry<String, List<DTOTenant>> entry : floorTenantMap.entrySet()) {
                    if (entry.getValue().size() > cell) {
                        cell = entry.getValue().size();
                    }
                }


                for (int i = 0; i < floorEntityList.size(); i++) {
                    List<DTOTenant> tenantDtoList = floorTenantMap.get(floorEntityList.get(i).getId());
                    if (tenantDtoList != null) {
                        Map<String, Object> floorMap = new HashMap<String, Object>();
                        List<Object> tenantObjList = new ArrayList<Object>();
                        floorMap.put("floorId", floorEntityList.get(i).getId());
                        floorMap.put("floorName", floorEntityList.get(i).getName());
                        floorMap.put("tenantList", tenantObjList);
                        for (DTOTenant dtoTenant : tenantDtoList) {
                            Map<String, Object> tenantMap = new HashMap<String, Object>();
                            tenantMap.put("tenantId", dtoTenant.getTenant().getId());
                            tenantMap.put("roomCodes", dtoTenant.getTenant().getRoomCodes());
                            tenantMap.put("activeTime", dtoTenant.getTenant().getActiveTime());
                            tenantMap.put("tenantName", dtoTenant.getTenant().getName());
                            tenantObjList.add(tenantMap);
                        }
                        floorList.add(floorMap);
                    }
                }

                //处理跨层

                for (int i = 0; i < floorList.size(); i++) {
                    Map<String, Object> floorMap = (Map<String, Object>) floorList.get(i);
                    List<Object> tenantObjList = (List<Object>) floorMap.get("tenantList");
                    for (int j = 0; j < tenantObjList.size(); j++) {
                        Map<String, Object> tenantMap = (Map<String, Object>) tenantObjList.get(j);
                        String tenantId = (String) tenantMap.get("tenantId");
                        int crossFloor = 1;//初始为跨一层
                        for (int k = i + 1; k < floorList.size(); k++) {
                            Map<String, Object> floorInnerMap = (Map<String, Object>) floorList.get(k);
                            List<Object> tenantObjInnerList = (List<Object>) floorInnerMap.get("tenantList");
                            boolean isFind = false;
                            for (int l = 0; l < tenantObjInnerList.size(); l++) {
                                Map<String, Object> tenantInnerMap = (Map<String, Object>) tenantObjInnerList.get(l);
                                String tenantInnerId = (String) tenantInnerMap.get("tenantId");
                                if (tenantId.equals(tenantInnerId)) {
                                    isFind = true;
                                    break;
                                }
                            }
                            if (isFind) {
                                crossFloor++;
                            } else {
                                break;
                            }
                        }
                        tenantMap.put("crossFloor", crossFloor);
                    }
                }
            }

            contentMap.put("cell", cell);
            contentMap.put("row", floorList.size());

            contentObj.add(contentMap);
            content.addAll(contentObj);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMFloorAlarmService");
        }
    }

    /**
     *  物业监控 -- 巡检查看 -- 所有租户按楼层
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMFloorEnergyInfoService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult floorEnergyInfo(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String energyTypeId = (String) dto.get("energyTypeId");

            if (buildingId == null || energyTypeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            Map<String, FnEnergyType> energyTypeMap = new HashMap<String, FnEnergyType>();
            for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                energyTypeMap.put(energyType.getId(), energyType);
            }

            List<Object> contentList = new ArrayList<Object>();

            List<FnFloor> floorEntityList = fnFloorService.queryList(buildingId, EMSOrder.Desc);
            if (floorEntityList != null && floorEntityList.size() > 0) {
                List<DTOTenant> tenantEntityList = fnTenantService.queryListByStatus(buildingId, EnumTenantStatus.ACTIVATED, EnumValidStatus.VALID);

                if (tenantEntityList != null && tenantEntityList.size() > 0) {
                    Map<String, List<DTOTenant>> floorTenantMap = new HashMap<String, List<DTOTenant>>();
                    for (DTOTenant dtoTenant : tenantEntityList) {
                        if (dtoTenant.getFloorList() != null && dtoTenant.getFloorList().size() > 0) {
                            for (String floorId : dtoTenant.getFloorList()) {
                                if (!floorTenantMap.containsKey(floorId)) {
                                    floorTenantMap.put(floorId, new ArrayList<DTOTenant>());
                                }
                                floorTenantMap.get(floorId).add(dtoTenant);
                            }
                        }
                    }

                    Date timeFrom = DateUtils.truncate(new Date(), Calendar.DATE);
                    Map<String, Map<String, Double>> tenantDataMap = fnTenantDataService.queryTenantData(buildingId, energyTypeId, EnumTimeType.T2, timeFrom);
                    Map<String, FnTenantPrePayParam> prePayParamMap = fnTenantPrePayParamService.queryTenantPreParamByBuilding(buildingId, energyTypeId);

                    Map<String, EnumPayType> payTypeMap = fnTenantService.queryTenantPayType(buildingId, energyTypeId);

                    for (FnFloor floor : floorEntityList) {
                        boolean isAlarm = false;

                        Map<String, Object> floorMap = new HashMap<String, Object>();
                        floorMap.put("floorId", floor.getId());
                        floorMap.put("floorName", floor.getName());


                        List<Object> tenantList = new ArrayList<Object>();
                        floorMap.put("tenantList", tenantList);

                        {
                            List<DTOTenant> dtoTenantList = floorTenantMap.get(floor.getId());
                            if (dtoTenantList == null || dtoTenantList.size() == 0) {
                                continue;
                            }

                            for (DTOTenant dtoTenant : dtoTenantList) {

                                Map<String, Object> tenantMap = new HashMap<String, Object>();

                                tenantMap.put("tenantId", dtoTenant.getTenant().getId());
                                tenantMap.put("tenantName", dtoTenant.getTenant().getName());
                                tenantMap.put("activeTime", dtoTenant.getTenant().getActiveTime());
                                List<Object> roomList = new ArrayList<Object>();
                                tenantMap.put("roomList", roomList);

                                EnumPayType payType = payTypeMap.get(dtoTenant.getTenant().getId());
                                if (payType == null) {
                                    continue;
                                }

                                tenantMap.put("type", payType.getValue());

                                Map<String, Double> dataMap = tenantDataMap.get(dtoTenant.getTenant().getId());

                                tenantMap.put("cost", dataMap == null ? null : dataMap.get(EnumEnergyMoney.Money.getValue() + ""));
                                tenantMap.put("data", dataMap == null ? null : dataMap.get(EnumEnergyMoney.Energy.getValue() + ""));
                                tenantMap.put("energyTypeName", "耗" + energyTypeMap.get(energyTypeId).getName());
                                if (payType == EnumPayType.PREPAY) {
                                    FnTenantPrePayParam prePayParam = prePayParamMap.get(dtoTenant.getTenant().getId());
                                    if (prePayParam != null) {
                                        boolean tenantIsAlarm = prePayParam.getIsAlarm() == null || prePayParam.getIsAlarm().intValue() == 0 ? false : true;
                                        tenantMap.put("isAlarm", tenantIsAlarm);
                                        tenantMap.put("remainDays", prePayParam.getRemainDays());
                                        if (tenantIsAlarm) {
                                            isAlarm = true;
                                        }
                                    } else {
                                        tenantMap.put("isAlarm", false);
                                        tenantMap.put("remainDays", null);
                                    }
                                } else {
                                    tenantMap.put("isAlarm", false);
                                    tenantMap.put("remainDays", null);
                                }
                                tenantList.add(tenantMap);
                                if (dtoTenant.getRoomList() != null && dtoTenant.getRoomList().size() > 0) {
                                    for (String roomCode : dtoTenant.getRoomList()) {
                                        Map<String, Object> roomMap = new HashMap<String, Object>();
                                        roomMap.put("roomId", roomCode);
                                        roomList.add(roomMap);
                                    }
                                }
                            }
                        }
                        if (tenantList.size() > 0) {
                            floorMap.put("isAlarm", isAlarm);
                            contentList.add(floorMap);
                        }
                    }
                }
            }
            content.addAll(contentList);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMFloorEnergyInfoService");
        }
    }

    /**
     *  物业监控 -- 巡检查看 --  获取楼层租户列表
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMFloorRoomTreeService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult floorRoomTree(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String typeId = (String) dto.get("typeId");

            if (buildingId == null || typeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            String[] paramArray = typeId.split("_");

            List<Object> contentList = new ArrayList<Object>();
            Map<String, FnMeter> dbMeterMap = fnMeterService.queryMap();
            List<FnFloor> floorEntityList = fnFloorService.queryList(buildingId, EMSOrder.Desc);
            if (floorEntityList != null && floorEntityList.size() > 0) {
                Map<String, List<DTORoom>> roomEntityList = fnRoomService.queryRoomByFloor(buildingId);
                for (FnFloor floor : floorEntityList) {
                    Map<String, Object> floorMap = new HashMap<String, Object>();
                    floorMap.put("id", floor.getId());
                    floorMap.put("name", floor.getName());
                    floorMap.put("level", 0);
                    List<Object> roomList = new ArrayList<Object>();
                    floorMap.put("dataList", roomList);
                    List<DTORoom> DTORoomList = roomEntityList.get(floor.getId());
                    if (DTORoomList != null && DTORoomList.size() > 0) {
                        for (DTORoom dtoRoom : DTORoomList) {
                            Map<String, Object> roomMap = new HashMap<String, Object>();
                            roomMap.put("id", dtoRoom.getRoomId());
                            roomMap.put("name", dtoRoom.getRoomCode());
                            roomMap.put("level", 1);
                            List<Object> meterList = new ArrayList<Object>();
                            roomMap.put("dataList", meterList);
                            List<DTOEnergyTypeMeter> eneryList = dtoRoom.getEneryList();
                            if (eneryList != null && eneryList.size() > 0) {
                                for (DTOEnergyTypeMeter dtoEnergyTypeMeter : eneryList) {
                                    if (paramArray[0].equals(dtoEnergyTypeMeter.getEnergyTypeId())) {
                                        List<String> meterList2 = dtoEnergyTypeMeter.getMeterList();
                                        if (meterList2 != null && meterList2.size() > 0) {
                                            for (String meterId : meterList2) {
                                                FnMeter fnMeter = dbMeterMap.get(meterId);
                                                if (fnMeter == null) {
                                                    continue;
                                                }
                                                FnProtocol fnProtocol = ConstantDBBaseData.ProtocolMap
                                                        .get(fnMeter.getProtocolId());
                                                if ("DuiBiZhi".equals(paramArray[1])) {
                                                    if (fnProtocol.getPayType() != EnumPayType.PREPAY.getValue()
                                                            .intValue()) {
                                                        continue;
                                                    }
                                                }
                                                Map<String, Object> meterMap = new HashMap<>();
                                                meterMap.put("id", meterId);
                                                meterMap.put("name", meterId);
                                                meterMap.put("level", 2);
                                                meterMap.put("unit", UnitUtil.getCumulantUnit(paramArray[0]));
                                                switch (paramArray[1]) {
                                                    case "HaoDianLiang":
                                                    case "HaoShuiLiang":
                                                    case "HaoReShuiLiang":
                                                    case "HaoRanQiLiang":
                                                        meterMap.put("unit", UnitUtil.getCumulantUnit(paramArray[0]));
                                                        meterMap.put("type", EnumPrepayChargeType.Liang.getValue());
                                                        break;
                                                    case "DianGongLv":
                                                        meterMap.put("unit", "kW");
                                                        break;
                                                    case "DuiBiZhi":
                                                        if (fnProtocol.getBillingMode() == EnumPrepayChargeType.Liang.getValue()
                                                                .intValue()) {
                                                            meterMap.put("unit", "kWh");
                                                        } else {
                                                            meterMap.put("unit", "元");
                                                        }
                                                        break;
                                                    default:
                                                        break;
                                                }
                                                meterList.add(meterMap);
                                            }
                                            roomList.add(roomMap);
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (roomList.size() > 0) {
                        contentList.add(floorMap);
                    }
                }
            }
            content.addAll(contentList);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMFloorRoomTreeService");
        }
    }

    /**
     *  物业监控 -- 数据分析 -- 获取楼层租户列表
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMFloorTreeService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult floorTree(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String typeId = (String) dto.get("typeId");

            if (buildingId == null || typeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            String[] paramArray = typeId.split("_");

            List<Object> contentList = new ArrayList<Object>();

            List<FnFloor> floorEntityList = fnFloorService.queryList(buildingId, EMSOrder.Desc);
            if (floorEntityList != null && floorEntityList.size() > 0) {
                List<DTOTenant> tenantEntityList = fnTenantService.queryListByStatus(buildingId, EnumTenantStatus.ACTIVATED,
                        EnumValidStatus.VALID);
                Map<String, Map<String, FnTenantPayType>> tenantEnergyTypeMap = fnTenantPayTypeService
                        .queryByBuildingId(buildingId);

                for (FnFloor floor : floorEntityList) {
                    Map<String, Object> floorMap = new HashMap<String, Object>();
                    floorMap.put("id", floor.getId());
                    floorMap.put("name", floor.getName());
                    floorMap.put("level", 0);
                    List<Object> tenantList = new ArrayList<Object>();
                    floorMap.put("tenantList", tenantList);

                    if (tenantEntityList != null) {
                        for (DTOTenant tenant : tenantEntityList) {
                            if (tenant.getFloorList() != null && tenant.getFloorList().size() > 0) {
                                for (int i = 0; i < tenant.getFloorList().size(); i++) {
                                    if (tenant.getFloorList().get(i).equals(floor.getId()) && i == 0) {
                                        if (paramArray[0] != null) {
                                            String energyTypeIds = tenant.getTenant().getEnergyTypeIds();
                                            if (energyTypeIds == null || "".equals(energyTypeIds.trim())) {
                                                continue;
                                            }
                                            String[] energyTypeArray = energyTypeIds.split(",");
                                            boolean isFind = false;
                                            for (String energyTypeObj : energyTypeArray) {
                                                if (paramArray[0].equals(energyTypeObj)) {
                                                    isFind = true;
                                                    break;
                                                }
                                            }
                                            if (!isFind) {
                                                continue;
                                            }

                                            if (tenantEnergyTypeMap.containsKey(tenant.getTenant().getId())
                                                    && tenantEnergyTypeMap.get(tenant.getTenant().getId())
                                                    .get(paramArray[0]) != null) {
                                                FnTenantPayType payType = tenantEnergyTypeMap
                                                        .get(tenant.getTenant().getId()).get(paramArray[0]);
                                                Map<String, Object> tenantMap = new HashMap<String, Object>();
                                                tenantMap.put("id", tenant.getTenant().getId());
                                                tenantMap.put("name", tenant.getTenant().getName());
                                                tenantMap.put("level", 1);
                                                switch (paramArray[1]) {
                                                    case "HaoDianLiang":
                                                    case "HaoShuiLiang":
                                                    case "HaoReShuiLiang":
                                                    case "HaoRanQiLiang":
                                                        tenantMap.put("unit", UnitUtil.getCumulantUnit(paramArray[0]));
                                                        tenantMap.put("type", EnumPrepayChargeType.Liang.getValue());
                                                        break;
                                                    case "ShengYuLiang":
                                                        if (payType.getPayType().intValue() != EnumPayType.PREPAY.getValue()) {
                                                            continue;
                                                        }
                                                        if (payType.getPrepayChargeType() != null
                                                                && payType.getPrepayChargeType()
                                                                .intValue() != EnumPrepayChargeType.None.getValue()) {
                                                            tenantMap.put("unit",
                                                                    UnitUtil.getBillModeUnit(paramArray[0], EnumPrepayChargeType
                                                                            .valueOf(payType.getPrepayChargeType())));
                                                            tenantMap.put("type", EnumPrepayChargeType
                                                                    .valueOf(payType.getPrepayChargeType()).getValue());
                                                        } else {
                                                            tenantMap.put("unit", UnitUtil.getCumulantUnit(paramArray[0]));
                                                            tenantMap.put("type", EnumPrepayChargeType.Liang.getValue());
                                                        }
                                                        break;
                                                    case "FeiYong":
                                                        tenantMap.put("unit", "元");
                                                        tenantMap.put("type", EnumPrepayChargeType.Qian.getValue());
                                                        break;
                                                    case "DianGongLv":
                                                        tenantMap.put("unit", "kW");
                                                        break;
                                                    default:
                                                        break;
                                                }
                                                tenantList.add(tenantMap);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (tenantList.size() > 0) {
                        contentList.add(floorMap);
                    }
                }

            }
            content.addAll(contentList);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMFloorTreeService");
        }
    }
    /**
     * 物业监控 -- 数据分析 -- 获取楼层列表
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMFloorService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult floor(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String typeId = (String) dto.get("typeId");

            if (buildingId == null || typeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            String[] paramArray = typeId.split("_");

            List<Object> contentList = new ArrayList<>();

            List<FnFloor> floorEntityList = fnFloorService.queryList(buildingId, EMSOrder.Desc);
            if (floorEntityList != null && floorEntityList.size() > 0) {
                List<DTOTenant> tenantEntityList = fnTenantService.queryListByStatus(buildingId, EnumTenantStatus.ACTIVATED,
                        EnumValidStatus.VALID);
                Map<String, Map<String, FnTenantPayType>> tenantEnergyTypeMap = fnTenantPayTypeService
                        .queryByBuildingId(buildingId);
                for (FnFloor floor : floorEntityList) {
                    Map<String, Object> floorMap = new HashMap<>();
                    floorMap.put("id", floor.getId());
                    floorMap.put("name", floor.getName());
                    List<Object> tenantList = new ArrayList<>();
                    if (tenantEntityList != null) {
                        for (DTOTenant tenant : tenantEntityList) {
                            if (tenant.getFloorList() != null && tenant.getFloorList().size() > 0) {
                                for (int i = 0; i < tenant.getFloorList().size(); i++) {
                                    if (tenant.getFloorList().get(i).equals(floor.getId()) && i == 0) {
                                        if (paramArray[0] != null) {
                                            String energyTypeIds = tenant.getTenant().getEnergyTypeIds();
                                            if (energyTypeIds == null || "".equals(energyTypeIds.trim())) {
                                                continue;
                                            }
                                            String[] energyTypeArray = energyTypeIds.split(",");
                                            boolean isFind = false;
                                            for (String energyTypeObj : energyTypeArray) {
                                                if (paramArray[0].equals(energyTypeObj)) {
                                                    isFind = true;
                                                    break;
                                                }
                                            }
                                            if (!isFind) {
                                                continue;
                                            }
                                            if (tenantEnergyTypeMap.containsKey(tenant.getTenant().getId())
                                                    && tenantEnergyTypeMap.get(tenant.getTenant().getId())
                                                    .get(paramArray[0]) != null) {
                                                FnTenantPayType payType = tenantEnergyTypeMap
                                                        .get(tenant.getTenant().getId()).get(paramArray[0]);
                                                switch (paramArray[1]) {
                                                    case "HaoDianLiang":
                                                    case "HaoShuiLiang":
                                                    case "HaoReShuiLiang":
                                                    case "HaoRanQiLiang":
                                                        floorMap.put("unit", UnitUtil.getCumulantUnit(paramArray[0]));
                                                        floorMap.put("type", EnumPrepayChargeType.Liang.getValue());
                                                        break;
                                                    case "ShengYuLiang":
                                                        if (payType.getPayType().intValue() != EnumPayType.PREPAY.getValue()) {
                                                            continue;
                                                        }
                                                        if (payType.getPrepayChargeType() != null
                                                                && payType.getPrepayChargeType()
                                                                .intValue() != EnumPrepayChargeType.None.getValue()) {
                                                            floorMap.put("unit",
                                                                    UnitUtil.getBillModeUnit(paramArray[0], EnumPrepayChargeType
                                                                            .valueOf(payType.getPrepayChargeType())));
                                                            floorMap.put("type", EnumPrepayChargeType
                                                                    .valueOf(payType.getPrepayChargeType()).getValue());
                                                        } else {
                                                            floorMap.put("unit", UnitUtil.getCumulantUnit(paramArray[0]));
                                                            floorMap.put("type", EnumPrepayChargeType.Liang.getValue());
                                                        }
                                                        break;
                                                    case "FeiYong":
                                                        floorMap.put("unit", "元");
                                                        floorMap.put("type", EnumPrepayChargeType.Qian.getValue());
                                                        break;
                                                    case "DianGongLv":
                                                        floorMap.put("unit", "kW");
                                                        break;
                                                    default:
                                                        break;
                                                }
                                                tenantList.add(floorMap);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (tenantList.size() > 0) {
                        contentList.add(floorMap);
                    }
                }

            }
            content.addAll(contentList);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMFloorService");
        }
    }


}
