package com.persagy.finein.fnmonitor.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.dto.DTOTenant;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.TimeDataUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 物业监控 -- 数据分析 -- 业态
 *
 * @Author：ls
 * @Date：2020/8/26 15:53
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FNMFormatsController extends BaseController {
    @Resource(name = "FNTenantTypeService")
    private FNTenantTypeService FNTenantTypeService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService fnTenantPayTypeService;

    @Resource(name = "FNTenantDataService")
    private FNTenantDataService fnTenantDataService;

    @Resource(name = "FNTenantMeterPowerStatService")
    private FNTenantMeterPowerStatService fnTenantMeterPowerStatService;

    /**
     * 物业监控 -- 数据分析 -- 获取业态列表
     *
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMFormatsService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult Formats(@RequestParam(value = "jsonString") String jsonString) {

        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String typeId = (String) dto.get("typeId");

            if (buildingId == null || typeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            String[] paramArray = typeId.split("_");

            List<Object> contentList = new ArrayList<>();
            List<FnTenantType> list = FNTenantTypeService.queryList(EMSOrder.Asc);

            if (list != null && list.size() > 0) {
                List<DTOTenant> tenantEntityList = fnTenantService.queryListByStatus(buildingId, EnumTenantStatus.ACTIVATED,
                        EnumValidStatus.VALID);
                Map<String, Map<String, FnTenantPayType>> tenantEnergyTypeMap = fnTenantPayTypeService
                        .queryByBuildingId(buildingId);
                for (FnTenantType tenantType : list) {
                    Map<String, Object> tenantTypeMap = new HashMap<>();
                    tenantTypeMap.put("id", tenantType.getId());
                    tenantTypeMap.put("name", tenantType.getName());
                    List<Object> tenantList = new ArrayList<>();
                    if (tenantEntityList != null) {
                        for (DTOTenant tenant : tenantEntityList) {
                            if (tenant.getTenantTypeList() != null && tenant.getTenantTypeList().size() > 0) {
                                for (int i = 0; i < tenant.getTenantTypeList().size(); i++) {
                                    if (tenant.getTenantTypeList().get(i).equals(tenantType.getName()) && i == 0) {
                                        if (paramArray[0] != null) {
                                            String energyTypeIds = tenant.getTenant().getEnergyTypeIds();
                                            if (energyTypeIds == null || "".equals(energyTypeIds.trim())) {
                                                continue;
                                            }
                                            String[] energyTypeArray = energyTypeIds.split(",");
                                            boolean isFind = false;
                                            for (String energyTypeObj : energyTypeArray) {
                                                if (paramArray[0].equals(energyTypeObj)) {
                                                    isFind = true;
                                                    break;
                                                }
                                            }
                                            if (!isFind) {
                                                continue;
                                            }
                                            if (tenantEnergyTypeMap.containsKey(tenant.getTenant().getId())
                                                    && tenantEnergyTypeMap.get(tenant.getTenant().getId())
                                                    .get(paramArray[0]) != null) {
                                                FnTenantPayType payType = tenantEnergyTypeMap
                                                        .get(tenant.getTenant().getId()).get(paramArray[0]);
                                                switch (paramArray[1]) {
                                                    case "HaoDianLiang":
                                                    case "HaoShuiLiang":
                                                    case "HaoReShuiLiang":
                                                    case "HaoRanQiLiang":
                                                        tenantTypeMap.put("unit", UnitUtil.getCumulantUnit(paramArray[0]));
                                                        tenantTypeMap.put("type", EnumPrepayChargeType.Liang.getValue());
                                                        break;
                                                    case "ShengYuLiang":
                                                        if (payType.getPayType().intValue() != EnumPayType.PREPAY.getValue()) {
                                                            continue;
                                                        }
                                                        if (payType.getPrepayChargeType() != null
                                                                && payType.getPrepayChargeType()
                                                                .intValue() != EnumPrepayChargeType.None.getValue()) {
                                                            tenantTypeMap.put("unit",
                                                                    UnitUtil.getBillModeUnit(paramArray[0], EnumPrepayChargeType
                                                                            .valueOf(payType.getPrepayChargeType())));
                                                            tenantTypeMap.put("type", EnumPrepayChargeType
                                                                    .valueOf(payType.getPrepayChargeType()).getValue());
                                                        } else {
                                                            tenantTypeMap.put("unit", UnitUtil.getCumulantUnit(paramArray[0]));
                                                            tenantTypeMap.put("type", EnumPrepayChargeType.Liang.getValue());
                                                        }
                                                        break;
                                                    case "FeiYong":
                                                        tenantTypeMap.put("unit", "元");
                                                        tenantTypeMap.put("type", EnumPrepayChargeType.Qian.getValue());
                                                        break;
                                                    case "DianGongLv":
                                                        tenantTypeMap.put("unit", "kW");
                                                        break;
                                                    default:
                                                        break;
                                                }
                                                tenantList.add(tenantTypeMap);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if (tenantList.size() > 0) {
                        contentList.add(tenantTypeMap);
                    }
                }

            }
            content.addAll(contentList);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMFormatsService");
        }
    }

    /**
     * 业态报表能耗图
     *
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMFormatsEnergyDataListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult FormatsEnergyDataList(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer density = (Integer) dto.get("density");// 1时 2日 4月 5年
          //  Integer doType = (Integer) dto.get("doType");// 0查看详情 1数据对比
            String startDate = (String) dto.get("startDate");
            String endDate = (String) dto.get("endDate");
            String buildingId = (String) dto.get("buildingId");
            String tenantTypeId = (String) dto.get("tenantTypeId");
            String typeId = (String) dto.get("typeId");
            if (density == null || buildingId == null || startDate == null || tenantTypeId == null || endDate == null
                    || typeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Date timeFrom = standard.parse(startDate);
            Date timeTo = standard.parse(endDate);

            String[] paramArray = typeId.split("_");

            Map<String, Object> contentObj = new HashMap<String, Object>();

            contentObj.put("unit", UnitUtil.getDataUnit(paramArray[0], paramArray[1], null));
            if (paramArray[1].equals("HaoDianLiang")) {//耗电量
                this.processFormatsData(contentObj, buildingId, tenantTypeId, timeFrom, timeTo, density, paramArray[0], EnumEnergyMoney.Energy);
            } else if (paramArray[1].equals("FeiYong")) {//费用
                this.processFormatsData(contentObj, buildingId, tenantTypeId, timeFrom, timeTo, density, paramArray[0], EnumEnergyMoney.Money);
            } else if (paramArray[1].equals("DianGongLv")) {
                this.processDianGongLvAnalysis(contentObj, tenantTypeId, buildingId, timeFrom, timeTo);
            }
            content.add(contentObj);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMFormatsEnergyDataListService");
        }
    }

    private void processFormatsData(Map<String, Object> contentObj, String buildingId, String tenantTypeId, Date timeFrom,
                                    Date timeTo, Integer density, String energyTypeId, EnumEnergyMoney energyMoney) throws Exception { //耗电量
        int timeTypeValue = density;
        if (density > 2) {
            timeTypeValue = 2;
        }

        List<FnTenant> list = fnTenantService.queryTenantBytenantTypeId(tenantTypeId, buildingId);
        List<String> tenantList = new ArrayList<>();
        for (FnTenant fnTenant : list) {
            tenantList.add(fnTenant.getId());
        }
        if (tenantList != null && tenantList.size() > 0) {
            Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.valueOf(density));

            List<FnTenantData> tenantDataList = fnTenantDataService.queryDataList(buildingId, tenantList,
                    EnumTimeType.valueOf(timeTypeValue), energyTypeId, timeFrom, timeTo, energyMoney);
            for (int i = 0; i < tenantDataList.size(); i++) {
                if (tenantDataList.get(i).getData() != null) {
                    String key = null;
                    if (EnumTimeType.T4.getValue() == density.intValue()) {

                        key = standard.format(tenantDataList.get(i).getTimeFrom()).substring(0, 7) + "-01 00:00:00";
                    } else if (EnumTimeType.T5.getValue() == density.intValue()) {
                        key = standard.format(tenantDataList.get(i).getTimeFrom()).substring(0, 4) + "-01-01 00:00:00";
                    } else {
                        key = standard.format(tenantDataList.get(i).getTimeFrom());
                    }
                    if (timeMap.get(key) == null) {

                        timeMap.put(key, tenantDataList.get(i).getData());
                    } else {

                        timeMap.put(key, timeMap.get(key) + tenantDataList.get(i).getData());
                    }
                }
            }

            List<Object> dataList = new ArrayList<>();
            for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
                Map<String, Object> map = new HashMap<>();
                map.put("x", entry.getKey());
                map.put("y", entry.getValue());
                dataList.add(map);
            }
            contentObj.put("dataList", dataList);
        }
    }

    private void processDianGongLvAnalysis(Map<String, Object> contentObj, String tenantTypeId, String buildingId,
                                           Date timeFrom, Date timeTo) throws Exception {

        List<FnTenant> list = fnTenantService.queryTenantBytenantTypeId(tenantTypeId, buildingId);
        List<String> tenantList = new ArrayList<>();
        for (FnTenant fnTenant : list) {
            tenantList.add(fnTenant.getId());
        }
        Map<String, Double> timeMap = null;
        timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Day_1);
        if (timeMap.size() > 7) {// 大于等于七
            if (tenantList != null && tenantList.size() > 0) {

                List<FnTenantMeterPowerStat> dataList = fnTenantMeterPowerStatService.queryTenantDataList(
                        buildingId, tenantList, EnumBodyType.TENANT, FineinConstant.EnergyType.Dian,
                        timeFrom, timeTo);
                for (int i = 0; i < dataList.size(); i++) {
                    if (timeMap.get(standard.format(DateUtils.truncate(dataList.get(i).getTimeFrom(), Calendar.DATE))) != null && dataList.get(i).getData() != null) {
                        timeMap.put(standard.format(DateUtils.truncate(dataList.get(i).getTimeFrom(), Calendar.DATE)),
                                timeMap.get(standard.format(DateUtils.truncate(dataList.get(i).getTimeFrom(), Calendar.DATE))) + dataList.get(i).getData());
                    } else if (dataList.get(i).getData() != null) {
                        timeMap.put(standard.format(DateUtils.truncate(dataList.get(i).getTimeFrom(), Calendar.DATE)),
                                dataList.get(i).getData());
                    }
                }
            }
        } else if (timeMap.size() <= 1 && timeMap.size() > 0) {
            timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Minute_15);

            List<FnTenantData> dataList = fnTenantDataService.queryDataList(buildingId,
                    tenantList, EnumTimeType.T0, FineinConstant.EnergyType.Dian, timeFrom, timeTo,
                    EnumEnergyMoney.Energy);
            //  timeMap.put(standard.format(fnTenantData.getTimeFrom()), fnTenantData.getData() * 4);
            for (int i = 0; i < dataList.size(); i++) {
                if (timeMap.get(standard.format(dataList.get(i).getTimeFrom())) != null && dataList.get(i).getData() != null) {
                    timeMap.put(standard.format(dataList.get(i).getTimeFrom()),
                            timeMap.get(standard.format(dataList.get(i).getTimeFrom())) + dataList.get(i).getData() * 4);
                } else if (dataList.get(i).getData() != null) {
                    timeMap.put(standard.format(dataList.get(i).getTimeFrom()),
                            dataList.get(i).getData() * 4);
                }
            }
        } else {
            timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Hour_1);
            List<FnTenantData> dataList = fnTenantDataService.queryDataList(buildingId,
                    tenantList, EnumTimeType.T0, FineinConstant.EnergyType.Dian, timeFrom, timeTo,
                    EnumEnergyMoney.Energy);
            for (int i = 0; i < dataList.size(); i++) {
                if (timeMap.get(standard.format(dataList.get(i).getTimeFrom())) != null && dataList.get(i).getData() != null) {
                    timeMap.put(standard.format(dataList.get(i).getTimeFrom()),
                            timeMap.get(standard.format(dataList.get(i).getTimeFrom())) + dataList.get(i).getData());
                } else if (dataList.get(i).getData() != null) {
                    timeMap.put(standard.format(dataList.get(i).getTimeFrom()),
                            dataList.get(i).getData());
                }
            }
        }
        {// 总
            List<Object> dataList = new ArrayList<>();
            for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
                Map<String, Object> map = new HashMap<>();
                map.put("x", entry.getKey());
                map.put("y", entry.getValue() == null ? 0.0 : entry.getValue());
                dataList.add(map);
            }
            contentObj.put("dataList", dataList);

        }


    }
}
