package com.persagy.finein.fnmonitor.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.dto.DTOTenant;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnEnergyType;
import com.persagy.ems.pojo.finein.FnTenantPrePayParam;
import com.persagy.ems.pojo.finein.FnTenantType;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.FNTenantDataService;
import com.persagy.finein.service.FNTenantPrePayParamService;
import com.persagy.finein.service.FNTenantService;
import com.persagy.finein.service.FNTenantTypeService;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FNMIndustryEnergyInfoController extends BaseController {

    @Resource(name = "FNTenantTypeService")
    private FNTenantTypeService FNTenantTypeService;

    @Resource(name = "FNTenantService")
    private FNTenantService FNTenantService;

    @Resource(name = "FNTenantDataService")
    private FNTenantDataService FNTenantDataService;

    @Resource(name = "FNTenantPrePayParamService")
    private FNTenantPrePayParamService FNTenantPrePayParamService;

    @RequestMapping("FNMIndustryEnergyInfoService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult IndustryEnergyInfo(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String energyTypeId = (String) dto.get("energyTypeId");

            if (buildingId == null || energyTypeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }


            Map<String, FnEnergyType> energyTypeMap = new HashMap<String, FnEnergyType>();
            for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                energyTypeMap.put(energyType.getId(), energyType);
            }

            List<Object> contentList = new ArrayList<Object>();

            List<FnTenantType> tenantTypeEntityList = FNTenantTypeService.queryList(EMSOrder.Asc);
            if (tenantTypeEntityList != null && tenantTypeEntityList.size() > 0) {
                //查询租户
                List<DTOTenant> tenantEntityList = FNTenantService.queryListByStatus(buildingId, EnumTenantStatus.ACTIVATED, EnumValidStatus.VALID);

                if (tenantEntityList != null && tenantEntityList.size() > 0) {
                    Map<String, List<DTOTenant>> typeTenantMap = new HashMap<String, List<DTOTenant>>();
                    for (DTOTenant dtoTenant : tenantEntityList) {
                        if (!typeTenantMap.containsKey(dtoTenant.getTenant().getTenantTypeId())) {
                            typeTenantMap.put(dtoTenant.getTenant().getTenantTypeId(), new ArrayList<DTOTenant>());
                        }
                        typeTenantMap.get(dtoTenant.getTenant().getTenantTypeId()).add(dtoTenant);
                    }

                    //查询租户付费类型

                    Date timeFrom = DateUtils.truncate(new Date(), Calendar.DATE);
                    Map<String, Map<String, Double>> tenantDataMap = FNTenantDataService.queryTenantData(buildingId, energyTypeId, EnumTimeType.T2, timeFrom);
                    Map<String, FnTenantPrePayParam> prePayParamMap = FNTenantPrePayParamService.queryTenantPreParamByBuilding(buildingId, energyTypeId);

                    Map<String, EnumPayType> payTypeMap = FNTenantService.queryTenantPayType(buildingId, energyTypeId);

                    for (FnTenantType tenantType : tenantTypeEntityList) {
                        boolean isAlarm = false;

                        Map<String, Object> tenantTypeMap = new HashMap<String, Object>();
                        tenantTypeMap.put("typeId", tenantType.getId());
                        tenantTypeMap.put("typeName", tenantType.getName());
                        List<Object> tenantList = new ArrayList<Object>();
                        tenantTypeMap.put("tenantList", tenantList);
                        {
                            List<DTOTenant> dtoTenantList = typeTenantMap.get(tenantType.getId());
                            if (dtoTenantList == null || dtoTenantList.size() == 0) {
                                continue;
                            }

                            for (DTOTenant dtoTenant : dtoTenantList) {
                                Map<String, Object> tenantMap = new HashMap<String, Object>();

                                tenantMap.put("tenantId", dtoTenant.getTenant().getId());
                                tenantMap.put("tenantName", dtoTenant.getTenant().getName());
                                tenantMap.put("activeTime", dtoTenant.getTenant().getActiveTime());
                                List<Object> roomList = new ArrayList<Object>();
                                tenantMap.put("roomList", roomList);

                                EnumPayType payType = payTypeMap.get(dtoTenant.getTenant().getId());
                                if (payType == null) {
                                    continue;
                                }

                                tenantMap.put("type", payType.getValue());

                                Map<String, Double> dataMap = tenantDataMap.get(dtoTenant.getTenant().getId());

                                tenantMap.put("cost", dataMap == null ? null : dataMap.get(EnumEnergyMoney.Money.getValue() + ""));
                                tenantMap.put("data", dataMap == null ? null : dataMap.get(EnumEnergyMoney.Energy.getValue() + ""));
                                tenantMap.put("energyTypeName", "耗" + energyTypeMap.get(energyTypeId).getName());
                                if (payType == EnumPayType.PREPAY) {
                                    FnTenantPrePayParam prePayParam = prePayParamMap.get(dtoTenant.getTenant().getId());
                                    if (prePayParam != null) {
                                        boolean tenantIsAlarm = prePayParam.getIsAlarm() == null || prePayParam.getIsAlarm().intValue() == 0 ? false : true;
                                        tenantMap.put("isAlarm", tenantIsAlarm);
                                        tenantMap.put("remainDays", prePayParam.getRemainDays());
                                        if (tenantIsAlarm) {
                                            isAlarm = true;
                                        }
                                    } else {
                                        tenantMap.put("isAlarm", false);
                                        tenantMap.put("remainDays", null);
                                    }
                                } else {
                                    tenantMap.put("isAlarm", false);
                                    tenantMap.put("remainDays", null);
                                }

                                tenantList.add(tenantMap);

                                if (dtoTenant.getRoomList() != null && dtoTenant.getRoomList().size() > 0) {
                                    for (String roomCode : dtoTenant.getRoomList()) {
                                        Map<String, Object> roomMap = new HashMap<String, Object>();
                                        roomMap.put("roomId", roomCode);
                                        roomList.add(roomMap);
                                    }
                                }
                            }
                        }
                        if (tenantList.size() > 0) {
                            tenantTypeMap.put("isAlarm", isAlarm);
                            contentList.add(tenantTypeMap);
                        }
                    }
                }
            }

            content.addAll(contentList);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMIndustryEnergyInfoService");
        }
    }
}
