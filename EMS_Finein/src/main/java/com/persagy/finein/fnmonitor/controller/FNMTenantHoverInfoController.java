package com.persagy.finein.fnmonitor.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnAlarm;
import com.persagy.ems.pojo.finein.FnAlarmType;
import com.persagy.ems.pojo.finein.FnTenantRoom;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.service.FNAlarmService;
import com.persagy.finein.service.FNTenantRoomService;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FNMTenantHoverInfoController extends BaseController {


    @Resource(name = "FNAlarmService")
    private FNAlarmService FNAlarmService;

    @Resource(name = "FNTenantRoomService")
    private FNTenantRoomService FNTenantRoomService;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @RequestMapping("FNMTenantHoverInfoService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult TenantHoverInfo(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String) dto.get("tenantId");

            if (tenantId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull("tenantId"));
            }


            Map<String, Object> contentObj = new HashMap<String, Object>();
            List<Object> roomList = new ArrayList<Object>();
            List<Object> alarmList = new ArrayList<Object>();
            contentObj.put("alarmList", alarmList);
            contentObj.put("roomList", roomList);

            List<FnTenantRoom> roomEntityList = FNTenantRoomService.queryRoomByTenantId(tenantId);
            if (roomEntityList != null) {
                for (FnTenantRoom room : roomEntityList) {
                    Map<String, Object> roomMap = new HashMap<String, Object>();
                    roomMap.put("roomId", room.getRoomCode());
                    roomList.add(roomMap);
                }
            }

            Map<String, FnAlarmType> alarmTypeMap = new HashMap<String, FnAlarmType>();
            for (FnAlarmType type : ConstantDBBaseData.AlarmTypeList) {
                alarmTypeMap.put(type.getId(), type);
            }

            List<FnAlarm> tenantAlarmList = FNAlarmService.queryAlarmByTenantId(tenantId);
            if (tenantAlarmList != null) {
                for (FnAlarm alarm : tenantAlarmList) {
                    Map<String, Object> alarmMap = new HashMap<String, Object>();
                    alarmMap.put("alarmTime", sdf.format(alarm.getAlarmTime()));
                    alarmMap.put("typeId", alarm.getAlarmTypeId());
                    alarmMap.put("typeName", alarmTypeMap.containsKey(alarm.getAlarmTypeId()) ? alarmTypeMap.get(alarm.getAlarmTypeId()).getName() : "");
                    alarmMap.put("isLimit", alarmTypeMap.containsKey(alarm.getAlarmTypeId()) ? alarmTypeMap.get(alarm.getAlarmTypeId()).getIsLimit() : 0);

                    alarmMap.put("unit", alarm.getUnit());
                    if ("ZHBJ_16".equals(alarm.getAlarmTypeId())) {//功率报警
                        JSONObject extendObj = null;
                        alarmMap.put("cumulanUnit", "kW");
                        try {
                            extendObj = (JSONObject) JSONValue.parse(alarm.getExtend());
                        } catch (Exception e) {
                        }
                        alarmMap.put("limitValue", alarm.getLimitValue() + "");
                        alarmMap.put("alarmValue", alarm.getCurrentValue() + "");
                        alarmMap.put("baseLoad", extendObj == null || extendObj.get("baseLoad") == null ? null : extendObj.get("baseLoad") + "");
                        alarmMap.put("meterId", alarm.getAlarmPositionId());
                        alarmMap.put("powerValue", extendObj == null || extendObj.get("powerValue") == null ? null : extendObj.get("powerValue") + "");

//					alarmMap.put("baseLoad",Math.random() * 100+"");
//					alarmMap.put("meterId","2003");
//					alarmMap.put("powerValue",Math.random() * 100+"");

                    } else if ("ZHBJ_04".equals(alarm.getParentAlarmTypeId())) {
                        JSONObject extendObj = null;
                        try {
                            extendObj = (JSONObject) JSONValue.parse(alarm.getExtend());
                        } catch (Exception e) {
                        }
                        alarmMap.put("interruptTime", extendObj == null || extendObj.get("interruptTime") == null ? null : sdf.parse((String) extendObj.get("interruptTime")));
                        alarmMap.put("alarmPositionId", alarm.getAlarmPositionId());
                    } else {
                        JSONObject extendObj = null;
                        try {
                            extendObj = (JSONObject) JSONValue.parse(alarm.getExtend());
                        } catch (Exception e) {
                        }
                        String cumulanUnit = UnitUtil.getCumulantUnit(alarm.getEnergyTypeId());
                        alarmMap.put("limitValue", alarm.getLimitValue() + "");
                        alarmMap.put("alarmValue", alarm.getCurrentValue() + "");
                        alarmMap.put("cumulanUnit", cumulanUnit);
                        alarmMap.put("historyAvgValue", extendObj == null || extendObj.get("historyAvgValue") == null ? null : extendObj.get("historyAvgValue") + "");
                        alarmMap.put("historyMaxValue", extendObj == null || extendObj.get("historyMaxValue") == null ? null : extendObj.get("historyMaxValue") + "");

//					alarmMap.put("historyAvgValue",Math.random() * 1000+"");
//					alarmMap.put("historyAvgValue",Math.random() * 1000+"");
                    }
                    alarmList.add(alarmMap);
                }
            }

            if (contentObj != null) {
                content.add(contentObj);
            }
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMTenantHoverInfoService");
        }
    }
}
