package com.persagy.finein.fnmonitor.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnAlarm;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.enumeration.EnumAlarmStatus;
import com.persagy.finein.service.FNAlarmService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FNMTenantAlarmController extends BaseController {

    @Resource(name = "FNAlarmService")
    private FNAlarmService FNAlarmService;

    @RequestMapping("FNMTenantAlarmService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult TenantAlarm(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");

            if (buildingId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull("buildingId"));
            }

            // 查询所有报警
            FnAlarm query = new FnAlarm();
            query.setBuildingId(buildingId);
            query.setStatus(EnumAlarmStatus.WeiHuiHu.getValue());
            query.setSort("alarmTime", EMSOrder.Desc);
            query.setSort("alarmTypeId", EMSOrder.Asc);
            List<FnAlarm> alarmList = FNAlarmService.query(query);

            // 租户报警
            List<String> tenantAlarmList = new ArrayList<>();
            if (alarmList != null) {
                for (FnAlarm fnAlarm : alarmList) {
                    if (!tenantAlarmList.contains(fnAlarm.getTenantId())) {
                        tenantAlarmList.add(fnAlarm.getTenantId());
                    }
                }
            }
            content.addAll(tenantAlarmList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNMTenantAlarmService");
        }
    }
}
