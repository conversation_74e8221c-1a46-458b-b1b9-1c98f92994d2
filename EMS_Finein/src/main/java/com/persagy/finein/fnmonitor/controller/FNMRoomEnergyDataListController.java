package com.persagy.finein.fnmonitor.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.FunctionTypeUtil;
import com.persagy.ems.finein.common.util.TimeDataUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.servicedata.ServiceData;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FNMRoomEnergyDataListController extends BaseController {

    @Resource(name = "FNMeterService")
    private FNMeterService FNMeterService;

    @Resource(name = "FNRoomMeterService")
    private FNRoomMeterService FNRoomMeterService;

    @Resource(name = "FNMeterPowerDayPeakService")
    private FNMeterPowerDayPeakService FNMeterPowerDayPeakService;

    @Resource(name = "FNTenantRoomService")
    private FNTenantRoomService FNTenantRoomService;

    @Resource(name = "FNServiceDataService")
    private FNServiceDataService FNServiceDataService;

    @Resource(name = "FNTenantMeterDataService")
    private FNTenantMeterDataService FNTenantMeterDataService;

    @RequestMapping("FNMRoomEnergyDataListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult RoomEnergyDataList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer density = (Integer) dto.get("density");// 1时 2日 4月 5年
            // Integer doType = (Integer) dto.get("doType");// 0查看详情 1数据对比
            String startDate = (String) dto.get("startDate");
            String buildingId = (String) dto.get("buildingId");
            String endDate = (String) dto.get("endDate");
            String meterId = (String) dto.get("meterId");
            String typeId = (String) dto.get("typeId");

            if (density == null || buildingId == null || startDate == null || meterId == null || endDate == null
                    || typeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Date timeFrom = standard.parse(startDate);
            Date timeTo = standard.parse(endDate);

            String[] paramArray = typeId.split("_");

            Map<String, Object> contentObj = new HashMap<String, Object>();
            FnMeter fnMeter = null;
            fnMeter = FNMeterService.queryMeterById(meterId);
            if (fnMeter == null) {
                throw new Exception("查无此仪表:" + meterId);
            }
            Integer destCumulantFunctionId = FunctionTypeUtil.getCumulantFunctionId(fnMeter.getEnergyTypeId());
            Integer cumulantFunctionId = this.queryCumulantFunctionId(fnMeter.getProtocolId(), fnMeter.getEnergyTypeId(),
                    destCumulantFunctionId);
            if (cumulantFunctionId == null) {
                throw new Exception("该仪表能耗无累计量功能,仪表编码:" + fnMeter.getId());
            }
            contentObj.put("unit", UnitUtil.getDataUnit(paramArray[0], paramArray[1], null));
            if (paramArray[1].equals("DianGongLv")) {
                this.processRoomDianGongLvData(contentObj, buildingId, fnMeter, timeFrom, timeTo, density,
                        cumulantFunctionId);
            } else if (paramArray[1].equals("DuiBiZhi")) {
                processRoomDuiBiCha(contentObj, buildingId, fnMeter, timeFrom, timeTo, density, cumulantFunctionId);
            } else {
                this.processRoomData(contentObj, buildingId, fnMeter, timeFrom, timeTo, density, cumulantFunctionId);
            }
            content.add(contentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNMRoomEnergyDataListService");
        }
    }

    private void processRoomDianGongLvData(Map<String, Object> contentObj, String buildingId, FnMeter fnMeter,
                                           Date timeFrom, Date timeTo, Integer density, Integer cumulantFunctionId) throws Exception {
        // Double limitValue =
        // FNAlarmUtil.queryAlarmLimit(fnTenant.getBuildingId(),
        // fnTenant.getId(), FineinConstant.AlarmType.FUHELVGUOGAO);
        Map<String, Double> timeMap = null;
        timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Day_1);
        if (timeMap.size() > 7) {// 大于等于七天
            List<FnMeterPowerDayPeak> dataList = FNMeterPowerDayPeakService.queryListGteLte(fnMeter.getId(), timeFrom,
                    timeTo);
            if (dataList != null && dataList.size() > 0) {
                for (FnMeterPowerDayPeak meterData : dataList) {
                    if (meterData.getData() != null) {
                        timeMap.put(standard.format(DateUtils.truncate(meterData.getTimeFrom(), Calendar.DATE)),
                                meterData.getData());
                    }
                }
            }
        } else if (timeMap.size() <= 1 && timeMap.size() > 0) {
            timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Minute_15);
            List<ServiceData> list = FNServiceDataService.queryServiceDataGteLt(buildingId, fnMeter.getId(),
                    cumulantFunctionId, EnumTimeType.T0, timeFrom, timeTo);
            if (list != null && list.size() > 0) {
                for (ServiceData meterData : list) {
                    if (meterData.getData() != null) {
                        timeMap.put(standard.format(meterData.getTimefrom()), meterData.getData() * 4);
                    }
                }
            }
        } else {
            timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Hour_1);
            List<ServiceData> list = FNServiceDataService.queryServiceDataGteLt(buildingId, fnMeter.getId(),
                    cumulantFunctionId, EnumTimeType.T1, timeFrom, timeTo);
            if (list != null && list.size() > 0) {
                for (ServiceData meterData : list) {
                    if (meterData.getData() != null) {
                        timeMap.put(standard.format(meterData.getTimefrom()), meterData.getData());
                    }
                }
            }
        }
        {// 总
            List<Object> dataList = new ArrayList<>();
            for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
                Map<String, Object> map = new HashMap<>();
                map.put("x", entry.getKey());
                map.put("y", entry.getValue() == null ? 0.0 : entry.getValue());//
                dataList.add(map);
            }
            contentObj.put("dataList", dataList);
            // contentObj.put("aList",dataList);
        }

    }

    private void processRoomDuiBiCha(Map<String, Object> contentObj, String buildingId, FnMeter fnMeter, Date timeFrom,
                                     Date timeTo, Integer density, Integer cumulantFunctionId) throws Exception {
        String protocolId = fnMeter.getProtocolId();
        FnProtocol fnProtocol = ConstantDBBaseData.ProtocolMap.get(protocolId);
        Map<String, Double> timeMap = null;
        timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Day_1);
        EnumTimeType timeType = EnumTimeType.T0;
        if (timeMap.size() > 7) {
            timeType = EnumTimeType.T2;
        } else if (1 < timeMap.size() && timeMap.size() <= 7) {
            timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Hour_1);
            timeType = EnumTimeType.T1;
        } else if (timeMap.size() <= 1) {
            timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Minute_15);
            timeType = EnumTimeType.T0;
        }
        Map<String, Double> meterDataMap = new LinkedHashMap<>(timeMap);
        Map<String, Double> RuanJianDataMap = new LinkedHashMap<>(timeMap);
        Map<String, Double> chaZhiDataMap = new LinkedHashMap<>(timeMap);
        if (fnProtocol.getPayType() == EnumPayType.PREPAY.getValue().intValue()) {// 预付费
            Integer billingMode = fnProtocol.getBillingMode();
            Integer shengYuFunction;
            EnumEnergyMoney energyMoney;
            if (billingMode == EnumPrepayChargeType.Qian.getValue().intValue()) {// 钱
                shengYuFunction = FunctionTypeUtil.getShengYuJinEFunctionId(fnMeter.getEnergyTypeId());
                energyMoney = EnumEnergyMoney.Money;
                contentObj.put("unit", "元");
            } else {
                shengYuFunction = FunctionTypeUtil.getShengYuLiangFunctionId(fnMeter.getEnergyTypeId());
                energyMoney = EnumEnergyMoney.Energy;
                contentObj.put("unit", UnitUtil.getCumulantUnit(fnMeter.getEnergyTypeId()));
            }

            {// 软件消耗
                Map<String, FnRoom> roomMap = FNRoomMeterService.queryRoomByMeter(fnMeter.getId());
                List<String> roomIds = new ArrayList<String>();
                for (Map.Entry<String, FnRoom> entry : roomMap.entrySet()) {
                    roomIds.add(entry.getKey());
                }
                List<FnTenant> tenantList = FNTenantRoomService.queryByRoomIds(buildingId, roomIds);
                List<FnTenantMeterData> meterDataList = new ArrayList<>();
                if (tenantList != null) {
                    for (FnTenant tenant : tenantList) {
                        meterDataList = FNTenantMeterDataService.queryListGteLt(buildingId, tenant.getId(),
                                fnMeter.getId(), cumulantFunctionId, fnMeter.getEnergyTypeId(), timeType, timeFrom,
                                timeTo, energyMoney);
                        for (FnTenantMeterData fnTenantMeterData : meterDataList) {
                            String key = standard.format(fnTenantMeterData.getTimeFrom());
                            if (RuanJianDataMap.get(key) == null) {
                                RuanJianDataMap.put(key, fnTenantMeterData.getData());
                            } else {
                                RuanJianDataMap.put(key, RuanJianDataMap.get(key) + fnTenantMeterData.getData());
                            }
                        }
                    }
                }
            }
            {// 仪表消耗
                if (shengYuFunction != null) {
                    List<ServiceData> meterDataList = FNServiceDataService.queryServiceDataGteLt(buildingId,
                            fnMeter.getId(), shengYuFunction, timeType, timeFrom, timeTo);
                    for (ServiceData serviceData : meterDataList) {
                        String key = standard.format(serviceData.getTimefrom());
                        if (meterDataMap.get(key) == null) {
                            meterDataMap.put(key, serviceData.getData());
                        } else {
                            meterDataMap.put(key, meterDataMap.get(key) + serviceData.getData());
                        }
                    }
                }

            }
            {// 差值
                for (String key : timeMap.keySet()) {
                    if (meterDataMap.get(key) != null && RuanJianDataMap.get(key) != null) {
                        chaZhiDataMap.put(key, RuanJianDataMap.get(key) - meterDataMap.get(key));
                    }
                }
            }
        } else {
            // 软件消耗
            Map<String, FnRoom> roomMap = FNRoomMeterService.queryRoomByMeter(fnMeter.getId());
            List<String> roomIds = new ArrayList<String>();
            for (Map.Entry<String, FnRoom> entry : roomMap.entrySet()) {
                roomIds.add(entry.getKey());
            }
            List<FnTenant> tenantList = FNTenantRoomService.queryByRoomIds(buildingId, roomIds);
            List<FnTenantMeterData> meterDataList = new ArrayList<>();
            if (tenantList != null) {
                for (FnTenant tenant : tenantList) {
                    meterDataList = FNTenantMeterDataService.queryListGteLt(buildingId, tenant.getId(), fnMeter.getId(),
                            cumulantFunctionId, fnMeter.getEnergyTypeId(), timeType, timeFrom, timeTo,
                            EnumEnergyMoney.Money);
                    for (FnTenantMeterData fnTenantMeterData : meterDataList) {
                        String key = standard.format(fnTenantMeterData.getTimeFrom());
                        if (RuanJianDataMap.get(key) == null) {
                            RuanJianDataMap.put(key, fnTenantMeterData.getData());
                        } else {
                            RuanJianDataMap.put(key, RuanJianDataMap.get(key) + fnTenantMeterData.getData());
                        }
                    }
                }
            }
            // List<MeterData> meterDataList =
            // FNMeterDataService.queryMeterDataGteLt(buildingId,
            // fnMeter.getId(),
            // FunctionTypeUtil.getCumulantFunctionId(fnMeter.getEnergyTypeId()),
            // timeType.getValue().intValue(),
            // timeFrom, timeTo);
            // for (MeterData meterData : meterDataList) {
            // String key = standard.format(meterData.getReceivetime());
            // if (RuanJianDataMap.get(key) == null) {
            // RuanJianDataMap.put(key, meterData.getData());
            // } else {
            // RuanJianDataMap.put(key, RuanJianDataMap.get(key) +
            // meterData.getData());
            // }
            // }
        }
        {// 总
            List<Object> AdataList = new ArrayList<>();
            List<Object> BdataList = new ArrayList<>();
            List<Object> CdataList = new ArrayList<>();
            for (Map.Entry<String, Double> entry : chaZhiDataMap.entrySet()) {
                Map<String, Object> map = new HashMap<>();
                map.put("x", entry.getKey());
                map.put("y", entry.getValue());//
                AdataList.add(map);
            }
            for (Map.Entry<String, Double> entry : meterDataMap.entrySet()) {
                Map<String, Object> map = new HashMap<>();
                map.put("x", entry.getKey());
                map.put("y", entry.getValue());//
                BdataList.add(map);
            }
            for (Map.Entry<String, Double> entry : RuanJianDataMap.entrySet()) {
                Map<String, Object> map = new HashMap<>();
                map.put("x", entry.getKey());
                map.put("y", entry.getValue());//
                CdataList.add(map);
            }
            contentObj.put("duiBiChaList", AdataList);
            contentObj.put("xiaoHaoList", BdataList);
            contentObj.put("yiBiaoList", CdataList);
        }

    }

    private void processRoomData(Map<String, Object> contentObj, String buildingId, FnMeter meter, Date timeFrom,
                                 Date timeTo, Integer density, Integer cumulantFunctionId) throws Exception {
        int timeTypeValue = density;
        if (density > 2) {
            timeTypeValue = 2;
        }
        Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.valueOf(density));

        List<ServiceData> list = FNServiceDataService.queryServiceDataGteLt(buildingId, meter.getId(),
                cumulantFunctionId, EnumTimeType.valueOf(timeTypeValue), timeFrom, timeTo);

        if (list != null) {
            for (ServiceData data : list) {
                if (data.getData() != null) {
                    String key = null;
                    if (EnumTimeType.T4.getValue() == density.intValue()) {
                        key = standard.format(data.getTimefrom()).substring(0, 7) + "-01 00:00:00";
                    } else if (EnumTimeType.T5.getValue() == density.intValue()) {
                        key = standard.format(data.getTimefrom()).substring(0, 4) + "-01-01 00:00:00";
                    } else {
                        key = standard.format(data.getTimefrom());
                    }
                    if (timeMap.get(key) == null) {
                        timeMap.put(key, data.getData());
                    } else {
                        timeMap.put(key, timeMap.get(key) + data.getData());
                    }
                }
            }
        }

        List<Object> dataList = new ArrayList<>();
        for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
            Map<String, Object> map = new HashMap<>();
            map.put("x", entry.getKey());
            map.put("y", entry.getValue());
            dataList.add(map);
        }
        contentObj.put("dataList", dataList);
    }

    public Integer queryCumulantFunctionId(String protocolId, String energyTypeId, int functionId) {
        List<FnProtocolFunction> functionList = ConstantDBBaseData.ProtocolFunctionMap.get(protocolId);
        if (functionList != null) {
            for (FnProtocolFunction protocolFunction : functionList) {
                if (protocolFunction.getFunctionId().intValue() == functionId) {
                    return functionId;
                }
            }
        }
        return null;
    }
}
