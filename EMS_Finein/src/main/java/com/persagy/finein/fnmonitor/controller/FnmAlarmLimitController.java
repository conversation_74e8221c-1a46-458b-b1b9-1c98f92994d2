package com.persagy.finein.fnmonitor.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnAlarmLimitCustomObj;
import com.persagy.ems.pojo.finein.FnAlarmLimitCustomSetting;
import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;
import com.persagy.ems.pojo.finein.FnAlarmType;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.service.FNAlarmLimitCustomObjService;
import com.persagy.finein.service.FNAlarmLimitCustomSettingService;
import com.persagy.finein.service.FNAlarmLimitGlobalService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 物业监控 -- 全局报警设置
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FnmAlarmLimitController extends BaseController {

    @Resource(name = "FNAlarmLimitGlobalService")
    private FNAlarmLimitGlobalService fnAlarmLimitGlobalService;

    @Resource(name = "FNAlarmLimitCustomSettingService")
    private FNAlarmLimitCustomSettingService fnAlarmLimitCustomSettingService;

    @Resource(name = "FNAlarmLimitCustomObjService")
    private FNAlarmLimitCustomObjService fnAlarmLimitCustomObjService;

    /**
     * 物业监控 -- 全局报警设置 -- 报警门限列表
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMAlarmLimitListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult alarmLimitList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String tenantId = (String) dto.get("tenantId");

            // 如果tenantId 为空，则查询全局配置
            Map<String, Object> contentObj = new HashMap<String, Object>();

            List<Object> typeList = new ArrayList<Object>();
            contentObj.put("typeList", typeList);

            if (tenantId == null || "".equals(tenantId)) {// 全局
                contentObj.put("setUp", 0);
                this.processGlobal(buildingId, contentObj, typeList);
            } else {
                FnAlarmLimitCustomSetting setting = fnAlarmLimitCustomSettingService.queryOne(buildingId, tenantId);
                if (setting == null || setting.getIsFollowGlobal().intValue() == 1) {
                    contentObj.put("setUp", 0);
                    this.processGlobal(buildingId, contentObj, typeList);
                } else {
                    contentObj.put("setUp", 1);
                    this.processCustom(buildingId, tenantId, contentObj, typeList);
                }
            }

            content.add(contentObj);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNMAlarmLimitListService");
        }
    }

    private void processGlobal(String buildingId, Map<String, Object> contentObj, List<Object> typeList)
            throws Exception {
        List<FnAlarmLimitGlobal> globalList = fnAlarmLimitGlobalService.queryList();

        Map<String, FnAlarmLimitGlobal> globalMap = new HashMap<String, FnAlarmLimitGlobal>();
        if (globalList != null && globalList.size() > 0) {
            for (FnAlarmLimitGlobal obj : globalList) {
                globalMap.put(obj.getAlarmTypeId(), obj);
            }
        }

        for (Map.Entry<String, List<FnAlarmType>> entry : ConstantDBBaseData.EnergyTypeAlarmTypeMap.entrySet()) {
            Map<String, Object> energyMap = new HashMap<String, Object>();
            List<Object> alarmList = new ArrayList<>();
            energyMap.put("type", entry.getKey());
            energyMap.put("alarmList", alarmList);

            for (FnAlarmType alarmType : entry.getValue()) {
                Map<String, Object> typeObj = new HashMap<String, Object>();
                typeObj.put("typeId", alarmType.getId());
                typeObj.put("typeName", alarmType.getName());
                typeObj.put("unit", alarmType.getUnit());
                FnAlarmLimitGlobal global = globalMap.get(alarmType.getId());
                if (global != null) {
                    if (global.getIsOpen() == null || global.getIsOpen().intValue() == 0) {
                        typeObj.put("valid", false);
                        typeObj.put("limit", global.getLimitValue());
                    } else {
                        typeObj.put("valid", true);
                        typeObj.put("limit", global.getLimitValue());
                    }
                } else {
                    typeObj.put("valid", false);
                    typeObj.put("limit", null);
                }
                alarmList.add(typeObj);
            }
            typeList.add(energyMap);
        }
    }

    private void processCustom(String buildingId, String tenantId, Map<String, Object> contentObj,
                               List<Object> typeList) throws Exception {
        List<FnAlarmLimitCustomObj> objList = fnAlarmLimitCustomObjService.queryList(buildingId, tenantId);

        Map<String, FnAlarmLimitCustomObj> customMap = new HashMap<String, FnAlarmLimitCustomObj>();
        if (objList != null && objList.size() > 0) {
            for (FnAlarmLimitCustomObj obj : objList) {
                customMap.put(obj.getAlarmTypeId(), obj);
            }
        }

        for (Map.Entry<String, List<FnAlarmType>> entry : ConstantDBBaseData.EnergyTypeAlarmTypeMap.entrySet()) {

            Map<String, Object> energyMap = new HashMap<String, Object>();
            List<Object> alarmList = new ArrayList<>();
            energyMap.put("type", entry.getKey());
            energyMap.put("alarmList", alarmList);

            for (FnAlarmType alarmType : entry.getValue()) {
                Map<String, Object> typeObj = new HashMap<String, Object>();
                typeObj.put("typeId", alarmType.getId());
                typeObj.put("typeName", alarmType.getName());
                typeObj.put("unit", alarmType.getUnit());
                FnAlarmLimitCustomObj customObj = customMap.get(alarmType.getId());
                if (customObj != null) {
                    if (customObj.getIsOpen() == null || customObj.getIsOpen().intValue() == 0) {
                        typeObj.put("valid", false);
                        typeObj.put("limit", customObj.getLimitValue());
                    } else {
                        typeObj.put("valid", true);
                        typeObj.put("limit", customObj.getLimitValue());
                    }
                } else {
                    typeObj.put("valid", false);
                    typeObj.put("limit", null);
                }
                alarmList.add(typeObj);
            }
            typeList.add(energyMap);
        }
    }

    /**
     * 物业监控 -- 全局报警设置 -- 保存
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMAlarmLimitSetUpService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult alarmLimitSetUp(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String buildingId = (String) dto.get("buildingId");
            String tenantId = (String) dto.get("tenantId");
            Integer setUp = (Integer) dto.get("setUp");
            List typeList = (List) dto.get("typeList");

            if (setUp == null || typeList == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Map<String, FnAlarmType> alarmTypeMap = new HashMap<String, FnAlarmType>();

            for (FnAlarmType alarmType : ConstantDBBaseData.AlarmTypeList) {
                alarmTypeMap.put(alarmType.getId(), alarmType);
            }

            if (tenantId == null || "".equals(tenantId)) {// 全局
                List<FnAlarmLimitGlobal> oldList = fnAlarmLimitGlobalService.queryList();
                Map<String, FnAlarmLimitGlobal> oldMap = new HashMap<>();
                for (FnAlarmLimitGlobal oldEntity : oldList) {
                    oldMap.put(oldEntity.getAlarmTypeId(), oldEntity);
                }
                List<FnAlarmLimitGlobal> saveList = new ArrayList<FnAlarmLimitGlobal>();
                for (int i = 0; i < typeList.size(); i++) {
                    Map<String, Object> typeObj = (Map<String, Object>) typeList.get(i);
                    String typeId = (String) typeObj.get("typeId");
                    boolean valid = (Boolean) typeObj.get("valid");
                    Double limit = null;
                    try {
                        limit = Double.parseDouble(typeObj.get("limit").toString());
                    } catch (Exception e) {
                    }
                    if (oldMap.containsKey(typeId)) {
                        FnAlarmLimitGlobal query = new FnAlarmLimitGlobal();
                        query.setAlarmTypeId(typeId);

                        FnAlarmLimitGlobal update = new FnAlarmLimitGlobal();
                        update.setLimitValue(limit);
                        update.setIsOpen(valid ? 1 : 0);
                        fnAlarmLimitGlobalService.update(query, update);
                    } else {
                        FnAlarmLimitGlobal saveObj = new FnAlarmLimitGlobal();
                        saveObj.setId(UUID.randomUUID().toString());
                        saveObj.setAlarmTypeId(typeId);

                        saveObj.setParentAlarmTypeId(alarmTypeMap.get(typeId).getParentId());
                        saveObj.setTreeId(alarmTypeMap.get(typeId).getTreeId());

                        saveObj.setLimitValue(limit);
                        saveObj.setIsOpen(valid ? 1 : 0);
                        saveList.add(saveObj);
                    }
                }
                if (saveList.size() > 0) {
                    fnAlarmLimitGlobalService.save(saveList);
                }
            } else {
                List<FnAlarmLimitCustomObj> saveList = new ArrayList<FnAlarmLimitCustomObj>();
                FnAlarmLimitCustomSetting setting = new FnAlarmLimitCustomSetting();
                setting.setBuildingId(buildingId);
                setting.setTenantId(tenantId);
                fnAlarmLimitCustomSettingService.remove(setting);
                setting.setId(UUID.randomUUID().toString());
                if (setUp.intValue() == 0) {// 跟随全局报警
                    setting.setIsFollowGlobal(1);
                } else {
                    setting.setIsFollowGlobal(0);
                    FnAlarmLimitCustomObj remove = new FnAlarmLimitCustomObj();
                    remove.setTenantId(tenantId);
                    fnAlarmLimitCustomObjService.remove(remove);

                    for (int i = 0; i < typeList.size(); i++) {
                        Map<String, Object> typeObj = (Map<String, Object>) typeList.get(i);
                        String typeId = (String) typeObj.get("typeId");
                        boolean valid = (Boolean) typeObj.get("valid");
                        Double limit = null;
                        try {
                            limit = Double.parseDouble(typeObj.get("limit").toString());
                        } catch (Exception e) {
                        }

                        FnAlarmLimitCustomObj saveObj = new FnAlarmLimitCustomObj();
                        saveObj.setId(UUID.randomUUID().toString());
                        saveObj.setBuildingId(buildingId);
                        saveObj.setTenantId(tenantId);
                        saveObj.setAlarmTypeId(typeId);
                        saveObj.setParentAlarmTypeId(alarmTypeMap.get(typeId).getParentId());
                        saveObj.setTreeId(alarmTypeMap.get(typeId).getTreeId());
                        saveObj.setLimitValue(limit);
                        saveObj.setIsOpen(valid ? 1 : 0);
                        saveList.add(saveObj);
                    }
                }
                this.fnAlarmLimitCustomSettingService.save(buildingId, tenantId, setting, saveList);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNMAlarmLimitSetUpService");
        }
    }
}
