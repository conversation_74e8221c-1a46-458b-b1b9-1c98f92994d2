package com.persagy.finein.fnmonitor.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.*;
import com.persagy.ems.pojo.finein.*;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumBodyType;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumStatTimeType;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.service.*;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 物业监控 -- 数据分析 -- 楼层
 *
 * @Author：ls
 * @Date：2020/8/25 16:58
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes"})
public class FNMFloorEnergyDataListController extends BaseController {
    @Resource(name = "FNTenantDataService")
    private FNTenantDataService fnTenantDataService;

    @Resource(name = "FNTenantService")
    private FNTenantService fnTenantService;

    @Resource(name = "FNTenantMeterPowerStatService")
    private FNTenantMeterPowerStatService fnTenantMeterPowerStatService;

    /**
     * 楼层报表能耗图
     *
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMFloorEnergyDataListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult FloorEnergyDataList(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Integer density = (Integer) dto.get("density");// 1时 2日 4月 5年
            //  Integer doType = (Integer) dto.get("doType");// 0查看详情 1数据对比
            String startDate = (String) dto.get("startDate");
            String endDate = (String) dto.get("endDate");
            String buildingId = (String) dto.get("buildingId");
            String floorId = (String) dto.get("floorId");
            String typeId = (String) dto.get("typeId");


            if (density == null || buildingId == null || startDate == null || floorId == null || endDate == null
                    || typeId == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            Date timeFrom = standard.parse(startDate);
            Date timeTo = standard.parse(endDate);

            String[] paramArray = typeId.split("_");

            Map<String, Object> contentObj = new HashMap<String, Object>();

            contentObj.put("unit", UnitUtil.getDataUnit(paramArray[0], paramArray[1], null));
            if (paramArray[1].equals("HaoDianLiang")) {//耗电量
                this.processHaoDianLiang(contentObj, buildingId, floorId, timeFrom, timeTo, density, paramArray[0], EnumEnergyMoney.Energy);
            } else if (paramArray[1].equals("FeiYong")) {//费用
                this.processHaoDianLiang(contentObj, buildingId, floorId, timeFrom, timeTo, density, paramArray[0], EnumEnergyMoney.Money);
            } else if (paramArray[1].equals("DianGongLv")) {//电功率
                this.processDianGongLvAnalysis(contentObj, floorId, buildingId, timeFrom, timeTo);
            }
            content.add(contentObj);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMFloorEnergyDataListService");
        }
    }

    private void processHaoDianLiang(Map<String, Object> contentObj, String buildingId, String floorId, Date timeFrom,
                                     Date timeTo, Integer density, String energyTypeId, EnumEnergyMoney energyMoney) throws Exception { //耗电量
        int timeTypeValue = density;
        if (density > 2) {
            timeTypeValue = 2;
        }
        List<FnTenant> list = fnTenantService.queryTenantByFloorId(floorId, buildingId);
        List<String> tenantList = new ArrayList<>();
        for (FnTenant fnTenant : list) {
            tenantList.add(fnTenant.getId());
        }

        if (tenantList != null && tenantList.size() > 0) {

            Map<String, Double> timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumTimeType.valueOf(density));
            List<FnTenantData> tenantDataList = fnTenantDataService.queryDataList(buildingId, tenantList,
                    EnumTimeType.valueOf(timeTypeValue), energyTypeId, timeFrom, timeTo, energyMoney);
            for (int i = 0; i < tenantDataList.size(); i++) {
                if (tenantDataList.get(i).getData() != null) {
                    String key = null;
                    if (EnumTimeType.T4.getValue() == density.intValue()) {
                        key = standard.format(tenantDataList.get(i).getTimeFrom()).substring(0, 7) + "-01 00:00:00";
                    } else if (EnumTimeType.T5.getValue() == density.intValue()) {
                        key = standard.format(tenantDataList.get(i).getTimeFrom()).substring(0, 4) + "-01-01 00:00:00";
                    } else {
                        key = standard.format(tenantDataList.get(i).getTimeFrom());
                    }
                    if (timeMap.get(key) == null) {
                        timeMap.put(key, tenantDataList.get(i).getData());
                    } else {
                        timeMap.put(key, timeMap.get(key) + tenantDataList.get(i).getData());
                    }
                }
            }

            List<Object> dataList = new ArrayList<>();
            for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
                Map<String, Object> map = new HashMap<>();
                map.put("x", entry.getKey());
                map.put("y", entry.getValue());
                dataList.add(map);
            }
            contentObj.put("dataList", dataList);
        }


    }


    private void processDianGongLvAnalysis(Map<String, Object> contentObj, String floorId, String buildingId,
                                           Date timeFrom, Date timeTo) throws Exception {

        List<FnTenant> list = fnTenantService.queryTenantByFloorId(floorId, buildingId);
        List<String> tenantList = new ArrayList<>();
        for (FnTenant fnTenant : list) {
            tenantList.add(fnTenant.getId());
        }
        Map<String, Double> timeMap = null;
        timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Day_1);
        if (timeMap.size() > 7) {// 大于等于七天
            if (tenantList != null && tenantList.size() > 0) {

                List<FnTenantMeterPowerStat> dataList = fnTenantMeterPowerStatService.queryTenantDataList(
                        buildingId, tenantList, EnumBodyType.TENANT, FineinConstant.EnergyType.Dian,
                        timeFrom, timeTo);
                for (int i = 0; i < dataList.size(); i++) {
                    if (timeMap.get(standard.format(DateUtils.truncate(dataList.get(i).getTimeFrom(), Calendar.DATE))) != null && dataList.get(i).getData() != null) {
                        timeMap.put(standard.format(DateUtils.truncate(dataList.get(i).getTimeFrom(), Calendar.DATE)),
                                timeMap.get(standard.format(DateUtils.truncate(dataList.get(i).getTimeFrom(), Calendar.DATE))) + dataList.get(i).getData());
                    } else if (dataList.get(i).getData() != null) {
                        timeMap.put(standard.format(DateUtils.truncate(dataList.get(i).getTimeFrom(), Calendar.DATE)),
                                dataList.get(i).getData());
                    }
                }
            }
        } else if (timeMap.size() <= 1 && timeMap.size() > 0) {
            timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Minute_15);

            List<FnTenantData> dataList = fnTenantDataService.queryDataList(buildingId,
                    tenantList, EnumTimeType.T0, FineinConstant.EnergyType.Dian, timeFrom, timeTo,
                    EnumEnergyMoney.Energy);
            for (int i = 0; i < dataList.size(); i++) {
                if (timeMap.get(standard.format(dataList.get(i).getTimeFrom())) != null && dataList.get(i).getData() != null) {
                    timeMap.put(standard.format(dataList.get(i).getTimeFrom()),
                            timeMap.get(standard.format(dataList.get(i).getTimeFrom())) + dataList.get(i).getData() * 4);
                } else if (dataList.get(i).getData() != null) {
                    timeMap.put(standard.format(dataList.get(i).getTimeFrom()),
                            dataList.get(i).getData() * 4);
                }
            }
        } else {
            timeMap = TimeDataUtil.getTimeDataMap(timeFrom, timeTo, EnumStatTimeType.Hour_1);
            List<FnTenantData> dataList = fnTenantDataService.queryDataList(buildingId,
                    tenantList, EnumTimeType.T0, FineinConstant.EnergyType.Dian, timeFrom, timeTo,
                    EnumEnergyMoney.Energy);
            for (int i = 0; i < dataList.size(); i++) {
                if (timeMap.get(standard.format(dataList.get(i).getTimeFrom())) != null && dataList.get(i).getData() != null) {
                    timeMap.put(standard.format(dataList.get(i).getTimeFrom()),
                            timeMap.get(standard.format(dataList.get(i).getTimeFrom())) + dataList.get(i).getData());
                } else if (dataList.get(i).getData() != null) {
                    timeMap.put(standard.format(dataList.get(i).getTimeFrom()),
                            dataList.get(i).getData());
                }
            }
        }
        {// 总
            List<Object> dataList = new ArrayList<>();
            for (Map.Entry<String, Double> entry : timeMap.entrySet()) {
                Map<String, Object> map = new HashMap<>();
                map.put("x", entry.getKey());
                map.put("y", entry.getValue() == null ? 0.0 : entry.getValue());
                dataList.add(map);
            }
            contentObj.put("dataList", dataList);
        }
    }

    /**
     * 物业监控 -- 数据分析 -- 楼层/业态能耗类型参数树
     *
     * @param jsonString
     * @return
     */
    @RequestMapping("FNMFloorEnergyService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult FloorEnergyTypeTree(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            List<Object> contentList = new ArrayList<>();

            for (FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList) {
                Map<String, Object> contentObj = new HashMap<>();
                contentObj.put("id", energyType.getId());
                contentObj.put("name", energyType.getName());
                List<Object> subTypeList = new ArrayList<>();
                contentObj.put("subTypeList", subTypeList);
                for (FnMonitorParam param : ConstantDBBaseData.MonitorParamList) {
                    if (energyType.getId().equals(param.getEnergyTypeId())) {
                        Map<String, Object> paramMap = new HashMap<>();
                        paramMap.put("id", param.getId());
                        paramMap.put("name", param.getName());
                        if ("对比差".equals(param.getName()) || "剩余量".contains(param.getName())) {
                            continue;
                        }
                        subTypeList.add(paramMap);
                    }
                }

                if (subTypeList.size() > 0) {
                    contentList.add(contentObj);
                }
            }
            content.addAll(contentList);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e, jsonString, "FNMFloorEnergyService");
        }
    }
}