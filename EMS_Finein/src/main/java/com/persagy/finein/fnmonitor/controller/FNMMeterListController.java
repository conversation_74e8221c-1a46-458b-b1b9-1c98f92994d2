package com.persagy.finein.fnmonitor.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumPrePayType;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNTenantPayTypeService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FNMMeterListController extends BaseController {

    @Resource(name = "FNMeterService")
    private FNMeterService FNMeterService;

    @Resource(name = "FNTenantPayTypeService")
    private FNTenantPayTypeService FNTenantPayTypeService;

    @RequestMapping("FNMMeterListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult FNMMeterListService(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String tenantId = (String)dto.get("tenantId");
            String typeId = (String)dto.get("typeId");

            if(tenantId == null || typeId == null){
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            String[] typeIdArray = typeId.split("_");
            String energyTypeId = typeIdArray[0];
            String paramId = typeIdArray[1];

            Map<String,Object> contentMap = new HashMap<>();
            contentMap.put("isShow", 1);

            if("ShengYuLiang".equals(paramId)){
                FnTenantPayType tenantPayType = FNTenantPayTypeService.query(tenantId, energyTypeId);
                if(tenantPayType != null){
                    if(tenantPayType.getPayType() != null && tenantPayType.getPayType().intValue() == EnumPayType.PREPAY.getValue()){
                        if(tenantPayType.getPrePayType().intValue() == EnumPrePayType.ONLINE_TENANTPAY.getValue()){
                            contentMap.put("isShow", 0);
                        }
                    }
                }
            }

            contentMap.put("meterList", FNMeterService.queryMeterList(tenantId, energyTypeId));
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNMMeterListService");
        }
    }
}
