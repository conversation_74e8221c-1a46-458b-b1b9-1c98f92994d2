package com.persagy.finein.core.init;

import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnSysParamValue;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantFlag;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.service.*;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service("FineinInit_9_TenantFlagBuild")
public class FineinInit_9_TenantFlagBuild implements SystemInitService {

	private static boolean IS_FRIST = true;

	private static final Logger log = Logger.getLogger(FineinInit_9_TenantFlagBuild.class);

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;

	@Resource(name = "FNProjectService")
	private FNProjectService FNProjectService;

	@Resource(name = "FNBuildingService")
	private FNBuildingService FNBuildingService;

	@Resource(name = "FNTenantFlagService")
	private FNTenantFlagService FNTenantFlagService;

	@Resource(name = "FNSysParamValueService")
	private FNSysParamValueService FNSysParamValueService;

	@Resource(name = "coreService")
	private CoreService coreService;
	
	

	@Override
	public void init() throws Exception {
		initSysParamValue();
		while (IS_FRIST) {
			try {
				this.handle();
			} catch (Exception e) {
				e.printStackTrace();
				Thread.sleep(1000);
				this.handle();
			}
		}
	}

	private void initSysParamValue() {
		try {
			Boolean isFrist = (Boolean) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_TenantFlagBuild);
			if (isFrist != null) {
				IS_FRIST = isFrist;
			}
		} catch (Exception e) {
		}
	}

	protected void handle() throws Exception {
		List<Building> buildingList = FNBuildingService.queryList(new Building());
		List<FnTenantFlag> saveList = new ArrayList<>();
		for (Building building : buildingList) {
	         StringBuffer sb=null;
			List<FnTenant> list = FNTenantService.queryListByValidStatus(building.getId(), EnumValidStatus.VALID);
			for (FnTenant fnTenant : list) {
				sb=new StringBuffer();
				sb.append(building.getId()).append("-").append(fnTenant.getId()).append("-").append((int)(Math.random()*90+10));
				FnTenantFlag save = new FnTenantFlag();
				save.setId(UUID.randomUUID().toString());
				save.setBuildingId(building.getId());
				save.setTenantId(fnTenant.getId());
				save.setTenantFlag(sb.toString());
				saveList.add(save);
			}
		}
		FNTenantFlagService.save(saveList);
		FnSysParamValue query = new FnSysParamValue();
		query.setId(FineinConstant.SysParamValueKey.Id_TenantFlagBuild);
		FnSysParamValue update = new FnSysParamValue();
		update.setValue("false");
		FNSysParamValueService.update(query, update);
		log.info("租户全码构建成功........");
		IS_FRIST = false;
	}

	@Override
	public int order() {
		return 100 + 9;
	}
}
