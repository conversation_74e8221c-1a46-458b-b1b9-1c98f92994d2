package com.persagy.finein.core.init;

import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.ems.pojo.finein.FnScheduleJob;
import com.persagy.finein.fnmanage.quartz.QuartzJobFactory;
import com.persagy.finein.service.FNScheduleJobService;
import org.apache.log4j.Logger;
import org.quartz.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service("FineinInit_8_Scheduler")
public class FineinInit_8_Scheduler implements SystemInitService {

	private static boolean IS_FRIST = true;

	private static final Logger log = Logger.getLogger(FineinInit_8_Scheduler.class);

	@Resource(name = "FNScheduleJobService")
	private FNScheduleJobService FNScheduleJobService;
	
	@Autowired
	private SchedulerFactoryBean schedulerFactoryBean;

	@Override
	public void init() throws Exception {
		while (IS_FRIST) {
			try {
				this.handle();
				IS_FRIST=false;
			} catch (Exception e) {
				e.printStackTrace();
				Thread.sleep(1000);
				this.handle();
			}
		}
	}
	protected void handle() throws Exception {
		FnScheduleJob query = new FnScheduleJob();
		query.setJobStatus(1);
		List<FnScheduleJob> list = FNScheduleJobService.query(query);
		if(list!=null&&list.size()>0){
			for (FnScheduleJob job : list) {
				TriggerKey triggerKey = TriggerKey.triggerKey(job.getJobName(), job.getJobGroup());
				Scheduler scheduler = schedulerFactoryBean.getScheduler();
				//获取trigger，即在spring配置文件中定义的 bean id="myTrigger"
				CronTrigger trigger = (CronTrigger) scheduler.getTrigger(triggerKey);
				
				//不存在，创建一个
				if (null == trigger) {
					JobDetail jobDetail = JobBuilder.newJob(QuartzJobFactory.class)
							.withIdentity(job.getJobName(), job.getJobGroup()).build();
					jobDetail.getJobDataMap().put("scheduleJob", job);
					
					//表达式调度构建器
//				CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(job
//						.getCronExpression());
					CronScheduleBuilder scheduleBuilder =CronScheduleBuilder.cronSchedule(job.getCronExpression()).withMisfireHandlingInstructionDoNothing();
					
					//按新的cronExpression表达式构建一个新的trigger
					trigger = TriggerBuilder.newTrigger().withIdentity(job.getJobName(), job.getJobGroup()).withSchedule(scheduleBuilder).build();
					
					scheduler.scheduleJob(jobDetail, trigger);
				} else {
					// Trigger已存在，那么更新相应的定时设置
					//表达式调度构建器
					CronScheduleBuilder scheduleBuilder = CronScheduleBuilder.cronSchedule(job
							.getCronExpression());
					//按新的cronExpression表达式重新构建trigger
					trigger = trigger.getTriggerBuilder().withIdentity(triggerKey)
							.withSchedule(scheduleBuilder).build();
					//按新的trigger重新设置job执行
					scheduler.rescheduleJob(triggerKey, trigger);
				}
			}
		}
	}

	@Override
	public int order() {
		return 100 + 8;
	}
}
