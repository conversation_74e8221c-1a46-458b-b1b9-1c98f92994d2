package com.persagy.finein.core.init;

import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.ToSpellUtil;
import com.persagy.ems.pojo.finein.FnSysParamValue;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantSpellName;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.service.*;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service("FineinInit_7_ChangeSpellName")
public class FineinInit_7_ChangeSpellName implements SystemInitService {

	private static boolean IS_FRIST = true;

	private static final Logger log = Logger.getLogger(FineinInit_7_ChangeSpellName.class);

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;

	@Resource(name = "FNProjectService")
	private FNProjectService FNProjectService;

	@Resource(name = "FNBuildingService")
	private FNBuildingService FNBuildingService;

	@Resource(name = "FNTenantSpellNameService")
	private FNTenantSpellNameService FNTenantSpellNameService;

	@Resource(name = "FNSysParamValueService")
	private FNSysParamValueService FNSysParamValueService;

	@Resource(name = "coreService")
	private CoreService coreService;

	@Override
	public void init() throws Exception {
		initSysParamValue();
		while (IS_FRIST) {
			try {
				this.handle();
			} catch (Exception e) {
				e.printStackTrace();
				Thread.sleep(1000);
				this.handle();
			}
		}
	}

	private void initSysParamValue() {
		try {
			Boolean isFrist = (Boolean) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_NameToSpell);
			if (isFrist != null) {
				IS_FRIST = isFrist;
			}
		} catch (Exception e) {
		}
	}

	protected void handle() throws Exception {
		coreService.remove(new FnTenantSpellName());
		List<Building> buildingList = FNBuildingService.queryList(new Building());
		List<FnTenantSpellName> saveList = new ArrayList<>();
		for (Building building : buildingList) {
			List<FnTenant> list = FNTenantService.queryListByValidStatus(building.getId(), EnumValidStatus.VALID);
			for (FnTenant fnTenant : list) {
				FnTenantSpellName save = new FnTenantSpellName();
				String name = fnTenant.getName();
				String pinYin = ToSpellUtil.Instance().getStringPinYin(name);
				save.setId(fnTenant.getId());
				save.setSpellName(pinYin);
				save.setTenantName(name);
				saveList.add(save);
			}
		}
		FNTenantSpellNameService.save(saveList);
		FnSysParamValue query = new FnSysParamValue();
		query.setId(FineinConstant.SysParamValueKey.Id_NameToSpell);
		FnSysParamValue update = new FnSysParamValue();
		update.setValue("false");
		FNSysParamValueService.update(query, update);
		log.info("租户名称转换拼音成功");
		IS_FRIST = false;
	}

	@Override
	public int order() {
		return 100 + 7;
	}

	public static void main(String[] args) {
		String stringPinYin = ToSpellUtil.Instance().getStringPinYin("博锐尚格as12");
		System.out.println(stringPinYin);
	}
}
