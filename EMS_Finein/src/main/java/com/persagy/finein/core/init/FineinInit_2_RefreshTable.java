package com.persagy.finein.core.init;

import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.core.constant.SystemConstant;
import com.persagy.core.thread.EmsMonthThread;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service("FineinInit_2_RefreshTable")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FineinInit_2_RefreshTable implements SystemInitService {
	
	private static final Logger log = Logger.getLogger(FineinInit_2_RefreshTable.class);

	@Override
	public void init() throws Exception {
		handle();
	}
	
	private void handle() throws Exception {
		EmsMonthThread runnable = (EmsMonthThread)SystemConstant.context.getBean("emsMonthThread");
		Thread thread = new Thread(runnable);
		thread.start();
		while(runnable.getIsRefresh() == null || !runnable.getIsRefresh()){
			try {
				Thread.sleep(1000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}
		log.info("【租户管理】刷新分表完成");
	}

	@Override
	public int order() {
		return 100 + 2;
	}

}
