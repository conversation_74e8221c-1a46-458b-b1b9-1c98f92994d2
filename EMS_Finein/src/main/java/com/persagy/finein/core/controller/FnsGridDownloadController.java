package com.persagy.finein.core.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.ems.finein.common.util.ExcelUtil;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.SystemPropertiesUtil;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.service.FNFileResourceService;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.*;


/**
 * 生成pdf 和图片
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes" })
public class FnsGridDownloadController extends BaseController {

    @Resource(name = "ExcelUtil")
    private ExcelUtil excelUtil;

    @Resource(name = "FNFileResourceService")
    private FNFileResourceService fnFileResourceService;

    @Resource(name = "SystemPropertiesUtil")
    private SystemPropertiesUtil systemPropertiesUtil;

    @RequestMapping("FNSGridDownloadService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult gridDownload(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String fileName = (String) dto.get("fileName");
            List gridContent = (List) dto.get("gridContent");

            if (fileName == null || gridContent == null) {
                throw new Exception(ExceptionUtil.ParamIsNull("gridContent"));
            }

            String subdirectory = this.getSubdirectory();

            String resouceId = UUID.randomUUID().toString();
            FileResource resource = new FileResource();
            resource.setId(resouceId);
            resource.setName(fileName);
            resource.setSuffix("xls");
            resource.setSubdirectory(subdirectory);

            String path = this.getPath();

            String fileDir = Paths.get(path, subdirectory).toString();
            File file = new File(fileDir);
            if (!file.exists()) {
                file.mkdirs();
            }

            excelUtil.build(this.getPath(), subdirectory, resouceId, gridContent);

            fnFileResourceService.save(resource);

            Map<String, Object> contentObj = new HashMap<String, Object>();
            contentObj.put("id", resouceId);

            content.add(contentObj);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNSGridDownloadService");
        }
    }

    private String getPath() throws IOException {
        return systemPropertiesUtil.getFileStorageDirectory();
    }

    private String getSubdirectory() {
        return File.separator + "temp" + File.separator + "download";
    }
}
