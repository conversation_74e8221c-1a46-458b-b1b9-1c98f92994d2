package com.persagy.finein.core.util;

import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnAlarmType;
import com.persagy.finein.core.constant.ConstantDBBaseData;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月18日 上午9:52:57

* 说明:
*/

public class AlarmTypeUtil {
	public static FnAlarmType queryAlarmTypeById(String alarmTypeId){
		for(FnAlarmType alarmType : ConstantDBBaseData.AlarmTypeList){
			if(alarmTypeId.equals(alarmType.getId())){
				return alarmType;
			}
		}
		return null;
	}
	
	public static FnAlarmType queryBuZuAlarmByEnergyTypeId(String energyTypeId){
		switch (energyTypeId) {
		case FineinConstant.EnergyType.Dian:
			return queryAlarmTypeById(FineinConstant.AlarmType.DIANFEIYONGBUZU);
		case FineinConstant.EnergyType.Shui:
			return queryAlarmTypeById(FineinConstant.AlarmType.SHUIFEIYONGBUZU);
		case FineinConstant.EnergyType.ReShui:
			return queryAlarmTypeById(FineinConstant.AlarmType.RESHUIFEIYONGBUZU);
		case FineinConstant.EnergyType.RanQi:
			return queryAlarmTypeById(FineinConstant.AlarmType.RANQIFEIYONGBUZU);
		default:
			break;
		}
		return null;
	}
	
	
}

