package com.persagy.finein.core.util;

import com.persagy.ems.pojo.finein.FnProtocolFunction;
import com.persagy.finein.core.constant.ConstantDBBaseData;

import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月25日 下午8:11:22

* 说明:
*/

public class FunctionUtil {
	public static boolean queryFunctionIdExsit(String protocolId,String energyTypeId,int functionId){
		List<FnProtocolFunction> functionList = ConstantDBBaseData.ProtocolFunctionMap.get(protocolId);
		if(functionList != null){
			for(FnProtocolFunction protocolFunction : functionList){
				if(protocolFunction.getFunctionId().intValue() == functionId){
					return true;
				}
			}
		}
		return false;
	}
}

