package com.persagy.finein.core.constant;

import com.persagy.ems.pojo.dictionary.meter.DictionaryFunction;
import com.persagy.ems.pojo.finein.*;

import java.util.*;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月9日 下午7:53:15

* 说明:数据库常量数据
*/

public class ConstantDBBaseData {
	
	public static final Map<String,Object> SysParamValueMap = new HashMap<String,Object>();
	
	public static final List<FnAlarmType> AlarmTypeList = new ArrayList<FnAlarmType>();
	
	public static final Map<String,List<FnAlarmType>> EnergyTypeAlarmTypeMap = new LinkedHashMap<String,List<FnAlarmType>>();
	
	public static final Map<String,Integer> AlarmType_Type = new HashMap<String,Integer>();//0剩余天数报警类型,1剩余量报警类型,2剩余金额类型报警
	
	public static final List<FnEnergyType> EnergyTypeList = new ArrayList<FnEnergyType>();
	
	public static final List<FnTenantType> TenantTypeList = new ArrayList<FnTenantType>();
	
	public static final List<FnMonitorParam> MonitorParamList = new ArrayList<FnMonitorParam>();
	
	public static final Map<String,FnProtocol> ProtocolMap = new HashMap<String,FnProtocol>();
	
	public static final Map<String,List<FnProtocolFunction>> ProtocolFunctionMap = new HashMap<String,List<FnProtocolFunction>>();
	
	public static final Map<String,DictionaryFunction> DictionaryFunctionMap = new HashMap<String,DictionaryFunction>();
	
}

