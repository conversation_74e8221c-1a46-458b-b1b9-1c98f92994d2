package com.persagy.finein.core.business.service.base;

import com.persagy.core.service.BaseBusinessService;

import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月31日 下午3:52:43

* 说明:
*/

public abstract class BaseFineinBusinessService<DTO> extends BaseBusinessService<DTO>{
	
	@SuppressWarnings("rawtypes")
	public String getParamUserId(DTO dto){
		try {
			Map map = (Map)dto;
			Map puser = (Map)map.get("puser");
			return (String)puser.get("id");
		} catch (Exception e) {
		}
		return "persagyAdmin";
	}
}

