package com.persagy.finein.core.filter;

import com.persagy.finein.core.constant.RegisterConstant;
import com.persagy.finein.enumeration.EnumRegisterType;
import org.apache.log4j.Logger;
import p1.DogConstant;

import javax.servlet.*;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 作者:zhangyuan(kedou)
 * <p>
 * 时间:2018年8月21日 上午11:05:53
 * <p>
 * 说明:
 */

public class RegisterFilter implements Filter {

    private static final Logger log = Logger.getLogger(RegisterFilter.class);

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {

    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {

        if (RegisterConstant.RegisterType == EnumRegisterType.Dog) {
            if (!DogConstant.dogStatus) {
                log.error("*********dogStatus:" + DogConstant.dogStatus);
                ((HttpServletResponse) response).sendRedirect("/");
                return;
            }
        } else if (RegisterConstant.RegisterType == EnumRegisterType.Soft) {
            //if (!AuthorizeUtil.Authorized()) {
            //	((HttpServletResponse) response).sendRedirect("/");
            //	return;
            //}
            //修改为智方加密
            try {
                if (!RegisterConstant.result.isResult()) {
                    System.out.println(RegisterConstant.result.getReason());
                    return;
                }
            } catch (Exception e) {
                e.printStackTrace();
                return;
            }
        }
        chain.doFilter(request, response);
    }

    @Override
    public void destroy() {

    }
}