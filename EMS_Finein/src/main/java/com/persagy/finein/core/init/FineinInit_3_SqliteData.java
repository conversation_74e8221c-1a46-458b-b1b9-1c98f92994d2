package com.persagy.finein.core.init;

import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.core.mvc.dao.CoreDao;
import com.persagy.core.mvc.pojo.BusinessObject;
import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.pojo.finein.*;
import com.persagy.ems.pojo.finein.sqlite.*;
import com.persagy.ems.pojo.system.FileResource;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("FineinInit_3_SqliteData")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FineinInit_3_SqliteData implements SystemInitService {

	private static final Logger log = Logger.getLogger(FineinInit_3_SqliteData.class);

	@Resource(name = "SqliteDao")
	private CoreDao sqliteDao;

	@Resource(name = "coreService")
	private CoreService coreService;

	@Override
	public void init() throws Exception {
		this.handle(FnSysParamValueSqlite.class, FnSysParamValue.class, false);
		this.handle(FnEnergyTypeSqlite.class, FnEnergyType.class, false);
		this.handle(FnMonitorParamSqlite.class, FnMonitorParam.class, false);
		this.handle(FnAlarmTypeSqlite.class, FnAlarmType.class, false);
		this.handle(FnTenantTypeSqlite.class, FnTenantType.class, false);
		this.handle(FnProtocolSqlite.class, FnProtocol.class, true);
		this.handle(FnProtocolFunctionSqlite.class, FnProtocolFunction.class, true);
		this.handle(FnAlarmLimitGlobalSqlite.class, FnAlarmLimitGlobal.class, false);
		this.handle(FnPermissionSqlite.class, FnPermission.class, true);
		this.handle(FnRolePermissionSqlite.class, FnRolePermission.class, false);
		this.handle(FnCommonBuildingUploadSqlite.class, FnCommonBuildingUpload.class, false);
		this.handle(FnCommonPrePaySystemParamSqlite.class, FnCommonPrePaySystemParam.class, false);
		this.handle(FnScheduleJobSqlite.class, FnScheduleJob.class, false);
		this.handle(FnGridTemplateSqlite.class, FnGridTemplate.class, false);
		this.handle(FileResourceSqlite.class, FileResource.class, false);
//		this.handle(FnSSOSSystemParamSqlite.class, FnSSOSSystemParam.class, false);
	}

	@SuppressWarnings({ "unchecked" })
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	private void handle(Class<? extends BusinessObject> classSqlite, Class<? extends BusinessObject> classBusiness,
			boolean isRemove) throws Exception {
		if (isRemove) {
			coreService.remove(classBusiness.newInstance());
			List<BusinessObject> srcList = (List<BusinessObject>) sqliteDao.query(classSqlite.newInstance());
			if (srcList != null && srcList.size() > 0) {
				for (BusinessObject src : srcList) {
					BusinessObject businessObject = classBusiness.newInstance();
					BeanUtils.copyProperties(businessObject, src);
					coreService.save(businessObject);
				}
			}
		} else {
			List<BusinessObject> destList = (List<BusinessObject>) coreService.query(classBusiness.newInstance());
			Map<String, BusinessObject> destMap = new HashMap<>();
			if (destList != null) {
				for (BusinessObject dest : destList) {
					String id = getIdValue(dest);
					if (id != null) {
						destMap.put(id, dest);
					}
				}
			}

			List<BusinessObject> srcList = (List<BusinessObject>) sqliteDao.query(classSqlite.newInstance());
			if (srcList != null && srcList.size() > 0) {
				for (BusinessObject src : srcList) {
					String id = getIdValue(src);
					if (id != null) {
						if (!destMap.containsKey(id)) {
							BusinessObject businessObject = classBusiness.newInstance();
							BeanUtils.copyProperties(businessObject, src);
							coreService.save(businessObject);
							log.info(">>>>>Finein初始化数据:" + classBusiness.getName() + "插入:" + id);
						}
					}
				}
			}
		}
		log.info(">>>>>Finein初始化数据:" + classBusiness.getName());
	}

	@Override
	public int order() {
		return 100 + 3;
	}

	private String getIdValue(BusinessObject businessObject) {
		try {
			Method m = businessObject.getClass().getMethod("getId");
			if (m != null) {
				return (String) m.invoke(businessObject);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
}
