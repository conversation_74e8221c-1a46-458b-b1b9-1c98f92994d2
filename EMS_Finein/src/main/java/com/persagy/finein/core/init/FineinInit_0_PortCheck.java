package com.persagy.finein.core.init;

import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.ems.finein.common.util.SystemPropertiesUtil;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 端口配置检查初始化服务
 * 用于检查配置文件中的端口设置并提供相应提示
 */
@Service("FineinInit_0_PortCheck")
public class FineinInit_0_PortCheck implements SystemInitService {

    private static final Logger log = Logger.getLogger(FineinInit_0_PortCheck.class);

    @Resource(name = "SystemPropertiesUtil")
    private SystemPropertiesUtil systemPropertiesUtil;

    @Override
    public void init() throws Exception {
        checkPortConfiguration();
    }

    private void checkPortConfiguration() {
        try {
            String configuredPort = systemPropertiesUtil.getProperty("server.port");
            log.info("=================================================================");
            log.info("【端口配置检查】配置文件中设置的端口: " + configuredPort);
            log.info("【端口配置检查】注意: 此项目为传统Web应用，实际端口由Web容器决定");
            log.info("【端口配置检查】如需修改端口，请:");
            log.info("【端口配置检查】1. IDE运行: 修改IDE中的Tomcat运行配置");
            log.info("【端口配置检查】2. 外部Tomcat: 修改 $TOMCAT_HOME/conf/server.xml");
            log.info("【端口配置检查】3. 将 <Connector port=\"8080\" 改为 <Connector port=\"" + configuredPort + "\"");
            log.info("=================================================================");
        } catch (Exception e) {
            log.error("【端口配置检查】读取端口配置失败", e);
        }
    }

    @Override
    public int order() {
        return 100 + 0; // 最先执行
    }
}
