package com.persagy.finein.core.util;

import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.RandomUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.finein.service.FNMeterService;
import org.springframework.stereotype.Component;
import org.wltea.expression.ExpressionEvaluator;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月31日 下午7:53:37

* 说明:
*/

@Component("ExpressionUtil")
public class ExpressionUtil {
	
	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;
	
	
	public Map<String, String> getSubExpressionMap(String expression) {
		expression = expression.replace("[", "#").replace("]", "#");
		Map<String, String> strMap = new HashMap<String, String>();
		int index = 0;
		while (index != -1) {
			index = expression.indexOf("#", index);
			if (index == -1) {
				break;
			}
			int end = expression.indexOf("#", index + 1);
			if (end == -1) {
				break;
			}
			String str = expression.substring(index+1, end);
			strMap.put(str, "#" + str + "#");
			index = end + 1;
		}
		return strMap;
	}
	
	public boolean verifyExpression(String energyTypeId,String expression){
		try {
			Map<String, String> subExpressionMap = getSubExpressionMap(expression);
			expression = expression.replace("[", "#").replace("]", "#");
			if(subExpressionMap.size() == 0){
				return false;
			}
			for(Map.Entry<String, String> entry : subExpressionMap.entrySet()){
				String[] keyArray = entry.getKey().split("\\.");
				if(keyArray.length != 2){
					return false;
				}
				Integer.parseInt(keyArray[1]);
				expression = expression.replace(entry.getValue(), RandomUtil.getRandomData()+"");
			}
			Double result = DoubleFormatUtil.Instance().getDoubleData(ExpressionEvaluator.evaluate(expression));
			if(result != null){
				boolean isAllExsit = true;
				for(Map.Entry<String, String> entry : subExpressionMap.entrySet()){
					String[] keyArray = entry.getKey().split("\\.");
					//查看表号是否存在
					String meterId = keyArray[0];
					FnMeter meter = FNMeterService.queryMeterById(meterId);
					if(meter == null || !meter.getEnergyTypeId().equals(energyTypeId)){
						isAllExsit = false;
						break;
					}
				}
				if(isAllExsit){
					return true;
				}else{
					return false;
				}
			}
		} catch (Exception e) {
		}
		return false;
	}
	
}

