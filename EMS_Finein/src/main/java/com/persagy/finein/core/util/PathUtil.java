package com.persagy.finein.core.util;

import com.persagy.ems.finein.common.util.SystemPropertiesUtil;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月21日 上午11:05:14

* 说明:
*/
@Component("PathUtil")
public class PathUtil {
	
	@Resource(name = "SystemPropertiesUtil")
	private SystemPropertiesUtil SystemPropertiesUtil;


	public String getPath() throws IOException{
		return SystemPropertiesUtil.getFileStorageDirectory();
	}
	
	/**
	 * 临时下载目录
	 * @return
	 */
	public String getTempDownloadSubDir(){
		return File.separator + "temp" + File.separator + "download";
	}
	
	/**
	 * 临时上传目录
	 * @return
	 */
	public String getTempUploadSubDir(){
		return File.separator + "temp" + File.separator + "download";
	}
}

