package com.persagy.finein.core.controller;

import com.persagy.ac.service.AcSystemRoleService;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.ac.AcSystemFunction;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 项目名：租户管理 接口名：通用充值前查询接口
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes" })
public class FntFunctionListController extends BaseController {

    @Resource(name = "AcSystemRoleService")
    private AcSystemRoleService acSystemRoleService;

    @RequestMapping("FNTFunctionListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult functionList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String systemCode = (String) dto.get("systemCode");
            String roleId = (String) dto.get("roleId");

            if (systemCode == null ) {
                throw new Exception(ExceptionUtil.ParamIsNull("systemCode"));
            }

            List<AcSystemFunction> functionList = acSystemRoleService.queryFunction(roleId);
            content.add(functionList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTFunctionListService");
        }
    }
}
