package com.persagy.finein.core.init;

import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.feeddog.FeedDogThread;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service("FineinInit_12_FeedDog")
@Transactional(propagation=Propagation.NOT_SUPPORTED)
public class FineinInit_12_FeedDog implements SystemInitService {

	private static final Logger log = Logger.getLogger(FineinInit_12_FeedDog.class);

	@Resource(name = "FeedDogThread")
	private FeedDogThread FeedDogThread;

	//喂狗ip
	private static final String ip = "127.0.0.1";
	//喂狗端口
	private static final int port = 56789;
	//喂狗间隔时间S
	private static final long feedDogSecond = 50;
	//喂狗项目名称
	private static final String projectName = "Finein";

	@Override
	public void init() throws Exception {
		this.handle();
	}
	
	protected void handle() {
		Boolean Id_FNFeedDogIsOpen = (Boolean) ConstantDBBaseData.SysParamValueMap
				.get(FineinConstant.SysParamValueKey.Id_FNFeedDogIsOpen);
		try {
			if(Id_FNFeedDogIsOpen){
				FeedDogThread.setParams(ip,port,feedDogSecond,projectName);
				FeedDogThread.start();
				log.info("喂狗线程启动。。。。。。。。。。。。。。。。。。。。。");
			}
//			try {
//				Thread.sleep(3000);
//			} catch (Exception e) {
//			}
//			FeedDogThread.requestStop();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	@Override
	public int order() {
		return 100 + 12;
	}

}
