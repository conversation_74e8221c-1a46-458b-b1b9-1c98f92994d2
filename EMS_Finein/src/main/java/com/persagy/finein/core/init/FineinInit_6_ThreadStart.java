package com.persagy.finein.core.init;

import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.core.constant.SystemConstant;
import com.persagy.core.thread.BaseThread;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service("FineinInit_6_ThreadStart")
public class FineinInit_6_ThreadStart implements SystemInitService {

	private static final Logger log = Logger.getLogger(FineinInit_6_ThreadStart.class);

	@Override
	public void init() throws Exception {
		Map<String,BaseThread> threadMap = SystemConstant.context.getBeansOfType(BaseThread.class);
		for(Map.Entry<String,BaseThread> entry : threadMap.entrySet()){
			entry.getValue().start();
			log.info("【租户管理】线程启动:"+entry.getKey());
		}
	}
	
	@Override
	public int order() {
		return 100 + 6;
	}
}
