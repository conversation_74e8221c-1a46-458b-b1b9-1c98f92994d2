package com.persagy.finein.core.controller;

import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.finein.common.util.SystemPropertiesUtil;
import com.persagy.ems.finein.common.util.WKHtmlToPdfOrImgUtil;
import com.persagy.ems.pojo.system.FileResource;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.enumeration.EnumWKFileType;
import com.persagy.finein.service.FNFileResourceService;
import org.apache.commons.io.FileUtils;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.io.File;
import java.nio.file.Paths;
import java.util.*;

/**
 * 生成pdf 和图片
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({"rawtypes" })
public class FnsPdfAndImgBuilderController extends BaseController {

    @Resource(name = "WKHtmlToPdfOrImgUtil")
    private WKHtmlToPdfOrImgUtil wkHtmlToPdfOrImgUtil;

    @Resource(name = "FNFileResourceService")
    private FNFileResourceService fnFileResourceService;

    @Resource(name = "SystemPropertiesUtil")
    private SystemPropertiesUtil systemPropertiesUtil;

    @RequestMapping("FNSPdfAndImgBuilderService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult pdfAndImgBuilder(@RequestParam(value = "jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String htmlContent = (String) dto.get("htmlContent");
            String fileName = (String) dto.get("fileName");
            Integer type = (Integer) dto.get("type");

            if (htmlContent == null || fileName == null || type == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }

            String path = this.getPath();
            String uploaddirectory = this.getUploaddirectory();
            String resouceId = UUID.randomUUID().toString();
            String uploadFilePath = Paths.get(path, uploaddirectory, resouceId + ".html").toString();

            {//保存文件
                String fileDir = Paths.get(path, uploaddirectory).toString();
                File file = new File(fileDir);
                if (!file.exists()) {
                    file.mkdirs();
                }

                FileUtils.writeStringToFile(new File(uploadFilePath), htmlContent, "utf-8");
            }

            String subdirectory = this.getSubdirectory();

            String newResourceId = UUID.randomUUID().toString();
            FileResource resource = new FileResource();
            resource.setId(newResourceId);
            resource.setName(fileName);
            resource.setSubdirectory(subdirectory);


            {
                String fileDir = Paths.get(path, subdirectory).toString();
                File file = new File(fileDir);
                if (!file.exists()) {
                    file.mkdirs();
                }
            }

            if (type.intValue() == EnumWKFileType.Pdf.getValue().intValue()) {
                String newFilePath = Paths.get(path, subdirectory, newResourceId + ".pdf").toString();
                resource.setSuffix("pdf");
                wkHtmlToPdfOrImgUtil.HtmlToPdf(uploadFilePath, newFilePath);
                File file = new File(newFilePath);
                file.renameTo(new File(newFilePath.substring(0, newFilePath.lastIndexOf("."))));
            } else if (type.intValue() == EnumWKFileType.Img.getValue().intValue()) {
                String newFilePath = Paths.get(path, subdirectory, newResourceId + ".png").toString();
                resource.setSuffix("png");
                wkHtmlToPdfOrImgUtil.HtmlToImage(uploadFilePath, newFilePath);
                File file = new File(newFilePath);
                file.renameTo(new File(newFilePath.substring(0, newFilePath.lastIndexOf("."))));
            } else {
                throw new Exception("不支持的type:" + type);
            }
            fnFileResourceService.save(resource);

            Map<String, Object> contentObj = new HashMap<String, Object>();
            contentObj.put("id", newResourceId);

            content.add(contentObj);
            return Result.SUCCESS(content);
        } catch (Exception e) {
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNSPdfAndImgBuilderService");
        }
    }

    private String getPath() throws Exception{
        return systemPropertiesUtil.getFileStorageDirectory();
    }

    private String getSubdirectory(){
        return File.separator + "temp" + File.separator + "download";
    }

    private String getUploaddirectory(){
        return File.separator + "temp" + File.separator + "upload";
    }
}
