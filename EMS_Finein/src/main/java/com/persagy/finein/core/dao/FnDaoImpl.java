package com.persagy.finein.core.dao;

import com.persagy.collect.core.dao.DataDAO;
import com.persagy.collect.dto.StaMeter;
import com.persagy.collect.dto.StaProject;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNProjectService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component("FnDao")
public class FnDaoImpl implements DataDAO {

	@Resource(name = "FNProjectService")
	private FNProjectService ProjectService;

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Override
	public List<StaMeter> queryMeterList() throws Exception {
		List<FnMeter> list = FNMeterService.query(new FnMeter());
		if (list == null || list.size() <= 0) {
			return null;
		}
		List<StaMeter> staMeterList = new ArrayList<>();
		for (FnMeter meter : list) {
			StaMeter staMeter = new StaMeter();
			staMeter.setCode(meter.getId());
			staMeter.setName(meter.getInstallAddress());
			if (meter.getEnergyTypeId().equals("Dian")) {
				staMeter.setType(FineinConstant.MeterType.Dian);
			} else if (meter.getEnergyTypeId().equals("Shui")) {
				staMeter.setType(FineinConstant.MeterType.Shui);
			} else if (meter.getEnergyTypeId().equals("ReShui")) {
				staMeter.setType(FineinConstant.MeterType.ReShui);
			} else if (meter.getEnergyTypeId().equals("RanQi")) {
				staMeter.setType(FineinConstant.MeterType.RanQi);
			}
			staMeterList.add(staMeter);
		}
		return staMeterList;
	}

	@Override
	public StaProject queryProject() throws Exception {
		Project project = ProjectService.queryProject();
		StaProject staProject = new StaProject();
		staProject.setCode(project.getId());
		staProject.setName(project.getName());
		return staProject;
	}

}
