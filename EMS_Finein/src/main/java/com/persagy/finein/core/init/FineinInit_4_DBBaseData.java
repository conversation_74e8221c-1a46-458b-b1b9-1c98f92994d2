package com.persagy.finein.core.init;

import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.mvc.dao.CoreDao;
import com.persagy.core.utils.CommonUtils;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.dictionary.meter.DictionaryFunction;
import com.persagy.ems.pojo.finein.*;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.enumeration.EnumAlarmRemainType;
import com.persagy.finein.enumeration.EnumBaseType;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.service.*;
import org.apache.log4j.Logger;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("FineinInit_4_DBBaseData")
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class FineinInit_4_DBBaseData implements SystemInitService {

	private static final Logger log = Logger.getLogger(FineinInit_4_DBBaseData.class);

	@Resource(name = "FNSysParamValueService")
	private FNSysParamValueService FNSysParamValueService;

	@Resource(name = "FNAlarmTypeService")
	private FNAlarmTypeService FNAlarmTypeService;

	@Resource(name = "FNEnergyTypeService")
	private FNEnergyTypeService FNEnergyTypeService;

	@Resource(name = "FNTenantTypeService")
	private FNTenantTypeService FNTenantTypeService;

	@Resource(name = "FNMonitorParamService")
	private FNMonitorParamService FNMonitorParamService;

	@Resource(name = "FNProtocolService")
	private FNProtocolService FNProtocolService;

	@Resource(name = "FNProtocolFunctionService")
	private FNProtocolFunctionService FNProtocolFunctionService;

	@Resource(name = "jdbcTemplateCoreDao")
	private CoreDao CoreDao;

//	@Resource(name = "SystemPropertiesUtil")
//	private SystemPropertiesUtil SystemPropertiesUtil;

	@Override
	public void init() throws Exception {
		this.initSysParamValue();
		this.initAlarmType();
		this.initEnergyType();
		this.initTenantType();
		this.initMonitorParam();
		this.initProtocol();
		this.initProtocolFunction();
		this.initDictionaryFunction();
	}

	private void initSysParamValue() {
		try {
			List<FnSysParamValue> sysParamValueList = FNSysParamValueService.query(new FnSysParamValue());
			if (sysParamValueList != null && sysParamValueList.size() > 0) {
				for (FnSysParamValue sysParamValue : sysParamValueList) {
					try {
						Object value = null;
						if (sysParamValue.getType().equals(EnumBaseType.Integer.getValue().toString())) {
							value = Integer.parseInt(sysParamValue.getValue());
						} else if (sysParamValue.getType().equals(EnumBaseType.Long.getValue().toString())) {
							value = Long.parseLong(sysParamValue.getValue());
						} else if (sysParamValue.getType().equals(EnumBaseType.Double.getValue().toString())) {
							value = Double.parseDouble(sysParamValue.getValue());
						} else if (sysParamValue.getType().equals(EnumBaseType.Boolean.getValue().toString())) {
							value = Boolean.parseBoolean(sysParamValue.getValue());
						} else if (sysParamValue.getType().equals(EnumBaseType.String.getValue().toString())) {
							value = sysParamValue.getValue().trim();
						} else if (sysParamValue.getType().equals(EnumBaseType.JSONArray.getValue().toString())) {
							value = JSONValue.parse(sysParamValue.getValue());
						} else if (sysParamValue.getType().equals(EnumBaseType.JSONObject.getValue().toString())) {
							value = JSONValue.parse(sysParamValue.getValue());
						} else {
							throw new Exception("FnSysParamValue未知的数据类型：" + sysParamValue.getId());
						}
						ConstantDBBaseData.SysParamValueMap.put(sysParamValue.getId(), value);
						log.info("***** FnSysParamValue 初始化:<" + sysParamValue.getId() + ">成功:值:"
								+ sysParamValue.getValue());
					} catch (Exception e) {
						e.printStackTrace();
						log.error("***** FnSysParamValue 初始化异常:" + CommonUtils.getExceptionStackTrace(e));
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("***** FnSysParamValue 初始化异常:" + CommonUtils.getExceptionStackTrace(e));
		}
	}

	private void initAlarmType() {
		try {
			List<FnAlarmType> list = FNAlarmTypeService.queryList(EnumValidStatus.VALID);
			List<FnAlarmType> dianAlarmTypeList = new ArrayList<>();
			List<FnAlarmType> shuiAlarmTypeList = new ArrayList<>();
			List<FnAlarmType> reShuiAlarmTypeList = new ArrayList<>();
			List<FnAlarmType> ranQiAlarmTypeList = new ArrayList<>();
			List<FnAlarmType> fuHeLvAlarmTypeList = new ArrayList<>();
			if (list != null && list.size() > 0) {
				for (FnAlarmType fnAlarmType : list) {

					switch (fnAlarmType.getId()) {
					case FineinConstant.AlarmType.DIANFEIYONGBUZU:
						dianAlarmTypeList.add(fnAlarmType);
						ConstantDBBaseData.AlarmType_Type.put(fnAlarmType.getId(),
								EnumAlarmRemainType.REMAIN_Day.getValue().intValue());
						break;
					case FineinConstant.AlarmType.DIANSHENGYUJINE:
						dianAlarmTypeList.add(fnAlarmType);
						ConstantDBBaseData.AlarmType_Type.put(fnAlarmType.getId(),
								EnumAlarmRemainType.REMAIN_JinE.getValue().intValue());
						break;
					case FineinConstant.AlarmType.DIANSHENGYULIANG:
						dianAlarmTypeList.add(fnAlarmType);
						ConstantDBBaseData.AlarmType_Type.put(fnAlarmType.getId(),
								EnumAlarmRemainType.REMAIN_Data.getValue().intValue());
						break;
					case FineinConstant.AlarmType.SHUIFEIYONGBUZU:
						shuiAlarmTypeList.add(fnAlarmType);
						ConstantDBBaseData.AlarmType_Type.put(fnAlarmType.getId(),
								EnumAlarmRemainType.REMAIN_Day.getValue().intValue());
						break;
					case FineinConstant.AlarmType.SHUISHENGYUJINE:
						shuiAlarmTypeList.add(fnAlarmType);
						ConstantDBBaseData.AlarmType_Type.put(fnAlarmType.getId(),
								EnumAlarmRemainType.REMAIN_JinE.getValue().intValue());
						break;
					case FineinConstant.AlarmType.SHUISHENGYULIANG:
						shuiAlarmTypeList.add(fnAlarmType);
						ConstantDBBaseData.AlarmType_Type.put(fnAlarmType.getId(),
								EnumAlarmRemainType.REMAIN_Data.getValue().intValue());
						break;
					case FineinConstant.AlarmType.RESHUIFEIYONGBUZU:
						reShuiAlarmTypeList.add(fnAlarmType);
						ConstantDBBaseData.AlarmType_Type.put(fnAlarmType.getId(),
								EnumAlarmRemainType.REMAIN_Day.getValue().intValue());
						break;
					case FineinConstant.AlarmType.RESHUISHENGYUJINE:
						reShuiAlarmTypeList.add(fnAlarmType);
						ConstantDBBaseData.AlarmType_Type.put(fnAlarmType.getId(),
								EnumAlarmRemainType.REMAIN_JinE.getValue().intValue());
						break;
					case FineinConstant.AlarmType.RESHUISHENGYULIANG:
						reShuiAlarmTypeList.add(fnAlarmType);
						ConstantDBBaseData.AlarmType_Type.put(fnAlarmType.getId(),
								EnumAlarmRemainType.REMAIN_Data.getValue().intValue());
						break;
					case FineinConstant.AlarmType.RANQIFEIYONGBUZU:
						ranQiAlarmTypeList.add(fnAlarmType);
						ConstantDBBaseData.AlarmType_Type.put(fnAlarmType.getId(),
								EnumAlarmRemainType.REMAIN_Day.getValue().intValue());
						break;
					case FineinConstant.AlarmType.RANQISHENGYULIANG:
						ranQiAlarmTypeList.add(fnAlarmType);
						ConstantDBBaseData.AlarmType_Type.put(fnAlarmType.getId(),
								EnumAlarmRemainType.REMAIN_Data.getValue().intValue());
						break;
					case FineinConstant.AlarmType.RANQISHENGYUJINE:
						ranQiAlarmTypeList.add(fnAlarmType);
						ConstantDBBaseData.AlarmType_Type.put(fnAlarmType.getId(),
								EnumAlarmRemainType.REMAIN_JinE.getValue().intValue());
						break;
					case FineinConstant.AlarmType.FUHELVGUOGAO:
						fuHeLvAlarmTypeList.add(fnAlarmType);
						break;
					default:
						break;
					}
				}
				ConstantDBBaseData.EnergyTypeAlarmTypeMap.put(FineinConstant.EnergyType.Dian, dianAlarmTypeList);
				ConstantDBBaseData.EnergyTypeAlarmTypeMap.put(FineinConstant.EnergyType.Shui, shuiAlarmTypeList);
				ConstantDBBaseData.EnergyTypeAlarmTypeMap.put(FineinConstant.EnergyType.RanQi, ranQiAlarmTypeList);
				ConstantDBBaseData.EnergyTypeAlarmTypeMap.put(FineinConstant.EnergyType.ReShui, reShuiAlarmTypeList);
				ConstantDBBaseData.EnergyTypeAlarmTypeMap.put("fuHeLv", fuHeLvAlarmTypeList);
				ConstantDBBaseData.AlarmTypeList.addAll(list);
				log.info("***** FnAlarmType 初始化成功");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("***** FnAlarmType 初始化异常:" + CommonUtils.getExceptionStackTrace(e));
		}
	}

	private void initEnergyType() {
		try {
			List<FnEnergyType> list = FNEnergyTypeService.queryList(EnumValidStatus.VALID, EMSOrder.Asc);
			if (list != null && list.size() > 0) {
				ConstantDBBaseData.EnergyTypeList.addAll(list);
				log.info("***** FnEnergyType 初始化成功");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("***** FnEnergyType 初始化异常:" + CommonUtils.getExceptionStackTrace(e));
		}
	}

	private void initTenantType() {
		try {
			List<FnTenantType> list = FNTenantTypeService.queryList(EMSOrder.Asc);
			if (list != null && list.size() > 0) {
				ConstantDBBaseData.TenantTypeList.addAll(list);
				log.info("***** FnTenantType 初始化成功");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("***** FnTenantType 初始化异常:" + CommonUtils.getExceptionStackTrace(e));
		}
	}

	private void initMonitorParam() {
		try {
			List<FnMonitorParam> list = FNMonitorParamService.query(new FnMonitorParam());
			if (list != null && list.size() > 0) {
				ConstantDBBaseData.MonitorParamList.addAll(list);
				log.info("***** FnMonitorParam 初始化成功");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("***** FnMonitorParam 初始化异常:" + CommonUtils.getExceptionStackTrace(e));
		}
	}

	private void initProtocol() {
		try {
			List<FnProtocol> list = FNProtocolService.query(new FnProtocol());
			if (list != null && list.size() > 0) {
				for (FnProtocol entity : list) {
					ConstantDBBaseData.ProtocolMap.put(entity.getId(), entity);
				}
				log.info("***** FnProtocol 初始化成功");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("***** FnProtocol 初始化异常:" + CommonUtils.getExceptionStackTrace(e));
		}
	}

	private void initProtocolFunction() {
		try {
			List<FnProtocolFunction> list = FNProtocolFunctionService.query(new FnProtocolFunction());
			if (list != null && list.size() > 0) {
				Map<String, List<FnProtocolFunction>> ProtocolFunctionMap = new HashMap<String, List<FnProtocolFunction>>();
				for (FnProtocolFunction entity : list) {
					if (!ProtocolFunctionMap.containsKey(entity.getProtocolId())) {
						ProtocolFunctionMap.put(entity.getProtocolId(), new ArrayList<FnProtocolFunction>());
					}
					ProtocolFunctionMap.get(entity.getProtocolId()).add(entity);
				}
				ConstantDBBaseData.ProtocolFunctionMap.putAll(ProtocolFunctionMap);
				;
				log.info("***** initProtocolFunction 初始化成功");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("***** initProtocolFunction 初始化异常:" + CommonUtils.getExceptionStackTrace(e));
		}
	}

	private void initDictionaryFunction() throws Exception {
		try {
			List<DictionaryFunction> list = CoreDao.query(new DictionaryFunction());
			if (list != null && list.size() > 0) {
				Map<String, DictionaryFunction> DictionaryFunctionMap = new HashMap<String, DictionaryFunction>();
				for (DictionaryFunction entity : list) {
					DictionaryFunctionMap.put(entity.getId(), entity);
				}
				ConstantDBBaseData.DictionaryFunctionMap.putAll(DictionaryFunctionMap);
				;
				log.info("***** initDictionaryFunction 初始化成功");
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.error("***** initDictionaryFunction 初始化异常:" + CommonUtils.getExceptionStackTrace(e));
		}
	}

	@Override
	public int order() {
		return 100 + 4;
	}

}
