package com.persagy.finein.core.controller;

import com.persagy.ac.service.AcSystemFunctionService;
import com.persagy.ac.service.AcSystemUserFunctionService;
import com.persagy.ac.service.AcSystemUserService;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.utils.CommonUtils;
import com.persagy.ems.finein.common.util.ExceptionUtil;
import com.persagy.ems.pojo.ac.AcSystemFunction;
import com.persagy.ems.pojo.ac.AcSystemUser;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.enumeration.EnumYesNo;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 登录
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class FncLoginController extends BaseController {

    @Resource(name = "AcSystemUserService")
    private AcSystemUserService acSystemUserService;

    @Resource(name = "AcSystemUserFunctionService")
    private AcSystemUserFunctionService acSystemUserFunctionService;

    @Resource(name = "AcSystemFunctionService")
    private AcSystemFunctionService acSystemFunctionService;


    @RequestMapping("FNTLoginService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult login(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String name = (String) dto.get("name");
            String password = (String) dto.get("password");
            if (name == null || password == null) {
                throw new Exception(ExceptionUtil.ParamIsNull());
            }
            AcSystemUser user = new AcSystemUser();
            user.setName(name);
            user.setPassword(CommonUtils.md5(password));
            user.setValid(EnumValidStatus.VALID.getValue());
            List<AcSystemUser> list = acSystemUserService.query(user);

            if (list == null || list.size() == 0) {
                throw new Exception("用户名密码错误");
            }

            Map<String, Object> userMap = new HashMap<String, Object>();
            List<AcSystemFunction> functionList = new ArrayList<AcSystemFunction>();
            userMap.put("functionList", functionList);
            AcSystemUser systemUser = list.get(0);
            userMap.put("id", systemUser.getId());
            userMap.put("name", systemUser.getName());
            userMap.put("showName", systemUser.getShowName());
            userMap.put("password", systemUser.getPassword());
            userMap.put("mobile", systemUser.getMobile());
            userMap.put("email", systemUser.getEmail());
            userMap.put("valid", systemUser.getValid());
            if ("persagyAdmin".equals(name.trim())) {
                // 超级管理员
                AcSystemFunction query = new AcSystemFunction();
                query.setProductId("Finein");
                query.setIsParent(1);
                query.setSort("orderBy", EMSOrder.Asc);
                List<AcSystemFunction> queryList = acSystemFunctionService.query(query);
                functionList.addAll(queryList);
            } else {
                Map<String, AcSystemFunction> functionMap = acSystemUserFunctionService.queryByUserId(systemUser.getId());
                if (functionMap != null) {
                    for (Map.Entry<String, AcSystemFunction> entry : functionMap.entrySet()) {
                        AcSystemFunction systemFunction = entry.getValue();
                        if (systemFunction.getIsParent() == EnumYesNo.YES.getValue().intValue()) {
                            functionList.add(entry.getValue());
                        }
                    }
                }
            }
            content.add(userMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"FNTLoginService");
        }
    }
}
