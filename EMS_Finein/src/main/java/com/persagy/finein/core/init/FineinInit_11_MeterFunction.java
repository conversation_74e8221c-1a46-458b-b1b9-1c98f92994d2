package com.persagy.finein.core.init;

import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.collect.core.thread.MeterFunctionRefreshThread;
import com.persagy.core.constant.SystemConstant;
import com.persagy.finein.core.dao.FnDaoImpl;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service("FineinInit_11_MeterFunction")
public class FineinInit_11_MeterFunction implements SystemInitService {

	private static final Logger log = Logger.getLogger(FineinInit_11_MeterFunction.class);

	@Resource(name = "FnDao")
	private FnDaoImpl FnDao;

	@Override
	public void init() throws Exception {
		MeterFunctionRefreshThread MeterFunctionRefreshThread = (MeterFunctionRefreshThread) SystemConstant.context
				.getBean("MeterFunctionRefreshThread");
		MeterFunctionRefreshThread.setOatherDataDAO(FnDao);
		log.info("项目仪表初始化完成。。。。。。。。。。。。。。。。。。。。。");
		
	}

	@Override
	public int order() {
		return 100 + 11;
	}
}
