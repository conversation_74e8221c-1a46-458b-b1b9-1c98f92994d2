package com.persagy.finein.core.util;

import com.persagy.ems.pojo.finein.FnEnergyType;
import com.persagy.finein.core.constant.ConstantDBBaseData;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月18日 上午9:52:57

* 说明:
*/

public class EnergyTypeUtil {
	public static FnEnergyType queryEnergyTypeById(String energyTypeId){
		for(FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList){
			if(energyTypeId.equals(energyType.getId())){
				return energyType;
			}
		}
		return null;
	}
	
	public static String queryEnergyTypeNameById(String energyTypeId){
		for(FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList){
			if(energyTypeId.equals(energyType.getId())){
				return energyType.getName();
			}
		}
		return null;
	}
	
	public static String queryEnergyTypeIdByName(String energyTypeName){
		for(FnEnergyType energyType : ConstantDBBaseData.EnergyTypeList){
			if(energyTypeName.equals(energyType.getName())){
				return energyType.getId();
			}
		}
		return null;
	}
}

