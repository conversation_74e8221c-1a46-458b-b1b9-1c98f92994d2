package com.persagy.finein.core.init;

import com.pbsage.serial.util.AuthorizeThread;
import com.pbsage.serial.util.AuthorizeUtil;
import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.core.utils.CommonUtils;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.finein.core.constant.ConstantDBBaseData;
import com.persagy.finein.core.constant.RegisterConstant;
import com.persagy.finein.enumeration.EnumRegisterType;
import org.aiotCube.bean.EncryptResult;
import org.aiotCube.utils.RegKeyUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import p1.DogConstant;
import p1.DogLoader;
import p1.DogThread;

@Service("FineinInit_10_Register")
public class FineinInit_10_Register implements SystemInitService {

	private static final Logger log = Logger.getLogger(FineinInit_10_Register.class);

	@Override
	public void init() throws Exception {
		initSysParamValue();
		if(RegisterConstant.RegisterType == null){
			RegisterConstant.RegisterType = EnumRegisterType.Soft;
		}
		handle();
	}

	private void initSysParamValue() {
		try {
			String type = (String) ConstantDBBaseData.SysParamValueMap
					.get(FineinConstant.SysParamValueKey.Id_RegisterType);
			RegisterConstant.RegisterType = EnumRegisterType.getType(type);
		} catch (Exception e) {
		}
	}

	protected void handle() throws Exception {

		log.error("注册类型为："+RegisterConstant.RegisterType.getView());
		log.error("********RegisterType:"+RegisterConstant.RegisterType.getValue());
		if(RegisterConstant.RegisterType == EnumRegisterType.Dog){
			try {
				DogLoader.load();
				DogConstant.dogThread = new DogThread();
				DogConstant.dogThread.start();
				log.error("加密狗启动成功");
				log.error("********RegisterType success");
			} catch (Exception e) {
				e.printStackTrace();
				log.error(CommonUtils.getExceptionStackTrace(e));
			}
		}else if(RegisterConstant.RegisterType == EnumRegisterType.Soft){
			try {
				//AuthorizeUtil.authorizeThread = new AuthorizeThread();
				//AuthorizeUtil.authorizeThread.start();
				//修改加密为智方加密
				RegisterConstant.result = RegKeyUtils.checkEncKey();;
				if (RegisterConstant.result.isResult()){
					log.error("软件认证启动成功");
				} else {
					log.error("软件认证启动失败");
					log.error(RegisterConstant.result.getReason());
				}

			} catch (Exception e) {
				e.printStackTrace();
				log.error(CommonUtils.getExceptionStackTrace(e));
			}
		}
	}

	@Override
	public int order() {
		return 100 + 10;
	}
}
