package com.persagy.finein.core.init;

import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.communication.mina.udp.client.UDPClientManager;
import com.persagy.communication.util.IClientManager;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.ems.pojo.finein.FnProtocol;
import com.persagy.ems.pojo.finein.FnSysParamValue;
import com.persagy.finein.communication.util.FineinComConstant;
import com.persagy.finein.communication.util.UDPClientUtil;
import com.persagy.finein.enumeration.EnumCommunicationType;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumPrePayType;
import com.persagy.finein.service.FNMeterService;
import com.persagy.finein.service.FNProtocolService;
import com.persagy.finein.service.FNSysParamValueService;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("FineinInit_5_CommunitionInit")
public class FineinInit_5_CommunitionInit implements SystemInitService {

	private static final Logger log = Logger.getLogger(FineinInit_5_CommunitionInit.class);

	@Resource(name = "FNMeterService")
	private FNMeterService FNMeterService;

	@Resource(name = "FNProtocolService")
	private FNProtocolService FNProtocolService;

	@Resource(name = "FNSysParamValueService")
	private FNSysParamValueService FNSysParamValueService;
	
	
	private static boolean CommunitionInitIsOpen = false;
	
	@Override
	public void init() throws Exception {
		this.handle();
	}
	
	protected void handle() {
		try {
			//初始化系统参数
			this.process_sys_param_value();
			//初始化UDP客户端
			if(CommunitionInitIsOpen){
				this.process_udp();
				log.error("【租户管理】通讯初始化已完成");
			}else{
				log.error("【租户管理】通讯初始化未打开【系统参数:CommunitionInitIsOpen】");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private void process_sys_param_value() throws Exception{
		List<FnSysParamValue> sysPramValueList = FNSysParamValueService.query(new FnSysParamValue());
		Map<String,FnSysParamValue> sysParamMap = new HashMap<>();
		if(sysPramValueList != null && sysPramValueList.size() > 0){
			for(FnSysParamValue sysParamValue : sysPramValueList){
				sysParamMap.put(sysParamValue.getId(), sysParamValue);
			}
		}
		{
			FineinComConstant.DI_C_01_Y_Q_001.clientIp = sysParamMap.get(FineinConstant.SysParamValueKey.Id_XJMeterClientIp).getValue();
			FineinComConstant.DI_C_01_Y_Q_001.clientPort = Integer.parseInt(sysParamMap.get(FineinConstant.SysParamValueKey.Id_XJMeterClientPort).getValue());
			FineinComConstant.DI_C_01_Y_Q_001.serverIp = sysParamMap.get(FineinConstant.SysParamValueKey.Id_XJMeterServerIp).getValue();
			FineinComConstant.DI_C_01_Y_Q_001.serverPort = Integer.parseInt(sysParamMap.get(FineinConstant.SysParamValueKey.Id_XJMeterServerPort).getValue());
		}
		{
			try {
				CommunitionInitIsOpen = Boolean.parseBoolean(sysParamMap.get(FineinConstant.SysParamValueKey.Id_CommunitionInitIsOpen).getValue());
			} catch (Exception e) {
			}
		}
	}
	
	private void process_udp() throws Exception{
		List<FnMeter> meterList = FNMeterService.query(new FnMeter());
		if(meterList == null || meterList.size() == 0){
			log.info("***** 【租户管理】UDP 初始化连接，未找到仪表，不需要初始化   *****");
			return;
		}
		
		List<FnProtocol> protocolList = FNProtocolService.query(new FnProtocol());
		Map<String,FnProtocol> protocolMap = new HashMap<>();
		if(protocolList != null && protocolList.size() > 0){
			for(FnProtocol protocol : protocolList){
				protocolMap.put(protocol.getId(), protocol);
			}
		}
		
		Map<String,String> udpMap = new HashMap<>();
		
		for(FnMeter meter : meterList){
			FnProtocol protocol = protocolMap.get(meter.getProtocolId());
			if(protocol == null){
				log.error("***** 【租户管理】仪表协议不存在:"+meter.getId());
				continue;
			}
			
			if(protocol.getPayType().intValue() == EnumPayType.PREPAY.getValue()){//预付费
				if(protocol.getPrePayType().intValue() == EnumPrePayType.ONLINE_METERPAY.getValue()){
					//表充表扣
					if(meter.getClientIp() == null || "".equals(meter.getClientIp().trim())){
						log.error("***** 【租户管理】仪表客户端IP不能为空:"+meter.getId());
						continue;
					}
					if(meter.getClientPort() == null){
						log.error("***** 【租户管理】仪表客户端Port不能为空:"+meter.getId());
						continue;
					}
					if(meter.getServerIp() == null || "".equals(meter.getServerIp().trim())){
						log.error("***** 【租户管理】仪表服务端IP不能为空:"+meter.getId());
						continue;
					}
					if(meter.getServerPort() == null){
						log.error("***** 【租户管理】仪表服务端Port不能为空:"+meter.getId());
						continue;
					}
					
					String clientKey = meter.getClientIp().trim() + "_" + meter.getClientPort();
					String serverKey = meter.getServerIp().trim() + "_" + meter.getServerPort();
					
					if(udpMap.containsKey(clientKey)){
						if(udpMap.get(clientKey).equals(serverKey)){
							continue;
						}else{
							log.error("***** 【租户管理】仪表客户端对应了不同的服务端:"+clientKey);
							continue;
						}
					}else{
						udpMap.put(clientKey, serverKey);
					}
				}
			}else{
				if(meter.getCommunicationType().intValue() == EnumCommunicationType.Serial.getValue()){
					if(meter.getClientIp() == null || "".equals(meter.getClientIp().trim())){
						log.error("***** 【租户管理】串口方式仪表客户端IP不能为空:"+meter.getId());
						continue;
					}
					if(meter.getClientPort() == null){
						log.error("***** 【租户管理】串口方式仪表客户端Port不能为空:"+meter.getId());
						continue;
					}
					if(meter.getServerIp() == null || "".equals(meter.getServerIp().trim())){
						log.error("***** 【租户管理】串口方式仪表服务端IP不能为空:"+meter.getId());
						continue;
					}
					if(meter.getServerPort() == null){
						log.error("***** 【租户管理】串口方式仪表服务端Port不能为空:"+meter.getId());
						continue;
					}
					
					String clientKey = meter.getClientIp().trim() + "_" + meter.getClientPort();
					String serverKey = meter.getServerIp().trim() + "_" + meter.getServerPort();
					
					if(udpMap.containsKey(clientKey)){
						if(udpMap.get(clientKey).equals(serverKey)){
							continue;
						}else{
							log.error("***** 【租户管理】串口方式仪表客户端对应了不同的服务端:"+clientKey);
							continue;
						}
					}else{
						udpMap.put(clientKey, serverKey);
					}
				}
			}
		}
		
		for(Map.Entry<String, String> entry : udpMap.entrySet()){
			String[] client = entry.getKey().split("_");
			String[] server = entry.getValue().split("_");
			IClientManager clientManager = new UDPClientManager(client[0], Integer.parseInt(client[1]), server[0], Integer.parseInt(server[1]), null, 1000);
			clientManager.Start();
			log.info("***** 【租户管理】UDP初始化完成,客户端:"+entry.getKey()+",服务端:"+entry.getValue());
			UDPClientUtil.ClientManagerMap.put(entry.getKey(), clientManager);
		}
	}

	@Override
	public int order() {
		return 100 + 5;
	}

}
