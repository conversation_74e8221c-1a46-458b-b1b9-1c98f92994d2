package com.persagy.ems.pojo.ac.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.ac.AcSystemRolePermission;


@Dimension
@Entity(name = "AcSystemRolePermissionSqlite")
@Table(name = "t_ac_system_role_permission", comment = "通用系统角色权限", schema = Schema.NONE, indexes = {

})
public class AcSystemRolePermissionSqlite extends  AcSystemRolePermission{

	private static final long serialVersionUID = -520102591915273804L;
}