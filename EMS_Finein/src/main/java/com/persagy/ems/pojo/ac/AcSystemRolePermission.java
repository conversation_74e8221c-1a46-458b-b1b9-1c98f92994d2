package com.persagy.ems.pojo.ac;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;


@Dimension
@Entity(name = "AcSystemRolePermission")
@Table(name = "t_ac_system_role_permission", comment = "角色权限关系", schema = Schema.EMS, 
	indexes = {
			@Index(columns = { "c_permission_id", "c_role_id" }, unique = true) 
		 }
)
public class AcSystemRolePermission extends BusinessObject {

	private static final long serialVersionUID = 1;
	

	@Id
	@Column(order = 1, name = "c_id", length = 36, nullable = false, comment = "主键")
	@JsonProperty("id")
	private String id;


	@Column(order = 2, name = "c_role_id", length = 36, nullable = false, comment = "角色主键")
	@JsonProperty("roleId")
	private String roleId;


	@Column(order = 3, name = "c_permission_id", length = 36, nullable = false, comment = "权限主键")
	@JsonProperty("permissionId")
	private String permissionId;


	public void setId(String id){
		this.id = id;
	}

	public String getId(){
		return this.id;
	}

	public void setRoleId(String roleId){
		this.roleId = roleId;
	}

	public String getRoleId(){
		return this.roleId;
	}

	public void setPermissionId(String permissionId){
		this.permissionId = permissionId;
	}

	public String getPermissionId(){
		return this.permissionId;
	}

}