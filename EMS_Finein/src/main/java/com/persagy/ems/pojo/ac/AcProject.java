package com.persagy.ems.pojo.ac;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;


@Dimension
@Entity(name = "AcProject")
@Table(name = "t_ac_project", comment = "项目", schema = Schema.EMS, 
	indexes = {
		 }
)
public class AcProject extends BusinessObject {

	private static final long serialVersionUID = 1;
	

	@Id
	@Column(order = 1, name = "c_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("id")
	private String id;


	@Column(order = 2, name = "c_name", length = 50, nullable = false, comment = "建筑名称")
	@JsonProperty("name")
	private String name;


	@Column(order = 3, name = "c_address", length = 200, nullable = true, comment = "建筑位置")
	@JsonProperty("address")
	private String address;


	@Column(order = 4, name = "c_remark", length = 50, nullable = true, comment = "备注")
	@JsonProperty("remark")
	private String remark;


	public void setId(String id){
		this.id = id;
	}

	public String getId(){
		return this.id;
	}

	public void setName(String name){
		this.name = name;
	}

	public String getName(){
		return this.name;
	}

	public void setAddress(String address){
		this.address = address;
	}

	public String getAddress(){
		return this.address;
	}

	public void setRemark(String remark){
		this.remark = remark;
	}

	public String getRemark(){
		return this.remark;
	}

}