package com.persagy.ems.pojo.ac;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;


@Dimension
@Entity(name = "AcSystemRole")
@Table(name = "t_ac_system_role", comment = "系统角色", schema = Schema.EMS, 
	indexes = {

		 }
)
public class AcSystemRole extends BusinessObject {

	private static final long serialVersionUID = 1;
	

	@Id
	@Column(order = 1, name = "c_id", length = 36, nullable = false, comment = "主键")
	@JsonProperty("id")
	private String id;


	@Column(order = 2, name = "c_product_id", length = 20, nullable = false, comment = "产品编码")
	@JsonProperty("productId")
	private String productId;


	@Column(order = 3, name = "c_name", length = 20, nullable = false, comment = "名称")
	@JsonProperty("name")
	private String name;


	@Column(order = 4, name = "c_description", length = 100, nullable = true, comment = "描述")
	@JsonProperty("description")
	private String description;


	@Column(order = 5, name = "c_order_by", length = 5, nullable = false, comment = "排序")
	@JsonProperty("orderBy")
	private Integer orderBy;


	public void setId(String id){
		this.id = id;
	}

	public String getId(){
		return this.id;
	}

	public void setProductId(String productId){
		this.productId = productId;
	}

	public String getProductId(){
		return this.productId;
	}

	public void setName(String name){
		this.name = name;
	}

	public String getName(){
		return this.name;
	}

	public void setDescription(String description){
		this.description = description;
	}

	public String getDescription(){
		return this.description;
	}

	public void setOrderBy(Integer orderBy){
		this.orderBy = orderBy;
	}

	public Integer getOrderBy(){
		return this.orderBy;
	}

}