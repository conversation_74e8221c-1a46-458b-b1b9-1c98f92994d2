package com.persagy.ems.pojo.ac.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.ac.AcSystemFunction;


@Dimension
@Entity(name = "AcSystemFunctionSqlite")
@Table(name = "t_ac_system_function", comment = "通用系统角色功能", schema = Schema.NONE, indexes = {

})
public class AcSystemFunctionSqlite extends AcSystemFunction {


	private static final long serialVersionUID = 7616125277363690910L;
	
}