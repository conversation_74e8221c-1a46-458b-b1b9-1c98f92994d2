package com.persagy.ems.pojo.ac;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "AcSSOSSystemParam")
@Table(name = "t_ac_ssos_system_param", comment = "单点登录系统参数", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_system_code" }, unique = true) })
public class AcSSOSSystemParam extends BaseId {

	private static final long serialVersionUID = -8278271005669229219L;

	@Column(order = 1, name = "c_system_code", length = 36, nullable = false, comment = "系统编码")
	@JsonProperty("systemCode")
	private String systemCode;

	@Column(order = 2, name = "c_system_name", length = 50, nullable = false, comment = "系统名称")
	@JsonProperty("systemName")
	private String systemName;

	@Column(order = 3, name = "c_encryption_key", length = 36, nullable = false, comment = "系统秘钥")
	@JsonProperty("encryptionKey")
	private String encryptionKey;

	@Column(order = 4, name = "c_is_valid", length = 1, nullable = false, comment = "是否啟用")
	@JsonProperty("isValid")
	private Integer isValid;

	public String getSystemCode() {
		return systemCode;
	}

	public void setSystemCode(String systemCode) {
		this.systemCode = systemCode;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getEncryptionKey() {
		return encryptionKey;
	}

	public void setEncryptionKey(String encryptionKey) {
		this.encryptionKey = encryptionKey;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}
}
