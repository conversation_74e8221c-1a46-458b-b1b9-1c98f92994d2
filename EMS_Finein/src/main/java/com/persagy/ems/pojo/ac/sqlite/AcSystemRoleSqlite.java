package com.persagy.ems.pojo.ac.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.ac.AcSystemRole;


@Dimension
@Entity(name = "AcSystemRoleSqlite")
@Table(name = "t_ac_system_role", comment = "通用系统角色", schema = Schema.NONE, indexes = {

})
public class AcSystemRoleSqlite extends  AcSystemRole{

	private static final long serialVersionUID = -7016281069211381811L;
}