package com.persagy.ems.pojo.ac.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.ac.AcSystemUser;


@Dimension
@Entity(name = "AcSystemUserSqlite")
@Table(name = "t_ac_system_user", comment = "通用系统用户", schema = Schema.NONE, indexes = {

})
public class AcSystemUserSqlite extends  AcSystemUser{

	private static final long serialVersionUID = -6175105611704668681L;

}