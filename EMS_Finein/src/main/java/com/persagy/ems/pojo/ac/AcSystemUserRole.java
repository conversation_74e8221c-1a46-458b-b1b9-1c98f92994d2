package com.persagy.ems.pojo.ac;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

@Dimension
@Entity(name = "AcSystemUserRole")
@Table(name = "t_ac_system_user_role", comment = "用户角色关系", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_user_id", "c_role_id" }, unique = true) })
public class AcSystemUserRole extends BusinessObject {

	private static final long serialVersionUID = 1;

	@Id
	@Column(order = 1, name = "c_id", length = 36, nullable = false, comment = "主键")
	@JsonProperty("id")
	private String id;

	@Column(order = 2, name = "c_user_id", length = 36, nullable = false, comment = "用户主键")
	@JsonProperty("userId")
	private String userId;

	@Column(order = 3, name = "c_role_id", length = 36, nullable = false, comment = "角色主键")
	@JsonProperty("roleId")
	private String roleId;

	public void setId(String id) {
		this.id = id;
	}

	public String getId() {
		return this.id;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserId() {
		return this.userId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getRoleId() {
		return this.roleId;
	}

}