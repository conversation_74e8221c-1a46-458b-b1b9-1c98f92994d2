package com.persagy.ems.pojo.ac;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;


@Dimension
@Entity(name = "AcBuildingProperty")
@Table(name = "t_ac_building_property", comment = "建筑属性", schema = Schema.EMS, 
	indexes = {
				
		 }
)
public class AcBuildingProperty extends BusinessObject {

	private static final long serialVersionUID = 1;
	

	@Id
	@Column(order = 1, name = "c_id", length = 50, nullable = false, comment = "建筑属性主键")
	@JsonProperty("id")
	private String id;


	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;


	@Column(order = 3, name = "c_property_name", length = 50, nullable = false, comment = "建筑属性编码")
	@JsonProperty("propertyName")
	private String propertyName;


	@Column(order = 4, name = "c_property_value", length = 2000, nullable = true, comment = "建筑属性值")
	@JsonProperty("propertyValue")
	private String propertyValue;


	public void setId(String id){
		this.id = id;
	}

	public String getId(){
		return this.id;
	}

	public void setBuildingId(String buildingId){
		this.buildingId = buildingId;
	}

	public String getBuildingId(){
		return this.buildingId;
	}

	public void setPropertyName(String propertyName){
		this.propertyName = propertyName;
	}

	public String getPropertyName(){
		return this.propertyName;
	}

	public void setPropertyValue(String propertyValue){
		this.propertyValue = propertyValue;
	}

	public String getPropertyValue(){
		return this.propertyValue;
	}

}