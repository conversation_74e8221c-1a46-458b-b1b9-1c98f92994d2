package com.persagy.ems.pojo.ac.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.ac.AcSSOSSystemParam;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月26日 下午5:15:45
 * 
 * 说明:
 */

@Dimension
@Entity(name = "AcSSOSSystemParamSqlite")
@Table(name = "t_ac_ssos_system_param", comment = "单点登录系统参数", schema = Schema.NONE, indexes = {

})
public class AcSSOSSystemParamSqlite extends AcSSOSSystemParam {

	private static final long serialVersionUID = 2960655121441515919L;

}
