package com.persagy.ems.pojo.ac;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

@Dimension
@Entity(name = "AcSystemRoleFunction")
@Table(name = "t_ac_system_role_function", comment = "角色功能关系", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_function_id", "c_role_id" }, unique = true) })
public class AcSystemRoleFunction extends BusinessObject {

	private static final long serialVersionUID = 1;

	@Id
	@Column(order = 1, name = "c_id", length = 36, nullable = false, comment = "主键")
	@JsonProperty("id")
	private String id;

	@Column(order = 2, name = "c_role_id", length = 36, nullable = false, comment = "角色主键")
	@JsonProperty("roleId")
	private String roleId;

	@Column(order = 3, name = "c_function_id", length = 36, nullable = false, comment = "功能主键")
	@JsonProperty("functionId")
	private String functionId;

	public void setId(String id) {
		this.id = id;
	}

	public String getId() {
		return this.id;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public String getRoleId() {
		return this.roleId;
	}

	public void setFunctionId(String functionId) {
		this.functionId = functionId;
	}

	public String getFunctionId() {
		return this.functionId;
	}

}