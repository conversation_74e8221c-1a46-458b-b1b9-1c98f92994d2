package com.persagy.ems.pojo.ac;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

@Dimension
@Entity(name = "AcSystemUserPermission")
@Table(name = "t_ac_system_user_permission", comment = "用户权限关系", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_permission_id", "c_user_id" }, unique = true) })
public class AcSystemUserPermission extends BusinessObject {

	private static final long serialVersionUID = -5102121502067104552L;

	@Id
	@Column(order = 1, name = "c_id", length = 36, nullable = false, comment = "主键")
	@JsonProperty("id")
	private String id;

	@Column(order = 2, name = "c_user_id", length = 36, nullable = false, comment = "用户主键")
	@JsonProperty("userId")
	private String userId;

	@Column(order = 3, name = "c_permission_id", length = 36, nullable = false, comment = "权限主键")
	@JsonProperty("permissionId")
	private String permissionId;

	public void setId(String id) {
		this.id = id;
	}

	public String getId() {
		return this.id;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public void setPermissionId(String permissionId) {
		this.permissionId = permissionId;
	}

	public String getPermissionId() {
		return this.permissionId;
	}

}