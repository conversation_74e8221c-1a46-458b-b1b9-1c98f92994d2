package com.persagy.ems.pojo.ac.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.ac.AcSystemRoleFunction;


@Dimension
@Entity(name = "AcSystemRoleFunctionSqlite")
@Table(name = "t_ac_system_role_function", comment = "通用系统角色功能", schema = Schema.NONE, indexes = {

})
public class AcSystemRoleFunctionSqlite extends  AcSystemRoleFunction{

	private static final long serialVersionUID = -4849756929566904696L;

}