package com.persagy.ems.pojo.ac.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.ac.AcSystemPermission;


@Dimension
@Entity(name = "AcSystemPermissionSqlite")
@Table(name = "t_ac_system_permission", comment = "通用系统权限", schema = Schema.NONE, indexes = {

})
public class AcSystemPermissionSqlite extends  AcSystemPermission{

	private static final long serialVersionUID = -3995583691437282778L;
	
}