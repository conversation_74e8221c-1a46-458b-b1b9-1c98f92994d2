package com.persagy.ems.pojo.ac;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;


@Dimension
@Entity(name = "AcSystemUser")
@Table(name = "t_ac_system_user", comment = "用户", schema = Schema.EMS, 
	indexes = {
			@Index(columns = { "c_name"}, unique = true) 
		 }
)
public class AcSystemUser extends BusinessObject {

	private static final long serialVersionUID = 1;
	

	@Id
	@Column(order = 1, name = "c_id", length = 36, nullable = false, comment = "账号")
	@JsonProperty("id")
	private String id;


	@Column(order = 2, name = "c_name", length = 20, nullable = false, comment = "登录名")
	@JsonProperty("name")
	private String name;


	@Column(order = 3, name = "c_show_name", length = 30, nullable = false, comment = "显示名")
	@JsonProperty("showName")
	private String showName;


	@Column(order = 4, name = "c_password", length = 50, nullable = false, comment = "密码")
	@JsonProperty("password")
	private String password;


	@Column(order = 5, name = "c_mobile", length = 20, nullable = true, comment = "电话")
	@JsonProperty("mobile")
	private String mobile;


	@Column(order = 6, name = "c_email", length = 100, nullable = true, comment = "邮箱")
	@JsonProperty("email")
	private String email;


	@Column(order = 7, name = "c_valid", length = 20, nullable = false, comment = "是否有效")
	@JsonProperty("valid")
	private Integer valid;


	public void setId(String id){
		this.id = id;
	}

	public String getId(){
		return this.id;
	}

	public void setName(String name){
		this.name = name;
	}

	public String getName(){
		return this.name;
	}

	public void setShowName(String showName){
		this.showName = showName;
	}

	public String getShowName(){
		return this.showName;
	}

	public void setPassword(String password){
		this.password = password;
	}

	public String getPassword(){
		return this.password;
	}

	public void setMobile(String mobile){
		this.mobile = mobile;
	}

	public String getMobile(){
		return this.mobile;
	}

	public void setEmail(String email){
		this.email = email;
	}

	public String getEmail(){
		return this.email;
	}

	public void setValid(Integer valid){
		this.valid = valid;
	}

	public Integer getValid(){
		return this.valid;
	}

}