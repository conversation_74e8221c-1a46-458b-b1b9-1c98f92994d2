package com.persagy.ems.pojo.ac;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;


@Dimension
@Entity(name = "AcSystemFunction")
@Table(name = "t_ac_system_function", comment = "系统功能", schema = Schema.EMS, 
	indexes = {
				
		 }
)
public class AcSystemFunction extends BusinessObject {

	private static final long serialVersionUID = 1;
	

	@Id
	@Column(order = 1, name = "c_id", length = 36, nullable = false, comment = "主键")
	@JsonProperty("id")
	private String id;


	@Column(order = 2, name = "c_product_id", length = 20, nullable = false, comment = "产品编码")
	@JsonProperty("productId")
	private String productId;


	@Column(order = 3, name = "c_name", length = 20, nullable = false, comment = "名称")
	@JsonProperty("name")
	private String name;


	@Column(order = 4, name = "c_icon", length = 30, nullable = true, comment = "图标")
	@JsonProperty("icon")
	private String icon;


	@Column(order = 5, name = "c_url", length = 200, nullable = true, comment = "链接")
	@JsonProperty("url")
	private String url;


	@Column(order = 6, name = "c_order_by", length = 5, nullable = false, comment = "排序")
	@JsonProperty("orderBy")
	private Integer orderBy;


	@Column(order = 7, name = "c_valid", length = 1, nullable = false, comment = "是否可用")
	@JsonProperty("valid")
	private Integer valid;
	
	@Column(order = 8, name = "c_is_parent", length = 1, nullable = false, comment = "是否为父功能")
	@JsonProperty("isParent")
	private Integer isParent;
	
	@Column(order = 9, name = "c_parent_id", length = 36, nullable = true, comment = "父功能主键")
	@JsonProperty("parentId")
	private String parentId;


	public void setId(String id){
		this.id = id;
	}

	public String getId(){
		return this.id;
	}

	public void setProductId(String productId){
		this.productId = productId;
	}

	public String getProductId(){
		return this.productId;
	}

	public void setName(String name){
		this.name = name;
	}

	public String getName(){
		return this.name;
	}

	public void setIcon(String icon){
		this.icon = icon;
	}

	public String getIcon(){
		return this.icon;
	}

	public void setUrl(String url){
		this.url = url;
	}

	public String getUrl(){
		return this.url;
	}

	public void setOrderBy(Integer orderBy){
		this.orderBy = orderBy;
	}

	public Integer getOrderBy(){
		return this.orderBy;
	}

	public void setValid(Integer valid){
		this.valid = valid;
	}

	public Integer getValid(){
		return this.valid;
	}

	public Integer getIsParent() {
		return isParent;
	}

	public void setIsParent(Integer isParent) {
		this.isParent = isParent;
	}

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

}