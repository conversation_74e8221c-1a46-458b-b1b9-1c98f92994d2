package com.persagy.ems.pojo.ac;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;


@Dimension
@Entity(name = "AcProjectProperty")
@Table(name = "t_ac_project_property", comment = "项目属性", schema = Schema.EMS, 
	indexes = {
				
		 }
)
public class AcProjectProperty extends BusinessObject {

	private static final long serialVersionUID = 1;
	

	@Id
	@Column(order = 1, name = "c_id", length = 50, nullable = false, comment = "项目属性主键")
	@JsonProperty("id")
	private String id;


	@Column(order = 2, name = "c_project_id", length = 20, nullable = false, comment = "项目编码")
	@JsonProperty("projectId")
	private String projectId;


	@Column(order = 3, name = "c_property_name", length = 50, nullable = false, comment = "项目属性编码")
	@JsonProperty("propertyName")
	private String propertyName;


	@Column(order = 4, name = "c_property_value", length = 2000, nullable = true, comment = "项目属性值")
	@JsonProperty("propertyValue")
	private String propertyValue;


	public void setId(String id){
		this.id = id;
	}

	public String getId(){
		return this.id;
	}

	public void setProjectId(String projectId){
		this.projectId = projectId;
	}

	public String getProjectId(){
		return this.projectId;
	}

	public void setPropertyName(String propertyName){
		this.propertyName = propertyName;
	}

	public String getPropertyName(){
		return this.propertyName;
	}

	public void setPropertyValue(String propertyValue){
		this.propertyValue = propertyValue;
	}

	public String getPropertyValue(){
		return this.propertyValue;
	}

}