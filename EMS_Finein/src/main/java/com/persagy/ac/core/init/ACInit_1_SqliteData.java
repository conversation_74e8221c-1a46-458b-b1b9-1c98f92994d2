package com.persagy.ac.core.init;

import com.persagy.business.service.logic.init.SystemInitService;
import com.persagy.core.mvc.dao.CoreDao;
import com.persagy.core.mvc.pojo.BusinessObject;
import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.pojo.ac.*;
import com.persagy.ems.pojo.ac.sqlite.*;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("ACInit_1_SqliteData")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class ACInit_1_SqliteData implements SystemInitService {

	private static final Logger log = Logger.getLogger(ACInit_1_SqliteData.class);

	@Resource(name = "SqliteDao")
	private CoreDao sqliteDao;

	@Resource(name = "coreService")
	private CoreService coreService;

	@Override
	public void init() throws Exception {

		this.handle(AcSystemFunctionSqlite.class, AcSystemFunction.class, false);
		this.handle(AcSystemPermissionSqlite.class, AcSystemPermission.class, false);
		this.handle(AcSystemRoleFunctionSqlite.class, AcSystemRoleFunction.class, false);
		this.handle(AcSystemRolePermissionSqlite.class, AcSystemRolePermission.class, false);
		this.handle(AcSystemRoleSqlite.class, AcSystemRole.class, false);
		this.handle(AcSystemUserSqlite.class, AcSystemUser.class, false);
		this.handle(AcSSOSSystemParamSqlite.class, AcSSOSSystemParam.class, false);
	}

	@SuppressWarnings({ "unchecked" })
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	private void handle(Class<? extends BusinessObject> classSqlite, Class<? extends BusinessObject> classBusiness,
			boolean isRemove) throws Exception {
		if (isRemove) {
			coreService.remove(classBusiness.newInstance());
			List<BusinessObject> srcList = (List<BusinessObject>) sqliteDao.query(classSqlite.newInstance());
			if (srcList != null && srcList.size() > 0) {
				for (BusinessObject src : srcList) {
					BusinessObject businessObject = classBusiness.newInstance();
					BeanUtils.copyProperties(businessObject, src);
					coreService.save(businessObject);
				}
			}
		} else {
			List<BusinessObject> destList = (List<BusinessObject>) coreService.query(classBusiness.newInstance());
			Map<String, BusinessObject> destMap = new HashMap<>();
			if (destList != null) {
				for (BusinessObject dest : destList) {
					String id = getIdValue(dest);
					if (id != null) {
						destMap.put(id, dest);
					}
				}
			}

			List<BusinessObject> srcList = (List<BusinessObject>) sqliteDao.query(classSqlite.newInstance());
			if (srcList != null && srcList.size() > 0) {
				for (BusinessObject src : srcList) {
					String id = getIdValue(src);
					if (id != null) {
						if (!destMap.containsKey(id)) {
							BusinessObject businessObject = classBusiness.newInstance();
							BeanUtils.copyProperties(businessObject, src);
							coreService.save(businessObject);
							log.info(">>>>>AC初始化数据:" + classBusiness.getName() + "插入:" + id);
						}
					}
				}
			}
		}
		log.info(">>>>>AC初始化数据:" + classBusiness.getName());
	}

	@Override
	public int order() {
		return 30 + 2;
	}

	private String getIdValue(BusinessObject businessObject) {
		try {
			Method m = businessObject.getClass().getMethod("getId");
			if (m != null) {
				return (String) m.invoke(businessObject);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
}
