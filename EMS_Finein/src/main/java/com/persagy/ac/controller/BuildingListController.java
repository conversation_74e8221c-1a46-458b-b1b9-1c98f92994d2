package com.persagy.ac.controller;

import com.persagy.ac.service.AcProjectService;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.ac.AcProject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 建筑-列表
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class BuildingListController extends BaseController {

    @Resource(name = "AcProjectService")
    private AcProjectService acProjectService;

    @RequestMapping("BuildingListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult buildingList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            List<Object> arrayList = new ArrayList<Object>();

            List<AcProject> list = acProjectService.query(new AcProject());
            for (int i = 0; i < list.size(); i++) {
                AcProject project = list.get(i);
                Map<String,Object> buildingMap = new HashMap<String,Object>();
                Map<String,Object> map = new HashMap<String,Object>();
                if(i==0){
                    buildingMap.put("isMain", true);
                }else{
                    buildingMap.put("isMain", false);
                }
                buildingMap.put("building", map);
                map.put("id", project.getId());
                map.put("name", project.getName());
                map.put("address", project.getAddress());
                map.put("remark", project.getRemark());
                arrayList.add(buildingMap);
            }
            content.add(arrayList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"BuildingListService");
        }
    }
}
