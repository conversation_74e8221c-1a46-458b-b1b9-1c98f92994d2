package com.persagy.ac.controller;

import com.persagy.ac.service.AcProjectService;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.ac.AcProject;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class AcProjectController extends BaseController {

    @Resource(name = "AcProjectService")
    private AcProjectService acProjectService;

    /**
     * 项目 —— 添加
     * @param jsonString
     * @return
     */
    @RequestMapping("ACProjectAddService")
    @ResponseBody
    public InterfaceResult projectAdd(@RequestParam(value="jsonString") String jsonString) throws Exception {
        AcProject query = new AcProject();
        List<AcProject> result=acProjectService.queryList(query);
        if (result.size()==0){
            try {
                Map dto = JsonStringUtil.jsonStringToMap(jsonString);
                List content = new ArrayList();
                String id = (String) dto.get("id");
                String name = (String) dto.get("name");
                String address = (String) dto.get("address");
                String remark = (String) dto.get("remark");

                if(id == null || name == null || address == null){
                    throw new Exception("参数不能为空！");
                }
                query.setId(id);
                query.setName(name);
                query.setAddress(address);
                query.setRemark(remark);

                acProjectService.addProject(query);
                return Result.SUCCESS(content);
            }catch (Exception e){
                e.printStackTrace();
                return Result.FAILURE(e,jsonString,"ACProjectAddService");
            }
        }
        return null;


    }

    /**
     * 项目 —— 删除
     * @param jsonString
     * @return
     */
    @RequestMapping("ACProjectDeleteService")
    @ResponseBody
    public InterfaceResult projectDelete(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");

            if(id == null){
                throw new Exception("参数不能为空！");
            }

            AcProject query = new AcProject();
            query.setId(id);

            acProjectService.remove(query);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACProjectDeleteService");
        }
    }

    /**
     * 项目 —— 详情
     * @param jsonString
     * @return
     */
    @RequestMapping("ACProjectDetailService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult projectDetail(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");

            if(id == null){
                throw new Exception("参数不能为空！");
            }

            Map<String,Object> projectMap = new HashMap<String,Object>();
            AcProject query = new AcProject();
            query.setId(id);

            List<AcProject> queryList = acProjectService.queryList(query);

            if( queryList != null && queryList.size() > 0){
                projectMap.put("id", queryList.get(0).getId());
                projectMap.put("name", queryList.get(0).getName());
                projectMap.put("address", queryList.get(0).getAddress());
                projectMap.put("remark", queryList.get(0).getRemark());
            }

            content.add(projectMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACProjectDetailService");
        }
    }

    /**
     * 项目 —— 列表
     * @param jsonString
     * @return
     */
    @RequestMapping("ACProjectListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult projectList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Map<String,Object> contentMap = new HashMap<String,Object>();
            List<Object> dataList = new ArrayList<Object>();
            contentMap.put("dataList", dataList);

            List<AcProject> list = acProjectService.queryList(new AcProject());
            for (AcProject acProject : list) {
                Map<String,Object> projectMap = new HashMap<String,Object>();
                projectMap.put("id", acProject.getId());
                projectMap.put("name", acProject.getName());
                projectMap.put("address", acProject.getAddress());
                projectMap.put("remark", acProject.getRemark());
                dataList.add(projectMap);
            }
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACProjectListService");
        }
    }

    /**
     * 项目 —— 更新
     * @param jsonString
     * @return
     */
    @RequestMapping("ACProjectUpdateService")
    @ResponseBody
    public InterfaceResult projectUpdate(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");
            String newId = (String) dto.get("id");
            String name = (String) dto.get("name");
            String address = (String) dto.get("address");
            String remark = (String) dto.get("remark");

            if(id == null || name == null || address == null ){
                throw new Exception("参数不能为空！");
            }

            AcProject query = new AcProject();
            query.setId(id);

            AcProject update = new AcProject();
            if(newId != null && !"".equals(newId.trim()) && !newId.equals(id)){
                update.setId(newId);
            }
            update.setName(name);
            update.setAddress(address);
            update.setRemark(remark);

            acProjectService.update(query, update);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACProjectUpdateService");
        }
    }
}
