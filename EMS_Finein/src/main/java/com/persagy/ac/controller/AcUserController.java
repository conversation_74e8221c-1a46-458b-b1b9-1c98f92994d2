package com.persagy.ac.controller;

import com.persagy.ac.service.*;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.CommonUtils;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.ac.*;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

/**
 * 用户
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class AcUserController extends BaseController {

    @Resource(name = "AcSystemUserService")
    private AcSystemUserService acSystemUserService;

    @Resource(name = "AcSystemUserFunctionService")
    private AcSystemUserFunctionService acSystemUserFunctionService;

    @Resource(name = "AcSystemUserPermissionService")
    private AcSystemUserPermissionService acSystemUserPermissionService;

    @Resource(name = "AcSystemUserRoleService")
    private AcSystemUserRoleService acSystemUserRoleService;

    @Resource(name = "AcSystemFunctionService")
    private AcSystemFunctionService acSystemFunctionService;

    @Resource(name = "AcSystemPermissionService")
    private AcSystemPermissionService acSystemPermissionService;

    @Resource(name = "AcSystemRoleFunctionService")
    private AcSystemRoleFunctionService acSystemRoleFunctionService;

    @Resource(name = "AcSystemRolePermissionService")
    private AcSystemRolePermissionService acSystemRolePermissionService;


    /**
     * 用户 —— 新建
     * @param jsonString
     * @return
     */
    @RequestMapping("ACUserAddService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult userAdd(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String name = (String) dto.get("name");
            String showName = (String) dto.get("showName");
            String mobile = (String) dto.get("mobile");
            String email = (String) dto.get("email");
            List<String> functionList = (List<String>) dto.get("functionList");
            List<String> permissionList = (List<String>) dto.get("permissionList");

            if (name == null) {
                throw new Exception("参数不能为空！");
            }
            String userId = UUID.randomUUID().toString();
            AcSystemUser query = new AcSystemUser();
            query.setName(name);
            List<AcSystemUser> list = acSystemUserService.query(query);
            if (list != null && list.size() > 0) {
                throw new Exception("用户名已存在：" + name);
            }
            query.setId(userId);
            if (showName == null) {
                showName = name;
            }
            query.setShowName(showName);
            query.setPassword(CommonUtils.md5("123456"));
            query.setMobile(mobile);
            query.setEmail(email);
            query.setValid(1);
            acSystemUserService.addUser(query);

            List<AcSystemUserFunction> saveFunctionList = new ArrayList<AcSystemUserFunction>();
            if (functionList != null) {
                for (String functionId : functionList) {
                    AcSystemUserFunction save = new AcSystemUserFunction();
                    save.setId(UUID.randomUUID().toString());
                    save.setFunctionId(functionId);
                    save.setUserId(userId);
                    saveFunctionList.add(save);
                }
            }
            List<AcSystemUserPermission> savePermissionList = new ArrayList<AcSystemUserPermission>();
            if (permissionList != null) {
                for (String permissionId : permissionList) {
                    AcSystemUserPermission save = new AcSystemUserPermission();
                    save.setId(UUID.randomUUID().toString());
                    save.setPermissionId(permissionId);
                    save.setUserId(userId);
                    savePermissionList.add(save);
                }
            }
            acSystemUserPermissionService.save(savePermissionList);
            acSystemUserFunctionService.save(saveFunctionList);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACUserAddService");
        }
    }

    /**
     * 用户 —— 删除
     * @param jsonString
     * @return
     */
    @RequestMapping("ACUserDeleteService")
    @ResponseBody
    public InterfaceResult userDelete(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");

            if(id == null){
                throw new Exception("参数不能为空！");
            }

            AcSystemUser query = new AcSystemUser();
            query.setId(id);
            acSystemUserService.remove(query);
            AcSystemUserRole userRole = new AcSystemUserRole();
            userRole.setUserId(id);

            acSystemUserRoleService.remove(userRole);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACUserDeleteService");
        }
    }

    /**
     * 用户 —— 详情
     * @param jsonString
     * @return
     */
    @RequestMapping("ACUserDetailService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult userDetail(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");
            if (id == null) {
                throw new Exception("参数不能为空！");
            }

            Map<String, Object> userMap = new HashMap<String, Object>();
            AcSystemUser query = new AcSystemUser();
            query.setId(id);
            List<AcSystemUser> queryList = acSystemUserService.queryList(query);
            if (queryList == null || queryList.size() == 0) {
                throw new Exception("用户不存在");
            }
            List<AcSystemUserFunction> functionList = acSystemUserFunctionService.queryFunctionByUserId(id);
            StringBuffer functions = new StringBuffer();
            if (functionList != null) {
                Map<String, AcSystemFunction> functionMap = acSystemFunctionService.queryMap();
                for (AcSystemUserFunction acSystemUserFunction : functionList) {

                    AcSystemFunction acSystemFunction = functionMap.get(acSystemUserFunction.getFunctionId());
                    if (acSystemFunction != null) {
                        if (functions.length() != 0) {
                            functions.append(",");
                        }
                        functions.append(acSystemFunction.getName());
                    }
                }
            }
            List<AcSystemUserPermission> permissionList = acSystemUserPermissionService.queryPermissionByUserId(id);
            StringBuffer permissions = new StringBuffer();
            if (permissionList != null) {
                Map<String, AcSystemPermission> permissionMap = acSystemPermissionService.queryMap();
                for (AcSystemUserPermission acSystemUserPermission : permissionList) {

                    AcSystemPermission acSystemPermission = permissionMap.get(acSystemUserPermission.getPermissionId());
                    if (acSystemPermission != null) {
                        if (permissions.length() != 0) {
                            permissions.append(",");
                        }
                        permissions.append(acSystemPermission.getName());
                    }
                }
            }
            {
                userMap.put("id", queryList.get(0).getId());
                userMap.put("name", queryList.get(0).getName());
                userMap.put("showName", queryList.get(0).getShowName());
                userMap.put("mobile", queryList.get(0).getMobile());
                userMap.put("email", queryList.get(0).getEmail());
                userMap.put("permissions", permissions.toString());
                userMap.put("functions", functions.toString());
                content.add(userMap);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACUserDetailService");
        }
    }

    /**
     * 用户 —— 判断用户是否存在
     * @param jsonString
     * @return
     */
    @RequestMapping("ACUserExistsService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult userExists(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String name = (String)dto.get("name");
            String email = (String)dto.get("email");
            if((name==null || "".equals(name)) && (email==null || "".equals(email))){
                throw new Exception("接口只接受非空的用户名和邮箱");
            }

            if(name!=null && !"".equals(name)){
                AcSystemUser user = new AcSystemUser();
                user.setName(name);
                user.setValid(1);
                if(coreDao.query(user).size()>0){
                    content.add(true);
                }else{
                    content.add(false);
                }
            }else if(email!=null && !"".equals(email)){
                AcSystemUser user = new AcSystemUser();
                user.setEmail(email);
                user.setValid(1);
                if(coreDao.query(user).size()>0){
                    content.add(true);
                }else{
                    content.add(false);
                }
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACUserExistsService");
        }
    }

    /**
     * 用户 —— 列表
     * @param jsonString
     * @return
     */
    @RequestMapping("ACUserListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult userList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Map<String, Object> contentMap = new HashMap<String, Object>();
            List<Object> dataList = new ArrayList<Object>();
            contentMap.put("dataList", dataList);

            AcSystemUser query = new AcSystemUser();
            query.setValid(1);
            query.setSort("name", EMSOrder.Asc);
            Map<String, List<AcSystemRole>> roleMap = acSystemUserRoleService.queryUserRoleMap();
            List<AcSystemUser> list = acSystemUserService.queryList(query);
            for (AcSystemUser acSystemUser : list) {
                if("persagyAdmin".equals(acSystemUser.getName())){
                    continue;
                }
                StringBuffer stringBuffer = new StringBuffer();
                Map<String, Object> userMap = new HashMap<String, Object>();
                userMap.put("id", acSystemUser.getId());
                userMap.put("name", acSystemUser.getName());
                userMap.put("showName", acSystemUser.getShowName());
                userMap.put("mobile", acSystemUser.getMobile());
                userMap.put("email", acSystemUser.getEmail());
                List<AcSystemRole> roleList = roleMap.get(acSystemUser.getId());
                if (roleList != null && roleList.size() > 0) {
                    for (AcSystemRole acSystemRole : roleList) {
                        if (stringBuffer.length() != 0) {
                            stringBuffer.append(",");
                        }
                        stringBuffer.append(acSystemRole.getName());

                    }
                }
                userMap.put("roleList", stringBuffer.toString());
                dataList.add(userMap);
            }
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACUserListService");
        }
    }

    /**
     * 用户 —— 更新
     * @param jsonString
     * @return
     */
    @RequestMapping("ACUserUpdateService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult userUpdate(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");
            String name = (String) dto.get("name");
            String showName = (String) dto.get("showName");
            String mobile = (String) dto.get("mobile");
            String email = (String) dto.get("email");
            List<String> functionList = (List<String>) dto.get("functionList");
            List<String> permissionList = (List<String>) dto.get("permissionList");
            if (id == null) {
                throw new Exception("参数不能为空！");

            }
            List<AcSystemUserFunction> saveFunctionList = new ArrayList<AcSystemUserFunction>();
            if (functionList != null) {
                for (String functionId : functionList) {
                    AcSystemUserFunction save = new AcSystemUserFunction();
                    save.setId(UUID.randomUUID().toString());
                    save.setFunctionId(functionId);
                    save.setUserId(id);
                    saveFunctionList.add(save);
                }
            }
            List<AcSystemUserPermission> savePermissionList = new ArrayList<AcSystemUserPermission>();
            if(permissionList!=null){
                for (String permissionId : permissionList) {
                    AcSystemUserPermission save = new AcSystemUserPermission();
                    save.setId(UUID.randomUUID().toString());
                    save.setPermissionId(permissionId);
                    save.setUserId(id);
                    savePermissionList.add(save);
                }
            }
            {
                AcSystemUser query = new AcSystemUser();
                query.setId(id);
                AcSystemUser update = new AcSystemUser();
                update.setName(name);
                update.setShowName(showName);
                update.setMobile(mobile);
                update.setEmail(email);
                acSystemUserService.updateUser(query, update);
                acSystemUserFunctionService.removeByUserId(id);
                acSystemUserPermissionService.removeByUserId(id);
                acSystemUserPermissionService.save(savePermissionList);
                acSystemUserFunctionService.save(saveFunctionList);
            }
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACUserUpdateService");
        }
    }

    /**
     * 用户 —— 密码重置
     * @param jsonString
     * @return
     */
    @RequestMapping("ACPasswordResetService")
    @ResponseBody
    public InterfaceResult passwordReset(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");
            if(id==null || "".equals(id)){
                throw new Exception("id 不能为空！");
            }
            AcSystemUser userQuery = new AcSystemUser();
            userQuery.setId(id);

            AcSystemUser userUpdate = new AcSystemUser();
            userUpdate.setPassword(CommonUtils.md5("123456"));

            acSystemUserService.update(userQuery, userUpdate);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACPasswordResetService");
        }
    }

    /**
     * 用户 —— 更改密码
     * @param jsonString
     * @return
     */
    @RequestMapping("ACUpdatePasswordService")
    @ResponseBody
    public InterfaceResult updatePassword(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");
            String oldPassword = (String) dto.get("oldPassword");
            String newPassword = (String) dto.get("password");
            if(id==null || "".equals(id)||oldPassword==null||newPassword==null){
                throw new Exception("参数不能为空");
            }
            AcSystemUser userQuery = new AcSystemUser();
            userQuery.setId(id);
            userQuery.setPassword(CommonUtils.md5(oldPassword));
            List<AcSystemUser> list = acSystemUserService.query(userQuery);
            if(list==null||list.size()==0){
                throw new Exception("旧密码输入错误");
            }
            AcSystemUser userUpdate = new AcSystemUser();
            userUpdate.setPassword(CommonUtils.md5(newPassword));
            acSystemUserService.update(userQuery, userUpdate);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACUpdatePasswordService");
        }
    }

    /**
     * 用户 —— 授权查询
     * @param jsonString
     * @return
     */
    @RequestMapping("ACUserAuthorizationQueryService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult userAuthorizationQuery(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");
            List<String> roleIds = (List<String>) dto.get("roleIds");
            String productId = (String) dto.get("productId");
            Map puser = (Map) dto.get("puser");
            String pid = (String) puser.get("id");
            Map<String, Map<String, Object>> zongMap = new LinkedHashMap<String, Map<String, Object>>();
            List<Object> functionList = new ArrayList<Object>();
            List<Object> permissionList = new ArrayList<Object>();
            Map<String, AcSystemFunction> parentFunctionMap = new LinkedHashMap<String, AcSystemFunction>();
            Map<String, AcSystemPermission> parentPermissionMap = new LinkedHashMap<String, AcSystemPermission>();
            if (pid.equals("persagyAdmin")) {// 超级管理员
                parentFunctionMap = acSystemFunctionService.queryMapByProduct(productId);
                parentPermissionMap = acSystemPermissionService.queryMapByProduct(productId);
            } else {
                parentFunctionMap = acSystemUserFunctionService.queryByUserId(pid);
                parentPermissionMap = acSystemUserPermissionService.queryByUserId(pid);
            }
            Map<String, AcSystemFunction> sonFunctionMap = new HashMap<String, AcSystemFunction>();
            Map<String, AcSystemPermission> sonPermissionMap = new HashMap<String, AcSystemPermission>();
            if (id != null && !"".equals(id)) {
                sonFunctionMap = acSystemUserFunctionService.queryByUserId(id);
                sonPermissionMap = acSystemUserPermissionService.queryByUserId(id);
            } else {
                if (roleIds != null) {
                    for (String roleId : roleIds) {
                        sonFunctionMap.putAll(acSystemRoleFunctionService.queryByRoleId(roleId));
                        sonPermissionMap.putAll(acSystemRolePermissionService.queryByRoleId(roleId));
                    }
                } else {// 新增
                    sonFunctionMap.putAll(parentFunctionMap);
                    sonPermissionMap.putAll(parentPermissionMap);
                }
            }
            processFunction(zongMap, functionList, parentFunctionMap, sonFunctionMap);

            for (Map.Entry<String, AcSystemPermission> enery : parentPermissionMap.entrySet()) {
                AcSystemPermission permission = enery.getValue();
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("id", permission.getId());
                map.put("name", permission.getName());
                map.put("isHave", 0);
                if (sonPermissionMap.containsKey(permission.getId())) {
                    map.put("isHave", 1);
                }
                permissionList.add(map);
            }
            Map<String, Object> map = new HashMap<String, Object>();
            map.put("functionList", functionList);
            map.put("permissionList", permissionList);
            content.add(map);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACUserAuthorizationQueryService");
        }
    }

    private void processFunction(Map<String, Map<String, Object>> zongMap, List<Object> functionList,
                                 Map<String, AcSystemFunction> parentFunctionMap, Map<String, AcSystemFunction> sonFunctionMap) {
        for (Map.Entry<String, AcSystemFunction> enery : parentFunctionMap.entrySet()) {
            AcSystemFunction patentFuntion = enery.getValue();
            Map<String, Object> funtionMap = new HashMap<String, Object>();
            funtionMap.put("id", patentFuntion.getId());
            funtionMap.put("name", patentFuntion.getName());
            funtionMap.put("List", new ArrayList<>());
            if (patentFuntion.getIsParent() == 0) {// 子功能
                String parentId = patentFuntion.getParentId();
                if (!zongMap.containsKey(parentId)) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("List", new ArrayList<Object>());
                    zongMap.put(parentId, map);
                }
                List list = (List) zongMap.get(parentId).get("List");
                list.add(funtionMap);
                zongMap.get(parentId).put("list", list);
            } else {
                zongMap.put(patentFuntion.getId(), funtionMap);
            }
        }
        for (Map.Entry<String, Map<String, Object>> entry : zongMap.entrySet()) {
            Map<String, Object> map = entry.getValue();
            map.put("isHave", 0);
            if (sonFunctionMap.containsKey(map.get("id"))) {// 有权限
                map.put("isHave", 1);
            }
            List<Map<String, Object>> sonFunctionList = (List<Map<String, Object>>) map.get("list");
            if (sonFunctionList != null) {
                List<Object> list = new ArrayList<Object>();
                for (Map<String, Object> sonFunction : sonFunctionList) {
                    sonFunction.put("isHave", 0);
                    if (sonFunctionMap.containsKey(sonFunction.get("id"))) {// 有权限
                        sonFunction.put("isHave", 1);
                    }
                    list.add(sonFunction);
                }
                map.put("list", list);
            }
            functionList.add(map);
        }
    }
}
