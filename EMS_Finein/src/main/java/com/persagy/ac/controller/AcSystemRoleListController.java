package com.persagy.ac.controller;

import com.persagy.ac.service.AcSystemRoleService;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.ac.AcSystemRole;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class AcSystemRoleListController extends BaseController {

    @Resource(name = "AcSystemRoleService")
    private AcSystemRoleService acSystemRoleService;

    @RequestMapping("ACSystemRoleListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult systemRoleList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String productId = (String)dto.get("productId");
            List <AcSystemRole> list= acSystemRoleService.queryByProductId(productId);
            content.add(list);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACSystemRoleListService");
        }
    }
}
