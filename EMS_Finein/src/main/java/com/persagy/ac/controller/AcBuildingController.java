package com.persagy.ac.controller;

import com.persagy.ac.service.AcBuildingService;
import com.persagy.ac.service.AcProjectService;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.ac.AcBuilding;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 建筑
 * <AUTHOR>
 */
@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class AcBuildingController extends BaseController {

    @Resource(name = "AcProjectService")
    private AcProjectService acProjectService;

    @Resource(name = "AcBuildingService")
    private AcBuildingService acBuildingService;

    /**
     * 建筑 —— 新建
     * @param jsonString
     * @return
     */
    @RequestMapping("ACBuildingAddService")
    @ResponseBody
    public InterfaceResult buildingAdd(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");
            String name = (String) dto.get("name");
            String address = (String) dto.get("address");
            String remark = (String) dto.get("remark");

            if(id == null || name == null || address == null){
                throw new Exception("参数不能为空！");
            }

            AcBuilding query = new AcBuilding();
            query.setId(id);
            query.setName(name);
            query.setAddress(address);
            query.setRemark(remark);

            acBuildingService.addBuilding(query);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACBuildingAddService");
        }
    }

    /**
     * 建筑 —— 删除
     * @param jsonString
     * @return
     */
    @RequestMapping("ACBuildingDeleteService")
    @ResponseBody
    public InterfaceResult buildingDelete(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");

            if(id == null){
                throw new Exception("参数不能为空！");
            }

            AcBuilding query = new AcBuilding();
            query.setId(id);

            acBuildingService.remove(query);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACBuildingDeleteService");
        }
    }

    /**
     * 建筑 —— 详情
     * @param jsonString
     * @return
     */
    @RequestMapping("ACBuildingDetailService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult buildingDetail(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");

            if(id == null){
                throw new Exception("参数不能为空！");
            }

            Map<String,Object> buildingMap = new HashMap<String,Object>();
            AcBuilding query = new AcBuilding();
            query.setId(id);

            List<AcBuilding> queryList = acBuildingService.queryList(query);

            if( queryList != null && queryList.size() > 0){
                buildingMap.put("id", queryList.get(0).getId());
                buildingMap.put("name", queryList.get(0).getName());
                buildingMap.put("address", queryList.get(0).getAddress());
                buildingMap.put("remark", queryList.get(0).getRemark());
            }

            content.add(buildingMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACBuildingDetailService");
        }
    }

    /**
     * 建筑 —— 列表
     * @param jsonString
     * @return
     */
    @RequestMapping("ACBuildingListService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult buildingList(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            Map<String,Object> contentMap = new HashMap<String,Object>();
            List<Object> dataList = new ArrayList<Object>();
            contentMap.put("dataList", dataList);

            List<AcBuilding> list = acBuildingService.queryList(new AcBuilding());
            for (AcBuilding acBuilding : list) {
                Map<String,Object> buildingMap = new HashMap<String,Object>();
                buildingMap.put("id", acBuilding.getId());
                buildingMap.put("name", acBuilding.getName());
                buildingMap.put("address", acBuilding.getAddress());
                buildingMap.put("remark", acBuilding.getRemark());
                dataList.add(buildingMap);
            }
            content.add(contentMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACBuildingListService");
        }
    }

    /**
     * 建筑 —— 更新
     * @param jsonString
     * @return
     */
    @RequestMapping("ACBuildingUpdateService")
    @ResponseBody
    public InterfaceResult buildingUpdate(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String id = (String) dto.get("id");
            String name = (String) dto.get("name");
            String address = (String) dto.get("address");
            String remark = (String) dto.get("remark");

            if(id == null || name == null || address == null ){
                throw new Exception("参数不能为空！");
            }

            AcBuilding query = new AcBuilding();
            query.setId(id);

            AcBuilding update = new AcBuilding();
            update.setName(name);
            update.setAddress(address);
            update.setRemark(remark);

            acProjectService.update(query, update);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACBuildingUpdateService");
        }
    }
}
