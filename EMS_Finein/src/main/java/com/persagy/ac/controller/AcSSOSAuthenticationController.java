package com.persagy.ac.controller;

import com.persagy.ac.core.utils.AESUtil;
import com.persagy.ac.service.AcSSOSSystemParamService;
import com.persagy.ac.service.AcSystemFunctionService;
import com.persagy.ac.service.AcSystemUserFunctionService;
import com.persagy.ac.service.AcSystemUserService;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.utils.BaseController;
import com.persagy.core.utils.JsonStringUtil;
import com.persagy.core.utils.Result;
import com.persagy.ems.pojo.ac.AcSSOSSystemParam;
import com.persagy.ems.pojo.ac.AcSystemFunction;
import com.persagy.ems.pojo.ac.AcSystemUser;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import java.util.*;

@Controller
@Scope("prototype")
@RequestMapping("/entrance/unifier/")
@SuppressWarnings({ "rawtypes" })
public class AcSSOSAuthenticationController extends BaseController {

    @Resource(name = "AcSystemUserService")
    private AcSystemUserService acSystemUserService;

    @Resource(name = "AcSystemUserFunctionService")
    private AcSystemUserFunctionService acSystemUserFunctionService;

    @Resource(name = "AcSystemFunctionService")
    private AcSystemFunctionService acSystemFunctionService;

    @Resource(name = "AcSSOSSystemParamService")
    private AcSSOSSystemParamService acSSOSSystemParamService;

    @RequestMapping("ACSSOSAuthenticationService")
    @ResponseBody
    @SuppressWarnings("unchecked")
    public InterfaceResult sSOSAuthentication(@RequestParam(value="jsonString") String jsonString) {
        try {
            Map dto = JsonStringUtil.jsonStringToMap(jsonString);
            List content = new ArrayList();
            String userName = (String) dto.get("name");// 登录名
            String token = (String) dto.get("token");
            String systemCode = (String) dto.get("systemCode");
            String productId = (String) dto.get("productId");// 所访问的产品编号
            if (userName == null || token == null || systemCode == null) {
                throw new Exception("参数不能为空");
            }

            AcSSOSSystemParam systemParam = acSSOSSystemParamService.queryByCode(systemCode);
            String decrypt = AESUtil.decrypt(token, systemParam.getEncryptionKey());
            if (decrypt == null) {
                throw new Exception("秘钥错误");
            }
            Date now = new Date();
            if (now.getTime() - Long.valueOf(decrypt) > (5 * 60 * 1000)) {
                throw new Exception("token超时");
            }
            Map<String, Object> userMap = new HashMap<String, Object>();
            List<AcSystemFunction> functionList = new ArrayList<AcSystemFunction>();
            userMap.put("functionList", functionList);
            AcSystemUser user = new AcSystemUser();
            user.setName(userName);
            user.setValid(1);
            List<AcSystemUser> list = acSystemUserService.query(user);
            if (list == null || list.size() == 0) {
                content.add(userMap);
                return Result.SUCCESS(content);
            }
            AcSystemUser systemUser = list.get(0);
            userMap.put("id", systemUser.getId());
            userMap.put("name", systemUser.getName());
            userMap.put("showName", systemUser.getShowName());
            userMap.put("password", systemUser.getPassword());
            userMap.put("mobile", systemUser.getMobile());
            userMap.put("email", systemUser.getEmail());
            userMap.put("valid", systemUser.getValid());
            if ("persagyAdmin".equals(userName.trim())) {// 超级管理员
                AcSystemFunction query = new AcSystemFunction();
                query.setProductId(productId);
                query.setIsParent(1);
                functionList = acSystemFunctionService.query(query);
                if (functionList != null) {
                    userMap.put("functionList", functionList);
                }
            } else {
                Map<String, AcSystemFunction> functionMap = acSystemUserFunctionService.queryByUserId(systemUser.getId());
                if (functionMap != null) {
                    for (Map.Entry<String, AcSystemFunction> entry : functionMap.entrySet()) {
                        AcSystemFunction systemFunction = entry.getValue();
                        if (systemFunction.getIsParent() == 1 && systemFunction.getProductId().equals(productId)) {
                            functionList.add(entry.getValue());
                        }
                    }
                }
            }
            content.add(userMap);
            return Result.SUCCESS(content);
        }catch (Exception e){
            e.printStackTrace();
            return Result.FAILURE(e,jsonString,"ACSSOSAuthenticationService");
        }
    }
}
