package com.persagy.ac.service;

import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.pojo.ac.AcBuilding;

import java.util.List;

public interface AcBuildingService extends CoreService{

	int updateBuilding(AcBuilding query, AcBuilding update) throws Exception;
	
	List<AcBuilding> queryList(AcBuilding query) throws Exception;
	
	void addBuilding(AcBuilding query) throws Exception;
	
	
}