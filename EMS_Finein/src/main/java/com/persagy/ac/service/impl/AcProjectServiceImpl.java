package com.persagy.ac.service.impl;

import com.persagy.ac.service.AcProjectService;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.ac.AcProject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service("AcProjectService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class AcProjectServiceImpl extends CoreServiceImpl implements AcProjectService {

	@Override
	public int updateProject(AcProject query, AcProject update) throws Exception {
	
		return this.update(query, update);
	}

	@Override
	public List<AcProject> queryList(AcProject query) throws Exception {
		
		return this.query(query);
	}

	@Override
	public void addProject(AcProject query) throws Exception {

		this.save(query);
	}
	
	
}

