package com.persagy.ac.service;


import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.pojo.ac.AcSystemFunction;
import com.persagy.ems.pojo.ac.AcSystemRole;

import java.util.List;

public interface AcSystemRoleService extends CoreService{

	AcSystemRole queryByRoleId(String roleId) throws Exception;

	List<AcSystemRole> queryByProductId(String productId) throws Exception;

	List<AcSystemFunction> queryFunction(String roleId) throws Exception;
}