package com.persagy.ac.service.impl;

import com.persagy.ac.service.AcBuildingService;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.ac.AcBuilding;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service("AcBuildingService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class AcBuildingServiceImpl extends CoreServiceImpl implements AcBuildingService{

	@Override
	public int updateBuilding(AcBuilding query, AcBuilding update) throws Exception {
		
		return this.update(query, update);
		
	}

	@Override
	public List<AcBuilding> queryList(AcBuilding query) throws Exception {

		return this.query(query);
		
	}

	@Override
	public void addBuilding(AcBuilding query) throws Exception {
		
		this.save(query);
	}
	
}

