package com.persagy.ac.service.impl;

import com.persagy.ac.service.AcSystemUserService;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.ac.AcSystemUser;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service("AcSystemUserService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class AcSystemUserServiceImpl extends CoreServiceImpl implements AcSystemUserService {

	@Override
	public int updateUser(AcSystemUser query, AcSystemUser update) throws Exception {
		
		return this.update(query, update);
	}

	@Override
	public List<AcSystemUser> queryList(AcSystemUser query) throws Exception {
		
		return this.query(query);
	}

	@Override
	public void addUser(AcSystemUser query) throws Exception {

		this.save(query);
	}
	
}

