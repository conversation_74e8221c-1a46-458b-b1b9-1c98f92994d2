package com.persagy.ac.service.impl;

import 	com.persagy.ac.core.utils.SchemaUtil;
import com.persagy.ac.service.AcSystemRoleFunctionService;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.ac.AcSystemFunction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("AcSystemRoleFunctionService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class AcSystemRoleFunctionServiceImpl extends CoreServiceImpl implements AcSystemRoleFunctionService {

	@Override
	public Map<String, AcSystemFunction> queryByRoleId(String id) throws Exception {

		Map<String, AcSystemFunction> result = new HashMap<String, AcSystemFunction>();

		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append("SELECT f.* from ");
		sqlBuffer.append(schema).append(".t_ac_system_function f,");
		sqlBuffer.append(schema).append(".t_ac_system_role_function rf where rf.c_role_id = '" + id + "'");
		sqlBuffer.append(" and rf.c_function_id = f.c_id ");
		sqlBuffer.append(" and f.c_valid = 1");
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null && list.size() > 0) {
			for (Map<String, Object> map : list) {
				String c_id = (String) map.get("c_id");
				String c_product_id = (String) map.get("c_product_id");
				String c_name = (String) map.get("c_name");
				Integer c_valid = (Integer) map.get("c_valid");
				Integer c_order_by = (Integer) map.get("c_order_by");
				Integer c_is_parent = (Integer) map.get("c_is_parent");
				String c_parent_id = (String) map.get("c_parent_id");

				AcSystemFunction permission = new AcSystemFunction();
				permission.setId(c_id);
				permission.setName(c_name);
				permission.setProductId(c_product_id);
				permission.setValid(c_valid);
				permission.setOrderBy(c_order_by);
				permission.setIsParent(c_is_parent);
				permission.setParentId(c_parent_id);
				result.put(c_id, permission);
			}
		}
		return result;

	}
	

}

