package com.persagy.ac.service;

import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.pojo.ac.AcSystemPermission;
import com.persagy.ems.pojo.ac.AcSystemUserPermission;

import java.util.List;
import java.util.Map;

public interface AcSystemUserPermissionService extends CoreService{

	List<AcSystemUserPermission> queryPermissionByUserId(String id) throws Exception;

	void removeByUserId(String id)throws Exception;

	Map<String, AcSystemPermission> queryByUserId(String pid)throws Exception;

}