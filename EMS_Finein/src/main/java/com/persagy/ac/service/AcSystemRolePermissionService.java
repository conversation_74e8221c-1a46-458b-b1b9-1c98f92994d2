package com.persagy.ac.service;

import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.pojo.ac.AcSystemPermission;

import java.util.List;
import java.util.Map;

public interface AcSystemRolePermissionService extends CoreService{

	List<AcSystemPermission> queryPermissionByRoleId(String roleId) throws Exception;

	Map<String, AcSystemPermission> queryByRoleId(String roleId) throws Exception;

}