package com.persagy.ac.service.impl;

import com.persagy.ac.service.AcBuildingPropertyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service("AcBuildingPropertyService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class AcBuildingPropertyServiceImpl implements AcBuildingPropertyService {
	
}

