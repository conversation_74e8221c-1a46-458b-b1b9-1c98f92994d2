package com.persagy.ac.service.impl;

import com.persagy.ac.service.AcSystemPermissionService;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.mvc.dao.impl.CoreDaoImpl;
import com.persagy.ems.pojo.ac.AcSystemPermission;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service("AcSystemPermissionService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class AcSystemPermissionServiceImpl extends CoreDaoImpl implements AcSystemPermissionService {

	@Override
	public Map<String, AcSystemPermission> queryMap() throws Exception {
		Map<String, AcSystemPermission> map = new HashMap<String, AcSystemPermission>();
		List<AcSystemPermission> list = this.query(new AcSystemPermission());
		if (list != null) {
			for (AcSystemPermission acSystemPermission : list) {
				map.put(acSystemPermission.getId(), acSystemPermission);
			}
		}
		return map;
	}

	@Override
	public Map<String, AcSystemPermission> queryMapByProduct(String productId) throws Exception {
		Map<String, AcSystemPermission> map = new LinkedHashMap<String, AcSystemPermission>();
		AcSystemPermission query = new AcSystemPermission();
		query.setProductId(productId);
		query.setSort("orderBy", EMSOrder.Asc);
		List<AcSystemPermission> list = this.query(query);
		if (list != null) {
			for (AcSystemPermission acSystemPermission : list) {
				map.put(acSystemPermission.getId(), acSystemPermission);
			}
		}
		return map;
	}

}
