package com.persagy.ac.service.impl;

import com.persagy.ac.service.AcSSOSSystemParamService;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.ac.AcSSOSSystemParam;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 说明:单点登录系统参数表
 */

@Service("AcSSOSSystemParamService")
public class AcSSOSSystemParamServiceImpl extends CoreServiceImpl implements AcSSOSSystemParamService {

	@Override
	public AcSSOSSystemParam queryByCode(String systemCode) throws Exception {
		AcSSOSSystemParam query = new AcSSOSSystemParam();
		query.setSystemCode(systemCode);
		query.setIsValid(1);
		List<AcSSOSSystemParam> list = (List<AcSSOSSystemParam>) this.query(query);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

}
