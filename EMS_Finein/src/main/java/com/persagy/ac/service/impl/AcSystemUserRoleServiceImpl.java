package com.persagy.ac.service.impl;


import com.persagy.ac.core.utils.SchemaUtil;
import com.persagy.ac.service.AcSystemUserRoleService;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.ac.AcSystemRole;
import com.persagy.ems.pojo.ac.AcSystemUserRole;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("AcSystemUserRoleService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class AcSystemUserRoleServiceImpl extends CoreServiceImpl implements AcSystemUserRoleService {

	@Override
	public void removeList(AcSystemUserRole query) throws Exception {

		this.remove(query);
	}

	@Override
	public void saveList(List<AcSystemUserRole> list) throws Exception {

		this.save(list);
	}

	@Override
	public List<AcSystemUserRole> queryList(AcSystemUserRole query) throws Exception {
		
		return this.query(query);
	}

	@Override
	public Map<String, List<AcSystemRole>> queryUserRoleMap() throws Exception {
		Map<String, List<AcSystemRole>> result = new HashMap<String,List<AcSystemRole>>();
		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("SELECT u.c_id tenant_id,r.* from ");
		sqlBuffer.append(schema).append(".t_ac_system_user u,");
		sqlBuffer.append(schema).append(".t_ac_system_user_role ur,");
		sqlBuffer.append(schema).append(".t_ac_system_role r where u.c_id = ur.c_user_id and ur.c_role_id=r.c_id");
	List<Map<String,Object>> list = this.queryBySql(sqlBuffer.toString(),null);
	if(list!=null&&list.size()>0){
		for (Map<String, Object> map : list) {
			String tenantId = (String)map.get("tenant_id");
			String c_product_id = (String)map.get("c_product_id");
			String c_id = (String)map.get("c_id");
			String c_name = (String)map.get("c_name");
			String c_description = (String)map.get("c_description");
			Integer c_order_by = (Integer)map.get("c_order_by");
			
			AcSystemRole role = new AcSystemRole();
			role.setId(c_id);
			role.setName(c_name);
			role.setProductId(c_product_id);
			role.setDescription(c_description);
			role.setOrderBy(c_order_by);
			if(result.get(tenantId)==null){
				result.put(tenantId, new ArrayList<AcSystemRole>());
			}
			result.get(tenantId).add(role);
		}
	}
		return result;
	}

	@Override
	public List<AcSystemUserRole> queryUserRole(String userId) throws Exception {
		AcSystemUserRole query = new AcSystemUserRole();
		query.setUserId(userId);
		return this.query(query);
	}
	
}

