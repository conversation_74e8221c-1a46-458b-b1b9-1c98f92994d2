package com.persagy.ac.service;


import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.pojo.ac.AcSystemRole;
import com.persagy.ems.pojo.ac.AcSystemUserRole;

import java.util.List;
import java.util.Map;

public interface AcSystemUserRoleService extends CoreService{

	void removeList(AcSystemUserRole query) throws Exception;
	
	void saveList(List<AcSystemUserRole> list) throws Exception;
	
	List<AcSystemUserRole> queryList(AcSystemUserRole query) throws Exception;

	Map<String, List<AcSystemRole>> queryUserRoleMap() throws Exception;

	List<AcSystemUserRole> queryUserRole(String userId) throws Exception;
	
}