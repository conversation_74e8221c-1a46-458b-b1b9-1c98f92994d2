package com.persagy.ac.service;

import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.pojo.ac.AcSystemUser;

import java.util.List;

public interface AcSystemUserService extends CoreService{

	int updateUser(AcSystemUser query, AcSystemUser update) throws Exception;
	
	List<AcSystemUser> queryList(AcSystemUser query) throws Exception;
	
	void addUser(AcSystemUser query) throws Exception;
	
}