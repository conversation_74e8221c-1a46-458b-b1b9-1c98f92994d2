package com.persagy.ac.service.impl;

import com.persagy.ac.service.AcSystemFunctionService;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.ac.AcSystemFunction;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service("AcSystemFunctionService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class AcSystemFunctionServiceImpl extends CoreServiceImpl implements AcSystemFunctionService {

	@Override
	public Map<String, AcSystemFunction> queryMap() throws Exception {
		Map<String, AcSystemFunction> map = new HashMap<String, AcSystemFunction>();
		List<AcSystemFunction> list = this.query(new AcSystemFunction());
		if (list != null) {
			for (AcSystemFunction systemFunction : list) {
				map.put(systemFunction.getId(), systemFunction);
			}
		}
		return map;
	}

	@Override
	public Map<String, AcSystemFunction> queryMapByProduct(String productId) throws Exception {
		Map<String, AcSystemFunction> map = new LinkedHashMap<String, AcSystemFunction>();
		AcSystemFunction query = new AcSystemFunction();
		query.setProductId(productId);
		query.setSort("orderBy", EMSOrder.Asc);
		List<AcSystemFunction> list = this.query(query);
		if (list != null) {
			for (AcSystemFunction systemFunction : list) {
				map.put(systemFunction.getId(), systemFunction);
			}
		}
		return map;
	}

}
