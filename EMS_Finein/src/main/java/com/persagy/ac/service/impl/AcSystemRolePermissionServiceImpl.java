package com.persagy.ac.service.impl;

import com.persagy.ac.core.utils.SchemaUtil;
import com.persagy.ac.service.AcSystemRolePermissionService;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.ac.AcSystemPermission;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service("AcSystemRolePermissionService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class AcSystemRolePermissionServiceImpl extends CoreServiceImpl implements AcSystemRolePermissionService {

	@Override
	public List<AcSystemPermission> queryPermissionByRoleId(String roleId) throws Exception {

		 List<AcSystemPermission> result = new ArrayList<AcSystemPermission>();
		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("SELECT sp.* from ");
		sqlBuffer.append(schema).append(".t_ac_system_permission sp,");
		sqlBuffer.append(schema).append(".t_ac_system_role_permission rp where rp.c_role_id = '"+roleId+"'");
		sqlBuffer.append(" and rp.c_permission_id = sp.c_id ");
		sqlBuffer.append(" and sp.c_valid = 1");
	List<Map<String,Object>> list = this.queryBySql(sqlBuffer.toString(),null);
	if(list!=null&&list.size()>0){
		for (Map<String, Object> map : list) {
			String c_id = (String)map.get("c_id");
			String c_product_id = (String)map.get("c_product_id");
			String c_name = (String)map.get("c_name");
			Integer c_valid = (Integer)map.get("c_valid");
			Integer c_order_by = (Integer)map.get("c_order_by");
			
			AcSystemPermission permission = new AcSystemPermission();
			permission.setId(c_id);
			permission.setName(c_name);
			permission.setProductId(c_product_id);
			permission.setValid(c_valid);
			permission.setOrderBy(c_order_by);
			result.add(permission);
		}
	}
		return result;
	
	}

	@Override
	public Map<String, AcSystemPermission> queryByRoleId(String roleId) throws Exception {

		Map<String, AcSystemPermission> result = new HashMap<String, AcSystemPermission>();

		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append("SELECT p.* from ");
		sqlBuffer.append(schema).append(".t_ac_system_permission p,");
		sqlBuffer.append(schema).append(".t_ac_system_role_permission rp where rp.c_role_id = '" + roleId + "'");
		sqlBuffer.append(" and rp.c_permission_id = p.c_id ");
		sqlBuffer.append(" and p.c_valid = 1");
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null && list.size() > 0) {
			for (Map<String, Object> map : list) {
				String c_id = (String) map.get("c_id");
				String c_product_id = (String) map.get("c_product_id");
				String c_name = (String) map.get("c_name");
				Integer c_valid = (Integer) map.get("c_valid");
				Integer c_order_by = (Integer) map.get("c_order_by");
				Integer c_is_parent = (Integer) map.get("c_is_parent");
				String c_parent_id = (String) map.get("c_parent_id");

				AcSystemPermission permission = new AcSystemPermission();
				permission.setId(c_id);
				permission.setName(c_name);
				permission.setProductId(c_product_id);
				permission.setValid(c_valid);
				permission.setOrderBy(c_order_by);
				result.put(c_id, permission);
			}
		}
		return result;

	}
	
}

