package com.persagy.ac.service.impl;

import com.persagy.ac.core.utils.SchemaUtil;
import com.persagy.ac.service.AcSystemUserPermissionService;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.ac.AcSystemPermission;
import com.persagy.ems.pojo.ac.AcSystemUserPermission;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service("AcSystemUserPermissionService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class AcSystemUserPermissionServiceImpl extends CoreServiceImpl implements AcSystemUserPermissionService {

	@Override
	public List<AcSystemUserPermission> queryPermissionByUserId(String id) throws Exception {
		AcSystemUserPermission query = new AcSystemUserPermission();
		query.setUserId(id);
		return this.query(query);
	}

	@Override
	public void removeByUserId(String id) throws Exception {
		AcSystemUserPermission query = new AcSystemUserPermission();
		query.setUserId(id);
		this.remove(query);
	}

	@Override
	public Map<String, AcSystemPermission> queryByUserId(String id) throws Exception {

		Map<String, AcSystemPermission> result = new LinkedHashMap<String, AcSystemPermission>();

		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append("SELECT p.* from ");
		sqlBuffer.append(schema).append(".t_ac_system_permission p,");
		sqlBuffer.append(schema).append(".t_ac_system_user_permission up where up.c_user_id = '" + id + "'");
		sqlBuffer.append(" and up.c_permission_id = p.c_id ");
		sqlBuffer.append(" and p.c_valid = 1");
		sqlBuffer.append(" order by p.c_order_by");
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null && list.size() > 0) {
			for (Map<String, Object> map : list) {
				String c_id = (String) map.get("c_id");
				String c_product_id = (String) map.get("c_product_id");
				String c_name = (String) map.get("c_name");
				Integer c_valid = (Integer) map.get("c_valid");
				Integer c_order_by = (Integer) map.get("c_order_by");

				AcSystemPermission permission = new AcSystemPermission();
				permission.setId(c_id);
				permission.setName(c_name);
				permission.setProductId(c_product_id);
				permission.setValid(c_valid);
				permission.setOrderBy(c_order_by);
				result.put(c_id, permission);
			}
		}
		return result;

	}

}
