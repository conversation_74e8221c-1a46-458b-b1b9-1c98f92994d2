package com.persagy.ac.service;

import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.pojo.ac.AcSystemFunction;
import com.persagy.ems.pojo.ac.AcSystemUserFunction;

import java.util.List;
import java.util.Map;

public interface AcSystemUserFunctionService extends CoreService {

	List<AcSystemUserFunction> queryFunctionByUserId(String id) throws Exception;

	void removeByUserId(String userId)throws Exception;

	Map<String, AcSystemFunction> queryByUserId(String userId)throws Exception;

}