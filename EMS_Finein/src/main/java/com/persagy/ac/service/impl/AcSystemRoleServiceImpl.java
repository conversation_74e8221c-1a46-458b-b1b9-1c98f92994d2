package com.persagy.ac.service.impl;

import com.persagy.ac.core.utils.SchemaUtil;
import com.persagy.ac.service.AcSystemRoleService;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.ems.pojo.ac.AcSystemFunction;
import com.persagy.ems.pojo.ac.AcSystemRole;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service("AcSystemRoleService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class AcSystemRoleServiceImpl extends CoreServiceImpl implements AcSystemRoleService {

	@Override
	public AcSystemRole queryByRoleId(String roleId) throws Exception {
		
		AcSystemRole query = new AcSystemRole();
		query.setId(roleId);
		
		List<AcSystemRole> list = this.query(query);
		if(list != null && list.size() > 0){
			return list.get(0);
		}
		
		return null;
	}

	@Override
	public List<AcSystemRole> queryByProductId(String productId) throws Exception {
		AcSystemRole query = new AcSystemRole();
		query.setProductId(productId);
		
		return this.query(query);
	}

	@Override
	public List<AcSystemFunction> queryFunction(String roleId) throws Exception {
   List<AcSystemFunction> resultList = new ArrayList<AcSystemFunction>();
		StringBuffer sqlBuffer = new StringBuffer();
		
		String schema = SchemaUtil.getSchema(Schema.EMS);
		
		sqlBuffer.append("SELECT f.* from ");
		sqlBuffer.append(schema).append(".t_ac_system_role_function rf,");
		sqlBuffer.append(schema).append(".t_ac_system_function f ");
		sqlBuffer.append(" where rf.c_role_id = '"+roleId+"'" );
		sqlBuffer.append(" and rf.c_function_id = f.c_id" );
		List<Map<String,Object>> list = this.queryBySql(sqlBuffer.toString(),null);
		if(list != null){
			for(Map<String,Object> map : list){
				AcSystemFunction acSystemFunction = new AcSystemFunction();
				acSystemFunction.setId((String)map.get("c_id"));
				acSystemFunction.setProductId((String)map.get("c_product_id"));
				acSystemFunction.setName((String)map.get("c_name"));
				acSystemFunction.setUrl( (String)map.get("c_url"));
				acSystemFunction.setIcon((String)map.get("c_icon"));
				acSystemFunction.setOrderBy((Integer)map.get("c_order_by"));
				acSystemFunction.setValid((Integer)map.get("c_valid"));
				resultList.add(acSystemFunction);
			}
		}
		return resultList;
	}

	
}

