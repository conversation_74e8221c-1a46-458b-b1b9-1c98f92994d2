<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<parent>
		<groupId>com.persagy</groupId>
		<artifactId>EMS_Finein_Parent</artifactId>
		<version>1.0.0</version>
		<relativePath>../EMS_Finein_Parent/pom.xml</relativePath>
	</parent>

	<artifactId>EMS_Finein</artifactId>
	<packaging>war</packaging>

	<dependencies>
		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>EMS_Finein_Common</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-codec</artifactId>
					<groupId>commons-codec</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>EMS_Finein_Communication</artifactId>
			<version>${project.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<exclusion>
					<artifactId>commons-codec</artifactId>
					<groupId>commons-codec</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>slf4j-api</artifactId>
					<groupId>org.slf4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>ems-excel</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>ems-serial</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.axis</groupId>
			<artifactId>axis</artifactId>
			<version>1.4</version>
		</dependency>

		<dependency>
			<groupId>axis</groupId>
			<artifactId>axis-ant</artifactId>
			<version>1.4</version>
		</dependency>

		<dependency>
			<groupId>javax.xml</groupId>
			<artifactId>jaxrpc</artifactId>
			<version>1.1</version>
		</dependency>

		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>ems_sms</artifactId>
			<version>${project.version}</version>
		</dependency>

		<dependency>
			<groupId>javax.servlet</groupId>
			<artifactId>javax.servlet-api</artifactId>
			<version>3.1.0</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>org.aiotCube</groupId>
			<artifactId>aiotcube_Feed_Dog</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>com.persagy</groupId>
			<artifactId>EMS_Data</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>org.aiotCube</groupId>
			<artifactId>AIOT_Encryp</artifactId>
			<version>1.0.2</version>
		</dependency>

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.1</version>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-api</artifactId>
			<version>2.10.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.10.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-web</artifactId>
			<version>2.10.0</version>
			<exclusions>
				<exclusion>
					<artifactId>log4j-api</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-core</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

	</dependencies>

	<build>
		<finalName>EMS_Finein</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>2.19.1</version>
				<configuration>
					<testFailureIgnore>true</testFailureIgnore>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>3.3.2</version>
				<configuration>
					<warName>EMS_Finein</warName>
					<webXml>src/main/webApp/WEB-INF/web.xml</webXml>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
