package com.persagy.ems.finein.common.util;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.*;
import java.nio.file.Paths;

/**
 * 网页文件转换成其他文件
 * 
 * <AUTHOR>
 * 
 */
@Component("WKHtmlToPdfOrImgUtil")
public class WKHtmlToPdfOrImgUtil {

	@Value("${file.storage.directory}")
	private String basePath;
	
	/**
	 * 
	 * @param htmlFilePath
	 *            待转换html文件路径
	 * @param pdfFilePath
	 *            生成的pdf文件路径 生成成功后默认删除网页文件
	 * @return
	 * @throws Exception 
	 */
	public Boolean HtmlToPdf(String htmlFilePath, String pdfFilePath) throws Exception {
		return HtmlToPdf(htmlFilePath, pdfFilePath, false);
	}

	/**
	 * 
	 * @param htmlFilePath
	 *            待转换html文件路径
	 * @param pdfFilePath
	 *            生成的pdf文件路径
	 * @param isDeleteHtmlFile
	 *            生成成功后是否删除htmlFile,默认为true
	 * @return
	 * @throws Exception 
	 */
	public Boolean HtmlToPdf(String htmlFilePath, String pdfFilePath, boolean isDeleteHtmlFile) throws Exception {
		File htmlfile = new File(htmlFilePath);
		if (!htmlfile.exists()) {
			return false;
		}
		File pdffile = new File(pdfFilePath);
		File parent = pdffile.getParentFile();
		// 如果pdf保存路径不存在，则创建路径
		if (!parent.exists()) {
			parent.mkdirs();
		}
		if (pdffile.exists()) {//
			pdffile.delete();
		}
		String wkhtmltopdfPath =null;
		String os = System.getProperty("os.name");  
		if(os.toLowerCase().startsWith("win")){  
			wkhtmltopdfPath= Paths.get(basePath, "wkhtmltopdf\\bin\\wkhtmltopdf.exe").toString();
		}else{
			StringBuffer buffer = new StringBuffer();
			buffer.append(basePath).append(File.separator).append("wkhtmltox").append(File.separator).append("bin").append(File.separator).append("wkhtmltopdf");
			wkhtmltopdfPath=buffer.toString();
		}
		File file=new File(wkhtmltopdfPath);
		if(!file.exists()){
			throw new Exception(wkhtmltopdfPath);
		}
		StringBuilder cmd = new StringBuilder();
		cmd.append(wkhtmltopdfPath);
		cmd.append(" --footer-center");
		cmd.append(" [page]");
		cmd.append(" ");
		cmd.append(htmlFilePath);
		cmd.append(" ");
		cmd.append(pdfFilePath);
		try {
			Process proc = Runtime.getRuntime().exec(cmd.toString());
			HtmlToPdfInterceptor error = new HtmlToPdfInterceptor(proc.getErrorStream());
			HtmlToPdfInterceptor output = new HtmlToPdfInterceptor(proc.getInputStream());
			error.run();
			output.run();
			proc.waitFor();
			if (isDeleteHtmlFile) {
				htmlfile.delete();
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}

	/**
	 * 
	 * @param htmlFilePath
	 *            html文件路径
	 * @param imageFilePath
	 *            图片保存路径
	 * @return
	 * @throws Exception 
	 */
	public Boolean HtmlToImage(String htmlFilePath, String imageFilePath) throws Exception {
		return HtmlToImage(htmlFilePath, imageFilePath, false);
	}

	/**
	 * 
	 * @param htmlFilePath
	 *            html文件路径
	 * @param imageFilePath
	 *            图片保存路径
	 * @param isDeleteHtmlFile
	 *            生成成功后是否删除htmlFile,默认为true
	 * @return
	 * @throws Exception 
	 */
	public Boolean HtmlToImage(String htmlFilePath, String imageFilePath, boolean isDeleteHtmlFile) throws Exception {
		File htmlfile = new File(htmlFilePath);
		if (!htmlfile.exists()) {
			return false;
		}
		File Imagefile = new File(imageFilePath);
		File parent = Imagefile.getParentFile();
		// 如果image保存路径不存在，则创建路径
		if (!parent.exists()) {
			parent.mkdirs();
		}
		if (Imagefile.exists()) {// 如果已经存在,则删除
			Imagefile.delete();
		}
		
		String wkhtmltopdfPath =null;
		String os = System.getProperty("os.name");  
		if(os.toLowerCase().startsWith("win")){  
			wkhtmltopdfPath= Paths.get(basePath, "wkhtmltopdf\\bin\\wkhtmltoimage.exe").toString();
		}else{
			StringBuffer buffer = new StringBuffer();
			buffer.append(basePath).append(File.separator).append("wkhtmltox").append(File.separator).append("bin").append(File.separator).append("wkhtmltoimage");
			wkhtmltopdfPath=buffer.toString();
		}
		File file=new File(wkhtmltopdfPath);
		if(!file.exists()){
			throw new Exception("未配置Wkhtmltopdf");
		}
		StringBuilder cmd = new StringBuilder();
		cmd.append(wkhtmltopdfPath);
		cmd.append(" ");
		cmd.append(htmlFilePath);
		cmd.append(" ");
		cmd.append(imageFilePath);
		try {
			Process proc = Runtime.getRuntime().exec(cmd.toString());
			HtmlToPdfInterceptor error = new HtmlToPdfInterceptor(proc.getErrorStream());
			HtmlToPdfInterceptor output = new HtmlToPdfInterceptor(proc.getInputStream());
			error.run();
			output.run();
			proc.waitFor();
			if (isDeleteHtmlFile) {
				htmlfile.delete();
			}
			return true;
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}

	class HtmlToPdfInterceptor {
		private InputStream is;

		public HtmlToPdfInterceptor(InputStream is) {
			this.is = is;
		}

		public void run() {
			try {
				InputStreamReader isr = new InputStreamReader(is, "utf-8");
				BufferedReader br = new BufferedReader(isr);
				String line = null;
				while ((line = br.readLine()) != null) {
					System.out.println(line.toString()); // 输出内容
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
}
