package com.persagy.ems.finein.common.constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 *
 * 时间:2017年9月25日 下午12:21:19
 *
 * 说明:
 */

public class FineinConstant {

	public static class Time {
		public static final long Minute_1 = 60 * 1000;
		public static final long Minute_5 = Minute_1 * 5;
		public static final long Minute_15 = Minute_1 * 15;
		public static final long Hour_1 = 60 * Minute_1;
		public static final long Day_1 = 24 * Hour_1;
	}

	public static class Product {
		public static class FunctionId {
			public static final String PropertyMonitor = "PropertyMonitor";// 物业监控
			public static final String Tenant = "Tenant";// 租赁管理
			public static final String TenantConfig = "TenantConfig";// 租户后台配置
		}

		public static class ProductId {
			public static final String Finein = "Finein";
			public static final String PersagyCloud = "PersagyCloud";
		}

		public static class ProductDomain {
			public static final String Finein = "Finein";
			public static final String PersagyCloud = "PersagyCloud";
		}

		public static class ProductContext {
			public static final String Finein = "Finein";
			public static final String PersagyCloud = "PersagyCloud";
		}
	}

	public static class TenantPropertyKey {

	}

	public static class BuildingPropertyKey {
		public static final String BuildingCenterDataDianLastUploadTime = "BuildingCenterDataDianLastUploadTime";
		public static final String BuildingCenterDataShuiLastUploadTime = "BuildingCenterDataShuiLastUploadTime";
		public static final String BuildingCenterDataReShuiLastUploadTime = "BuildingCenterDataReShuiLastUploadTime";
		public static final String BuildingCenterDataRanQiLastUploadTime = "BuildingCenterDataRanQiLastUploadTime";
	}

	public static class MeterPropertyKey {
		public static final String DI_C_01_Y_Q_001_LastSaleCount = "DI_C_01_Y_Q_001_LastSaleCount";// 北电上次购电次数

	}

	public static class SysParamValueKey {
		public static final String Id_AlarmLimitSetting = "AlarmLimitSetting";// 报警门限设置
		public static final String Id_MessagePrePayTemplate = "MessagePrePayTemplate";// 预付费消息剩余天数不足模板
		public static final String Id_MessagePrePayTemplate_RemainData = "MessagePrePayRemainDataTemplate";// 预付费剩余数据不足消息模板
		public static final String Id_MessageSignature = "MessageSignature";// 消息签名
		public static final String Id_MessagePostPayTemplate = "MessagePostPayTemplate";// 后付费消息模板
		public static final String Id_PerPaySuccessTemplate = "perPaySuccessTemplate";// 充值到账消息模板
		public static final String Id_SendPayMessage = "SendPayMessage";// 发送充值到账消息
		public static final String Id_MessageSendOverdueTimeSecond = "MessagePostPayTemplate";// 发送消息过期时间秒数
		public static final String Id_MessageSendThreadSleepSecond = "MessagePostPayTemplate";// 发送消息线程睡眠秒数
		public static final String Id_MessageSendQueryLimit = "MessagePostPayTemplate";// 发送消息查询未发送记录每次查询条数
		public static final String Id_MessageSendThreadIsOpen = "MessageSendThreadIsOpen";// 发送消息线程是否打开
		public static final String Id_MessageSendMaxTryTimes = "MessageSendMaxTryTimes";// 发送消息最大重试次数
		public static final String Id_MessageSendServer = "MessageSendServer";// 消息发送服务地址
		public static final String Id_MessageSendUser = "MessageSendUser";// 消息发送服务用户名

		public static final String Id_AlarmThreadIsOpen = "AlarmThreadIsOpen";// 报警线程是否打开
		public static final String Id_AlarmThreadSleepSecond = "AlarmThreadSleepSecond";// 报警线程睡眠时间
		public static final String Id_AlarmGongLvGaoLastDataTolerateSecond = "AlarmGongLvGaoLastDataTolerateSecond";// 功率取最后数据容忍秒数
		public static final String Id_AlarmDataSpaceSecond = "AlarmDataSpaceSecond";// 报警线程原始数据间隔时间

		public static final String Id_MeterFaultInstantToleraterSecond = "MeterFaultInstantToleraterSecond";// 仪表数据中断线程瞬时量最后数据容忍秒数
		public static final String Id_MeterFaultCumulantToleraterSecond = "MeterFaultCumulantToleraterSecond";// 仪表数据中断线程累计量最后数据容忍秒数
		public static final String Id_MeterFaultThreadSleepSecond = "MeterFaultThreadSleepSecond";// 仪表数据中断线程睡眠秒数
		public static final String Id_MeterFaultThreadIsOpen = "MeterFaultThreadIsOpen";// 仪表数据中断线程是否打开

		public static final String Id_ComputeTMAvgThreadSleepSecond = "ComputeTMAvgThreadSleepSecond";// 计算租户仪表近*天平均值线程睡眠秒数
		public static final String Id_ComputeTMAvgThreadDays = "ComputeTMAvgThreadDays";// 计算租户仪表近*天平均值线程计算天数
		public static final String Id_ComputeTotalPowerThreadSleepSecond = "ComputeTotalPowerThreadSleepSecond";// 计算总功率线程睡眠秒数
		public static final String Id_ComputeTotalPowerThreadIsOpen = "ComputeTotalPowerThreadIsOpen";// 计算总功率线程是否启动
		public static final String Id_ComputeTotalPowerThreadDelaySecond = "ComputeTotalPowerThreadDelaySecond";// 计算总功率线程延时计算时间

		public static final String Id_ComputeMaxPowerThreadSleepSecond = "ComputeMaxPowerThreadSleepSecond";// 计算最大总功率线程睡眠秒数
		public static final String Id_ComputeMaxPowerThreadIsOpen = "ComputeMaxPowerThreadIsOpen";// 计算最大功率线程是否启动
		public static final String Id_ComputeMaxPowerThreadDelaySecond = "ComputeMaxPowerThreadDelaySecond";// 计算最大功率线程延时计算时间

		public static final String Id_ComputeMeterPowerThreadSleepSecond = "ComputeMeterPowerThreadSleepSecond";// 计算仪表功率线程睡眠秒数
		public static final String Id_ComputeMeterPowerThreadIsOpen = "ComputemMeterPowerThreadIsOpen";// 计算仪表功率线程是否启动
		public static final String Id_ComputeMeterPowerThreadDelaySecond = "ComputeMeterPowerThreadDelaySecond";// 计算仪表率线程延时计算时间

		public static final String Id_ComputePowerStatThreadSleepSecond = "ComputePowerStatThreadSleepSecond";// 计算总功率线程睡眠秒数
		public static final String Id_ComputePowerStatThreadIsOpen = "ComputePowerStatThreadIsOpen";// 计算总功率线程是否启动

		public static final String Id_ComputeTenantThreadSleepSecond = "ComputeTenantThreadSleepSecond";// 计算租户线程睡眠秒数
		public static final String Id_ComputeTenantThreadIsOpen = "ComputeTenantThreadIsOpen";// 计算租户线程是否启动
		public static final String Id_ComputeTMAvgThreadIsOpen = "ComputeTMAvgThreadIsOpen";// 计算租户仪表近*天平均值线程是否启动
		public static final String Id_ComputeRemainDaysThreadSleepSecond = "ComputeRemainDaysThreadSleepSecond";// 计算剩余天数线程睡眠秒数
		public static final String Id_ComputeRemainDaysThreadIsOpen = "ComputeRemainDaysThreadIsOpen";// 计算剩余天数线程是否打开
		public static final String Id_ComputeRemainDaysThreadTolerateSecond = "ComputeRemainDaysThreadTolerateSecond";// 计算剩余天数线程容忍秒数
		public static final String Id_StatDataStartTime = "StatDataStartTime";// 统计数据开始时间

		public static final String Id_ComputeMaxDataThreadIsOpen = "ComputeMaxDataThreadIsOpen";// 统计日最大值线程是否打开
		public static final String Id_ComputeMaxDataThreadSleepSecond = "ComputeMaxDataThreadSleepSecond";// 统计日最大值线程睡眠秒数

		public static final String Id_ComputeBuildingCostThreadIsOpen = "ComputeBuildingCostThreadIsOpen";// 统计项目能耗成本线程是否打开
		public static final String Id_ComputeBuildingCostThreadSleepSecond = "ComputeBuildingCostThreadSleepSecond";// 统计项目能耗成本线程睡眠秒数

		public static final String Id_XJMeterServerPort = "XJMeterServerPort";// 北电仪表数据解析服务服务端Port
		public static final String Id_XJMeterServerIp = "XJMeterServerIp";// 北电仪表数据解析服务服务端Ip
		public static final String Id_XJMeterClientPort = "XJMeterClientPort";// 北电仪表数据解析服务客户端Port
		public static final String Id_XJMeterClientIp = "XJMeterClientIp";// 北电仪表数据解析服务客户端Ip

		public static final String Id_CollectDataThreadIsOpen = "CollectDataThreadIsOpen";// 采集数据线程是否打开
		public static final String Id_CollectDataThreadSleepSecond = "CollectDataThreadSleepSecond";// 采集数据线程睡眠秒数

		public static final String Id_SendDataThreadIsOpen = "SendDataThreadIsOpen";// 发送数据线程是否打开
		public static final String Id_SendDataThreadSleepSecond = "SendDataThreadSleepSecond";// 发送数据线程睡眠秒数

		public static final String Id_SendDataCumulantClientIp = "SendDataCumulantClientIp";// 发送数据UDP低频量客户端Ip
		public static final String Id_SendDataCumulantClientPort = "SendDataCumulantClientPort";// 发送数据UDP低频量客户端Port
		public static final String Id_SendDataCumulantServerIp = "SendDataCumulantServerIp";// 发送数据UDP低频量服务端Ip
		public static final String Id_SendDataCumulantServerPort = "SendDataCumulantServerPort";// 发送数据UDP低频量服务端Port

		public static final String Id_SendDataInstantClientIp = "SendDataInstantClientIp";// 发送数据UDP高频量客户端Ip
		public static final String Id_SendDataInstantClientPort = "SendDataInstantClientPort";// 发送数据UDP高频量客户端Port
		public static final String Id_SendDataInstantServerIp = "SendDataInstantServerIp";// 发送数据UDP高频量服务端Ip
		public static final String Id_SendDataInstantServerPort = "SendDataInstantServerPort";// 发送数据UDP高频量服务端Port

		public static final String Id_SendDataMaxSize = "SendDataMaxSize";// 发送数据最大字节数
		public static final String Id_SendDataIsSeparate = "SendDataIsSeparate";// 发送数据是否分隔
		public static final String Id_SendDataSeparateBegin = "SendDataSeparateBegin";// 发送数据分隔开始符
		public static final String Id_SendDataSeparateEnd = "SendDataSeparateEnd";// 发送数据分隔结束符

		public static final String Id_CommunitionInitIsOpen = "CommunitionInitIsOpen";// 通讯初始化是否打开

		public static final String Id_AlarmPushThreadIsOpen = "AlarmPushThreadIsOpen";// 报警推送线程是否打开
		public static final String Id_AlarmPushThreadSleepSecond = "AlarmPushThreadSleepSecond";// 报警推送线程睡眠时间

		public static final String Id_AlarmCountThreadIsOpen = "AlarmCountThreadIsOpen";// 报警记录查询线程是否打开
		public static final String Id_AlarmCountThreadSleepSecond = "AlarmCountThreadSleepSecond";// 报警记录查询线程睡眠时间

		public static final String Id_BuildingDataThreadIsOpen = "BuildingDataThreadIsOpen";// 建筑能耗计算线程是否打开
		public static final String Id_BuildingDataThreadSleepSecond = "BuildingDataThreadSleepSecond";// 建筑能耗计算线程睡眠时间
		public static final String Id_ZhongHaiDingZhiServiceUrl = "ZhongHaiDingZhiServiceUrl";// 中海定制建筑能耗计算、查询报警记录接口url

		public static final String Id_CloudUploadTenantBaseThreadIsOpen = "CloudUploadTenantBaseThreadIsOpen";// 云客户端租户基础数据更新线程是否打开
		public static final String Id_CloudUploadTenantBaseThreadSleepSecond = "CloudUploadTenantBaseThreadSleepSecond";// 云客户端租户基础数据更新线程睡眠时间

		public static final String Id_CloudUploadTenantDataThreadIsOpen = "CloudUploadTenantDataThreadIsOpen";// 云客户端动态数据更新线程是否打开
		public static final String Id_CloudUploadTenantDataThreadSleepSecond = "CloudUploadTenantDataThreadSleepSecond";// 云客户端动态数据更新线程睡眠时间

		public static final String Id_CloudUploadProjectMap = "CloudUploadProjectMap";// 云客户端发送数据项目ID、建筑编码、密码集合
		// public static final String Id_CloudUploadSecret =
		// "CloudUploadSecret";//
		public static final String Id_CloudUploadUrl = "CloudUploadUrl";//

		public static final String Id_NameToSpell = "NameToSpell";// 租户名字转拼音初始化

		public static final String Id_TenantFlagBuild = "TenantFlagBuild";// 租户构建全码表初始化线程

		public static final String Id_RegisterIsNeed = "RegisterIsNeed";// 是否启用注册
		public static final String Id_RegisterType = "RegisterType";// soft dog

		public static final String Id_ProcessPrePayCount = "ProcessPrePayCount";// 尝试处理远程充值线程次数

		public static final String Id_CommonUploadTenantStaticThreadIsOpen = "CommonUploadTenantStaticThreadIsOpen";// 通用租户基础数据上传线程是否打开
		public static final String Id_CommonUploadTenantStaticThreadSleepSecond = "CommonUploadTenantStaticThreadSleepSecond";// 通用租户基础数据上传线程睡眠时间

		public static final String Id_CommonUploadHistoryDataThreadIsOpen = "CommonUploadHistoryDataThreadIsOpen";// 通用租户历史数据上传线程是否打开
		public static final String Id_CommonUploadHistoryDataThreadSleepSecond = "CommonUploadHistoryDataThreadSleepSecond";// 通用租户历史数据上传线程睡眠时间
		public static final String Id_CommonUploadHistoryDataThreadSendCount = "CommonUploadHistoryDataThreadSendCount";// 通用租户历史数据上传线程每次发送条数

		public static final String Id_CommonUploadRealTimeDataThreadIsOpen = "CommonUploadRealTimeDataThreadIsOpen";// 通用租户实时数据上传线程是否打开
		public static final String Id_CommonUploadRealTimeDataThreadSleepSecond = "CommonUploadRealTimeDataThreadSleepSecond";// 通用租户实时数据上传线程睡眠时间

		public static final String Id_CommonUploadBuildingCostDataThreadIsOpen = "CommonUploadBuildingCostDataThreadIsOpen";// 通用建筑成本数据上传线程是否打开
		public static final String Id_CommonUploadBuildingCostDataThreadSleepSecond = "CommonUploadBuildingCostDataThreadSleepSecond";// 通用建筑成本数据上传线程睡眠时间

		public static final String Id_CommonUploadBuildingRemainDataThreadIsOpen = "CommonUploadBuildingRemainDataThreadIsOpen";// 通用建筑剩余数据上传线程是否打开
		public static final String Id_CommonUploadBuildingRemainDataThreadSleepSecond = "CommonUploadBuildingRemainDataThreadSleepSecond";// 通用建筑剩余数据上传线程睡眠时间

		public static final String Id_CommonUploadPrePayReturnThreadIsOpen = "CommonUploadPrePayReturnThreadIsOpen";// 通用建筑充值退费记录数据上传线程是否打开
		public static final String Id_CommonUploadPrePayReturnThreadSleepSecond = "CommonUploadPrePayReturnThreadSleepSecond";// 通用建筑充值退费记录数据上传线程睡眠时间

		public static final String Id_FNOtherSystemPrePayThreadIsOpen = "FNOtherSystemPrePayThreadIsOpen";// 远程充值线程是否打开
		public static final String Id_FNOtherSystemPrePayThreadSleepSecond = "FNOtherSystemPrePayThreadSleepSecond";// 远程充值线程线程睡眠时间
		public static final String Id_FNOtherSystemPrePayMaxMoney = "FNOtherSystemPrePayMinMoney";// 远程充值最小充值金额
		public static final String Id_FNOtherSystemPrePaySpaceSecond = "FNOtherSystemPrePaySpaceSecond";// 单次充值间隔时间
		public static final String Id_FNFeedDogIsOpen = "FeedDogIsOpen";// 进程守护是否开启

		public static final String Id_PersagyCloudUrl = "PersagyCloudUrl";// 尚格云路径

		public static final String Id_GlobalAlarmPushThreadIsOpen = "GlobalAlarmPushThreadIsOpen";// 全局报警推送线程是否打开
		public static final String Id_GlobalAlarmPushThreadSleepSecond = "GlobalAlarmPushThreadSleepSecond";// 全局报警推送线程睡眠时间

		public static final String Id_GlobalAlarmPushUrl = "GlobalAlarmPushUrl";// 全局报警推送路径
		public static final String Id_GlobalAlarmPushSystemId = "GlobalAlarmPushSystemId";// 全局报警推送租户系统编码

		public static final String Id_FNEnergyMoneyDuiBiCha = "FNEnergyMoneyDuiBiCha";// 仪表消耗费用能耗对比差值

		public static final String Id_CommonDataCollectThreadIsOpen = "CommonDataCollectThreadIsOpen";// 通用采集数据线程
		public static final String Id_CommonDataCollectThreadSleepSecond = "CommonDataCollectThreadSleepSecond";// 通用采集数据线程睡眠时间
		public static final String Id_CommonDataCollectStartTime = "CommonDataCollectStartTime";// 采集数据开始时间
		public static final String Id_CommonDataCollectURL = "CommonDataCollectURL";// 采集数据路径
		public static final String Id_CommonDataCollectSpaceSecond = "CommonDataCollectSpaceSecond";// 采集数据间隔时间

	}

	public static class Exception {
		public static final String PARAM_CAN_NOT_BE_NULL = "参数不能为空";
	}

	public static class AlarmType {
		public static final String SHUIFEIYONGBUZU = "ZHBJ_05";// 水费用不足
		public static final String RESHUIFEIYONGBUZU = "ZHBJ_07";// 热水费用不足
		public static final String RANQIFEIYONGBUZU = "ZHBJ_09";// 燃气费用不足
		public static final String DIANFEIYONGBUZU = "ZHBJ_15";// 电费用不足
		public static final String FUHELVGUOGAO = "ZHBJ_16";// 负荷率过高
		public static final String DIANSHENGYUJINE = "ZHBJ_17";// 电剩余金额
		public static final String DIANSHENGYULIANG = "ZHBJ_18";// 电剩余量
		public static final String SHUISHENGYUJINE = "ZHBJ_19";// 水剩余金额
		public static final String SHUISHENGYULIANG = "ZHBJ_20";// 水剩余量
		public static final String RESHUISHENGYUJINE = "ZHBJ_21";// 热水剩余金额
		public static final String RESHUISHENGYULIANG = "ZHBJ_22";// 热水剩余量
		public static final String RANQISHENGYUJINE = "ZHBJ_23";// 燃气剩余金额
		public static final String RANQISHENGYULIANG = "ZHBJ_24";// 燃气剩余量
		public static final String DIANINTERRUPT = "ZHBJ_25";// 仪表数据中断
		public static final String SHUIINTERRUPT = "ZHBJ_26";// 仪表数据中断
		public static final String RANQIINTERRUPT = "ZHBJ_27";// 仪表数据中断
		public static final String RESHUIINTERRUPT = "ZHBJ_28";// 仪表数据中断
	}

	public static class AlarmTypeByEnergyType {
		public static final String[] DIAN = { "ZHBJ_15", "ZHBJ_17", "ZHBJ_18" };
	}

	public static class EnergyType {
		public static final String Dian = "Dian";
		public static final String RanQi = "RanQi";
		public static final String ReShui = "ReShui";
		public static final String Shui = "Shui";
	}

	public static class BackConfigType {
		public static final String Base_Room_Config = "Base_Room_Config";
		public static final String Base_Floor_Config = "Base_Floor_Config";
		public static final String Base_Tenant_Config = "Base_Tenant_Config";

		public static final String Common_Price_Config = "Common_Price_Config";
		public static final String Meter_Dian_Config = "Meter_Dian_Config";
		public static final String Meter_Shui_Config = "Meter_Shui_Config";
		public static final String Meter_ReShui_Config = "Meter_ReShui_Config";
		public static final String Meter_RanQi_Config = "Meter_RanQi_Config";
	}

	public static class FuntionTypeId {
		public static final int Cumulant_Dian = 10101;
		public static final int Cumulant_Shui = 12106;
		public static final int Cumulant_ReShui = 12106;
		public static final int Cumulant_RanQi = 13502;

		public static final int Instant_ShengYuLiang_Dian = 10701;
		public static final int Instant_YiGou_Dian=10702; //当前购买电量
		public static final int Instant_ShengYuLiang_Shui = 12109;
		public static final int Instant_ShengYuLiang_ReShui = 12109;
		public static final int Instant_ShengYuLiang_RanQi = 13503;

		public static final int Instant_ShengYuJinE_Dian = 10704;
		public static final int Instant_ShengYuJinE_Shui = 12110;
		public static final int Instant_ShengYuJinE_ReShui = 12110;
		public static final int Instant_ShengYuJinE_RanQi = 13504;

		public static final int Instant_Dian_ZongGongLv = 10304;// 电总功率
		public static final int Instant_Dian_AGongLv = 10301;// 电A相功率
		public static final int Instant_Dian_BGongLv = 10302;// 电B相功率
		public static final int Instant_Dian_CGongLv = 10303;// 电C相功率

		public static final int Instant_TouZhiJinE_Dian = 10710;
		public static final int Instant_TouZhiLiang_Dian = 10709;

		public static final int Instant_DanJia_Dian = 10711;//单价功能号
		public static final int Instant_BaoDian_Dian = 10717;//保电功能号
		public static final int Instant_BeiLv_Dian = 10712;//倍率功能号

		public static final int Instant_GouDianCount_Dian=10713;//购电次数

		public static final int Instant_TouZhiLiang_Shui=10415;//透支量
	}

	public static class MeterType {
		public static final String Dian = "101";
		public static final String Shui = "124";
		public static final String ReShui = "125";
		public static final String RanQi = "135";
	}

	public static class SerialCollect {
		public static final String Dian = "101";
		public static final String Shui = "124";
		public static final String ReShui = "125";
		public static final String RanQi = "135";
	}

	public static Map<String, String> systemSerialNumberMap = new HashMap<String, String>();

	public static Boolean SerialCollect = false;
}
