package com.persagy.ems.finein.common.util;

import com.persagy.ems.finein.common.constant.FineinConstant;

import java.util.Arrays;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月26日 下午2:18:51

* 说明:
*/

public class ExceptionUtil {
	
	public static String ParamIsNull(String... paramKey){
		StringBuffer sb = new StringBuffer();
		sb.append(FineinConstant.Exception.PARAM_CAN_NOT_BE_NULL).append(":"+ Arrays.asList(paramKey));
		return sb.toString();
	}
	
	public static String ParamIsNull(){
		StringBuffer sb = new StringBuffer();
		sb.append(FineinConstant.Exception.PARAM_CAN_NOT_BE_NULL);
		return sb.toString();
	}
}
