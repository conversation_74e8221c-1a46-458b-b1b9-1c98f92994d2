package com.persagy.ems.finein.common.util;

import com.persagy.finein.enumeration.EnumStatTimeType;
import com.persagy.finein.enumeration.EnumTimeType;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月26日 下午2:18:51

* 说明:
*/

public class TimeDataUtil {
	
	public static Map<String,Double> getTimeDataMap(Date timeFrom,Date timeTo,EnumTimeType timeType){
		Map<String,Double> result = new LinkedHashMap<String,Double>();
		
		Calendar c = Calendar.getInstance();
		c.setTime(timeFrom);
		SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		while(c.getTime().getTime() < timeTo.getTime()){
			result.put(standard.format(c.getTime()), null);
			switch (timeType) {
			case T0:
				c.add(Calendar.MINUTE, 15);
				break;
			case T1:
				c.add(Calendar.HOUR, 1);
				break;
			case T2:
				c.add(Calendar.DATE, 1);
				break;
			case T3:
				c.add(Calendar.DATE, 7);
				break;
			case T4:
				c.add(Calendar.MONTH, 1);
				break;
			case T5:
				c.add(Calendar.YEAR, 1);
				break;
			default:
				c.add(Calendar.DATE, 1);
				break;
			}
		}
		return result;
	}
	
	public static Map<String,Double> getTimeDataMap(Date timeFrom,Date timeTo,EnumStatTimeType timeType){
		Map<String,Double> result = new LinkedHashMap<String,Double>();
		
		Calendar c = Calendar.getInstance();
		c.setTime(timeFrom);
		SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		while(c.getTime().getTime() < timeTo.getTime()){
			result.put(standard.format(c.getTime()), null);
			switch (timeType) {
			case Minute_5:
				c.add(Calendar.MINUTE, 5);
				break;
			case Minute_15:
				c.add(Calendar.MINUTE, 15);
				break;
			case Hour_1:
				c.add(Calendar.HOUR, 1);
				break;
			case Day_1:
				c.add(Calendar.DATE, 1);
				break;
			default:
				c.add(Calendar.DATE, 1);
				break;
			}
		}
		return result;
	}
}
