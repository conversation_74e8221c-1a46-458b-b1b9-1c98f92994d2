package com.persagy.ems.finein.common.util;

import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Properties;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月13日 下午12:07:47

* 说明:
*/
@Component("SystemPropertiesUtil")
public class SystemPropertiesUtil {
	
	
	public String getProperty(String propertyName) throws IOException{
		Properties properties = new Properties();
		properties.load(SystemPropertiesUtil.class.getResourceAsStream("/config/system.properties"));
		String fileStorageDirectory = properties.getProperty(propertyName);
		return fileStorageDirectory;
	}
	
	public String getFileStorageDirectory() throws IOException{
		return this.getProperty("file.storage.directory");
	}
	
	public Boolean getIsUsePersagyDictionary() throws IOException{
		return Boolean.valueOf(this.getProperty("isUsePersagyDictionary"));
	}
}

