package com.persagy.ems.finein.common.util;

import com.persagy.ems.finein.common.constant.FineinConstant;

import java.util.HashMap;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)

 * 时间:2017年10月18日 上午10:27:18

 * 说明:
 */

public class FunctionTypeUtil {

	public static int getCumulantFunctionId(String energyTypeId){
		int result = -1;
		switch (energyTypeId) {
			case FineinConstant.EnergyType.Dian:
				result = FineinConstant.FuntionTypeId.Cumulant_Dian;
				break;
			case FineinConstant.EnergyType.Shui:
				result = FineinConstant.FuntionTypeId.Cumulant_Shui;
				break;
			case FineinConstant.EnergyType.ReShui:
				result = FineinConstant.FuntionTypeId.Cumulant_ReShui;
				break;
			case FineinConstant.EnergyType.RanQi:
				result = FineinConstant.FuntionTypeId.Cumulant_RanQi;
				break;
			default:
				break;
		}
		return result;
	}

	public static Map<String,Integer> getCumulantDianMultiple(){
		Map<String,Integer> result = new HashMap<>();
		result.put("F", 10705);
		result.put("P", 10706);
		result.put("G", 10707);
		result.put("J", 10708);
		return result;
	}

	public static Map<String,Integer> getPriceDianMultiple(){
		Map<String,Integer> result = new HashMap<>();
		result.put("F", 10720);
		result.put("P", 10721);
		result.put("G", 10722);
		result.put("J", 10719);
		return result;
	}


	public static int getShengYuLiangFunctionId(String energyTypeId){
		int result = -1;
		switch (energyTypeId) {
			case FineinConstant.EnergyType.Dian:
				result = FineinConstant.FuntionTypeId.Instant_ShengYuLiang_Dian;
				break;
			case FineinConstant.EnergyType.Shui:
				result = FineinConstant.FuntionTypeId.Instant_ShengYuLiang_Shui;
				break;
			case FineinConstant.EnergyType.ReShui:
				result = FineinConstant.FuntionTypeId.Instant_ShengYuLiang_ReShui;
				break;
			case FineinConstant.EnergyType.RanQi:
				result = FineinConstant.FuntionTypeId.Instant_ShengYuLiang_RanQi;
				break;
			default:
				break;
		}
		return result;
	}

	public static int getTouZhiJEFunctionId(String energyTypeId){
		int result = -1;
		switch (energyTypeId) {
			case FineinConstant.EnergyType.Dian:
				result = FineinConstant.FuntionTypeId.Instant_TouZhiJinE_Dian;
				break;
			case FineinConstant.EnergyType.Shui:
				result = FineinConstant.FuntionTypeId.Instant_TouZhiJinE_Dian;
				break;
			case FineinConstant.EnergyType.ReShui:
				result = FineinConstant.FuntionTypeId.Instant_TouZhiJinE_Dian;
				break;
			case FineinConstant.EnergyType.RanQi:
				result = FineinConstant.FuntionTypeId.Instant_TouZhiJinE_Dian;
				break;
			default:
				break;
		}
		return result;
	}

	public static int getTouZhiLiangFunctionId(String energyTypeId){
		int result = -1;
		switch (energyTypeId) {
			case FineinConstant.EnergyType.Dian:
				result = FineinConstant.FuntionTypeId.Instant_TouZhiLiang_Dian;
				break;
			case FineinConstant.EnergyType.Shui:
				result = FineinConstant.FuntionTypeId.Instant_TouZhiLiang_Dian;
				break;
			case FineinConstant.EnergyType.ReShui:
				result = FineinConstant.FuntionTypeId.Instant_TouZhiLiang_Dian;
				break;
			case FineinConstant.EnergyType.RanQi:
				result = FineinConstant.FuntionTypeId.Instant_TouZhiLiang_Dian;
				break;
			default:
				break;
		}
		return result;
	}

	public static int getShengYuJinEFunctionId(String energyTypeId){
		int result = -1;
		switch (energyTypeId) {
			case FineinConstant.EnergyType.Dian:
				result = FineinConstant.FuntionTypeId.Instant_ShengYuJinE_Dian;
				break;
			case FineinConstant.EnergyType.Shui:
				result = FineinConstant.FuntionTypeId.Instant_ShengYuJinE_Shui;
				break;
			case FineinConstant.EnergyType.ReShui:
				result = FineinConstant.FuntionTypeId.Instant_ShengYuJinE_ReShui;
				break;
			case FineinConstant.EnergyType.RanQi:
				result = FineinConstant.FuntionTypeId.Instant_ShengYuJinE_RanQi;
				break;
			default:
				break;
		}
		return result;
	}

	public static int getZongGongLvFunctionId(){
		return FineinConstant.FuntionTypeId.Instant_Dian_ZongGongLv;
	}

	public static int getPriceFunctionId(String energyTypeId){
		int result = -1;
		switch (energyTypeId) {
			case FineinConstant.EnergyType.Dian:
				result = FineinConstant.FuntionTypeId.Instant_DanJia_Dian;
				break;
			case FineinConstant.EnergyType.Shui:
				result = FineinConstant.FuntionTypeId.Cumulant_Shui;
				break;
			case FineinConstant.EnergyType.ReShui:
				result = FineinConstant.FuntionTypeId.Cumulant_ReShui;
				break;
			case FineinConstant.EnergyType.RanQi:
				result = FineinConstant.FuntionTypeId.Cumulant_RanQi;
				break;
			default:
				break;
		}
		return result;
	}

}

