package com.persagy.ems.finein.common.util;

import java.math.BigDecimal;

public class DoubleFormatUtil {
	private static DoubleFormatUtil instance = new DoubleFormatUtil();

	public synchronized static DoubleFormatUtil Instance() {
		return instance;
	}

	private DoubleFormatUtil() {

	}
	
	public Double doubleFormat(Object src,Long digits){
		if(src == null || digits == null){
			return null;
		}
		BigDecimal b = null;
		if(src instanceof Double){
			b = new BigDecimal((Double)src);
		}else if(src instanceof Long){
			b = new BigDecimal((Long)src);
		}else if(src instanceof Integer){
			b = new BigDecimal((Integer)src);
		} else if (src instanceof Float) {
			b = new BigDecimal((Float)src);
		} else if (src instanceof BigDecimal) {
			b = (BigDecimal)src;
		}else if(src instanceof String){
			try {
				b = new BigDecimal(Double.parseDouble((String)src));
			} catch (Exception e) {
				return null;
			}
		}else{
			return null;
		}
		return b.setScale(digits.intValue(), BigDecimal.ROUND_HALF_UP).doubleValue();
	}

	public Double getDoubleData(Object obj) {
		try {
			if (obj instanceof Long) {
				return ((Long) obj).doubleValue();
			} else if (obj instanceof Integer) {
				return((Integer) obj).doubleValue();
			} else if (obj instanceof String) {
				return Double.parseDouble((String) obj);
			} else if (obj instanceof Double) {
				return (Double) obj;
			} else if (obj instanceof Float) {
				return ((Float) obj).doubleValue();
			} else if (obj instanceof BigDecimal) {
				return ((BigDecimal) obj).doubleValue();
			} else {
				return null;
			}
		} catch (Exception e) {
		}
		return null;
	}

	public Double getDoubleData_00(Object obj) {
		try {
			if (obj instanceof Long) {
				BigDecimal b = new BigDecimal(((Long) obj).doubleValue());
				return b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			} else if (obj instanceof String) {
				BigDecimal b = new BigDecimal(Double.parseDouble((String) obj));
				return b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			} else if (obj instanceof Double) {
				BigDecimal b = new BigDecimal((Double) obj);
				return b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			} else if (obj instanceof Float) {
				BigDecimal b = new BigDecimal(((Float) obj).doubleValue());
				return b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			} else if (obj instanceof BigDecimal) {
				BigDecimal b = (BigDecimal) obj;
				return b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
			} else {
				return null;
			}
		} catch (Exception e) {
		}
		return null;
	}

	public Double getDoubleData_0(Object obj) {
		try {
			if (obj instanceof Long) {
				BigDecimal b = new BigDecimal(((Long) obj).doubleValue());
				return b.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
			} else if (obj instanceof String) {
				BigDecimal b = new BigDecimal(Double.parseDouble((String) obj));
				return b.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
			} else if (obj instanceof Double) {
				BigDecimal b = new BigDecimal((Double) obj);
				return b.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
			} else if (obj instanceof Float) {
				BigDecimal b = new BigDecimal(((Float) obj).doubleValue());
				return b.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
			} else if (obj instanceof BigDecimal) {
				BigDecimal b = (BigDecimal) obj;
				return b.setScale(1, BigDecimal.ROUND_HALF_UP).doubleValue();
			} else {
				return null;
			}
		} catch (Exception e) {
		}
		return null;
	}
}
