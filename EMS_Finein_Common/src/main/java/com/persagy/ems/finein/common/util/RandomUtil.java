package com.persagy.ems.finein.common.util;

import com.persagy.finein.enumeration.EnumTimeType;

import java.text.SimpleDateFormat;
import java.util.*;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月10日 下午8:19:31

* 说明:
*/

public class RandomUtil {

	public static Double getRandomData(){
		return Math.random() * 1000;
	}
	
	public static List<Map<String,Object>> getRandomChart(Date timeFrom,Date timeTo,EnumTimeType timeType,int radio,String xKey,String yKey){
		Calendar c = Calendar.getInstance();
		c.setTime(timeFrom);
		List<Map<String,Object>> result = new ArrayList<Map<String,Object>>();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		while(c.getTime().getTime() < timeTo.getTime()){
			Map<String,Object> map = new HashMap<String, Object>();
			map.put(xKey, sdf.format(c.getTime()));
			Double data = null;
			if(c.getTime().getTime() <= new Date().getTime()){
				data = Math.random() * radio;
			}
			map.put(yKey, data);
			if(timeType == EnumTimeType.T0){
				c.add(Calendar.MINUTE, 10);
			}else if(timeType == EnumTimeType.T1){
				c.add(Calendar.HOUR, 1);
			}else if(timeType == EnumTimeType.T2){
				c.add(Calendar.DATE, 1);
			}else if(timeType == EnumTimeType.T3){
				c.add(Calendar.DATE, 7);
			}else if(timeType == EnumTimeType.T4){
				c.add(Calendar.MONTH, 1);
			}else if(timeType == EnumTimeType.T5){
				c.add(Calendar.YEAR, 1);
			}else{
				c.add(Calendar.DATE, 1);
			}
			result.add(map);
		}
		return result;
	}
	
	public static List<Map<String,Object>> getRandomChart(Date timeFrom,Date timeTo,EnumTimeType timeType,int radio,String xKey,String yKey,String zKey,String aKey){
		Calendar c = Calendar.getInstance();
		c.setTime(timeFrom);
		List<Map<String,Object>> result = new ArrayList<Map<String,Object>>();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		while(c.getTime().getTime() < timeTo.getTime()){
			Map<String,Object> map = new HashMap<String, Object>();
			map.put(xKey, sdf.format(c.getTime()));
			Double data = null;
			if(c.getTime().getTime() <= new Date().getTime()){
				data = Math.random() * radio;
			}
			map.put(yKey, data);
			map.put(zKey, Math.random() * radio);
			map.put(aKey, 1);
			if(timeType == EnumTimeType.T0){
				c.add(Calendar.MINUTE, 10);
			}else if(timeType == EnumTimeType.T1){
				c.add(Calendar.HOUR, 1);
			}else if(timeType == EnumTimeType.T2){
				c.add(Calendar.DATE, 1);
			}else if(timeType == EnumTimeType.T3){
				c.add(Calendar.DATE, 7);
			}else if(timeType == EnumTimeType.T4){
				c.add(Calendar.MONTH, 1);
			}else if(timeType == EnumTimeType.T5){
				c.add(Calendar.YEAR, 1);
			}else{
				c.add(Calendar.DATE, 1);
			}
			result.add(map);
		}
		return result;
	}
}

