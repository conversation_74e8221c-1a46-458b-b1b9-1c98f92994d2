package com.persagy.ems.finein.common.util;

import org.apache.poi.hssf.usermodel.*;
import org.springframework.stereotype.Component;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月13日 上午10:58:02

* 说明:
*/
@Component("ExcelUtil")
public class ExcelUtil {
	@SuppressWarnings({ "rawtypes", "deprecation" })
	public void build(String path,String subPath,String fileName,List<Object> dataList){
		HSSFWorkbook wb = new HSSFWorkbook();
		FileOutputStream fout = null;
		HSSFSheet sheet = wb.createSheet("数据");
		
		HSSFCellStyle style = wb.createCellStyle(); 
        style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
        style.setAlignment(HSSFCellStyle.ALIGN_CENTER);
        
        if(dataList != null){
        	for(int i=0;i<dataList.size();i++){
        		HSSFRow row = sheet.createRow((short) i);   
                List subList = (List)dataList.get(i);
                if(subList != null && subList.size() > 0){
                	for(int j=0;j<subList.size();j++){
                		HSSFCell cell = row.createCell((short)j);   
                        if(i==0){
                        	cell.setCellStyle(style);
                        }
                        cell.setCellValue(subList.get(j) == null || "null".equals(subList.get(j)) ? "--" : subList.get(j)+""); 
                	}
                }
        	}
        }
        
        String newFilePath = Paths.get(path, subPath,fileName).toString();
		try {
			fout = new FileOutputStream(newFilePath);  
			wb.write(fout);  
		} catch (Exception e) {
			e.printStackTrace();
		}finally{
			if(fout!=null){
				try {
					fout.close();
				} catch (IOException e1) {
				}
			}
			if(wb!=null){
				try {
					wb.close();
				} catch (IOException e1) {
				}
			}
		}
	}
	
}

