package com.persagy.ems.finein.common.util;

import com.persagy.finein.enumeration.EnumPrepayChargeType;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年10月10日 下午8:19:31

* 说明:
*/

public class UnitUtil {
	
	/**
	 * 获取累计量单位
	 * @param energyTypeId 能耗类型
	 * @return
	 */
	public static String getCumulantUnit(String energyTypeId){
		if("Dian".equals(energyTypeId)){
			return "kWh";
		}else if("Shui".equals(energyTypeId)){
			return "m³";
		}else if("ReShui".equals(energyTypeId)){
			return "m³";
		}else if("RanQi".equals(energyTypeId)){
			return "m³";
		}else{
			return null;
		}
	}

	public static String getDataUnit(String energyTypeId,String paramType,EnumPrepayChargeType mode){
		if("Dian".equals(energyTypeId)){
			if("DianGongLv".equals(paramType)){
				return "kW";
			}else if("FeiYong".equals(paramType)){
				return "元";
			}else if("HaoDianLiang".equals(paramType)){
				return "kWh";
			}else if("LeiJiLiang".equals(paramType)){
				return "kWh";
			}else if("ShengYuLiang".equals(paramType)){
				if(mode == null || mode == EnumPrepayChargeType.Liang){
					return "kWh";
				}else{
					return "元";
				}
			}else{
				return null;
			}
		}else if("Shui".equals(energyTypeId)){
			if("FeiYong".equals(paramType)){
				return "元";
			}else if("HaoShuiLiang".equals(paramType)){
				return "m³";
			}else if("LeiJiLiang".equals(paramType)){
				return "m³";
			}else if("ShengYuLiang".equals(paramType)){
				if(mode == null || mode == EnumPrepayChargeType.Liang){
					return "m³";
				}else{
					return "元";
				}
			}else{
				return null;
			}
		}else if("ReShui".equals(energyTypeId)){
			if("FeiYong".equals(paramType)){
				return "元";
			}else if("HaoReShuiLiang".equals(paramType)){
				return "m³";
			}else if("LeiJiLiang".equals(paramType)){
				return "m³";
			}else if("ShengYuLiang".equals(paramType)){
				if(mode == null || mode == EnumPrepayChargeType.Liang){
					return "m³";
				}else{
					return "元";
				}
			}else{
				return null;
			}
		}else if("RanQi".equals(energyTypeId)){
			if("FeiYong".equals(paramType)){
				return "元";
			}else if("HaoRanQiLiang".equals(paramType)){
				return "m³";
			}else if("LeiJiLiang".equals(paramType)){
				return "m³";
			}else if("ShengYuLiang".equals(paramType)){
				if(mode == null || mode == EnumPrepayChargeType.Liang){
					return "m³";
				}else{
					return "元";
				}
			}else{
				return null;
			}
		}else{
			return null;
		}
	}
	
	public static String getBillModeUnit(String energyTypeId,EnumPrepayChargeType mode){
		if("Dian".equals(energyTypeId)){
			if(mode == EnumPrepayChargeType.Liang){
				return "kWh";
			}else{
				return "元";
			}
		}else if("Shui".equals(energyTypeId)){
			if(mode == EnumPrepayChargeType.Liang){
				return "m³";
			}else{
				return "元";
			}
		}else if("ReShui".equals(energyTypeId)){
			if(mode == EnumPrepayChargeType.Liang){
				return "m³";
			}else{
				return "元";
			}
		}else if("RanQi".equals(energyTypeId)){
			if(mode == EnumPrepayChargeType.Liang){
				return "m³";
			}else{
				return "元";
			}
		}else{
			return null;
		}
	}
	
	public static String getAreaDataUnit(String energyTypeId,String paramType){
		if("Dian".equals(energyTypeId)){
			if("FeiYong".equals(paramType)){
				return "元/m²";
			}else if("HaoDianLiang".equals(paramType)){
				return "kWh/m²";
			}else{
				return null;
			}
		}else if("Shui".equals(energyTypeId)){
			if("FeiYong".equals(paramType)){
				return "元/m²";
			}else if("HaoShuiLiang".equals(paramType)){
				return "m³/m²";
			}else{
				return null;
			}
		}else if("ReShui".equals(energyTypeId)){
			if("FeiYong".equals(paramType)){
				return "元/m²";
			}else if("HaoReShuiLiang".equals(paramType)){
				return "m³/m²";
			}else{
				return null;
			}
		}else if("RanQi".equals(energyTypeId)){
			if("FeiYong".equals(paramType)){
				return "元/m²";
			}else if("HaoRanQiLiang".equals(paramType)){
				return "m³/m²";
			}else{
				return null;
			}
		}else{
			return null;
		}
		
	}
}

