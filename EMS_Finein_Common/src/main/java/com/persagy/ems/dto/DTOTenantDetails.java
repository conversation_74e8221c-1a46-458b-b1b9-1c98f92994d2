package com.persagy.ems.dto;

import com.persagy.ems.pojo.finein.FnPriceTemplate;

import java.util.Date;

/**
 * 作者:zhangyuan(kedou)
 *
 * 时间:2017年10月10日 下午1:55:39
 *
 * 说明:
 */

public class DTOTenantDetails {

	private String tenantId;
	private String tenantFlag;
	private String tenantName;
	private String buildingId;
	private String buildingName;
	private String tenantTypeId;
	private String tenantTypeName;
	private Double area;
	private String contactName;
	private String contactMobile;
	private String meterId;
	private String energyTypeId;
	private Integer remoteRechargeStatus;
	private String roomCode;
	private Date activeTime;
	private Integer status;
	private Integer isAlarm;

	private Double remainData;
	private Integer remainMaxDays;
	private Integer remainMinDays;

	//租户价格详情    xsg
	private String priceId;
	private String priceEnergyTypeId;
	private String priceName;
	private String priceCreateUserId;
	private Date priceCreateTime;
	private Integer priceType;
	private String priceContent;
	private Date priceLastUpdateTime;
	private Integer priceIsValid;
	private Date priceInvalidTime;
	private String priceLastUpdateUserId;

	public Date getPriceInvalidTime() {
		return priceInvalidTime;
	}

	public void setPriceInvalidTime(Date priceInvalidTime) {
		this.priceInvalidTime = priceInvalidTime;
	}

	public String getPriceId() {
		return priceId;
	}

	public void setPriceId(String priceId) {
		this.priceId = priceId;
	}

	public String getPriceEnergyTypeId() {
		return priceEnergyTypeId;
	}

	public void setPriceEnergyTypeId(String priceEnergyTypeId) {
		this.priceEnergyTypeId = priceEnergyTypeId;
	}

	public String getPriceName() {
		return priceName;
	}

	public void setPriceName(String priceName) {
		this.priceName = priceName;
	}

	public String getPriceCreateUserId() {
		return priceCreateUserId;
	}

	public void setPriceCreateUserId(String priceCreateUserId) {
		this.priceCreateUserId = priceCreateUserId;
	}

	public Date getPriceCreateTime() {
		return priceCreateTime;
	}

	public void setPriceCreateTime(Date priceCreateTime) {
		this.priceCreateTime = priceCreateTime;
	}

	public Integer getPriceType() {
		return priceType;
	}

	public void setPriceType(Integer priceType) {
		this.priceType = priceType;
	}

	public String getPriceContent() {
		return priceContent;
	}

	public void setPriceContent(String priceContent) {
		this.priceContent = priceContent;
	}

	public Date getPriceLastUpdateTime() {
		return priceLastUpdateTime;
	}

	public void setPriceLastUpdateTime(Date priceLastUpdateTime) {
		this.priceLastUpdateTime = priceLastUpdateTime;
	}

	public Integer getPriceIsValid() {
		return priceIsValid;
	}

	public void setPriceIsValid(Integer priceIsValid) {
		this.priceIsValid = priceIsValid;
	}

	public String getPriceLastUpdateUserId() {
		return priceLastUpdateUserId;
	}

	public void setPriceLastUpdateUserId(String priceLastUpdateUserId) {
		this.priceLastUpdateUserId = priceLastUpdateUserId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getTenantName() {
		return tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getBuildingName() {
		return buildingName;
	}

	public void setBuildingName(String buildingName) {
		this.buildingName = buildingName;
	}

	public String getTenantTypeId() {
		return tenantTypeId;
	}

	public void setTenantTypeId(String tenantTypeId) {
		this.tenantTypeId = tenantTypeId;
	}

	public String getTenantTypeName() {
		return tenantTypeName;
	}

	public void setTenantTypeName(String tenantTypeName) {
		this.tenantTypeName = tenantTypeName;
	}

	public Double getArea() {
		return area;
	}

	public void setArea(Double area) {
		this.area = area;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactMobile() {
		return contactMobile;
	}

	public void setContactMobile(String contactMobile) {
		this.contactMobile = contactMobile;
	}

	public String getRoomCode() {
		return roomCode;
	}

	public void setRoomCode(String roomCode) {
		this.roomCode = roomCode;
	}

	public Date getActiveTime() {
		return activeTime;
	}

	public void setActiveTime(Date activeTime) {
		this.activeTime = activeTime;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public Double getRemainData() {
		return remainData;
	}

	public void setRemainData(Double remainData) {
		this.remainData = remainData;
	}

	public Integer getRemainMaxDays() {
		return remainMaxDays;
	}

	public void setRemainMaxDays(Integer remainMaxDays) {
		this.remainMaxDays = remainMaxDays;
	}

	public Integer getRemainMinDays() {
		return remainMinDays;
	}

	public void setRemainMinDays(Integer remainMinDays) {
		this.remainMinDays = remainMinDays;
	}

	public Integer getRemoteRechargeStatus() {
		return remoteRechargeStatus;
	}

	public void setRemoteRechargeStatus(Integer remoteRechargeStatus) {
		this.remoteRechargeStatus = remoteRechargeStatus;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Integer getIsAlarm() {
		return isAlarm;
	}

	public void setIsAlarm(Integer isAlarm) {
		this.isAlarm = isAlarm;
	}

	public String getTenantFlag() {
		return tenantFlag;
	}

	public void setTenantFlag(String tenantFlag) {
		this.tenantFlag = tenantFlag;
	}

}
