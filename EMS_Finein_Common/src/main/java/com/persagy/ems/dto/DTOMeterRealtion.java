package com.persagy.ems.dto;

import java.util.List;

public class DTOMeterRealtion {

	// 表id
	private String meterId;
	// 表类型
	private String meterType;
	// 表类型名称
	private String meterTypeName;
	// 表排序
	private Integer order;
	// 非默认电表监测信息点列表
	private List<String> functionList;

	public Integer getOrder() {
		return order;
	}

	public void setOrder(Integer order) {
		this.order = order;
	}

	public List<String> getFunctionList() {
		return functionList;
	}

	public void setFunctionList(List<String> functionList) {
		this.functionList = functionList;
	}

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public String getMeterType() {
		return meterType;
	}

	public void setMeterType(String meterType) {
		this.meterType = meterType;
	}

	public String getMeterTypeName() {
		return meterTypeName;
	}

	public void setMeterTypeName(String meterTypeName) {
		this.meterTypeName = meterTypeName;
	}

}
