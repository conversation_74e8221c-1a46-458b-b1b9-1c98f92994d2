package com.persagy.ems.dto;

import java.util.Date;

public class DTORecordPrePayReturn {
	private String buildingId;
	private String buildingName;
	private String orderId;
	private Integer orderType;// 账单类型，0充值,1退费
	private Integer source;// 账单来源,0本地//1其他系统
	private String energyTypeId;// 账单来源,0本地//1其他系统
	private Integer bodyType;// 0租户//1仪表  不可为null
	private String tenantId;// 租户id,不可为null
	private String tenantFlag;// 租户id,不可为null
	private String tenantName;// 租户姓名
	private String bodyCode;//主体编号,不可为null
	private Double money;// 充值金额
	private Double amount;// 充值能耗 
	private Integer status;// 账单状态
	private String userId;//操作人id
	private String userName;// 操作人姓名
	private Date operateTime;//时间 ,不可为null
	private String systemCode;//充值系统编号
	private Date orderTime;//账单时间
	
	
	
	public String getBuildingId() {
		return buildingId;
	}
	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}
	public String getBuildingName() {
		return buildingName;
	}
	public void setBuildingName(String buildingName) {
		this.buildingName = buildingName;
	}
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	
	
	public Integer getOrderType() {
		return orderType;
	}
	public void setOrderType(Integer orderType) {
		this.orderType = orderType;
	}
	public Integer getSource() {
		return source;
	}
	public void setSource(Integer source) {
		this.source = source;
	}
	public String getEnergyTypeId() {
		return energyTypeId;
	}
	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}
	public Integer getBodyType() {
		return bodyType;
	}
	public void setBodyType(Integer bodyType) {
		this.bodyType = bodyType;
	}
	public String getTenantId() {
		return tenantId;
	}
	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
	public String getTenantName() {
		return tenantName;
	}
	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}
	public String getBodyCode() {
		return bodyCode;
	}
	public void setBodyCode(String bodyCode) {
		this.bodyCode = bodyCode;
	}
	public Double getMoney() {
		return money;
	}
	public void setMoney(Double money) {
		this.money = money;
	}
	public Double getAmount() {
		return amount;
	}
	public void setAmount(Double amount) {
		this.amount = amount;
	}
	public Integer getStatus() {
		return status;
	}
	public void setStatus(Integer status) {
		this.status = status;
	}
	public String getUserId() {
		return userId;
	}
	public void setUserId(String userId) {
		this.userId = userId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public Date getOperateTime() {
		return operateTime;
	}
	public void setOperateTime(Date operateTime) {
		this.operateTime = operateTime;
	}
	public String getSystemCode() {
		return systemCode;
	}
	public void setSystemCode(String systemCode) {
		this.systemCode = systemCode;
	}
	public String getTenantFlag() {
		return tenantFlag;
	}
	public void setTenantFlag(String tenantFlag) {
		this.tenantFlag = tenantFlag;
	}
	
	public Date getOrderTime() {
		return orderTime;
	}
	public void setOrderTime(Date orderTime) {
		this.orderTime = orderTime;
	}
	@Override
	public String toString() {
		return "DTORecordPrePayReturn [buildingId=" + buildingId + ", buildingName=" + buildingName + ", orderId="
				+ orderId + ", orderType=" + orderType + ", source=" + source + ", energyTypeId=" + energyTypeId
				+ ", bodyType=" + bodyType + ", tenantId=" + tenantId + ", tenantFlag=" + tenantFlag + ", tenantName="
				+ tenantName + ", bodyCode=" + bodyCode + ", money=" + money + ", amount=" + amount + ", status="
				+ status + ", userId=" + userId + ", userName=" + userName + ", operateTime=" + operateTime
				+ ", systemCode=" + systemCode + "]";
	}
	
}
