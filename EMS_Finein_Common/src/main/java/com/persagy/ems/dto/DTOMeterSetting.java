package com.persagy.ems.dto;


/**
 * 可设置仪表DTO
 * <AUTHOR>
 */
public class DTOMeterSetting {

    /**
     * 仪表ID
     */
    private String meterId;

    /**
     * 安装位置(房间编号)
     */
    private String installAddress;

    /**
     * 建筑
     */
    private String buildingId;

    /**
     * 楼层ID
     */
    private String floorId;

    /**
     * 面积
     */
    private Double area;

    public String getMeterId() {
        return meterId;
    }

    public void setMeterId(String meterId) {
        this.meterId = meterId;
    }

    public String getInstallAddress() {
        return installAddress;
    }

    public void setInstallAddress(String installAddress) {
        this.installAddress = installAddress;
    }

    public String getBuildingId() {
        return buildingId;
    }

    public void setBuildingId(String buildingId) {
        this.buildingId = buildingId;
    }

    public String getFloorId() {
        return floorId;
    }

    public void setFloorId(String floorId) {
        this.floorId = floorId;
    }

    public Double getArea() {
        return area;
    }

    public void setArea(Double area) {
        this.area = area;
    }

    @Override
    public String toString() {
        return "DTOMeterSetting{" +
                "meterId='" + meterId + '\'' +
                ", installAddress='" + installAddress + '\'' +
                ", buildingId='" + buildingId + '\'' +
                ", floorId='" + floorId + '\'' +
                ", area='" + area + '\'' +
                '}';
    }
}
