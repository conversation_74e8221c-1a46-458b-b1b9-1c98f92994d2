package com.persagy.ems.dto;


/**
 * 作者:zhangyuan(kedou)

 * 时间:2017年10月10日 下午1:55:39

 * 说明:
 */

public class DTOMeter {
	private String meterId;
	private String meterName;
	private String energyTypeId;
	private String protocolId;
	private Double radio;
	private String clientIp;
	private String serverIp;
	private String extend;
	private Integer meterType;
	private Integer isUse;



	public String getMeterId() {
		return meterId;
	}
	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}
	public String getMeterName() {
		return meterName;
	}
	public void setMeterName(String meterName) {
		this.meterName = meterName;
	}
	public String getEnergyTypeId() {
		return energyTypeId;
	}
	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}
	public String getProtocolId() {
		return protocolId;
	}
	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}
	public Double getRadio() {
		return radio;
	}
	public void setRadio(Double radio) {
		this.radio = radio;
	}
	public String getExtend() {
		return extend;
	}
	public void setExtend(String extend) {
		this.extend = extend;
	}
	public Integer getMeterType() {
		return meterType;
	}
	public void setMeterType(Integer meterType) {
		this.meterType = meterType;
	}
	public Integer getIsUse() {
		return isUse;
	}
	public void setIsUse(Integer isUse) {
		this.isUse = isUse;
	}
	public String getClientIp() {
		return clientIp;
	}
	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}
	public String getServerIp() {
		return serverIp;
	}
	public void setServerIp(String serverIp) {
		this.serverIp = serverIp;
	}
}

