package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnRolePermission;

/**

* 说明:
*/

@Dimension
@Entity(name="FnRolePermissionSqlite")
@Table(
		name="t_fn_role_permission",
		comment="角色权限表关系表",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnRolePermissionSqlite extends FnRolePermission {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3222643299976675856L;




}
