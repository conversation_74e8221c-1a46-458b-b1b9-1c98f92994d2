package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnProtocolFunction")
@Table(name = "t_fn_protocol_function", comment = "协议功能", schema = Schema.EMS, indexes = {})
public class FnProtocolFunction extends BaseId {

	private static final long serialVersionUID = -8572110174704830084L;

	@Column(order = 2, name = "c_protocol_id", length = 50, nullable = false, comment = "协议主键")
	@JsonProperty("protocolId")
	private String protocolId;

	@Column(order = 3, name = "c_function_id", length = 11, nullable = false, comment = "功能号")
	@JsonProperty("functionId")
	private Integer functionId;
	
	

	public String getProtocolId() {
		return protocolId;
	}

	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}

	public Integer getFunctionId() {
		return functionId;
	}

	public void setFunctionId(Integer functionId) {
		this.functionId = functionId;
	}



}
