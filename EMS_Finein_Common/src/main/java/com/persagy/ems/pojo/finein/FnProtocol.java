package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnProtocol")
@Table(name = "t_fn_protocol", comment = "仪表协议", schema = Schema.EMS, indexes = {})
public class FnProtocol extends BaseId {


	private static final long serialVersionUID = -8825928571769385619L;

	@Column(order = 2, name = "c_name", length = 100, nullable = false, comment = "仪表协议名称")
	@JsonProperty("name")
	private String name;
	
	@Column(order = 3, name = "c_energy_type_id", length = 10, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;
	
	@Column(order = 4, name = "c_pay_type", length = 1, nullable = false, comment = "付费方式")
	@JsonProperty("payType")
	private Integer payType;
	
	@Column(order = 5, name = "c_pre_pay_type", length = 1, nullable = false, comment = "预付费方式")
	@JsonProperty("prePayType")
	private Integer prePayType;
	
	@Column(order = 6, name = "c_billing_mode", length = 1, nullable = true, comment = "充值模式")
	@JsonProperty("billingMode")
	private Integer billingMode;
	
	@Column(order = 7, name = "c_meter_type", length = 1, nullable = true, comment = "对应仪表类型")
	@JsonProperty("meterType")
	private Integer meterType;

	@Column(order = 8, name = "c_extend", length = 500, nullable = true, comment = "仪表协议扩展内容")
	@JsonProperty("extend")
	private String extend;

	@Column(order = 9, name = "c_remark", length = 500, nullable = true, comment = "说明")
	@JsonProperty("remark")
	private String remark;

	
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Integer getPrePayType() {
		return prePayType;
	}

	public void setPrePayType(Integer prePayType) {
		this.prePayType = prePayType;
	}

	public Integer getBillingMode() {
		return billingMode;
	}

	public void setBillingMode(Integer billingMode) {
		this.billingMode = billingMode;
	}

	public Integer getMeterType() {
		return meterType;
	}

	public void setMeterType(Integer meterType) {
		this.meterType = meterType;
	}

	public String getExtend() {
		return extend;
	}

	public void setExtend(String extend) {
		this.extend = extend;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}
	
	


	
}
