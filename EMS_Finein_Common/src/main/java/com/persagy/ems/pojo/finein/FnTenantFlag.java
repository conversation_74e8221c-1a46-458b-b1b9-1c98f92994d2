package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnTenantFlag")
@Table(name = "t_fn_tenant_flag", comment = "租户全码对应表", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_tenant_flag" }, unique = true),
		@Index(columns = { "c_building_id", "c_tenant_id" }, unique = true) })
public class FnTenantFlag extends BaseId {

	private static final long serialVersionUID = 5218453782949736370L;

	@Column(order = 2, name = "c_building_id", length = 36, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 3, name = "c_tenant_id", length = 100, nullable = false, comment = "租户ID")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_tenant_flag", length = 100, nullable = false, comment = "租户唯一标识")
	@JsonProperty("tenantFlag")
	private String tenantFlag;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantFlag() {
		return tenantFlag;
	}

	public void setTenantFlag(String tenantFlag) {
		this.tenantFlag = tenantFlag;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
}
