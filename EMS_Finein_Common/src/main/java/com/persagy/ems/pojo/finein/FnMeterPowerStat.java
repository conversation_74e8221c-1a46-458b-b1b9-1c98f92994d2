package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnMeterPowerStat")
@Table(name = "t_fn_meter_power_stat", comment = "仪表功率统计", transaction = false, schema = Schema.EMS, indexes = {
		@Index(columns = { "c_meter_id", "c_data_type" }, unique = true) })
public class FnMeterPowerStat extends BusinessObject {

	private static final long serialVersionUID = 2419770382898837225L;

	@Column(order = 1, name = "c_meter_id", length = 20, nullable = false, comment = "仪表主键")
	@JsonProperty("meterId")
	private String meterId;

	@Column(order = 2, name = "c_data_type", length = 1, nullable = false, comment = "数据类型")
	@JsonProperty("dataType")
	private Integer dataType;

	@Column(order = 3, name = "c_data", length = 18, scale = 8, nullable = false, comment = "数据")
	@JsonProperty("data")
	private Double data;

	@Column(order = 4, name = "c_time_from", length = 0, nullable = false, comment = "时间")
	@JsonProperty("timeFrom")
	private Date timeFrom;

	@Column(order = 5, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	public Double getData() {
		return data;
	}

	public void setData(Double data) {
		this.data = data;
	}

	public Date getTimeFrom() {
		return timeFrom;
	}

	public void setTimeFrom(Date timeFrom) {
		this.timeFrom = timeFrom;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

}
