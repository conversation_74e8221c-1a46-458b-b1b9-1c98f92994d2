package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnRecordForOtherSystemExtend")
@Table(name = "t_fn_record_for_other_system_extend", comment = "远程充值记录扩展表(表扣类型)", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_id" }) }, transaction = false)
public class FnRecordForOtherSystemExtend extends BaseId {

	private static final long serialVersionUID = 1400821364684312455L;

	@Column(order = 2, name = "c_meter_id", length = 36, nullable = false, comment = "仪表id")
	@JsonProperty("meterId")
	private String meterId;

	@Column(order = 3, name = "c_remain_data", length = 18, scale = 8, nullable = false, comment = "仪表剩余量")
	@JsonProperty("remainData")
	private Double remainData;

	@Column(order = 4, name = "c_update_time", length = 0, nullable = true, comment = "更新时间")
	@JsonProperty("updateTime")
	private Date updateTime;

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public Double getRemainData() {
		return remainData;
	}

	public void setRemainData(Double remainData) {
		this.remainData = remainData;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
