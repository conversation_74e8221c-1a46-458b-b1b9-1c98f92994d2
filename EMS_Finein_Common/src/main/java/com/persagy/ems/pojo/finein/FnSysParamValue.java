package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月15日 下午7:53:59
 * 
 * 说明:
 */

@Dimension
@Entity(name = "FnSysParamValue")
@Table(name = "t_fn_sys_param_value", comment = "系统参数", schema = Schema.EMS, indexes = {})
public class FnSysParamValue extends BusinessObject{

	private static final long serialVersionUID = -4950522388808505228L;

	@Id
	@Column(order = 1, name = "c_id", length = 50, nullable = false, comment = "主键")
	@JsonProperty("id")
	private String id;
	
	@Column(order = 2, name = "c_name", length = 100, nullable = false, comment = "系统参数名称")
	@JsonProperty("name")
	private String name;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	@Column(order = 3, name = "c_type", length = 1, nullable = false, comment = "系统参数值类型 0 Integer 1 Long 2 String 3 Double 4 Boolean 5 JSON")
	@JsonProperty("type")
	private String type;

	@Column(order = 4, name = "c_value", length = 2000, nullable = false, comment = "系统参数值")
	@JsonProperty("value")
	private String value;
	
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	

}
