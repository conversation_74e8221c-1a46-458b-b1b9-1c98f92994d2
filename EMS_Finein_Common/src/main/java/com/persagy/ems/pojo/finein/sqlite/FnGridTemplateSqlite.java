package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnGridTemplate;


@Dimension
@Entity(name="FnGridTemplateSqlite")
@Table(
		name="t_fn_grid_template",
		comment="租户报表模板",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnGridTemplateSqlite extends FnGridTemplate {

	private static final long serialVersionUID = -2092690931398951635L;
	
}
