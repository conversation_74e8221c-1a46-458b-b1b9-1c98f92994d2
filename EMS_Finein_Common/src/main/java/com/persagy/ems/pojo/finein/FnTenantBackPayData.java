package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnTenantBackPayData")
@Table(name = "t_fn_tenant_back_pay_data",
		transaction = false,
		comment = "租户后台付费能耗/费用数据计算", schema = Schema.EMS, indexes = { @Index(columns = {
		"c_building_id", "c_tenant_id", "c_energy_type_id", "c_time_type", "c_prepay_charge_type", "c_time_from"}, unique = true),
				@Index(columns = {
						"c_building_id", "c_tenant_id", "c_energy_type_id", "c_time_type", "c_prepay_charge_type"}) })
@Month(column="c_time_from")
public class FnTenantBackPayData extends BusinessObject {

	private static final long serialVersionUID = -8300395284282610667L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 4, name = "c_time_type", length = 1, nullable = false, comment = "时间类型")
	@JsonProperty("timeType")
	private Integer timeType;

	@Column(order = 5, name = "c_prepay_charge_type", length = 1, nullable = false, comment = "扣费类型")
	@JsonProperty("prepayChargeType")
	private Integer prepayChargeType;

	@Column(order = 6, name = "c_time_from", length = 0, nullable = false, comment = "时间")
	@JsonProperty("timeFrom")
	private Date timeFrom;
	
	@Column(order = 7, name = "c_data", length = 18, scale = 8, nullable = true, comment = "数据")
	@JsonProperty("data")
	private Double data;

	@Column(order = 8, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;
	
	

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Integer getTimeType() {
		return timeType;
	}

	public void setTimeType(Integer timeType) {
		this.timeType = timeType;
	}

	public Integer getPrepayChargeType() {
		return prepayChargeType;
	}

	public void setPrepayChargeType(Integer prepayChargeType) {
		this.prepayChargeType = prepayChargeType;
	}

	public Date getTimeFrom() {
		return timeFrom;
	}

	public void setTimeFrom(Date timeFrom) {
		this.timeFrom = timeFrom;
	}

	public Double getData() {
		return data;
	}

	public void setData(Double data) {
		this.data = data;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
	
	

	
}
