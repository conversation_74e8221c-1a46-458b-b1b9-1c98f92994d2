package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnTenant")
@Table(name = "t_fn_tenant", comment = "租户", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_building_id" }) })
public class FnTenant extends BaseId {


	private static final long serialVersionUID = -582003873118154988L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 3, name = "c_tenant_type_id", length = 50, nullable = false, comment = "租户类型主键")
	@JsonProperty("tenantTypeId")
	private String tenantTypeId;

	@Column(order = 4, name = "c_name", length = 200, nullable = false, comment = "租户名称")
	@JsonProperty("name")
	private String name;

	@Column(order = 5, name = "c_area", length = 18, scale = 8, nullable = true, comment = "租户面积")
	@JsonProperty("area")
	private Double area;
	
	@Column(order = 6, name = "c_room_codes", length = 200, nullable = true, comment = "房间编号")
	@JsonProperty("roomCodes")
	private String roomCodes;

	@Column(order = 7, name = "c_energy_type_ids", length = 80, nullable = true, comment = "能耗类型列表")
	@JsonProperty("energyTypeIds")
	private String energyTypeIds;
	
	@Column(order = 8, name = "c_status", length = 1, nullable = false, comment = "租户状态，未激活，已激活，已退租")
	@JsonProperty("status")
	private Integer status;

	@Column(order = 9, name = "c_contact_name", length = 20, nullable = false, comment = "联系人名称")
	@JsonProperty("contactName")
	private String contactName;

	@Column(order = 10, name = "c_contact_mobile", length = 20, nullable = false, comment = "联系人电话")
	@JsonProperty("contactMobile")
	private String contactMobile;

	@Column(order = 11, name = "c_remark", length = 200, nullable = true, comment = "备注")
	@JsonProperty("remark")
	private String remark;

	@Column(order = 12, name = "c_create_time", length = 0, nullable = true, comment = "房间创建时间")
	@JsonProperty("createTime")
	private Date createTime;

	@Column(order = 13, name = "c_create_user_id", length = 50, nullable = true, comment = "房间创建人")
	@JsonProperty("createUserId")
	private String createUserId;

	@Column(order = 14, name = "c_is_valid", length = 1, nullable = false, comment = "房间是否有效")
	@JsonProperty("isValid")
	private Integer isValid;

	@Column(order = 15, name = "c_invalid_time", length = 0, nullable = true, comment = "房间无效时间")
	@JsonProperty("invalidTime")
	private Date invalidTime;

	@Column(order = 16, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	@Column(order = 17, name = "c_last_update_user_id", length = 50, nullable = true, comment = "最后修改人")
	@JsonProperty("lastUpdateUserId")
	private String lastUpdateUserId;

	@Column(order = 18, name = "c_active_time", length = 0, nullable = true, comment = "激活时间")
	@JsonProperty("activeTime")
	private Date activeTime;

	@Column(order = 19, name = "c_active_user_id", length = 50, nullable = true, comment = "激活人")
	@JsonProperty("activeUserId")
	private String activeUserId;

	@Column(order = 20, name = "c_leave_time", length = 0, nullable = true, comment = "退租时间")
	@JsonProperty("leaveTime")
	private Date leaveTime;

	@Column(order = 21, name = "c_leave_user_id", length = 50, nullable = true, comment = "退组人")
	@JsonProperty("leaveUserId")
	private String leaveUserId;
	
	

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantTypeId() {
		return tenantTypeId;
	}

	public void setTenantTypeId(String tenantTypeId) {
		this.tenantTypeId = tenantTypeId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Double getArea() {
		return area;
	}

	public void setArea(Double area) {
		this.area = area;
	}

	public String getRoomCodes() {
		return roomCodes;
	}

	public void setRoomCodes(String roomCodes) {
		this.roomCodes = roomCodes;
	}

	public String getEnergyTypeIds() {
		return energyTypeIds;
	}

	public void setEnergyTypeIds(String energyTypeIds) {
		this.energyTypeIds = energyTypeIds;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactMobile() {
		return contactMobile;
	}

	public void setContactMobile(String contactMobile) {
		this.contactMobile = contactMobile;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Date getInvalidTime() {
		return invalidTime;
	}

	public void setInvalidTime(Date invalidTime) {
		this.invalidTime = invalidTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId;
	}

	public Date getActiveTime() {
		return activeTime;
	}

	public void setActiveTime(Date activeTime) {
		this.activeTime = activeTime;
	}

	public String getActiveUserId() {
		return activeUserId;
	}

	public void setActiveUserId(String activeUserId) {
		this.activeUserId = activeUserId;
	}

	public Date getLeaveTime() {
		return leaveTime;
	}

	public void setLeaveTime(Date leaveTime) {
		this.leaveTime = leaveTime;
	}

	public String getLeaveUserId() {
		return leaveUserId;
	}

	public void setLeaveUserId(String leaveUserId) {
		this.leaveUserId = leaveUserId;
	}
	
	

	
}
