package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FNTenantCloudTypeDic;

/**


* 说明:租户云租赁业态
*/

@Dimension
@Entity(name="FNTenantCloudTypeDicSqlite")
@Table(
		name="t_fn_tenant_cloud_type_dic",
		comment="云端-租户租赁业态",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FNTenantCloudTypeDicSqlite extends FNTenantCloudTypeDic {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3681325742363172856L;


}
