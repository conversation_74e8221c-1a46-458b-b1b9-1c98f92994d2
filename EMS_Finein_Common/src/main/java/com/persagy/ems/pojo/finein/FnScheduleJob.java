package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnScheduleJob")
@Table(name = "t_fn_schedule_job", comment = "定时任务", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_job_name","c_job_group" }) })
public class FnScheduleJob extends BaseId {

	private static final long serialVersionUID = -1637469797231368917L;

	@Column(order = 1, name = "c_job_name", length = 20, nullable = false, comment = "任务名称")
	@JsonProperty("jobName")
	private String jobName;

	@Column(order = 2, name = "c_job_group", length = 50, nullable = false, comment = "任务分组")
	@JsonProperty("jobGroup")
	private String jobGroup;

	@Column(order = 3, name = "c_job_status", length =1, nullable = false, comment = "任务状态0关闭,1启动")
	@JsonProperty("jobStatus")
	private Integer jobStatus;

	@Column(order = 4, name = "c_cron_expression", length = 20, nullable = false, comment = "cron表达式")
	@JsonProperty("cronExpression")
	private String cronExpression;
	
	@Column(order = 5, name = "c_time_list", length =20, nullable = true, comment = "发送时间(小时)")
	@JsonProperty("timeList")
	private String timeList;
	

	@Column(order = 6, name = "c_create_time", length = 0, nullable = true, comment = "创建时间")
	@JsonProperty("createTime")
	private Date createTime;
	
	@Column(order = 7, name = "c_update_time", length = 0, nullable = true, comment = "更新时间")
	@JsonProperty("updateTime")
	private Date updateTime;

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public String getJobGroup() {
		return jobGroup;
	}

	public void setJobGroup(String jobGroup) {
		this.jobGroup = jobGroup;
	}

	public Integer getJobStatus() {
		return jobStatus;
	}

	public void setJobStatus(Integer jobStatus) {
		this.jobStatus = jobStatus;
	}

	public String getCronExpression() {
		return cronExpression;
	}

	public void setCronExpression(String cronExpression) {
		this.cronExpression = cronExpression;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public String getTimeList() {
		return timeList;
	}

	public void setTimeList(String timeList) {
		this.timeList = timeList;
	}

}
