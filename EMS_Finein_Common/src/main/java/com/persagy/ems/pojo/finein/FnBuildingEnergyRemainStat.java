package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnBuildingEnergyRemainStat")
@Table(name = "t_fn_building_energy_remain_stat", comment = "项目能耗剩余统计", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_building_id", "c_time_from", "c_energy_type_id" }, unique = true) })
public class FnBuildingEnergyRemainStat extends BusinessObject {

	private static final long serialVersionUID = 7550762485694115064L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 2, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;



	@Column(order =3, name = "c_remain_amount", length = 18, scale = 8, nullable = true, comment = "本期剩余量")
	@JsonProperty("remainAmount")
	private Double remainAmount;

	@Column(order =4, name = "c_remain_money", length = 18, scale = 8, nullable = true, comment = "本期剩余金额")
	@JsonProperty("remainMoney")
	private Double remainMoney;
	
	@Column(order = 5, name = "c_amount_to_money", length = 18, scale = 8, nullable = true, comment = "剩余量转钱")
	@JsonProperty("amountToMoney")
	private Double amountToMoney;
	
	@Column(order = 6, name = "c_money_to_amount", length = 18, scale = 8, nullable = true, comment = "剩余金额转量")
	@JsonProperty("moneyToAmount")
	private Double moneyToAmount;

	@Column(order =7, name = "c_time_from", length = 0, nullable = false, comment = "时间")
	@JsonProperty("timeFrom")
	private Date timeFrom;

	@Column(order =8, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}


	public Double getRemainAmount() {
		return remainAmount;
	}

	public void setRemainAmount(Double remainAmount) {
		this.remainAmount = remainAmount;
	}

	public Double getRemainMoney() {
		return remainMoney;
	}

	public void setRemainMoney(Double remainMoney) {
		this.remainMoney = remainMoney;
	}
	
	public Double getAmountToMoney() {
		return amountToMoney;
	}

	public void setAmountToMoney(Double amountToMoney) {
		this.amountToMoney = amountToMoney;
	}

	public Double getMoneyToAmount() {
		return moneyToAmount;
	}

	public void setMoneyToAmount(Double moneyToAmount) {
		this.moneyToAmount = moneyToAmount;
	}

	public Date getTimeFrom() {
		return timeFrom;
	}

	public void setTimeFrom(Date timeFrom) {
		this.timeFrom = timeFrom;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}


}
