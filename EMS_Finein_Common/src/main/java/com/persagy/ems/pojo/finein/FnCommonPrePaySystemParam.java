package com.persagy.ems.pojo.finein;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;


@Dimension
@Entity(name = "FnCommonPrePaySystemParam")
@Table(name = "t_fn_common_pre_pay_system_param", comment = "通用充值系统参数", schema = Schema.EMS, 
		transaction = false,
		indexes = {
				@Index(columns = { "c_system_code" })
}
		)
public class FnCommonPrePaySystemParam extends BaseId {
	

	private static final long serialVersionUID = -5306632248870690143L;

	@Column(order = 1, name = "c_system_code", length =50, nullable = false, comment = "系统编码")
	@JsonProperty("systemCode")
	private String systemCode;
	
	@Column(order = 2, name = "c_system_name", length = 50, nullable = false, comment = "系统名称")
	@JsonProperty("systemName")
	private String systemName;
	
	@Column(order = 3, name = "c_encryption_key", length = 50, nullable = false, comment = "系统秘钥")
	@JsonProperty("encryptionKey")
	private String encryptionKey;

	public String getSystemCode() {
		return systemCode;
	}

	public void setSystemCode(String systemCode) {
		this.systemCode = systemCode;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getEncryptionKey() {
		return encryptionKey;
	}

	public void setEncryptionKey(String encryptionKey) {
		this.encryptionKey = encryptionKey;
	}
}
