package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnRoomMeter")
@Table(name = "t_fn_room_meter", comment = "房间仪表", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_building_id" }), @Index(columns = { "c_room_id" }),
		@Index(columns = { "c_energy_type_id" }) })
public class FnRoomMeter extends BaseId {


	private static final long serialVersionUID = 4271609432023879248L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 3, name = "c_room_id", length = 50, nullable = false, comment = "房间主键")
	@JsonProperty("roomId")
	private String roomId;
	
	@Column(order = 4, name = "c_room_code", length = 100, nullable = false, comment = "房间编号")
	@JsonProperty("roomCode")
	private String roomCode;

	@Column(order = 5, name = "c_energy_type_id", length = 8, nullable = false, comment = "房间能耗类型")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 6, name = "c_meter_id", length = 15, nullable = false, comment = "仪表主键")
	@JsonProperty("meterId")
	private String meterId;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getRoomId() {
		return roomId;
	}

	public void setRoomId(String roomId) {
		this.roomId = roomId;
	}

	public String getRoomCode() {
		return roomCode;
	}

	public void setRoomCode(String roomCode) {
		this.roomCode = roomCode;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	

}
