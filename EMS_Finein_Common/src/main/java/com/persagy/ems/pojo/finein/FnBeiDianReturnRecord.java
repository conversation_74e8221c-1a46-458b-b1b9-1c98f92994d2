package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnBeiDianReturnRecord")
@Table(name = "t_fn_beidian_return_record", comment = "北电仪表退费明细", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_meter_id"}, unique = true) })
public class FnBeiDianReturnRecord extends BaseId {

	private static final long serialVersionUID = -5922900603532516919L;

	@Column(order = 2, name = "c_meter_id", length = 20, nullable = false, comment = "仪表编号")
	@JsonProperty("meterId")
	private String meterId;
	
	
	@Column(order = 3, name = "c_protocol_id", length =20, nullable = false, comment = "仪表协议")
	@JsonProperty("protocolId")
	private String protocolId;
	
	@Column(order = 4, name = "c_money", length = 18, scale = 8, nullable = false, comment = "退费金额")
	@JsonProperty("money")
	private Double money;

	@Column(order = 5, name = "c_update_time", length =0, nullable = false, comment = "更新时间")
	@JsonProperty("updateTime")
	private Date updateTime;


	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public String getProtocolId() {
		return protocolId;
	}

	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}

	public Double getMoney() {
		return money;
	}

	public void setMoney(Double money) {
		this.money = money;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

}
