package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnTenantContact")
@Table(name = "t_fn_tenant_contact", comment = "租户联系人", schema = Schema.EMS, indexes = { 
		@Index(columns = { "c_tenant_id" }) })
public class FnTenantContact extends BaseId {

	private static final long serialVersionUID = -582003873118154988L;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户编码")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_contact_name", length = 20, nullable = false, comment = "联系人名称")
	@JsonProperty("contactName")
	private String contactName;

	@Column(order = 4, name = "c_contact_mobile", length = 20, nullable = false, comment = "联系人电话")
	@JsonProperty("contactMobile")
	private String contactMobile;
	
	

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContactMobile() {
		return contactMobile;
	}

	public void setContactMobile(String contactMobile) {
		this.contactMobile = contactMobile;
	}


}
