package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnTenantFunctionData")
@Table(name = "t_fn_tenant_function_data", comment = "租户按功能数据", schema = Schema.EMS, 
		transaction = false,
		indexes = {
		@Index(columns = { "c_building_id", "c_tenant_id", "c_function_id", "c_data_type", "c_time_type", "c_time_from" }, unique = true) })
@Month(column="c_time_from")
public class FnTenantFunctionData extends BusinessObject {

	private static final long serialVersionUID = -1884793317493411681L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_function_id", length = 11, nullable = false, comment = "功能号")
	@JsonProperty("functionId")
	private Integer functionId;
	
	@Column(order = 4, name = "c_time_type", length = 1, nullable = false, comment = "时间类型")
	@JsonProperty("timeType")
	private Integer timeType;

	@Column(order = 5, name = "c_data_type", length = 1, nullable = false, comment = "数据类型")
	@JsonProperty("dataType")
	private Integer dataType;
	
	@Column(order = 6, name = "c_time_from", length = 0, nullable = false, comment = "时间")
	@JsonProperty("timeFrom")
	private Date timeFrom;
	
	@Column(order = 7, name = "c_data", length = 18, scale = 8, nullable = false, comment = "数据")
	@JsonProperty("data")
	private Double data;

	@Column(order = 8, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	
	
	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public Integer getFunctionId() {
		return functionId;
	}

	public void setFunctionId(Integer functionId) {
		this.functionId = functionId;
	}

	public Integer getTimeType() {
		return timeType;
	}

	public void setTimeType(Integer timeType) {
		this.timeType = timeType;
	}

	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	public Date getTimeFrom() {
		return timeFrom;
	}

	public void setTimeFrom(Date timeFrom) {
		this.timeFrom = timeFrom;
	}

	public Double getData() {
		return data;
	}

	public void setData(Double data) {
		this.data = data;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
	
	

	
}
