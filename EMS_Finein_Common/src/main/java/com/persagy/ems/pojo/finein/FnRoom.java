package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnRoom")
@Table(name = "t_fn_room", comment = "房间", schema = Schema.EMS, indexes = { @Index(columns = { "c_building_id" }),
		@Index(columns = { "c_floor_id" }), @Index(columns = { "c_code" }) })
public class FnRoom extends BaseId {


	private static final long serialVersionUID = -5267675002112169835L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 3, name = "c_floor_id", length = 50, nullable = false, comment = "楼层主键")
	@JsonProperty("floorId")
	private String floorId;

	@Column(order = 4, name = "c_code", length = 50, nullable = false, comment = "房间编码")
	@JsonProperty("code")
	private String code;

	@Column(order = 5, name = "c_area", length = 18, scale = 8, nullable = false, comment = "房间面积")
	@JsonProperty("area")
	private Double area;


	@Column(order = 6, name = "c_energy_type_ids", length = 200, nullable = false, comment = "房间能耗类型")
	@JsonProperty("energyTypeIds")
	private String energyTypeIds;

	@Column(order = 7, name = "c_status", length = 1, nullable = false, comment = "房间状态,0空闲，1使用中")
	@JsonProperty("status")
	private Integer status;

	@Column(order = 8, name = "c_remark", length = 200, nullable = true, comment = "备注")
	@JsonProperty("remark")
	private String remark;

	@Column(order = 9, name = "c_create_time", length = 0, nullable = true, comment = "房间创建时间")
	@JsonProperty("createTime")
	private Date createTime;

	@Column(order = 10, name = "c_create_user_id", length = 50, nullable = true, comment = "房间创建人")
	@JsonProperty("createUserId")
	private String createUserId;

	@Column(order = 11, name = "c_is_valid", length = 1, nullable = false, comment = "房间是否有效")
	@JsonProperty("isValid")
	private Integer isValid;

	@Column(order = 12, name = "c_invalid_time", length = 0, nullable = true, comment = "房间无效时间")
	@JsonProperty("invalidTime")
	private Date invalidTime;

	
	
	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getFloorId() {
		return floorId;
	}

	public void setFloorId(String floorId) {
		this.floorId = floorId;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Double getArea() {
		return area;
	}

	public void setArea(Double area) {
		this.area = area;
	}

	public String getEnergyTypeIds() {
		return energyTypeIds;
	}

	public void setEnergyTypeIds(String energyTypeIds) {
		this.energyTypeIds = energyTypeIds;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Date getInvalidTime() {
		return invalidTime;
	}

	public void setInvalidTime(Date invalidTime) {
		this.invalidTime = invalidTime;
	}

	
	

}
