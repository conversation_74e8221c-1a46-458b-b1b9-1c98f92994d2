package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnBuildingEnergyCost")
@Table(name = "t_fn_building_energy_cost", comment = "项目能耗成本", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_building_id", "c_time_from", "c_energy_type_id" }, unique = true) })
public class FnBuildingEnergyCost extends BusinessObject {

	private static final long serialVersionUID = -4596608143369892798L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 2, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;


	@Column(order =3, name = "c_amount", length = 18, scale = 8, nullable = false, comment = "本期能耗")
	@JsonProperty("amount")
	private Double amount;

	@Column(order =4, name = "c_money", length = 18, scale = 8, nullable = true, comment = "本期费用")
	@JsonProperty("money")
	private Double money;
	
	@Column(order = 5, name = "c_pre_pay_money", length = 18, scale = 8, nullable = true, comment = "充值金额")
	@JsonProperty("prePayMoney")
	private Double prePayMoney;
	
	@Column(order = 6, name = "c_pre_pay_amount", length = 18, scale = 8, nullable = true, comment = "充值量")
	@JsonProperty("prePayAmount")
	private Double prePayAmount;

	@Column(order =7, name = "c_time_from", length = 0, nullable = false, comment = "时间")
	@JsonProperty("timeFrom")
	private Date timeFrom;

	@Column(order =8, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}


	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public Double getMoney() {
		return money;
	}

	public void setMoney(Double money) {
		this.money = money;
	}

	public Double getPrePayMoney() {
		return prePayMoney;
	}

	public void setPrePayMoney(Double prePayMoney) {
		this.prePayMoney = prePayMoney;
	}

	public Double getPrePayAmount() {
		return prePayAmount;
	}

	public void setPrePayAmount(Double prePayAmount) {
		this.prePayAmount = prePayAmount;
	}

	public Date getTimeFrom() {
		return timeFrom;
	}

	public void setTimeFrom(Date timeFrom) {
		this.timeFrom = timeFrom;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}


}
