package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnRecordFault")
@Table(name = "t_fn_record_fault", comment = "故障记录", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_id" }) })
public class FnRecordFault extends BaseId {


	private static final long serialVersionUID = -6784882760663363253L;
	
	@Column(order = 3, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 4, name = "c_fault_time", length = 0, nullable = false, comment = "故障时间")
	@JsonProperty("faultTime")
	private Date faultTime;
	
	@Column(order = 5, name = "c_fault_type", length = 1, nullable = false, comment = "故障类型")
	@JsonProperty("faultType")
	private Integer faultType;
	
	@Column(order = 6, name = "c_fault_src_type", length = 1, nullable = false, comment = "故障源类型")
	@JsonProperty("faultSrcType")
	private Integer faultSrcType;

	@Column(order = 7, name = "c_fault_src_id", length = 50, nullable = false, comment = "故障源编码")
	@JsonProperty("faultSrcId")
	private String faultSrcId;
	
	@Column(order = 8, name = "c_fault_src_name", length = 100, nullable = true, comment = "故障源名称")
	@JsonProperty("faultSrcName")
	private String faultSrcName;
	
	@Column(order = 9, name = "c_create_time", length = 0, nullable = false, comment = "记录插入时间")
	@JsonProperty("createTime")
	private Date createTime;
	
	@Column(order = 10, name = "c_remark", length = 100, nullable = true, comment = "故障说明")
	@JsonProperty("remark")
	private String remark;

	@Column(order = 11, name = "c_extend", length = 500, nullable = true, comment = "扩展信息")
	@JsonProperty("extend")
	private String extend;
	
	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Date getFaultTime() {
		return faultTime;
	}

	public void setFaultTime(Date faultTime) {
		this.faultTime = faultTime;
	}

	public Integer getFaultType() {
		return faultType;
	}

	public void setFaultType(Integer faultType) {
		this.faultType = faultType;
	}

	public Integer getFaultSrcType() {
		return faultSrcType;
	}

	public void setFaultSrcType(Integer faultSrcType) {
		this.faultSrcType = faultSrcType;
	}

	public String getFaultSrcId() {
		return faultSrcId;
	}

	public void setFaultSrcId(String faultSrcId) {
		this.faultSrcId = faultSrcId;
	}

	public String getFaultSrcName() {
		return faultSrcName;
	}

	public void setFaultSrcName(String faultSrcName) {
		this.faultSrcName = faultSrcName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	public String getExtend() {
		return extend;
	}

	public void setExtend(String extend) {
		this.extend = extend;
	}

	

}
