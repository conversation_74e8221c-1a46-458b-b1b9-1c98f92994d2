package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnCommonBuildingUpload;

/**


* 说明:租户云信息点
*/

@Dimension
@Entity(name="FnBuildingUploadRecordSqlite")
@Table(
		name="t_fn_common_building_upload",
		comment="通用建筑数据上传记录",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnCommonBuildingUploadSqlite extends FnCommonBuildingUpload {


	private static final long serialVersionUID = 4260748084417049759L;



}
