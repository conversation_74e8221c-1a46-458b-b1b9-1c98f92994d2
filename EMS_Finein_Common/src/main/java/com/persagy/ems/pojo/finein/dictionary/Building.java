package com.persagy.ems.pojo.finein.dictionary;

import com.persagy.core.dictionary.annotation.DictionaryCategory;
import com.persagy.core.dictionary.annotation.DictionaryCode;
import com.persagy.core.dictionary.dto.BaseDictonaryId;

/**
 * 数字字典对象：建筑
 * 数字字典编码：BU
 * */
@DictionaryCategory(category="BU")
public class Building extends BaseDictonaryId{

	// 建筑名称
	@DictionaryCode(code = "10002")
	private String name;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
}
