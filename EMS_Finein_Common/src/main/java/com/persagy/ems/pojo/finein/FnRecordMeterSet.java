package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnRecordMeterSet")
@Table(name = "t_fn_record_meter_set", comment = "仪表设置记录", schema = Schema.EMS, indexes = { @Index(columns = { "c_tenant_id" }) })
public class FnRecordMeterSet extends BaseId {

	private static final long serialVersionUID = -2287019773141474665L;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_meter_id", length = 50, nullable = false, comment = "仪表主键")
	@JsonProperty("meterId")
	private String meterId;

	@Column(order = 4, name = "c_room_code", length = 50, nullable = false, comment = "房间编码")
	@JsonProperty("roomCode")
	private String roomCode;

	@Column(order = 5, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 6, name = "c_operate_type", length = 1, nullable = false, comment = "操作类型")
	@JsonProperty("operateType")
	private Integer operateType;

	@Column(order = 7, name = "c_create_time", length = 0, nullable = false, comment = "插入时间")
	@JsonProperty("createTime")
	private Date createTime;
	
	@Column(order = 8, name = "c_user_id", length = 50, nullable = false, comment = "创建人")
	@JsonProperty("userId")
	private String userId;
	
	@Column(order = 9, name = "c_user_name", length =50, nullable = false, comment = "创建人姓名")
	@JsonProperty("userName")
	private String userName;

	
	@Column(order = 10, name = "c_extend", length = 500, nullable = true, comment = "扩展字段")
	@JsonProperty("extend")
	private String extend;


	public String getTenantId() {
		return tenantId;
	}


	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}


	public String getMeterId() {
		return meterId;
	}


	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}


	public String getRoomCode() {
		return roomCode;
	}


	public void setRoomCode(String roomCode) {
		this.roomCode = roomCode;
	}


	public String getEnergyTypeId() {
		return energyTypeId;
	}


	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}




	public Integer getOperateType() {
		return operateType;
	}


	public void setOperateType(Integer operateType) {
		this.operateType = operateType;
	}


	public Date getCreateTime() {
		return createTime;
	}


	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}


	public String getUserId() {
		return userId;
	}


	public void setUserId(String userId) {
		this.userId = userId;
	}


	public String getUserName() {
		return userName;
	}


	public void setUserName(String userName) {
		this.userName = userName;
	}


	public String getExtend() {
		return extend;
	}


	public void setExtend(String extend) {
		this.extend = extend;
	}


	

}
