package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnRecordPrePayErrorOrder")
@Table(name = "t_fn_record_pre_pay_error_order", comment = "充值异常记录", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_order_id" }, unique = true) })
public class FnRecordPrePayErrorOrder extends BusinessObject {

	private static final long serialVersionUID = -689371006597436598L;

	@Id
	@Column(order = 11, name = "c_order_id", length = 50, nullable = false, comment = "账单编号")
	@JsonProperty("orderId")
	private String orderId;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 3, name = "c_building_name", length = 100, nullable = false, comment = "建筑名称")
	@JsonProperty("buildingName")
	private String buildingName;

	@Column(order = 4, name = "c_body_type", length = 1, nullable = false, comment = "充值主体")
	@JsonProperty("bodyType")
	private Integer bodyType;

	@Column(order = 5, name = "c_tenant_id", length = 36, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 6, name = "c_tenant_name", length = 200, nullable = false, comment = "租户名称")
	@JsonProperty("tenantName")
	private String tenantName;

	@Column(order = 7, name = "c_body_code", length = 50, nullable = false, comment = "充值主体编码")
	@JsonProperty("bodyCode")
	private String bodyCode;

	@Column(order = 8, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 9, name = "c_order_time", length = 0, nullable = false, comment = "账单时间")
	@JsonProperty("orderTime")
	private Date orderTime;

	@Column(order = 10, name = "c_money", length = 18, scale = 8, nullable = true, comment = "充值金额")
	@JsonProperty("money")
	private Double money;

	@Column(order = 11, name = "c_amount", length = 18, scale = 8, nullable = true, comment = "充值量")
	@JsonProperty("amount")
	private Double amount;
	
	@Column(order = 12, name = "c_amount_unit", length = 20, nullable = false, comment = "单位")
	@JsonProperty("amountUnit")
	private String amountUnit;

	@Column(order = 12, name = "c_system_code", length = 20, nullable = false, comment = "系统编码")
	@JsonProperty("systemCode")
	private String systemCode;
	
	@Column(order = 13, name = "c_system_name", length = 20, nullable = false, comment = "系统编码")
	@JsonProperty("systemName")
	private String systemName;

	@Column(order = 14, name = "c_user_id", length = 50, nullable = true, comment = "充值人编码")
	@JsonProperty("userId")
	private String userId;

	@Column(order = 15, name = "c_user_name", length = 50, nullable = true, comment = "充值人姓名")
	@JsonProperty("userName")
	private String userName;

	@Column(order = 16, name = "c_status", length = 1, nullable = false, comment = "充值记录状态")
	@JsonProperty("status")
	private Integer status;

	@Column(order = 17, name = "c_operate_user_id", length = 50, nullable = true, comment = "操作人编码")
	@JsonProperty("operateUserId")
	private String operateUserId;

	@Column(order = 18, name = "c_operate_user_name", length = 50, nullable = true, comment = "操作人姓名")
	@JsonProperty("operateUserName")
	private String operateUserName;

	@Column(order = 19, name = "c_operate_time", length = 0, nullable = false, comment = "操作时间")
	@JsonProperty("operateTime")
	private Date operateTime;

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getBuildingName() {
		return buildingName;
	}

	public void setBuildingName(String buildingName) {
		this.buildingName = buildingName;
	}

	public Integer getBodyType() {
		return bodyType;
	}

	public void setBodyType(Integer bodyType) {
		this.bodyType = bodyType;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getTenantName() {
		return tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	public String getBodyCode() {
		return bodyCode;
	}

	public void setBodyCode(String bodyCode) {
		this.bodyCode = bodyCode;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Date getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(Date orderTime) {
		this.orderTime = orderTime;
	}

	public Double getMoney() {
		return money;
	}

	public void setMoney(Double money) {
		this.money = money;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public String getSystemCode() {
		return systemCode;
	}

	public void setSystemCode(String systemCode) {
		this.systemCode = systemCode;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getOperateUserId() {
		return operateUserId;
	}

	public void setOperateUserId(String operateUserId) {
		this.operateUserId = operateUserId;
	}

	public String getOperateUserName() {
		return operateUserName;
	}

	public void setOperateUserName(String operateUserName) {
		this.operateUserName = operateUserName;
	}

	public Date getOperateTime() {
		return operateTime;
	}

	public void setOperateTime(Date operateTime) {
		this.operateTime = operateTime;
	}

	public String getAmountUnit() {
		return amountUnit;
	}

	public void setAmountUnit(String amountUnit) {
		this.amountUnit = amountUnit;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}
	
}
