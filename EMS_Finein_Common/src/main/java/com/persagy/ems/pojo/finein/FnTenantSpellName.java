package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnTenantSpellName")
@Table(name = "t_fn_tenant_spell_name", comment = "租户英文名", schema = Schema.EMS, indexes = {})
public class FnTenantSpellName extends BaseId {

	private static final long serialVersionUID = -7603049881782539986L;

	@Column(order = 2, name = "c_spell_name", length = 50, nullable = true, comment = "英文名")
	@JsonProperty("spellName")
	private String spellName;
	
	@Column(order = 2, name = "c_tenant_name", length = 50, nullable = true, comment = "租户名称")
	@JsonProperty("tenantName")
	private String tenantName;

	public String getSpellName() {
		return spellName;
	}

	public void setSpellName(String spellName) {
		this.spellName = spellName;
	}

	public String getTenantName() {
		return tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	
	

}
