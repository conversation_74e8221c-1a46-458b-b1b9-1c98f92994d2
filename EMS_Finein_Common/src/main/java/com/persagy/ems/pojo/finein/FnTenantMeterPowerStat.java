package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnTenantMeterPowerStat")
@Table(name = "t_fn_tenant_meter_power_stat", comment = "租户-仪表功率统计值", schema = Schema.EMS, transaction = false, indexes = {
		@Index(columns = { "c_building_id", "c_tenant_id", "c_body_type", "c_body_id", "c_energy_type_id",
				"c_count_time" }, unique = true) })
@Month(column = "c_time_from")
public class FnTenantMeterPowerStat extends BusinessObject {

	private static final long serialVersionUID = -735506396749564165L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_body_type", length = 1, nullable = false, comment = "主体类型")
	@JsonProperty("bodyType")
	private Integer bodyType;

	@Column(order = 4, name = "c_body_id", length = 20, nullable = false, comment = "主体id")
	@JsonProperty("bodyId")
	private String bodyId;

	@Column(order = 5, name = "c_energy_type_id", length = 20, nullable = false, comment = "能耗类型")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 6, name = "c_data", length = 18, scale = 8, nullable = false, comment = "功率数据")
	@JsonProperty("data")
	private Double data;

	@Column(order = 7, name = "c_time_from", length = 0, nullable = false, comment = "数据时间")
	@JsonProperty("timeFrom")
	private Date timeFrom;

	@Column(order = 8, name = "c_count_time", length = 0, nullable = true, comment = "统计时间")
	@JsonProperty("countTime")
	private Date countTime;

	@Column(order = 9, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public Integer getBodyType() {
		return bodyType;
	}

	public void setBodyType(Integer bodyType) {
		this.bodyType = bodyType;
	}

	public String getBodyId() {
		return bodyId;
	}

	public void setBodyId(String bodyId) {
		this.bodyId = bodyId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Double getData() {
		return data;
	}

	public void setData(Double data) {
		this.data = data;
	}

	public Date getTimeFrom() {
		return timeFrom;
	}

	public void setTimeFrom(Date timeFrom) {
		this.timeFrom = timeFrom;
	}

	public Date getCountTime() {
		return countTime;
	}

	public void setCountTime(Date countTime) {
		this.countTime = countTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

}
