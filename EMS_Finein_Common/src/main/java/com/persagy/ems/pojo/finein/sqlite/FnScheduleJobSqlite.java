package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnScheduleJob;


@Dimension
@Entity(name="FnScheduleJobSqlite")
@Table(
		name="t_fn_schedule_job",
		comment="定时任务表",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnScheduleJobSqlite extends FnScheduleJob {
	private static final long serialVersionUID = 1851009727445166023L;

}
