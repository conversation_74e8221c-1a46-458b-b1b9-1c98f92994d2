package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnBuildingUploadRecord")
@Table(name = "t_fn_common_building_upload", comment = "建筑上传记录", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_building_id", "c_type","c_url" }, unique = true) })
public class FnCommonBuildingUpload extends BaseId {

	private static final long serialVersionUID = 136239459461239214L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 3, name = "c_type", length = 50, nullable = false, comment = "上传数据类型")
	@JsonProperty("type")
	private String type;

	@Column(order = 4, name = "c_url", length = 500, nullable = false, comment = "上传路径")
	@JsonProperty("url")
	private String url;

	@Column(order = 5, name = "c_last_upload_time", length = 0, nullable = true, comment = "最后上传时间")
	@JsonProperty("lastUploadTime")
	private Date lastUploadTime;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getUrl() {
		return url;
	}

	public void setUrl(String url) {
		this.url = url;
	}

	public Date getLastUploadTime() {
		return lastUploadTime;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public void setLastUploadTime(Date lastUploadTime) {
		this.lastUploadTime = lastUploadTime;
	}
}
