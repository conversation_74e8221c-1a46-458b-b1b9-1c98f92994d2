package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnChargeMessage")
@Table(name = "t_fn_charge_message", comment = "充值提醒", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_id" }) })
public class FnChargeMessage extends BaseId {

	private static final long serialVersionUID = 7430172808689034817L;
	

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;
	
	@Column(order = 3, name = "c_tenant_name", length = 100, nullable = false, comment = "租户名称")
	@JsonProperty("tenantName")
	private String tenantName;
	
	@Column(order = 4, name = "c_contact_mobile", length = 20, nullable = false, comment = "联系人电话")
	@JsonProperty("contactMobile")
	private String contactMobile;
	
	@Column(order = 5, name = "c_contact_name", length = 50, nullable = true, comment = "联系人姓名")
	@JsonProperty("contactName")
	private String contactName;
	
	@Column(order = 6, name = "c_content", length = 200, nullable = false, comment = "短信内容")
	@JsonProperty("content")
	private String content;

	@Column(order = 7, name = "c_create_time", length = 0, nullable = false, comment = "插入记录时间")
	@JsonProperty("createTime")
	private Date createTime;
	
	@Column(order = 8, name = "c_last_update_time", length = 0, nullable = false, comment = "插入记录时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;
	
	@Column(order = 9, name = "c_status", length = 1, nullable = false, comment = "短信状态")
	@JsonProperty("status")
	private Integer status;

	@Column(order = 10, name = "c_exception_reason", length = 200, nullable = true, comment = "异常原因")
	@JsonProperty("exceptionReason")
	private String exceptionReason;
	
	@Column(order = 11, name = "c_try_times", length = 3, nullable = false, comment = "重试次数")
	@JsonProperty("tryTimes")
	private Integer tryTimes;
	
	@Column(order = 12, name = "c_send_time", length = 0, nullable = true, comment = "发送成功时间")
	@JsonProperty("sendTime")
	private Date sendTime;
	
	
	

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getTenantName() {
		return tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	public String getContactMobile() {
		return contactMobile;
	}

	public void setContactMobile(String contactMobile) {
		this.contactMobile = contactMobile;
	}

	public String getContactName() {
		return contactName;
	}

	public void setContactName(String contactName) {
		this.contactName = contactName;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public String getExceptionReason() {
		return exceptionReason;
	}

	public void setExceptionReason(String exceptionReason) {
		this.exceptionReason = exceptionReason;
	}

	public Integer getTryTimes() {
		return tryTimes;
	}

	public void setTryTimes(Integer tryTimes) {
		this.tryTimes = tryTimes;
	}

	public Date getSendTime() {
		return sendTime;
	}

	public void setSendTime(Date sendTime) {
		this.sendTime = sendTime;
	}

	
	

}
