package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnAlarmPushStatus")
@Table(name = "t_fn_alarm_push_status", comment = "报警推送状态", schema = Schema.EMS, indexes = { @Index(columns = { "c_building_id" }) ,@Index(columns = { "c_tenant_id" }) })
public class FnAlarmPushStatus extends BaseId {

	 
	private static final long serialVersionUID = 7056016694403159355L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;
	
	@Column(order = 3, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 4, name = "c_alarm_type_id", length = 50, nullable = false, comment = "报警类型主键")
	@JsonProperty("alarmTypeId")
	private String alarmTypeId;

	@Column(order = 7, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 9, name = "c_alarm_position_type", length = 1, nullable = false, comment = "报警位置类型")
	@JsonProperty("alarmPositionType")
	private Integer alarmPositionType;

	@Column(order = 10, name = "c_alarm_position_id", length = 50, nullable = false, comment = "报警位置编码")
	@JsonProperty("alarmPositionId")
	private String alarmPositionId;

	@Column(order = 12, name = "c_status", length = 1, nullable = false, comment = "报警状态")
	@JsonProperty("status")
	private Integer status;

	@Column(order = 16, name = "c_last_update_time", length = 0, nullable = false, comment = "报警最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	@Column(order = 21, name = "c_push_status", length = 1, nullable = false, comment = "报警推送状态")
	@JsonProperty("pushStatus")
	private Integer pushStatus;
	
	
	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getAlarmTypeId() {
		return alarmTypeId;
	}

	public void setAlarmTypeId(String alarmTypeId) {
		this.alarmTypeId = alarmTypeId;
	}


	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}


	public Integer getAlarmPositionType() {
		return alarmPositionType;
	}

	public void setAlarmPositionType(Integer alarmPositionType) {
		this.alarmPositionType = alarmPositionType;
	}

	public String getAlarmPositionId() {
		return alarmPositionId;
	}

	public void setAlarmPositionId(String alarmPositionId) {
		this.alarmPositionId = alarmPositionId;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public Integer getPushStatus() {
		return pushStatus;
	}

	public void setPushStatus(Integer pushStatus) {
		this.pushStatus = pushStatus;
	}

}
