package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.system.FileResource;


@Dimension
@Entity(name="FileResourceSqlite")
@Table(
		name="t_system_file_resource",
		comment="文件资源存储",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FileResourceSqlite extends FileResource{

	private static final long serialVersionUID = -1249176759337696803L;

	
}
