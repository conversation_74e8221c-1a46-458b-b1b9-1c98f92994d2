package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnRecordScheduleJob")
@Table(name = "t_fn_record_schedule_job", comment = "定时任务执行记录", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_job_name","c_job_group","c_execute_time" }) })
public class FnRecordScheduleJob extends BaseId {

	private static final long serialVersionUID = -1637469797231368917L;

	@Column(order = 2, name = "c_job_name", length = 20, nullable = false, comment = "任务名称")
	@JsonProperty("jobName")
	private String jobName;

	@Column(order = 3, name = "c_job_group", length = 50, nullable = false, comment = "任务分组")
	@JsonProperty("jobGroup")
	private String jobGroup;

	@Column(order = 4, name = "c_execute_time", length = 0, nullable = false, comment = "执行时间")
	@JsonProperty("executeTime")
	private Date executeTime;
	
	@Column(order = 5, name = "c_update_time", length = 0, nullable = false, comment = "更新时间")
	@JsonProperty("updateTime")
	private Date updateTime;
	
	@Column(order = 6, name = "c_times", length = 1, nullable = false, comment = "当天执行次数")
	@JsonProperty("times")
	private Integer times;

	public String getJobName() {
		return jobName;
	}

	public void setJobName(String jobName) {
		this.jobName = jobName;
	}

	public String getJobGroup() {
		return jobGroup;
	}

	public void setJobGroup(String jobGroup) {
		this.jobGroup = jobGroup;
	}

	public Date getExecuteTime() {
		return executeTime;
	}

	public void setExecuteTime(Date executeTime) {
		this.executeTime = executeTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getTimes() {
		return times;
	}

	public void setTimes(Integer times) {
		this.times = times;
	}


}
