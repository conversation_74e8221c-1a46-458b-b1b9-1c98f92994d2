package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月26日 下午5:15:45

* 说明:
*/

@Dimension
@Entity(name="FnAlarmLimitGlobalSqlite")
@Table(
		name="t_fn_alarm_limit_global",
		comment="报警全局门限模板",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnAlarmLimitGlobalSqlite extends FnAlarmLimitGlobal {

	private static final long serialVersionUID = -4196581605700648151L;

}
