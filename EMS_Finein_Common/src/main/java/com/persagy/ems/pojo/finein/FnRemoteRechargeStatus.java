package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnRemoteRechargeStatus")
@Table(name = "t_fn_remote_recharge_status", comment = "跨系统远程充值状态", schema = Schema.EMS, indexes = {
		@Index(columns = {"c_tenant_flag","c_system_code"},unique = true) })
public class FnRemoteRechargeStatus extends BusinessObject {

	private static final long serialVersionUID = -4042047437638051716L;

	@Column(order = 1, name = "c_tenant_flag", length = 100, nullable = false, comment = "租户全码")
	@JsonProperty("tenantFlag")
	private String tenantFlag;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 3, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 4, name = "c_system_code", length = 100, nullable = false, comment = "系统编码")
	@JsonProperty("systemCode")
	private String systemCode;

	@Column(order = 5, name = "c_remote_recharge_status", length = 1, nullable = false, comment = "是否支持远程充值")
	@JsonProperty("remoteRechargeStatus")
	private Integer remoteRechargeStatus;

	@Column(order = 6, name = "c_user_id", length = 50, nullable = false, comment = "操作人编码")
	@JsonProperty("userId")
	private String userId;

	@Column(order = 7, name = "c_user_name", length = 50, nullable = false, comment = "操作人姓名")
	@JsonProperty("userName")
	private String userName;

	@Column(order = 8, name = "c_last_time", length = 0, nullable = false, comment = "上次更新时间")
	@JsonProperty("lastTime")
	private Date lastTime;

	public String getTenantFlag() {
		return tenantFlag;
	}

	public void setTenantFlag(String tenantFlag) {
		this.tenantFlag = tenantFlag;
	}

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getSystemCode() {
		return systemCode;
	}

	public void setSystemCode(String systemCode) {
		this.systemCode = systemCode;
	}

	public Integer getRemoteRechargeStatus() {
		return remoteRechargeStatus;
	}

	public void setRemoteRechargeStatus(Integer remoteRechargeStatus) {
		this.remoteRechargeStatus = remoteRechargeStatus;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Date getLastTime() {
		return lastTime;
	}

	public void setLastTime(Date lastTime) {
		this.lastTime = lastTime;
	}


}
