package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

/**
 * @Author: ls
 * @Date: 2022/2/10 10:58
 */
@Dimension
@Entity(name = "FnTimingUpdatePriceRecord")
@Table(name = "t_fn_timing_update_price_record", comment = "定时修改价格记录", schema = SchemaConstant.Schema.EMS, indexes = {})
public class FnTimingUpdatePriceRecord extends BaseId {

    private static final long serialVersionUID = 5692868589217003540L;


    @Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
    @JsonProperty("tenantId")
    private String tenantId;

    @Column(order = 3, name = "c_tenant_name", length = 50, nullable = false, comment = "租户名称")
    @JsonProperty("tenantName")
    private String tenantName;

    @Column(order = 4, name = "c_meter_id", length = 50, nullable = false, comment = "仪表编码")
    @JsonProperty("meterId")
    private String meterId;

    @Column(order = 5, name = "c_content", length = 200, nullable = true, comment = "设置内容")
    @JsonProperty("content")
    private String content;

    @Column(order = 6, name = "c_result", length = 1, nullable = false, comment = "结果")
    @JsonProperty("result")
    private Integer result;

    @Column(order = 7, name = "c_after_price", length = 100, nullable = true, comment = "修改之后的价格")
    @JsonProperty("afterPrice")
    private String afterPrice;

    @Column(order = 8, name = "c_update_time", length = 0, nullable = false, comment = "修改时间")
    @JsonProperty("updateTime")
    private Date updateTime;

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getTenantName() {
        return tenantName;
    }

    public void setTenantName(String tenantName) {
        this.tenantName = tenantName;
    }

    public String getMeterId() {
        return meterId;
    }

    public void setMeterId(String meterId) {
        this.meterId = meterId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Integer getResult() {
        return result;
    }

    public void setResult(Integer result) {
        this.result = result;
    }

    public String getAfterPrice() {
        return afterPrice;
    }

    public void setAfterPrice(String afterPrice) {
        this.afterPrice = afterPrice;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
