package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnRecordMeterChangeExtend")
@Table(name = "t_fn_record_meter_change_extend", comment = "换表记录仪表功能信息", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_id" }) })
public class FnRecordMeterChangeExtend extends BaseId {

	private static final long serialVersionUID = 3310968082893342258L;

	@Column(order = 2, name = "c_meter_id", length = 50, nullable = false, comment = "仪表编码")
	@JsonProperty("meterId")
	private String meterId;
	
	@Column(order = 3, name = "c_function_id", length = 11, nullable = false, comment = "功能号")
	@JsonProperty("functionId")
	private Integer functionId;
	
	@Column(order = 4, name = "c_change_time", length = 0, nullable = false, comment = "换表时间")
	@JsonProperty("changeTime")
	private Date changeTime;
	
	@Column(order = 5, name = "c_energy_type_id", length = 10, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;
	
	@Column(order = 6, name = "c_data", length = 18, scale = 8, nullable = false, comment = "读数")
	@JsonProperty("data")
	private Double data;
	
	@Column(order = 7, name = "c_create_time", length = 0, nullable = false, comment = "记录插入时间")
	@JsonProperty("createTime")
	private Date createTime;
	
	@Column(order = 8, name = "c_user_id", length = 50, nullable = true, comment = "操作人编码")
	@JsonProperty("userId")
	private String userId;
	
	@Column(order = 9, name = "c_user_name", length = 50, nullable = true, comment = "操作人姓名")
	@JsonProperty("userName")
	private String userName;
	
	
	

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public Integer getFunctionId() {
		return functionId;
	}

	public void setFunctionId(Integer functionId) {
		this.functionId = functionId;
	}

	public Date getChangeTime() {
		return changeTime;
	}

	public void setChangeTime(Date changeTime) {
		this.changeTime = changeTime;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Double getData() {
		return data;
	}

	public void setData(Double data) {
		this.data = data;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}
	
	


}
