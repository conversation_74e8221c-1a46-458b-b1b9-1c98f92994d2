package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnTenantType")
@Table(name = "t_fn_tenant_type", comment = "租户类型 ", schema = Schema.EMS, indexes = {})
public class FnTenantType extends BaseId {

	private static final long serialVersionUID = 5411897875859993224L;

	@Column(order = 1, name = "c_name", length = 100, nullable = false, comment = "租户类型名称")
	@JsonProperty("name")
	private String name;

	@Column(order = 2, name = "c_order_by", length = 3, nullable = false, comment = "租户类型排序")
	@JsonProperty("orderBy")
	private Integer orderBy;


	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getOrderBy() {
		return orderBy;
	}

	public void setOrderBy(Integer orderBy) {
		this.orderBy = orderBy;
	}

}
