package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnMeterProperty")
@Table(name = "t_fn_meter_property", comment = "仪表属性", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_meter_id","c_property_name" }) })
public class FnMeterProperty extends BaseId {

	private static final long serialVersionUID = 1588577557591915663L;

	@Column(order = 2, name = "c_meter_id", length = 20, nullable = false, comment = "仪表编码")
	@JsonProperty("meterId")
	private String meterId;

	@Column(order = 3, name = "c_property_name", length = 50, nullable = false, comment = "仪表属性编码")
	@JsonProperty("propertyName")
	private String propertyName;

	@Column(order = 4, name = "c_property_value", length = 2000, nullable = true, comment = "仪表属性值")
	@JsonProperty("propertyValue")
	private String propertyValue;
	
	

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public String getPropertyName() {
		return propertyName;
	}

	public void setPropertyName(String propertyName) {
		this.propertyName = propertyName;
	}

	public String getPropertyValue() {
		return propertyValue;
	}

	public void setPropertyValue(String propertyValue) {
		this.propertyValue = propertyValue;
	}

}
