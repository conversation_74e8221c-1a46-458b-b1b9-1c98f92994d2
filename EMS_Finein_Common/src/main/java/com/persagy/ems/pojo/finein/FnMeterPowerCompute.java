package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnMeterPowerCompute")
@Table(name = "t_fn_meter_power_compute", comment = "仪表功率计算时间", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_meter_id", "c_data_type" }, unique = true) })
public class FnMeterPowerCompute extends BusinessObject {

	private static final long serialVersionUID = 3175303752318440110L;

	@Column(order = 1, name = "c_meter_id", length = 20, nullable = false, comment = "仪表编码")
	@JsonProperty("meterId")
	private String meterId;

	@Column(order = 2, name = "c_data_type", length = 1, nullable = false, comment = "数据类型")
	@JsonProperty("dataType")
	private Integer dataType;

	@Column(order = 3, name = "c_last_compute_time", length = 0, nullable = true, comment = "上次计算到的时间")
	@JsonProperty("lastComputeTime")
	private Date lastComputeTime;

	@Column(order = 4, name = "c_last_update_time", length = 0, nullable = true, comment = "数据更新时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	public Date getLastComputeTime() {
		return lastComputeTime;
	}

	public void setLastComputeTime(Date lastComputeTime) {
		this.lastComputeTime = lastComputeTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
	
}
