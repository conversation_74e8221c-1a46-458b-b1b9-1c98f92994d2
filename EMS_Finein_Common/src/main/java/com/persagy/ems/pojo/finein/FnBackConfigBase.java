package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnBackConfigBase")
@Table(name = "t_fn_back_config_base", comment = "后台配置基础", schema = Schema.EMS, indexes = {})
public class FnBackConfigBase extends BaseId {

	private static final long serialVersionUID = 8881413143792246465L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 3, name = "c_logic_code", length = 50, nullable = false, comment = "后台配置逻辑编码")
	@JsonProperty("logicCode")
	private String logicCode;

	@Column(order = 4, name = "c_name", length = 100, nullable = false, comment = "配置名称")
	@JsonProperty("name")
	private String name;

	@Column(order = 5, name = "c_file_id", length = 50, nullable = true, comment = "文件ID")
	@JsonProperty("fileId")
	private String fileId;

	@Column(order = 6, name = "c_file_name", length = 200, nullable = true, comment = "文件名称")
	@JsonProperty("fileName")
	private String fileName;

	@Column(order = 7, name = "c_template_resource_id", length = 50, nullable = false, comment = "模板资源ID")
	@JsonProperty("templateResourceId")
	private String templateResourceId;

	@Column(order = 8, name = "c_is_exsist", length = 3, nullable = false, comment = "是否存在")
	@JsonProperty("isExsit")
	private Integer isExsit;

	@Column(order = 9, name = "c_user_id", length = 50, nullable = true, comment = "上传人")
	@JsonProperty("userId")
	private String userId;

	@Column(order = 10, name = "c_upload_time", length = 0, nullable = true, comment = "上传时间")
	@JsonProperty("uploadTime")
	private Date uploadTime;

	@Column(order = 11, name = "c_order_by", length = 3, nullable = false, comment = "排序")
	@JsonProperty("orderBy")
	private Integer orderBy;
	
	

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getLogicCode() {
		return logicCode;
	}

	public void setLogicCode(String logicCode) {
		this.logicCode = logicCode;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getFileId() {
		return fileId;
	}

	public void setFileId(String fileId) {
		this.fileId = fileId;
	}

	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public String getTemplateResourceId() {
		return templateResourceId;
	}

	public void setTemplateResourceId(String templateResourceId) {
		this.templateResourceId = templateResourceId;
	}

	public Integer getIsExsit() {
		return isExsit;
	}

	public void setIsExsit(Integer isExsit) {
		this.isExsit = isExsit;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public Date getUploadTime() {
		return uploadTime;
	}

	public void setUploadTime(Date uploadTime) {
		this.uploadTime = uploadTime;
	}

	public Integer getOrderBy() {
		return orderBy;
	}

	public void setOrderBy(Integer orderBy) {
		this.orderBy = orderBy;
	}


}
