package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnPermission;

/**

* 说明:
*/

@Dimension
@Entity(name="FnPermissionSqlite")
@Table(
		name="t_fn_permission",
		comment="用户权限表",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnPermissionSqlite extends FnPermission {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1716870980242703855L;



}
