package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnRecordForOtherSystemError")
@Table(name = "t_fn_record_for_other_system_error",
        transaction = false,comment = "远程充值记录错误信息", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_id" }) })
public class FnRecordForOtherSystemError extends BaseId {

	private static final long serialVersionUID = 8844147885699434131L;

	@Column(order = 2, name = "c_error_type", length =50, nullable = false, comment = "错误类型编码")
	@JsonProperty("errorType")
	private String errorType;
	
	@Column(order = 3, name = "c_try_times", length = 3, nullable = false, comment = "重试次数")
	@JsonProperty("tryTimes")
	private Integer tryTimes;
	
	@Column(order =4, name = "c_error_message", length = 50, nullable = true, comment = "错误信息")
	@JsonProperty("errorMessage")
	private String errorMessage;
	
	@Column(order = 5, name = "c_update_time", length = 0, nullable = true, comment = "更新时间")
	@JsonProperty("updateTime")
	private Date updateTime;

	public String getErrorType() {
		return errorType;
	}

	public void setErrorType(String errorType) {
		this.errorType = errorType;
	}

	public Integer getTryTimes() {
		return tryTimes;
	}

	public void setTryTimes(Integer tryTimes) {
		this.tryTimes = tryTimes;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}


	
}
