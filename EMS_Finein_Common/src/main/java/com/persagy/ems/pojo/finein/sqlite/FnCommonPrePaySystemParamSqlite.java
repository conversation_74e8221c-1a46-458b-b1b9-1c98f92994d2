package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnCommonPrePaySystemParam;

@Dimension
@Entity(name = "FnCommonPrePaySystemParamSqlite")
@Table(name = "t_fn_common_pre_pay_system_param", comment = "通用充值系统参数表", schema = Schema.NONE, indexes = {

})
public class FnCommonPrePaySystemParamSqlite extends FnCommonPrePaySystemParam {

	private static final long serialVersionUID = -7862008078332527121L;

}
