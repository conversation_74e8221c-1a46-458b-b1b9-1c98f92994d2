package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnAlarmType;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月26日 下午5:15:45

* 说明:
*/

@Dimension
@Entity(name="FnAlarmTypeSqlite")
@Table(
		name="t_fn_alarm_type",
		comment="报警类型",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnAlarmTypeSqlite extends FnAlarmType {

	private static final long serialVersionUID = -3831828427417817876L;


}
