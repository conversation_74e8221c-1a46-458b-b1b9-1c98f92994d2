package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnMeter")
@Table(name = "t_fn_meter", comment = "仪表", schema = Schema.EMS, indexes = {})
public class FnMeter extends BaseId {

	private static final long serialVersionUID = 9194499915237129106L;

	@Column(order = 2, name = "c_energy_type_id", length = 8, nullable = false, comment = "仪表能耗类型")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 3, name = "c_protocol_id", length = 50, nullable = false, comment = "仪表协议主键")
	@JsonProperty("protocolId")
	private String protocolId;

	@Column(order = 4, name = "c_install_address", length = 200, nullable = true, comment = "仪表安装位置")
	@JsonProperty("installAddress")
	private String installAddress;

	@Column(order = 5, name = "c_is_use", length = 1, nullable = false, comment = "仪表使用状态")
	@JsonProperty("isUse")
	private Integer isUse;
	
	@Column(order = 6, name = "c_communication_type", length = 2, nullable = false, comment = "通讯类型")
	@JsonProperty("communicationType")
	private Integer communicationType;
	
	@Column(order = 7, name = "c_client_ip", length = 50, nullable = true, comment = "客户端IP")
	@JsonProperty("clientIp")
	private String clientIp;
	
	@Column(order = 8, name = "c_client_port", length = 6, nullable = true, comment = "客户端Port")
	@JsonProperty("clientPort")
	private Integer clientPort;
	
	@Column(order = 9, name = "c_server_ip", length = 50, nullable = true, comment = "服务端IP")
	@JsonProperty("serverIp")
	private String serverIp;
	
	@Column(order = 10, name = "c_server_port", length = 6, nullable = true, comment = "服务端Port")
	@JsonProperty("serverPort")
	private Integer serverPort;
	
	@Column(order = 11, name = "c_pay_type", length = 1, nullable = false, comment = "付费方式")
	@JsonProperty("payType")
	private Integer payType;
	
	@Column(order = 12, name = "c_billing_mode", length = 1, nullable = true, comment = "充值模式")
	@JsonProperty("billingMode")
	private Integer billingMode;

	@Column(order = 13, name = "c_meter_type", length = 1, nullable = false, comment = "仪表功能（普通、多费率）")
	@JsonProperty("meterType")
	private Integer meterType;
	
	@Column(order = 14, name = "c_radio", length = 18, scale = 8, nullable = false, comment = "比例(CT)")
	@JsonProperty("radio")
	private Double radio;
	
	@Column(order = 15, name = "c_extend", length = 1000, nullable = true, comment = "仪表扩展信息")
	@JsonProperty("extend")
	private String extend;

	
	
	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public String getProtocolId() {
		return protocolId;
	}

	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}

	public String getInstallAddress() {
		return installAddress;
	}

	public void setInstallAddress(String installAddress) {
		this.installAddress = installAddress;
	}

	public Integer getIsUse() {
		return isUse;
	}

	public void setIsUse(Integer isUse) {
		this.isUse = isUse;
	}

	public Integer getCommunicationType() {
		return communicationType;
	}

	public void setCommunicationType(Integer communicationType) {
		this.communicationType = communicationType;
	}

	public String getClientIp() {
		return clientIp;
	}

	public void setClientIp(String clientIp) {
		this.clientIp = clientIp;
	}

	public Integer getClientPort() {
		return clientPort;
	}

	public void setClientPort(Integer clientPort) {
		this.clientPort = clientPort;
	}

	public String getServerIp() {
		return serverIp;
	}

	public void setServerIp(String serverIp) {
		this.serverIp = serverIp;
	}

	public Integer getServerPort() {
		return serverPort;
	}

	public void setServerPort(Integer serverPort) {
		this.serverPort = serverPort;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Integer getBillingMode() {
		return billingMode;
	}

	public void setBillingMode(Integer billingMode) {
		this.billingMode = billingMode;
	}

	public Integer getMeterType() {
		return meterType;
	}

	public void setMeterType(Integer meterType) {
		this.meterType = meterType;
	}

	public Double getRadio() {
		return radio;
	}

	public void setRadio(Double radio) {
		this.radio = radio;
	}

	public String getExtend() {
		return extend;
	}

	public void setExtend(String extend) {
		this.extend = extend;
	}
	
	

}
