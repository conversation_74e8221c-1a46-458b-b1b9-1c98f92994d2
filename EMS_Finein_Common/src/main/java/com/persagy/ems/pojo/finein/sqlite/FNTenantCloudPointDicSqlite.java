package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FNTenantCloudPointDic;

/**


* 说明:租户云信息点
*/

@Dimension
@Entity(name="FNTenantCloudPointDicSqlite")
@Table(
		name="t_fn_tenant_cloud_point_dic",
		comment="云端-租户信息点",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FNTenantCloudPointDicSqlite extends FNTenantCloudPointDic {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6866838897512068527L;

	/**
	 * 
	 */


}
