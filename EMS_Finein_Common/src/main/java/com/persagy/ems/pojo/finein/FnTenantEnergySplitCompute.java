package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnTenantEnergySplitCompute")
@Table(name = "t_fn_tenant_energy_split_compute", 
		comment = "租户后台付费计算时间", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_building_id", "c_tenant_id","c_energy_type_id"}, unique = true) })
public class FnTenantEnergySplitCompute extends BusinessObject {

	private static final long serialVersionUID = -2939154028559461406L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_energy_type_id", length = 10, nullable = false, comment = "能耗类型编码")
	@JsonProperty("energyTypeId")
	private String energyTypeId;
	
	@Column(order = 4, name = "c_last_compute_time", length = 0, nullable = true, comment = "上次计算到的时间")
	@JsonProperty("lastComputeTime")
	private Date lastComputeTime;

	@Column(order = 5, name = "c_last_update_time", length = 0,  nullable = true, comment = "数据更新时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;
	

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Date getLastComputeTime() {
		return lastComputeTime;
	}

	public void setLastComputeTime(Date lastComputeTime) {
		this.lastComputeTime = lastComputeTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
	

}
