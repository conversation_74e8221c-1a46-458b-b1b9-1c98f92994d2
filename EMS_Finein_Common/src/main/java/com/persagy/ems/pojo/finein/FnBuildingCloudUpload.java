package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnBuildingCloudUpload")
@Table(name = "t_fn_building_cloud_upload", comment = "云客户端建筑能耗上传时间", schema = Schema.EMS,indexes = {
		@Index(columns = { "c_system_name","c_building_id"},unique = true) })
public class FnBuildingCloudUpload extends BusinessObject {

	private static final long serialVersionUID = 3368766835166603531L;


	@Column(order = 1, name = "c_system_name", length = 50, nullable = false, comment = "系统名")
	@JsonProperty("systemName")
	private String systemName;


	@Column(order = 2, name = "c_building_id", length =30, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;
	
	
	@Column(order = 3, name = "c_last_update_time", length =0, nullable = false, comment = "上次更新时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;




	public String getSystemName() {
		return systemName;
	}


	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}


	public String getBuildingId() {
		return buildingId;
	}


	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}


	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}


	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}





}
