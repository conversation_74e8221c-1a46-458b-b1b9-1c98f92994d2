package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnTenantPrice")
@Table(name = "t_fn_tenant_price", comment = "租户价格方案 ", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_tenant_id" }), @Index(columns = { "c_price_template_id" }) })
public class FnTenantPrice extends BaseId {


	private static final long serialVersionUID = 3376465077071948977L;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_price_template_id", length = 50, nullable = false, comment = "价格模板主键")
	@JsonProperty("priceTemplateId")
	private String priceTemplateId;

	@Column(order = 4, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 5, name = "c_last_update_user_id", length = 50, nullable = true, comment = "最后修改人")
	@JsonProperty("lastUpdateUserId")
	private String lastUpdateUserId;

	@Column(order = 6, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getPriceTemplateId() {
		return priceTemplateId;
	}

	public void setPriceTemplateId(String priceTemplateId) {
		this.priceTemplateId = priceTemplateId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

}
