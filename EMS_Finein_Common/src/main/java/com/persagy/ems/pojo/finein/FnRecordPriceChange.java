package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnRecordPriceChange")
@Table(name = "t_fn_record_price_change", comment = "价格方案变更记录", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_id" }) })
public class FnRecordPriceChange extends BaseId {

	private static final long serialVersionUID = -2297481017530303973L;
	
	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;
	
	@Column(order = 3, name = "c_building_name", length = 100, nullable = false, comment = "建筑名称")
	@JsonProperty("buildingName")
	private String buildingName;
	
	@Column(order = 4, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;
	
	@Column(order = 5, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;
	
	@Column(order = 6, name = "c_tenant_name", length = 100, nullable = false, comment = "租户名称")
	@JsonProperty("tenantName")
	private String tenantName;

	@Column(order = 7, name = "c_change_time", length = 0, nullable = false, comment = "操作时间")
	@JsonProperty("changeTime")
	private Date changeTime;

	@Column(order = 8, name = "c_before_price_id", length = 50, nullable = true, comment = "修改前价格方案编码")
	@JsonProperty("beforePriceId")
	private String beforePriceId;
	
	@Column(order = 9, name = "c_before_price_name", length = 100, nullable = true, comment = "修改前价格方案名称")
	@JsonProperty("beforePriceName")
	private String beforePriceName;
	
	@Column(order =10, name = "c_after_price_id", length = 50, nullable = false, comment = "修改前价格方案编码")
	@JsonProperty("afterPriceId")
	private String afterPriceId;
	
	@Column(order = 11, name = "c_after_price_name", length = 100, nullable = false, comment = "修改前价格方案编码")
	@JsonProperty("afterPriceName")
	private String afterPriceName;
	
	@Column(order = 12, name = "c_user_id", length = 50, nullable = true, comment = "操作人编码")
	@JsonProperty("userId")
	private String userId;
	
	@Column(order = 13, name = "c_user_name", length = 50, nullable = true, comment = "操作人姓名")
	@JsonProperty("userName")
	private String userName;
	
	@Column(order = 14, name = "c_room_ids", length = 200, nullable = false, comment = "房间编码")
	@JsonProperty("roomIds")
	private String roomIds;
	
	@Column(order = 15, name = "c_create_time", length = 0, nullable = false, comment = "插入记录时间")
	@JsonProperty("createTime")
	private Date createTime;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getBuildingName() {
		return buildingName;
	}

	public void setBuildingName(String buildingName) {
		this.buildingName = buildingName;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public String getTenantName() {
		return tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	public Date getChangeTime() {
		return changeTime;
	}

	public void setChangeTime(Date changeTime) {
		this.changeTime = changeTime;
	}

	public String getBeforePriceId() {
		return beforePriceId;
	}

	public void setBeforePriceId(String beforePriceId) {
		this.beforePriceId = beforePriceId;
	}

	public String getBeforePriceName() {
		return beforePriceName;
	}

	public void setBeforePriceName(String beforePriceName) {
		this.beforePriceName = beforePriceName;
	}

	public String getAfterPriceId() {
		return afterPriceId;
	}

	public void setAfterPriceId(String afterPriceId) {
		this.afterPriceId = afterPriceId;
	}

	public String getAfterPriceName() {
		return afterPriceName;
	}

	public void setAfterPriceName(String afterPriceName) {
		this.afterPriceName = afterPriceName;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getRoomIds() {
		return roomIds;
	}

	public void setRoomIds(String roomIds) {
		this.roomIds = roomIds;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	
	

}
