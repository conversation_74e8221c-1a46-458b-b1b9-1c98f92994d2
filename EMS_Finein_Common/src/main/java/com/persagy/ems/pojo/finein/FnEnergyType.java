package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnEnergyType")
@Table(name = "t_fn_energy_type", comment = "能耗类型", schema = Schema.EMS, indexes = {  })
public class FnEnergyType extends BaseId {

	private static final long serialVersionUID = 6176573429357308934L;

	@Column(order = 2, name = "c_code", length = 50, nullable = false, comment = "能耗类型逻辑编码")
	@JsonProperty("code")
	private String code;

	@Column(order = 3, name = "c_name", length = 50, nullable = false, comment = "能耗类型名称")
	@JsonProperty("name")
	private String name;

	@Column(order = 4, name = "c_order_by", length = 3, nullable = false, comment = "排序")
	@JsonProperty("orderBy")
	private Integer orderBy;

	@Column(order = 5, name = "c_is_valid", length = 1, nullable = false, comment = "是否可用")
	@JsonProperty("isValid")
	private Integer isValid;

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getOrderBy() {
		return orderBy;
	}

	public void setOrderBy(Integer orderBy) {
		this.orderBy = orderBy;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

}
