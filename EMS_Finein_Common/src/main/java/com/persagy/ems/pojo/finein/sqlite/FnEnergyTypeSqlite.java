package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnEnergyType;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月26日 下午5:15:45

* 说明:
*/

@Dimension
@Entity(name="FnEnergyTypeSqlite")
@Table(
		name="t_fn_energy_type",
		comment="能耗类型",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnEnergyTypeSqlite extends FnEnergyType {

	private static final long serialVersionUID = 473094146498925923L;

}
