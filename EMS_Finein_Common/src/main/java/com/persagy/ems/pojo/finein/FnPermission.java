package com.persagy.ems.pojo.finein;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnPermission")
@Table(name = "t_fn_permission", comment = "权限表", schema = Schema.EMS, indexes = { })
public class FnPermission extends BaseId {

	private static final long serialVersionUID = -6523802339878504293L;

	@Column(order = 2, name = "c_name", length = 50, nullable = false, comment = "权限名称")
	@JsonProperty("name")
	private String name;
	
	@Column(order = 3, name = "c_system_name", length = 50, nullable = false, comment = "系统名称")
	@JsonProperty("systemName")
	private String systemName;

	@Column(order = 4, name = "c_content", length = 50, nullable = false, comment = "权限内容")
	@JsonProperty("content")
	private String content;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getSystemName() {
		return systemName;
	}

	public void setSystemName(String systemName) {
		this.systemName = systemName;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}



	

}
