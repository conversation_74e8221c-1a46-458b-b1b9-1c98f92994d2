package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnTenantMeterPowerCompute")
@Table(name = "t_fn_tenant_meter_power_compute", comment = "租户按功能统计计算时间", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_building_id", "c_tenant_id", "c_meter_id", "c_dataType" }, unique = true) })
public class FnTenantMeterPowerCompute extends BusinessObject {

	private static final long serialVersionUID = 1978636362340785289L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 2, name = "c_meter_id", length = 50, nullable = false, comment = "仪表租户主键")
	@JsonProperty("meterId")
	private String meterId;

	@Column(order = 3, name = "c_data_type", length = 1, nullable = false, comment = "数据类型")
	@JsonProperty("dataType")
	private Integer dataType;

	@Column(order = 4, name = "c_last_compute_time", length = 0, nullable = false, comment = "时间")
	@JsonProperty("lastComputeTime")
	private Date lastComputeTime;

	@Column(order = 5, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public Date getLastComputeTime() {
		return lastComputeTime;
	}

	public void setLastComputeTime(Date lastComputeTime) {
		this.lastComputeTime = lastComputeTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}
}
