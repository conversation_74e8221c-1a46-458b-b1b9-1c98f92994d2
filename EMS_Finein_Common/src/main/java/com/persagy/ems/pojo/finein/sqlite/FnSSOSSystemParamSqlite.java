package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnSSOSSystemParam;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月26日 下午5:15:45
 * 
 * 说明:
 */

@Dimension
@Entity(name = "FnSSOSSystemParamSqlite")
@Table(name = "t_fn_ssos_system_param", comment = "单点登录系统参数", schema = Schema.NONE, indexes = {

})
public class FnSSOSSystemParamSqlite extends FnSSOSSystemParam {

	private static final long serialVersionUID = 2597720836484745656L;

}
