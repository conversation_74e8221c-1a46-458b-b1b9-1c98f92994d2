package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnTenantType;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月26日 下午5:15:45

* 说明:
*/

@Dimension
@Entity(name="FnTenantTypeSqlite")
@Table(
		name="t_fn_tenant_type",
		comment="租户类型",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnTenantTypeSqlite extends FnTenantType {

	private static final long serialVersionUID = -3781345691126632797L;

}
