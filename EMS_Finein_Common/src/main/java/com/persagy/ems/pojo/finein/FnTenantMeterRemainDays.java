package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnTenantMeterRemainDays")
@Table(name = "t_fn_tenant_meter_remain_days", 
		transaction = false,
		comment = "租户-仪表预付费剩余使用天数 ", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_building_id","c_tenant_id","c_pay_body_type", "c_body_id", "c_energy_type_id" }, unique = true) })
public class FnTenantMeterRemainDays extends BusinessObject {


	private static final long serialVersionUID = 459450996082519228L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑主键")
	@JsonProperty("buildingId")
	private String buildingId;
	
	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;
	
	@Column(order = 3, name = "c_pay_body_type", length = 1, nullable = false, comment = "付费主体类型")
	@JsonProperty("payBodyType")
	private Integer payBodyType;

	@Column(order = 4, name = "c_body_id", length = 50, nullable = false, comment = "付费主体主键")
	@JsonProperty("bodyId")
	private String bodyId;

	@Column(order = 5, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 6, name = "c_remain_data", length = 18, scale = 8, nullable = true, comment = "剩余数据")
	@JsonProperty("remainData")
	private Double remainData;
	
	@Column(order = 7, name = "c_max_days", length = 11, nullable = true, comment = "最大使用天数")
	@JsonProperty("maxDays")
	private Integer maxDays;

	@Column(order = 8, name = "c_min_days", length = 11, nullable = true, comment = "最小使用天数")
	@JsonProperty("minDays")
	private Integer minDays;

	@Column(order = 9, name = "c_last_compute_time", length = 0, nullable = false, comment = "最后计算时间")
	@JsonProperty("lastComputeTime")
	private Date lastComputeTime;
	
	

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public Integer getPayBodyType() {
		return payBodyType;
	}

	public void setPayBodyType(Integer payBodyType) {
		this.payBodyType = payBodyType;
	}

	public String getBodyId() {
		return bodyId;
	}

	public void setBodyId(String bodyId) {
		this.bodyId = bodyId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Double getRemainData() {
		return remainData;
	}

	public void setRemainData(Double remainData) {
		this.remainData = remainData;
	}

	public Integer getMaxDays() {
		return maxDays;
	}

	public void setMaxDays(Integer maxDays) {
		this.maxDays = maxDays;
	}

	public Integer getMinDays() {
		return minDays;
	}

	public void setMinDays(Integer minDays) {
		this.minDays = minDays;
	}

	public Date getLastComputeTime() {
		return lastComputeTime;
	}

	public void setLastComputeTime(Date lastComputeTime) {
		this.lastComputeTime = lastComputeTime;
	}

	
	
	
}
