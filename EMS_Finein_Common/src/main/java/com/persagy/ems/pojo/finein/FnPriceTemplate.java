package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnPriceTemplate")
@Table(name = "t_fn_price_template", comment = "价格模板", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_name" },unique = true) })
public class FnPriceTemplate extends BaseId {

	private static final long serialVersionUID = 4442866389217003540L;

	@Column(order = 3, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 4, name = "c_name", length = 50, nullable = false, comment = "价格模板名称")
	@JsonProperty("name")
	private String name;

	@Column(order = 5, name = "c_create_user_id", length = 50, nullable = true, comment = "创建人")
	@JsonProperty("createUserId")
	private String createUserId;

	@Column(order = 6, name = "c_create_time", length = 0, nullable = false, comment = "报警插入时间")
	@JsonProperty("createTime")
	private Date createTime;

	@Column(order = 7, name = "c_type", length = 1, nullable = false, comment = "价格类型")
	@JsonProperty("type")
	private Integer type;
	
	@Column(order = 8, name = "c_content", length = 500, nullable = false, comment = "内容")
	@JsonProperty("content")
	private String content;

	@Column(order = 9, name = "c_last_update_time", length = 0, nullable = false, comment = "报警最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	@Column(order = 10, name = "c_is_valid", length = 1, nullable = false, comment = "是否可用")
	@JsonProperty("isValid")
	private Integer isValid;

	@Column(order = 11, name = "c_invalid_time", length = 0, nullable = true, comment = "不可用时间")
	@JsonProperty("invalidTime")
	private Date invalidTime;

	@Column(order = 12, name = "c_last_update_user_id", length = 50, nullable = true, comment = "最后修改人")
	@JsonProperty("lastUpdateUserId")
	private String lastUpdateUserId;
	

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCreateUserId() {
		return createUserId;
	}

	public void setCreateUserId(String createUserId) {
		this.createUserId = createUserId;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getContent() {
		return content;
	}

	public void setContent(String content) {
		this.content = content;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}

	public Date getInvalidTime() {
		return invalidTime;
	}

	public void setInvalidTime(Date invalidTime) {
		this.invalidTime = invalidTime;
	}

	public String getLastUpdateUserId() {
		return lastUpdateUserId;
	}

	public void setLastUpdateUserId(String lastUpdateUserId) {
		this.lastUpdateUserId = lastUpdateUserId;
	}

	

}
