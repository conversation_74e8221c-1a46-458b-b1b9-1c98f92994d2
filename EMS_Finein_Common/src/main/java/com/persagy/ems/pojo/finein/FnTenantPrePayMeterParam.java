package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnTenantPrePayMeterParam")
@Table(name = "t_fn_tenant_pre_pay_meter_param", 
		transaction = false,
		comment = "租户-预付费-仪表-参数", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_tenant_id","c_meter_id","c_energy_type_id" },unique = true) })
public class FnTenantPrePayMeterParam extends BusinessObject {

	
	private static final long serialVersionUID = -1938832323418675075L;
	
	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑主键")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;
	
	@Column(order = 3, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;
	
	@Column(order = 4, name = "c_meter_id", length = 30, nullable = false, comment = "仪表主键")
	@JsonProperty("meterId")
	private String meterId;
	
	@Column(order = 5, name = "c_pre_pay_type", length = 1, nullable = false, comment = "预付费充值类型")
	@JsonProperty("prePayType")
	private Integer prePayType;
	
	@Column(order = 6, name = "c_current_month_energy", length = 18, scale = 8, nullable = true, comment = "本月能耗")
	@JsonProperty("currentMonthEnergy")
	private Double currentMonthEnergy;
	
	@Column(order = 8, name = "c_prepay_charge_type", length = 1, scale = 0, nullable = true, comment = "充值类型")
	@JsonProperty("prepayChargeType")
	private Integer prepayChargeType;
	
	@Column(order = 9, name = "c_remain_data", length = 18, scale = 8, nullable = true, comment = "剩余数据")
	@JsonProperty("remainData")
	private Double remainData;
	
	@Column(order = 10, name = "c_remain_days", length = 50, scale = 0, nullable = true, comment = "剩余天数")
	@JsonProperty("remainDays")
	private String remainDays;
	
	@Column(order = 11, name = "c_is_alarm", length = 1, nullable = true, comment = "是否不足")
	@JsonProperty("isAlarm")
	private Integer isAlarm;

	@Column(order = 12, name = "c_last_update_time", length = 0, nullable = false, comment = "上次更新时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;
	
	

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public Integer getPrePayType() {
		return prePayType;
	}

	public void setPrePayType(Integer prePayType) {
		this.prePayType = prePayType;
	}

	public Double getCurrentMonthEnergy() {
		return currentMonthEnergy;
	}

	public void setCurrentMonthEnergy(Double currentMonthEnergy) {
		this.currentMonthEnergy = currentMonthEnergy;
	}

	public Integer getPrepayChargeType() {
		return prepayChargeType;
	}

	public void setPrepayChargeType(Integer prepayChargeType) {
		this.prepayChargeType = prepayChargeType;
	}

	public Double getRemainData() {
		return remainData;
	}

	public void setRemainData(Double remainData) {
		this.remainData = remainData;
	}

	public String getRemainDays() {
		return remainDays;
	}

	public void setRemainDays(String remainDays) {
		this.remainDays = remainDays;
	}

	public Integer getIsAlarm() {
		return isAlarm;
	}

	public void setIsAlarm(Integer isAlarm) {
		this.isAlarm = isAlarm;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
	
	

}
