package com.persagy.ems.pojo.finein;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;


@Dimension
@Entity(name = "FnRecordPrePayExtend")
@Table(name = "t_fn_record_pre_pay_extend", comment = "充值记录扩展表", schema = Schema.EMS, 
		transaction = false,
		indexes = {	@Index(columns = { "c_id"}, unique = true)}
		)
public class FnRecordPrePayExtend extends BaseId {
	

	private static final long serialVersionUID = -3070991169483623812L;

	@Column(order = 2, name = "c_source", length =1, nullable = false, comment = "充值来源")
	@JsonProperty("source")
	private Integer source;
	
	@Column(order = 3, name = "c_pre_pay_system_code", length = 50, nullable = true, comment = "充值系统编号")
	@JsonProperty("prePaySystemCode")
	private String prePaySystemCode;
	

	public Integer getSource() {
		return source;
	}

	public void setSource(Integer source) {
		this.source = source;
	}

	public String getPrePaySystemCode() {
		return prePaySystemCode;
	}

	public void setPrePaySystemCode(String prePaySystemCode) {
		this.prePaySystemCode = prePaySystemCode;
	}
}
