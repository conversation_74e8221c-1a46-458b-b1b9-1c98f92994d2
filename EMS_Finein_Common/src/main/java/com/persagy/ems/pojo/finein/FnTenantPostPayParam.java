package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnTenantPostPayParam")
@Table(name = "t_fn_tenant_post_pay_param",
		transaction = false,
		comment = "租户-后付费-参数", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_tenant_id","c_energy_type_id" },unique = true) })
public class FnTenantPostPayParam extends BusinessObject {

	private static final long serialVersionUID = -5437156391986976223L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑主键")
	@JsonProperty("buildingId")
	private String buildingId;
	
	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;
	
	@Column(order = 3, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;
	
	@Column(order = 4, name = "c_last_clearing_time", length = 0, nullable = true, comment = "上次结算时间")
	@JsonProperty("lastClearingTime")
	private Date lastClearingTime;
	
	@Column(order = 5, name = "c_no_billing_energy", length = 18, scale = 8, nullable = true, comment = "未结算能耗")
	@JsonProperty("noBillingEnergy")
	private Double noBillingEnergy;
	
	@Column(order = 6, name = "c_no_billing_money", length = 18, scale = 8, nullable = true, comment = "未结算金额")
	@JsonProperty("noBillingMoney")
	private Double noBillingMoney;

	@Column(order = 7, name = "c_no_pay_order_count", length = 3, scale = 0, nullable = true, comment = "未缴费账单数")
	@JsonProperty("noPayOrderCount")
	private Integer noPayOrderCount;
	
	@Column(order = 8, name = "c_billing_energy", length = 18, scale = 8, nullable = true, comment = "结算能耗")
	@JsonProperty("billingEnergy")
	private Double billingEnergy;
	
	@Column(order = 9, name = "c_billing_money", length = 18, scale = 8, nullable = true, comment = "结算金额")
	@JsonProperty("billingMoney")
	private Double billingMoney;

	@Column(order = 10, name = "c_no_billing_type", length = 3, scale = 0, nullable = true, comment = "未结算类型")
	@JsonProperty("noBillingType")
	private Integer noBillingType;
	
	@Column(order = 11, name = "c_last_update_time", length = 0, nullable = false, comment = "上次更新时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;
	
	

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Date getLastClearingTime() {
		return lastClearingTime;
	}

	public void setLastClearingTime(Date lastClearingTime) {
		this.lastClearingTime = lastClearingTime;
	}

	public Double getNoBillingEnergy() {
		return noBillingEnergy;
	}

	public void setNoBillingEnergy(Double noBillingEnergy) {
		this.noBillingEnergy = noBillingEnergy;
	}

	public Double getNoBillingMoney() {
		return noBillingMoney;
	}

	public void setNoBillingMoney(Double noBillingMoney) {
		this.noBillingMoney = noBillingMoney;
	}

	public Integer getNoPayOrderCount() {
		return noPayOrderCount;
	}

	public void setNoPayOrderCount(Integer noPayOrderCount) {
		this.noPayOrderCount = noPayOrderCount;
	}

	public Double getBillingEnergy() {
		return billingEnergy;
	}

	public void setBillingEnergy(Double billingEnergy) {
		this.billingEnergy = billingEnergy;
	}

	public Double getBillingMoney() {
		return billingMoney;
	}

	public void setBillingMoney(Double billingMoney) {
		this.billingMoney = billingMoney;
	}

	public Integer getNoBillingType() {
		return noBillingType;
	}

	public void setNoBillingType(Integer noBillingType) {
		this.noBillingType = noBillingType;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	

}
