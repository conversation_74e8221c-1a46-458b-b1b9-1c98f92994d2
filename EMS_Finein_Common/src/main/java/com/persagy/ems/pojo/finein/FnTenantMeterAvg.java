package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnTenantMeterAvg")
@Table(name = "t_fn_tenant_meter_avg", 
		transaction = false,
		comment = "平均值", schema = Schema.EMS, indexes = { @Index(columns = {
		"c_building_id","c_tenant_id", "c_pay_body_type", "c_body_id", "c_data_type", "c_energy_type_id" }, unique = true) })
public class FnTenantMeterAvg extends BusinessObject {

	private static final long serialVersionUID = 1452240915149934333L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;
	
	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_pay_body_type", length = 1, nullable = false, comment = "付费主体类型")
	@JsonProperty("payBodyType")
	private Integer payBodyType;

	@Column(order = 4, name = "c_body_id", length = 50, nullable = false, comment = "付费主体主键")
	@JsonProperty("bodyId")
	private String bodyId;

	@Column(order = 5, name = "c_data_type", length = 1, nullable = false, comment = "数据，费用")
	@JsonProperty("dataType")
	private Integer dataType;

	@Column(order = 6, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 7, name = "c_data_count", length = 11, nullable = false, comment = "统计个数")
	@JsonProperty("dataCount")
	private Integer dataCount;

	@Column(order = 8, name = "c_time_from", length = 0, nullable = false, comment = "时间")
	@JsonProperty("timeFrom")
	private Date timeFrom;

	@Column(order = 9, name = "c_time_to", length = 0, nullable = false, comment = "时间")
	@JsonProperty("timeTo")
	private Date timeTo;

	@Column(order = 10, name = "c_data", length = 18, scale = 8, nullable = false, comment = "数据")
	@JsonProperty("data")
	private Double data;

	@Column(order = 11, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;
	
	

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public Integer getPayBodyType() {
		return payBodyType;
	}

	public void setPayBodyType(Integer payBodyType) {
		this.payBodyType = payBodyType;
	}

	public String getBodyId() {
		return bodyId;
	}

	public void setBodyId(String bodyId) {
		this.bodyId = bodyId;
	}

	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Integer getDataCount() {
		return dataCount;
	}

	public void setDataCount(Integer dataCount) {
		this.dataCount = dataCount;
	}

	public Date getTimeFrom() {
		return timeFrom;
	}

	public void setTimeFrom(Date timeFrom) {
		this.timeFrom = timeFrom;
	}

	public Date getTimeTo() {
		return timeTo;
	}

	public void setTimeTo(Date timeTo) {
		this.timeTo = timeTo;
	}

	public Double getData() {
		return data;
	}

	public void setData(Double data) {
		this.data = data;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
	
	



}
