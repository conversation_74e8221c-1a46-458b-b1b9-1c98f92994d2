package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnProtocolFunction;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月26日 下午5:15:45

* 说明:
*/

@Dimension
@Entity(name="FnProtocolFunctionSqlite")
@Table(
		name="t_fn_protocol_function",
		comment="协议功能",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnProtocolFunctionSqlite extends FnProtocolFunction {

	private static final long serialVersionUID = 191042730692034311L;

}
