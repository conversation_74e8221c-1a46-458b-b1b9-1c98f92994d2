package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnProtocol;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月26日 下午5:15:45

* 说明:
*/

@Dimension
@Entity(name="FnProtocolSqlite")
@Table(
		name="t_fn_protocol",
		comment="仪表协议",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnProtocolSqlite extends FnProtocol {

	private static final long serialVersionUID = -7538676899439353902L;


}
