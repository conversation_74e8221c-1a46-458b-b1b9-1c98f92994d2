package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnAlarm")
@Table(name = "t_fn_alarm", comment = "报警记录", schema = Schema.EMS, indexes = { @Index(columns = { "c_building_id" }) ,@Index(columns = { "c_tenant_id" }) })
public class FnAlarm extends BaseId {

	private static final long serialVersionUID = -2287019773141474665L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;
	
	@Column(order = 3, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 4, name = "c_alarm_type_id", length = 50, nullable = false, comment = "报警类型主键")
	@JsonProperty("alarmTypeId")
	private String alarmTypeId;

	@Column(order = 5, name = "c_parent_alarm_type_id", length = 50, nullable = false, comment = "父级报警类型主键")
	@JsonProperty("parentAlarmTypeId")
	private String parentAlarmTypeId;

	@Column(order = 6, name = "c_tree_id", length = 100, nullable = false, comment = "报警类型树")
	@JsonProperty("treeId")
	private String treeId;

	@Column(order = 7, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 8, name = "c_alarm_time", length = 0, nullable = false, comment = "报警时间")
	@JsonProperty("alarmTime")
	private Date alarmTime;

	@Column(order = 9, name = "c_alarm_position_type", length = 1, nullable = false, comment = "报警位置类型")
	@JsonProperty("alarmPositionType")
	private Integer alarmPositionType;

	@Column(order = 10, name = "c_alarm_position_id", length = 50, nullable = false, comment = "报警位置编码")
	@JsonProperty("alarmPositionId")
	private String alarmPositionId;

	@Column(order = 11, name = "c_alarm_position_name", length = 100, nullable = false, comment = "报警位置名称")
	@JsonProperty("alarmPositionName")
	private String alarmPositionName;

	@Column(order = 12, name = "c_status", length = 1, nullable = false, comment = "报警状态")
	@JsonProperty("status")
	private Integer status;

	@Column(order = 13, name = "c_limit_value", length = 18, scale = 8, nullable = true, comment = "报警门限值")
	@JsonProperty("limitValue")
	private Double limitValue;

	@Column(order = 14, name = "c_current_value", length = 18, scale = 8, nullable = true, comment = "报警当前值")
	@JsonProperty("currentValue")
	private Double currentValue;

	@Column(order = 15, name = "c_create_time", length = 0, nullable = false, comment = "报警插入时间")
	@JsonProperty("createTime")
	private Date createTime;

	@Column(order = 16, name = "c_last_update_time", length = 0, nullable = false, comment = "报警最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	@Column(order = 17, name = "c_finish_time", length = 0, nullable = true, comment = "报警完成时间")
	@JsonProperty("finishTime")
	private Date finishTime;

	@Column(order = 18, name = "c_unit", length = 20, nullable = true, comment = "报警单位")
	@JsonProperty("unit")
	private String unit;

	@Column(order = 19, name = "c_is_read", length = 1, nullable = false, comment = "报警是否已读")
	@JsonProperty("isRead")
	private Integer isRead;

	@Column(order = 20, name = "c_extend", length = 500, nullable = true, comment = "扩展字段")
	@JsonProperty("extend")
	private String extend;

	@Column(order = 21, name = "c_push_status", length = 1, nullable = false, comment = "报警推送状态")
	@JsonProperty("pushStatus")
	private Integer pushStatus;
	
	

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getAlarmTypeId() {
		return alarmTypeId;
	}

	public void setAlarmTypeId(String alarmTypeId) {
		this.alarmTypeId = alarmTypeId;
	}

	public String getParentAlarmTypeId() {
		return parentAlarmTypeId;
	}

	public void setParentAlarmTypeId(String parentAlarmTypeId) {
		this.parentAlarmTypeId = parentAlarmTypeId;
	}

	public String getTreeId() {
		return treeId;
	}

	public void setTreeId(String treeId) {
		this.treeId = treeId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Date getAlarmTime() {
		return alarmTime;
	}

	public void setAlarmTime(Date alarmTime) {
		this.alarmTime = alarmTime;
	}

	public Integer getAlarmPositionType() {
		return alarmPositionType;
	}

	public void setAlarmPositionType(Integer alarmPositionType) {
		this.alarmPositionType = alarmPositionType;
	}

	public String getAlarmPositionId() {
		return alarmPositionId;
	}

	public void setAlarmPositionId(String alarmPositionId) {
		this.alarmPositionId = alarmPositionId;
	}

	public String getAlarmPositionName() {
		return alarmPositionName;
	}

	public void setAlarmPositionName(String alarmPositionName) {
		this.alarmPositionName = alarmPositionName;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Double getLimitValue() {
		return limitValue;
	}

	public void setLimitValue(Double limitValue) {
		this.limitValue = limitValue;
	}

	public Double getCurrentValue() {
		return currentValue;
	}

	public void setCurrentValue(Double currentValue) {
		this.currentValue = currentValue;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	public Date getFinishTime() {
		return finishTime;
	}

	public void setFinishTime(Date finishTime) {
		this.finishTime = finishTime;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public Integer getIsRead() {
		return isRead;
	}

	public void setIsRead(Integer isRead) {
		this.isRead = isRead;
	}

	public String getExtend() {
		return extend;
	}

	public void setExtend(String extend) {
		this.extend = extend;
	}

	public Integer getPushStatus() {
		return pushStatus;
	}

	public void setPushStatus(Integer pushStatus) {
		this.pushStatus = pushStatus;
	}

	

}
