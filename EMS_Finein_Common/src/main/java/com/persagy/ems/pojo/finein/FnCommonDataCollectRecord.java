package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnCommonDataCollectRecord")
@Table(name = "t_fn_common_data_collect_record", comment = "通用数据采集记录", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_meter_id" }) })
public class FnCommonDataCollectRecord extends BusinessObject {

	private static final long serialVersionUID = 7701311112372840508L;

	@Column(order = 1, name = "c_meter_id", length = 20, nullable = false, comment = "仪表编码")
	@JsonProperty("meterId")
	private String meterId;

	@Column(order = 2, name = "c_last_collect_time", length = 0, nullable = false, comment = "上次采集时间")
	@JsonProperty("lastCollectTime")
	private Date lastCollectTime;

	@Column(order = 3, name = "c_last_update_time", length = 0, nullable = false, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public Date getLastCollectTime() {
		return lastCollectTime;
	}

	public void setLastCollectTime(Date lastCollectTime) {
		this.lastCollectTime = lastCollectTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

}
