package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnSysParamValue;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月26日 下午5:15:45

* 说明:
*/

@Dimension
@Entity(name="FnSysParamValueSqlite")
@Table(
		name="t_fn_sys_param_value",
		comment="系统参数",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnSysParamValueSqlite extends FnSysParamValue {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1619084191325210405L;


}
