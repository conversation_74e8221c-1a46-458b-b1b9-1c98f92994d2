package com.persagy.ems.pojo.finein;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnRolePermission")
@Table(name = "t_fn_role_permission", comment = "角色权限关系表", schema = Schema.EMS, indexes = { })
public class FnRolePermission extends BaseId {
	
	private static final long serialVersionUID = -7909727720471414119L;

	@Column(order = 1, name = "c_permission_id", length = 50, nullable = false, comment = "权限主键")
	@JsonProperty("permissionId")
	private String permissionId;
	
	@Column(order = 2, name = "c_role_id", length = 50, nullable = false, comment = "角色主键")
	@JsonProperty("roleId")
	private String roleId;

	@Column(order = 4, name = "c_is_have", length = 1, nullable = false, comment = "是否拥有此权限")
	@JsonProperty("isHave")
	private Integer isHave;

	public String getPermissionId() {
		return permissionId;
	}

	public void setPermissionId(String permissionId) {
		this.permissionId = permissionId;
	}

	public String getRoleId() {
		return roleId;
	}

	public void setRoleId(String roleId) {
		this.roleId = roleId;
	}

	public Integer getIsHave() {
		return isHave;
	}

	public void setIsHave(Integer isHave) {
		this.isHave = isHave;
	}

	
	

}
