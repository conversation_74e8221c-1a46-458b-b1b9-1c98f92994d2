package com.persagy.ems.pojo.finein.dictionary;

import com.persagy.core.dictionary.annotation.DictionaryCategory;
import com.persagy.core.dictionary.annotation.DictionaryCode;
import com.persagy.core.dictionary.dto.BaseDictonaryId;

@DictionaryCategory(category="MT")
public class Meter extends BaseDictonaryId {

	//名称
	@DictionaryCode(code="10002")
	private String name;
	
	//类型
	@DictionaryCode(code="20001")
	private String type;
	
	//功能列表（内部存储json数组）
	@DictionaryCode(code="20002")
	private String function;
	
	//CT
	@DictionaryCode(code="20003")
	private Double ct;
		
	//PT
	@DictionaryCode(code="20004")
	private Double pt;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getFunction() {
		return function;
	}

	public void setFunction(String function) {
		this.function = function;
	}

	public Double getCt() {
		return ct;
	}

	public void setCt(Double ct) {
		this.ct = ct;
	}

	public Double getPt() {
		return pt;
	}

	public void setPt(Double pt) {
		this.pt = pt;
	}

}
