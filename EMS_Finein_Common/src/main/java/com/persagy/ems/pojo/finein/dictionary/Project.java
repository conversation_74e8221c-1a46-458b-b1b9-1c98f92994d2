package com.persagy.ems.pojo.finein.dictionary;

import com.persagy.core.dictionary.annotation.DictionaryCategory;
import com.persagy.core.dictionary.annotation.DictionaryCode;
import com.persagy.core.dictionary.dto.BaseDictonaryId;

@DictionaryCategory(category="PO")
public class Project extends BaseDictonaryId {

	//名称
	@DictionaryCode(code="10002")
	private String name;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}
	
}
