package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;


@Dimension
@Entity(name = "FnSendData")
@Table(name = "t_fn_send_data", comment = "租户串口采集待传数据", schema = Schema.EMS, 
		transaction = false,
		indexes = {
		@Index(columns = { "c_id"
				 }),
		@Index(columns = { "c_building_id"
		 })
		}

		)
public class FnSendData extends BaseId {
	
	private static final long serialVersionUID = -1045805434524683370L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 2, name = "c_meter_id", length = 15, nullable = false, comment = "仪表编码")
	@JsonProperty("meterId")
	private String meterId;
	
	@Column(order = 3, name = "c_function_id", length = 11, nullable = false, comment = "功能号")
	@JsonProperty("functionId")
	private Integer functionId;
	
	@Column(order = 4, name = "c_data_type", length = 1, nullable = false, comment = "数据类型")
	@JsonProperty("dataType")
	private Integer dataType;
	
	@Column(order = 5, name = "c_receive_time", length = 11, nullable = false, comment = "时间")
	@JsonProperty("receiveTime")
	private Date receiveTime;
	
	@Column(order = 6, name = "c_data",  length = 18, scale = 8, nullable = false, comment = "数据值")
	@JsonProperty("data")
	private Double data;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public Integer getFunctionId() {
		return functionId;
	}

	public void setFunctionId(Integer functionId) {
		this.functionId = functionId;
	}

	public Integer getDataType() {
		return dataType;
	}

	public void setDataType(Integer dataType) {
		this.dataType = dataType;
	}

	public Date getReceiveTime() {
		return receiveTime;
	}

	public void setReceiveTime(Date receiveTime) {
		this.receiveTime = receiveTime;
	}

	public Double getData() {
		return data;
	}

	public void setData(Double data) {
		this.data = data;
	}
	
	
}
