package com.persagy.ems.pojo.finein.sqlite;

import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.pojo.finein.FnMonitorParam;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月26日 下午5:15:45

* 说明:
*/

@Dimension
@Entity(name="FnMonitorParamSqlite")
@Table(
		name="t_fn_monitor_param",
		comment="监控参数",
		schema=Schema.NONE,
		indexes={
				
		}
)
public class FnMonitorParamSqlite extends FnMonitorParam {

	private static final long serialVersionUID = 6943716691140346082L;

}
