package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Dimension
@Entity(name = "FnRecordPayChannel")
@Table(name = "t_fn_record_pay_channel", comment = "微信充值状态", schema = SchemaConstant.Schema.EMS, indexes = {
        @Index(columns = { "c_id" }) })
public class FnRecordPayChannel extends BaseId {


    @Column(order = 2, name = "c_record_pay_id", length = 36, nullable = false, comment = "充值记录表ID")
    @JsonProperty("recordPayID")
    private String recordPayID;

    @Column(order = 3, name = "c_channel_type", length = 1, nullable = false, comment = "微信充值状态")//0微信正常充值，1异常账单补冲
    @JsonProperty("channelType")
    private Integer channelType;

    @Column(order = 4, name = "c_pay_type", length = 1, nullable = false, comment = "充值类别") //0预付费 ，1后付费
    @JsonProperty("payType")
    private Integer payType;

    @Column(order = 5, name = "c_info_id", length = 300, nullable = true, comment = "备用字段")
    @JsonProperty("infoId")
    private String infoId;

    @Column(order = 6, name = "c_create_time", length = 0, nullable = false, comment = "创建时间")
    @JsonProperty("createTime")
    private Date createTime;

    @Column(order = 7, name = "c_updata_time", length = 0, nullable = true, comment = "修改时间")
    @JsonProperty("updataTime")
    private Date updataTime;

    @Column(order = 8, name = "c_delete_time", length = 0, nullable = true, comment = "删除时间")
    @JsonProperty("deleteTime")
    private Date deleteTime;

    public String getRecordPayID() {
        return recordPayID;
    }

    public void setRecordPayID(String recordPayID) {
        this.recordPayID = recordPayID;
    }

    public Integer getChannelType() {
        return channelType;
    }

    public void setChannelType(Integer channelType) {
        this.channelType = channelType;
    }

    public Integer getPayType() {
        return payType;
    }

    public void setPayType(Integer payType) {
        this.payType = payType;
    }

    public String getInfoId() {
        return infoId;
    }

    public void setInfoId(String infoId) {
        this.infoId = infoId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdataTime() {
        return updataTime;
    }

    public void setUpdataTime(Date updataTime) {
        this.updataTime = updataTime;
    }

    public Date getDeleteTime() {
        return deleteTime;
    }

    public void setDeleteTime(Date deleteTime) {
        this.deleteTime = deleteTime;
    }

    @Override
    public String toString() {
        return "FnRecordPayChannel{" +
                "recordPayID='" + recordPayID + '\'' +
                ", channelType=" + channelType +
                ", payType=" + payType +
                ", infoId=" + infoId +
                ", createTime=" + createTime +
                ", updataTime=" + updataTime +
                ", deleteTime=" + deleteTime +
                '}';
    }
}
