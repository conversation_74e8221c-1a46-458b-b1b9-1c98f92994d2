package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnBuildingCostTime")
@Table(name = "t_fn_building_cost_time", transaction = false, comment = "租户-能耗成本统计时间", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_building_id", "c_energy_type_id"}, unique = true) })
public class FnBuildingCostTime extends BusinessObject {

	private static final long serialVersionUID = 459450996082519228L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑主键")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 2, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 3, name = "c_time", length = 0, nullable = false, comment = "最后计算时间")
	@JsonProperty("time")
	private Date time;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public Date getTime() {
		return time;
	}

	public void setTime(Date time) {
		this.time = time;
	}

}
