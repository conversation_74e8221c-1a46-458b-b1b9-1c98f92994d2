package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnAlarmType")
@Table(name = "t_fn_alarm_type", comment = "报警类型", schema = Schema.EMS, indexes = { @Index(columns = { "c_is_valid" }) })
public class FnAlarmType extends BaseId {


	private static final long serialVersionUID = 8176630020152039821L;

	@Column(order = 2, name = "c_parent_id", length = 50, nullable = true, comment = "报警类型父级")
	@JsonProperty("parentId")
	private String parentId;

	@Column(order = 3, name = "c_tree_id", length = 100, nullable = false, comment = "报警类型全码")
	@JsonProperty("treeId")
	private String treeId;

	@Column(order = 4, name = "c_name", length = 50, nullable = false, comment = "报警类型名称")
	@JsonProperty("name")
	private String name;

	@Column(order = 5, name = "c_unit", length = 10, nullable = true, comment = "单位")
	@JsonProperty("unit")
	private String unit;
	
	@Column(order = 6, name = "c_is_limit", length = 1, nullable = false, comment = "是否比较门限")
	@JsonProperty("isLimit")
	private Integer isLimit;
	
	@Column(order = 7, name = "c_compare_type", length = 1, nullable = false, comment = "比较门限类型")
	@JsonProperty("compareType")
	private Integer compareType;

	@Column(order = 8, name = "c_extend", length = 500, nullable = true, comment = "扩展字段")
	@JsonProperty("extend")
	private String extend;

	@Column(order = 9, name = "c_is_valid", length = 1, nullable = false, comment = "报警类型是否可用")
	@JsonProperty("isValid")
	private Integer isValid;
	
	

	public String getParentId() {
		return parentId;
	}

	public void setParentId(String parentId) {
		this.parentId = parentId;
	}

	public String getTreeId() {
		return treeId;
	}

	public void setTreeId(String treeId) {
		this.treeId = treeId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getUnit() {
		return unit;
	}

	public void setUnit(String unit) {
		this.unit = unit;
	}

	public Integer getIsLimit() {
		return isLimit;
	}

	public void setIsLimit(Integer isLimit) {
		this.isLimit = isLimit;
	}

	public Integer getCompareType() {
		return compareType;
	}

	public void setCompareType(Integer compareType) {
		this.compareType = compareType;
	}

	public String getExtend() {
		return extend;
	}

	public void setExtend(String extend) {
		this.extend = extend;
	}

	public Integer getIsValid() {
		return isValid;
	}

	public void setIsValid(Integer isValid) {
		this.isValid = isValid;
	}
}
