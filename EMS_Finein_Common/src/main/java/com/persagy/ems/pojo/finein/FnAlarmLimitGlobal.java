package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnAlarmLimitGlobal")
@Table(name = "t_fn_alarm_limit_global", comment = "报警全局门限模板", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_parent_alarm_type_id" }) })
public class FnAlarmLimitGlobal extends BaseId {


	private static final long serialVersionUID = 2206847194544856234L;

	@Column(order = 3, name = "c_alarm_type_id", length = 50, nullable = true, comment = "报警类型主键")
	@JsonProperty("alarmTypeId")
	private String alarmTypeId;

	@Column(order = 4, name = "c_parent_alarm_type_id", length = 50, nullable = false, comment = "父级报警类型主键")
	@JsonProperty("parentAlarmTypeId")
	private String parentAlarmTypeId;

	@Column(order = 5, name = "c_tree_id", length = 100, nullable = false, comment = "报警类型树")
	@JsonProperty("treeId")
	private String treeId;

	@Column(order = 6, name = "c_limit_value", length = 18, scale = 8, nullable = true, comment = "报警门限值")
	@JsonProperty("limitValue")
	private Double limitValue;

	@Column(order = 7, name = "c_is_open", length = 1, nullable = false, comment = "是否打开")
	@JsonProperty("isOpen")
	private Integer isOpen;

	public String getAlarmTypeId() {
		return alarmTypeId;
	}

	public void setAlarmTypeId(String alarmTypeId) {
		this.alarmTypeId = alarmTypeId;
	}

	public String getParentAlarmTypeId() {
		return parentAlarmTypeId;
	}

	public void setParentAlarmTypeId(String parentAlarmTypeId) {
		this.parentAlarmTypeId = parentAlarmTypeId;
	}

	public String getTreeId() {
		return treeId;
	}

	public void setTreeId(String treeId) {
		this.treeId = treeId;
	}

	public Double getLimitValue() {
		return limitValue;
	}

	public void setLimitValue(Double limitValue) {
		this.limitValue = limitValue;
	}

	public Integer getIsOpen() {
		return isOpen;
	}

	public void setIsOpen(Integer isOpen) {
		this.isOpen = isOpen;
	}

}
