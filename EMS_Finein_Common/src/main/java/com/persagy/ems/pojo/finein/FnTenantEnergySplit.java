package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnTenantEnergySplit")
@Table(name = "t_fn_tenant_energy_split", comment = "租户能耗拆分", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_tenant_id","c_energy_type_id" }) })
public class FnTenantEnergySplit extends BaseId {


	private static final long serialVersionUID = -5273192829248158779L;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 4, name = "c_expression", length = 200, nullable = false, comment = "表达式")
	@JsonProperty("expression")
	private String expression;

	@Column(order = 5, name = "c_elements", length = 200, nullable = false, comment = "表达式元素")
	@JsonProperty("elements")
	private String elements;

	@Column(order = 6, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public String getExpression() {
		return expression;
	}

	public void setExpression(String expression) {
		this.expression = expression;
	}

	public String getElements() {
		return elements;
	}

	public void setElements(String elements) {
		this.elements = elements;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

}
