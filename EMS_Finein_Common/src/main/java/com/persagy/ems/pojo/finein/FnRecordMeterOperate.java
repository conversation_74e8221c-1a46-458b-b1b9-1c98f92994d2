package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnRecordMeterOperate")
@Table(name = "t_fn_record_meter_operate", comment = "仪表操作记录", transaction = false, schema = Schema.EMS, indexes = {
		@Index(columns = { "c_tenant_id"}) })
public class FnRecordMeterOperate extends BaseId {

	private static final long serialVersionUID = -938079834014602063L;

	@Column(order = 2, name = "c_meter_id", length = 50, nullable = false, comment = "仪表编码")
	@JsonProperty("meterId")
	private String meterId;

	@Column(order = 3, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;
	
	@Column(order = 4, name = "c_tenant_name", length = 50, nullable = false, comment = "租户名称")
	@JsonProperty("tenantName")
	private String tenantName;

	@Column(order = 5, name = "c_protocol_id", length = 50, nullable = false, comment = "协议主键")
	@JsonProperty("protocolId")
	private String protocolId;

	@Column(order = 6, name = "c_operate_type", length = 1, nullable = false, comment = "操作类型")
	@JsonProperty("changeTime")
	private Integer operateType;

	@Column(order = 7, name = "c_operate_time", length = 0, nullable = false, comment = "操作时间")
	@JsonProperty("operateTime")
	private Date operateTime;

	@Column(order = 8, name = "c_value", length = 200, nullable = false, comment = "操作值")
	@JsonProperty("value")
	private String value;

	@Column(order = 9, name = "c_request", length = 1000, nullable = false, comment = "请求报文")
	@JsonProperty("request")
	private String request;

	@Column(order = 10, name = "c_respond", length = 100, nullable = true, comment = "响应报文")
	@JsonProperty("respond")
	private String respond;

	@Column(order = 11, name = "c_user_id", length = 50, nullable = false, comment = "操作人编码")
	@JsonProperty("userId")
	private String userId;

	@Column(order = 12, name = "c_user_name", length = 50, nullable = false, comment = "操作人姓名")
	@JsonProperty("userName")
	private String userName;

	public String getTenantId() {
		return tenantId;
	}

	public String getTenantName() {
		return tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getProtocolId() {
		return protocolId;
	}

	public void setProtocolId(String protocolId) {
		this.protocolId = protocolId;
	}

	public String getValue() {
		return value;
	}

	public void setValue(String value) {
		this.value = value;
	}

	public String getRequest() {
		return request;
	}

	public void setRequest(String request) {
		this.request = request;
	}

	public String getRespond() {
		return respond;
	}

	public void setRespond(String respond) {
		this.respond = respond;
	}

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public Integer getOperateType() {
		return operateType;
	}

	public void setOperateType(Integer operateType) {
		this.operateType = operateType;
	}

	public Date getOperateTime() {
		return operateTime;
	}

	public void setOperateTime(Date operateTime) {
		this.operateTime = operateTime;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

}
