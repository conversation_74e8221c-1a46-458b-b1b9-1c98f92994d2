package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FNTenantCloudMeterBuilder")
@Table(name = "t_fn_tenant_cloud_meter_builder", comment = "云端-建筑租户仪表编码生成", schema = Schema.EMS,indexes= {
		@Index(columns = "c_building_id",unique = true) })
public class FNTenantCloudMeterBuilder extends BusinessObject{

	private static final long serialVersionUID = 5792809620469006015L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;
	
	@Column(order = 2, name = "c_cloud_meter_id", length = 10, nullable = false, comment = "租户云端仪表编码")
	@JsonProperty("cloudMeterId")
	private Integer cloudMeterId;

	
	@Column(order = 3, name = "c_last_update_time", length = 0, nullable = false, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public Integer getCloudMeterId() {
		return cloudMeterId;
	}

	public void setCloudMeterId(Integer cloudMeterId) {
		this.cloudMeterId = cloudMeterId;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	

	
	
}
