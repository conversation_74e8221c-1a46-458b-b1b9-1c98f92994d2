package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnOrderExtend")
@Table(name = "t_fn_order_extend", comment = "账单扩展表", schema = Schema.EMS, indexes = {@Index(columns = { "c_order_id"}) })
public class FnOrderExtend extends BusinessObject {

	private static final long serialVersionUID = -4399737913510755380L;
	
	@Column(order = 1, name = "c_order_id", length =50, nullable = false, comment = "账单主键")
	@JsonProperty("orderId")
	private String orderId;
	
	@Column(order = 2, name = "c_tenant_id", length =50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_operate_type", length = 1, nullable = false, comment = "操作类型")
	@JsonProperty("operateType")
	private Integer operateType;
	
	@Column(order = 4, name = "c_data", length =18,scale = 8, nullable = false, comment = "操作量")
	@JsonProperty("data")
	private Double data;
	
	@Column(order = 5, name = "c_remain_data", length = 18,scale = 8, nullable = false, comment = "操作后剩余量")
	@JsonProperty("remainData")
	private Double remainData;
	
	@Column(order = 6, name = "c_charge_type", length =18, nullable = false, comment = "操作量类型（钱、量）")
	@JsonProperty("chargeType")
	private Integer chargeType;

	@Column(order = 7, name = "c_operate_time", length = 0, nullable = false, comment = "操作时间")
	@JsonProperty("operateTime")
	private Date operateTime;
	

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public Integer getOperateType() {
		return operateType;
	}

	public void setOperateType(Integer operateType) {
		this.operateType = operateType;
	}

	public Double getData() {
		return data;
	}

	public void setData(Double data) {
		this.data = data;
	}

	public Double getRemainData() {
		return remainData;
	}

	public void setRemainData(Double remainData) {
		this.remainData = remainData;
	}



	public Integer getChargeType() {
		return chargeType;
	}

	public void setChargeType(Integer chargeType) {
		this.chargeType = chargeType;
	}

	public Date getOperateTime() {
		return operateTime;
	}

	public void setOperateTime(Date operateTime) {
		this.operateTime = operateTime;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}
	
	
	
}
