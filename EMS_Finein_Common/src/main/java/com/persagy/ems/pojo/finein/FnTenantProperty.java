package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnTenantProperty")
@Table(name = "t_fn_tenant_property", comment = "租户属性", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_tenant_id" }) })
public class FnTenantProperty extends BaseId {


	private static final long serialVersionUID = 661244528073774955L;

	@Column(order = 2, name = "c_tenant_id", length = 20, nullable = false, comment = "租户编码")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_property_name", length = 50, nullable = false, comment = "租户属性编码")
	@JsonProperty("propertyName")
	private String propertyName;

	@Column(order = 4, name = "c_property_value", length = 2000, nullable = true, comment = "租户属性值")
	@JsonProperty("propertyValue")
	private String propertyValue;

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getPropertyName() {
		return propertyName;
	}

	public void setPropertyName(String propertyName) {
		this.propertyName = propertyName;
	}

	public String getPropertyValue() {
		return propertyValue;
	}

	public void setPropertyValue(String propertyValue) {
		this.propertyValue = propertyValue;
	}

}
