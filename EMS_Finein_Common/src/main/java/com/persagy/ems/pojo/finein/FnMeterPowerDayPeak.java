package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnMeterPowerDayPeak")
@Table(name = "t_fn_meter_power_day_peak", comment = "仪表功率日峰值", schema = Schema.EMS, transaction = false, indexes = {
		@Index(columns = { "c_meter_id", "c_count_time" }, unique = true) })
@Month(column = "c_time_from")
public class FnMeterPowerDayPeak extends BusinessObject {

	private static final long serialVersionUID = -8908112764308137193L;

	@Column(order = 1, name = "c_meter_id", length = 20, nullable = false, comment = "仪表id")
	@JsonProperty("meterId")
	private String meterId;

	@Column(order = 2, name = "c_data", length = 18, scale = 8, nullable = false, comment = "功率数据")
	@JsonProperty("data")
	private Double data;

	@Column(order = 3, name = "c_time_from", length = 0, nullable = false, comment = "数据时间")
	@JsonProperty("timeFrom")
	private Date timeFrom;

	@Column(order = 4, name = "c_count_time", length = 0, nullable = true, comment = "统计时间")
	@JsonProperty("countTime")
	private Date countTime;

	@Column(order = 5, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public Double getData() {
		return data;
	}

	public void setData(Double data) {
		this.data = data;
	}

	public Date getTimeFrom() {
		return timeFrom;
	}

	public void setTimeFrom(Date timeFrom) {
		this.timeFrom = timeFrom;
	}

	public Date getCountTime() {
		return countTime;
	}

	public void setCountTime(Date countTime) {
		this.countTime = countTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
}
