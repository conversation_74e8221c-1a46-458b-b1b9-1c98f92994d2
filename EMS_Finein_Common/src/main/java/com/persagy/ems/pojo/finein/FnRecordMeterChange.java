package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnRecordMeterChange")
@Table(name = "t_fn_record_meter_change", comment = "换表记录", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_id" }) })
public class FnRecordMeterChange extends BaseId {


	private static final long serialVersionUID = 7819615942340404505L;

	@Column(order = 2, name = "c_meter_id", length = 50, nullable = false, comment = "仪表编码")
	@JsonProperty("meterId")
	private String meterId;
	
	@Column(order = 3, name = "c_change_time", length = 0, nullable = false, comment = "换表时间")
	@JsonProperty("changeTime")
	private Date changeTime;
	
	@Column(order = 4, name = "c_create_time", length = 0, nullable = false, comment = "记录插入时间")
	@JsonProperty("createTime")
	private Date createTime;
	
	@Column(order = 5, name = "c_energy_type_id", length = 10, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;
	
	@Column(order = 6, name = "c_user_id", length = 50, nullable = true, comment = "操作人编码")
	@JsonProperty("userId")
	private String userId;
	
	@Column(order = 7, name = "c_user_name", length = 50, nullable = true, comment = "操作人姓名")
	@JsonProperty("userName")
	private String userName;
	
	

	public String getMeterId() {
		return meterId;
	}

	public void setMeterId(String meterId) {
		this.meterId = meterId;
	}

	public Date getChangeTime() {
		return changeTime;
	}

	public void setChangeTime(Date changeTime) {
		this.changeTime = changeTime;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}
	
	

}
