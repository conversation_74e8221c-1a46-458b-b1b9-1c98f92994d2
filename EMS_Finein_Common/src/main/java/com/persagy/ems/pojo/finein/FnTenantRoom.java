package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnTenantRoom")
@Table(name = "t_fn_tenant_room", comment = "租户房间占用", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_tenant_id" }), @Index(columns = { "c_building_id" }) })
public class FnTenantRoom extends BaseId {

	private static final long serialVersionUID = -399475643941653945L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑主键")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 3, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 4, name = "c_room_id", length = 50, nullable = false, comment = "房间主键")
	@JsonProperty("roomId")
	private String roomId;
	
	@Column(order = 5, name = "c_room_code", length = 100, nullable = false, comment = "房间编号")
	@JsonProperty("roomCode")
	private String roomCode;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getRoomId() {
		return roomId;
	}

	public void setRoomId(String roomId) {
		this.roomId = roomId;
	}

	public String getRoomCode() {
		return roomCode;
	}

	public void setRoomCode(String roomCode) {
		this.roomCode = roomCode;
	}

	

}
