package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnTenantMeterPowerStatRecord")
@Table(name = "t_fn_tenant_meter_power_stat_record", comment = "租户-仪表功率统计记录", schema = Schema.EMS,
		indexes = {
		@Index(columns = { "c_building_id", "c_tenant_id","c_energy_type_id"}, unique = true) })
public class FnTenantMeterPowerStatRecord extends BusinessObject {

	private static final long serialVersionUID = -3054437475313216320L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;
	
	@Column(order = 5, name = "c_energy_type_id", length = 20, nullable = false, comment = "能耗类型")
	@JsonProperty("energyTypeId")
	private String energyTypeId;
	
	@Column(order = 6, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;


	public String getBuildingId() {
		return buildingId;
	}


	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}


	public String getTenantId() {
		return tenantId;
	}


	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}


	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}


	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}


	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

}
