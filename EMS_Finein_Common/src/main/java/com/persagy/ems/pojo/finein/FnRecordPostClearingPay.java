package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnRecordPostClearingPay")
@Table(name = "t_fn_record_post_clearing_pay", comment = "后付费-结算记录", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_id" }) })
public class FnRecordPostClearingPay extends BaseId {

	
	private static final long serialVersionUID = 3678754432998894350L;

	
	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;
	
	@Column(order = 3, name = "c_building_name", length = 100, nullable = false, comment = "建筑名称")
	@JsonProperty("buildingName")
	private String buildingName;
	
	@Column(order = 4, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;
	
	@Column(order = 5, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;
	
	@Column(order = 6, name = "c_tenant_name", length = 100, nullable = false, comment = "租户名称")
	@JsonProperty("tenantName")
	private String tenantName;
	
	@Column(order = 7, name = "c_order_id", length = 50, nullable = false, comment = "账单编号")
	@JsonProperty("orderId")
	private String orderId;
	
	@Column(order = 8, name = "c_last_clearing_time", length = 0, nullable = true, comment = "上次结算时间")
	@JsonProperty("lastClearingTime")
	private Date lastClearingTime;
	
	@Column(order = 9, name = "c_current_clearing_time", length = 0, nullable = false, comment = "本次结算时间")
	@JsonProperty("currentClearingTime")
	private Date currentClearingTime;
	
	@Column(order = 10, name = "c_order_time", length = 50, nullable = false, comment = "账单时间")
	@JsonProperty("orderTime")
	private String orderTime;
	
	@Column(order = 11, name = "c_current_sum_energy", length = 18, scale = 8, nullable = false, comment = "本期累计能耗")
	@JsonProperty("currentSumEnergy")
	private Double currentSumEnergy;
	
	@Column(order = 12, name = "c_current_sum_energy_unit", length = 15, nullable = true, comment = "当前累计能耗单位")
	@JsonProperty("currentSumEnergyUnit")
	private String currentSumEnergyUnit;

	@Column(order = 13, name = "c_money", length = 18, scale = 8, nullable = false, comment = "充值金额")
	@JsonProperty("money")
	private Double money;
	
	@Column(order = 14, name = "c_pay_status", length = 1, scale = 0, nullable = false, comment = "缴费状态")
	@JsonProperty("payStatus")
	private Integer payStatus;

	@Column(order = 15, name = "c_user_id", length = 50, nullable = true, comment = "操作人编码")
	@JsonProperty("userId")
	private String userId;
	
	@Column(order = 16, name = "c_user_name", length = 50, nullable = true, comment = "操作人姓名")
	@JsonProperty("userName")
	private String userName;
	
	@Column(order = 17, name = "c_room_ids", length = 200, nullable = false, comment = "房间编码")
	@JsonProperty("roomIds")
	private String roomIds;
	
	@Column(order = 18, name = "c_create_time", length = 0, nullable = false, comment = "插入记录时间")
	@JsonProperty("createTime")
	private Date createTime;
	
	

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getBuildingName() {
		return buildingName;
	}

	public void setBuildingName(String buildingName) {
		this.buildingName = buildingName;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public String getTenantName() {
		return tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public Date getLastClearingTime() {
		return lastClearingTime;
	}

	public void setLastClearingTime(Date lastClearingTime) {
		this.lastClearingTime = lastClearingTime;
	}

	public Date getCurrentClearingTime() {
		return currentClearingTime;
	}

	public void setCurrentClearingTime(Date currentClearingTime) {
		this.currentClearingTime = currentClearingTime;
	}

	public String getOrderTime() {
		return orderTime;
	}

	public void setOrderTime(String orderTime) {
		this.orderTime = orderTime;
	}

	public Double getCurrentSumEnergy() {
		return currentSumEnergy;
	}

	public void setCurrentSumEnergy(Double currentSumEnergy) {
		this.currentSumEnergy = currentSumEnergy;
	}

	public String getCurrentSumEnergyUnit() {
		return currentSumEnergyUnit;
	}

	public void setCurrentSumEnergyUnit(String currentSumEnergyUnit) {
		this.currentSumEnergyUnit = currentSumEnergyUnit;
	}

	public Double getMoney() {
		return money;
	}

	public void setMoney(Double money) {
		this.money = money;
	}

	public Integer getPayStatus() {
		return payStatus;
	}

	public void setPayStatus(Integer payStatus) {
		this.payStatus = payStatus;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public String getRoomIds() {
		return roomIds;
	}

	public void setRoomIds(String roomIds) {
		this.roomIds = roomIds;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}
	
	
	

	

}
