package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;

@Dimension
@Entity(name = "FnOrderId")
@Table(name = "t_fn_order_id", comment = "账单号", schema = Schema.EMS, indexes = {@Index(columns = { "c_time_from","c_pay_type" },unique = true) })
public class FnOrderId extends BusinessObject {

	private static final long serialVersionUID = 1303895699871639846L;

	@Column(order = 2, name = "c_time_from", length = 0, nullable = false, comment = "时间")
	@JsonProperty("timeFrom")
	private Date timeFrom;
	
	@Column(order = 3, name = "c_pay_type", length = 1, nullable = false, comment = "付费类型")
	@JsonProperty("payType")
	private Integer payType;
	
	@Column(order = 4, name = "c_sequence", length = 11, nullable = false, comment = "序列号")
	@JsonProperty("sequence")
	private Integer sequence;
	
	
	public Date getTimeFrom() {
		return timeFrom;
	}

	public void setTimeFrom(Date timeFrom) {
		this.timeFrom = timeFrom;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Integer getSequence() {
		return sequence;
	}

	public void setSequence(Integer sequence) {
		this.sequence = sequence;
	}
	
}
