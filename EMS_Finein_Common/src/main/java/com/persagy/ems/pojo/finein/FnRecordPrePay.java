package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FnRecordPrePay")
@Table(name = "t_fn_record_pre_pay", comment = "预付费-充值记录", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_id" }) })
public class FnRecordPrePay extends BaseId {

	private static final long serialVersionUID = -1170804838477356136L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;
	
	@Column(order = 3, name = "c_building_name", length = 100, nullable = false, comment = "建筑名称")
	@JsonProperty("buildingName")
	private String buildingName;
	
	@Column(order = 4, name = "c_type", length = 1, nullable = false, comment = "充值类型")
	@JsonProperty("type")
	private Integer type;
	
	@Column(order = 5, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;
	
	@Column(order = 6, name = "c_tenant_name", length = 100, nullable = false, comment = "租户名称")
	@JsonProperty("tenantName")
	private String tenantName;
	
	@Column(order = 7, name = "c_code", length = 50, nullable = false, comment = "充值主体编码")
	@JsonProperty("code")
	private String code;
	
	@Column(order = 8, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;
	
	@Column(order = 9, name = "c_name", length = 100, nullable = false, comment = "充值主体名称")
	@JsonProperty("name")
	private String name;
	
	@Column(order = 10, name = "c_order_id", length = 50, nullable = false, comment = "账单编号")
	@JsonProperty("orderId")
	private String orderId;
	
	@Column(order = 11, name = "c_money", length = 18, scale = 8, nullable = true, comment = "充值金额")
	@JsonProperty("money")
	private Double money;
	
	@Column(order = 12, name = "c_amount", length = 18, scale = 8, nullable = true, comment = "充值量")
	@JsonProperty("amount")
	private Double amount;
	
	@Column(order = 13, name = "c_amount_unit", length = 15, nullable = true, comment = "充值单位")
	@JsonProperty("amountUnit")
	private String amountUnit;
	
	@Column(order = 14, name = "c_operate_time", length = 0, nullable = false, comment = "操作时间")
	@JsonProperty("operateTime")
	private Date operateTime;

	@Column(order = 15, name = "c_user_id", length = 50, nullable = true, comment = "操作人编码")
	@JsonProperty("userId")
	private String userId;
	
	@Column(order = 16, name = "c_user_name", length = 50, nullable = true, comment = "操作人姓名")
	@JsonProperty("userName")
	private String userName;
	
	@Column(order = 17, name = "c_create_time", length = 0, nullable = false, comment = "插入记录时间")
	@JsonProperty("createTime")
	private Date createTime;
	
	

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getBuildingName() {
		return buildingName;
	}

	public void setBuildingName(String buildingName) {
		this.buildingName = buildingName;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getTenantName() {
		return tenantName;
	}

	public void setTenantName(String tenantName) {
		this.tenantName = tenantName;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	public Double getMoney() {
		return money;
	}

	public void setMoney(Double money) {
		this.money = money;
	}

	public Double getAmount() {
		return amount;
	}

	public void setAmount(Double amount) {
		this.amount = amount;
	}

	public String getAmountUnit() {
		return amountUnit;
	}

	public void setAmountUnit(String amountUnit) {
		this.amountUnit = amountUnit;
	}

	public Date getOperateTime() {
		return operateTime;
	}

	public void setOperateTime(Date operateTime) {
		this.operateTime = operateTime;
	}

	public String getUserId() {
		return userId;
	}

	public void setUserId(String userId) {
		this.userId = userId;
	}

	public String getUserName() {
		return userName;
	}

	public void setUserName(String userName) {
		this.userName = userName;
	}

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}


}
