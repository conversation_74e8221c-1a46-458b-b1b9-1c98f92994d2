package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

import java.util.Date;


@Dimension
@Entity(name = "FnGridTemplate")
@Table(name = "t_fn_grid_template", comment = "报表模板", schema = Schema.EMS, 
	indexes = {


 			@Index(columns = { "c_name" }, unique = true) 

				
		 }
)
public class FnGridTemplate extends BusinessObject {

	private static final long serialVersionUID = 1;
	

	@Id
	@Column(order = 1, name = "c_id", length = 50, nullable = false, comment = "主键")
	@JsonProperty("id")
	private String id;


	@Column(order = 2, name = "c_name", length = 200, nullable = false, comment = "名称")
	@JsonProperty("name")
	private String name;


	@Column(order = 3, name = "c_type", length = 10, nullable = false, comment = "时间类型")
	@JsonProperty("type")
	private String type;


	@Column(order = 4, name = "c_is_system", length = 1, nullable = false, comment = "是否系统模板")
	@JsonProperty("isSystem")
	private Integer isSystem;


	@Column(order = 5, name = "c_is_include_build", length = 1, nullable = false, comment = "是否需要选建筑")
	@JsonProperty("isIncludeBuild")
	private Integer isIncludeBuild;


	@Column(order = 6, name = "c_include", length = 50, nullable = true, comment = "选择参数")
	@JsonProperty("include")
	private String include;


	@Column(order = 7, name = "c_resource_id", length = 50, nullable = false, comment = "资源ID")
	@JsonProperty("resourceId")
	private String resourceId;


	@Column(order = 8, name = "c_valid", length = 1, nullable = false, comment = "是否可用")
	@JsonProperty("valid")
	private Integer valid;


	@Column(order = 9, name = "c_create_user_id", length = 50, nullable = true, comment = "创建人")
	@JsonProperty("createUserId")
	private String createUserId;


	@Column(order = 10, name = "c_create_time", length = 50, nullable = true, comment = "创建时间")
	@JsonProperty("createTime")
	private Date createTime;


	@Column(order = 11, name = "c_last_update_user_id", length = 50, nullable = true, comment = "最后修改人")
	@JsonProperty("lastUpdateUserId")
	private String lastUpdateUserId;


	@Column(order = 12, name = "c_last_update_time", length = 50, nullable = true, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;


	public void setId(String id){
		this.id = id;
	}

	public String getId(){
		return this.id;
	}

	public void setName(String name){
		this.name = name;
	}

	public String getName(){
		return this.name;
	}

	public void setType(String type){
		this.type = type;
	}

	public String getType(){
		return this.type;
	}

	public void setIsSystem(Integer isSystem){
		this.isSystem = isSystem;
	}

	public Integer getIsSystem(){
		return this.isSystem;
	}

	public void setIsIncludeBuild(Integer isIncludeBuild){
		this.isIncludeBuild = isIncludeBuild;
	}

	public Integer getIsIncludeBuild(){
		return this.isIncludeBuild;
	}

	public void setInclude(String include){
		this.include = include;
	}

	public String getInclude(){
		return this.include;
	}

	public void setResourceId(String resourceId){
		this.resourceId = resourceId;
	}

	public String getResourceId(){
		return this.resourceId;
	}

	public void setValid(Integer valid){
		this.valid = valid;
	}

	public Integer getValid(){
		return this.valid;
	}

	public void setCreateUserId(String createUserId){
		this.createUserId = createUserId;
	}

	public String getCreateUserId(){
		return this.createUserId;
	}

	public void setCreateTime(Date createTime){
		this.createTime = createTime;
	}

	public Date getCreateTime(){
		return this.createTime;
	}

	public void setLastUpdateUserId(String lastUpdateUserId){
		this.lastUpdateUserId = lastUpdateUserId;
	}

	public String getLastUpdateUserId(){
		return this.lastUpdateUserId;
	}

	public void setLastUpdateTime(Date lastUpdateTime){
		this.lastUpdateTime = lastUpdateTime;
	}

	public Date getLastUpdateTime(){
		return this.lastUpdateTime;
	}

}