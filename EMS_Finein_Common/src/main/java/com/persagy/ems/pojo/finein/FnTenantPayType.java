package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnTenantPayType")
@Table(name = "t_fn_tenant_pay_type", comment = "租户-能耗-付费方式 ", schema = Schema.EMS, indexes = {})
public class FnTenantPayType extends BaseId {

	private static final long serialVersionUID = -962569485459496097L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;
	
	@Column(order = 3, name = "c_tenant_id", length = 20, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 4, name = "c_pay_type", length = 1, nullable = false, comment = "付费方式")
	@JsonProperty("payType")
	private Integer payType;
	
	@Column(order = 5, name = "c_pre_pay_type", length = 1, nullable = false, comment = "扣费类型")
	@JsonProperty("prePayType")
	private Integer prePayType;

	@Column(order = 6, name = "c_prepay_charge_type", length = 1, nullable = false, comment = "预付费充值类型")
	@JsonProperty("prepayChargeType")
	private Integer prepayChargeType;

	@Column(order = 7, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public Integer getPayType() {
		return payType;
	}

	public void setPayType(Integer payType) {
		this.payType = payType;
	}

	public Integer getPrePayType() {
		return prePayType;
	}

	public void setPrePayType(Integer prePayType) {
		this.prePayType = prePayType;
	}

	public Integer getPrepayChargeType() {
		return prepayChargeType;
	}

	public void setPrepayChargeType(Integer prepayChargeType) {
		this.prepayChargeType = prepayChargeType;
	}

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	
}
