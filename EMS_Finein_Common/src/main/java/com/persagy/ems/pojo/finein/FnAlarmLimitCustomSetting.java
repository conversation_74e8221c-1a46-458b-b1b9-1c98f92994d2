package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnAlarmLimitCustomSetting")
@Table(name = "t_fn_alarm_limit_custom_setting", comment = "报警自定义门限设置", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_tenant_id" }) })
public class FnAlarmLimitCustomSetting extends BaseId {


	private static final long serialVersionUID = -4087675513614154690L;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;


	@Column(order = 4, name = "c_is_follow_global", length = 1, nullable = false, comment = "是否跟随全局报警")
	@JsonProperty("isFollowGlobal")
	private Integer isFollowGlobal;


	public String getTenantId() {
		return tenantId;
	}


	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}


	public String getBuildingId() {
		return buildingId;
	}


	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}


	public Integer getIsFollowGlobal() {
		return isFollowGlobal;
	}


	public void setIsFollowGlobal(Integer isFollowGlobal) {
		this.isFollowGlobal = isFollowGlobal;
	}


	

}
