package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnFloor")
@Table(name = "t_fn_floor", comment = "楼层", schema = Schema.EMS, indexes = { @Index(columns = { "c_building_id" }) })
public class FnFloor extends BaseId {

	private static final long serialVersionUID = 8744135132078164184L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 3, name = "c_name", length = 50, nullable = false, comment = "楼层名称")
	@JsonProperty("name")
	private String name;

	@Column(order = 4, name = "c_order_by", length = 3, nullable = false, comment = "楼层排序")
	@JsonProperty("orderBy")
	private Integer orderBy;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getOrderBy() {
		return orderBy;
	}

	public void setOrderBy(Integer orderBy) {
		this.orderBy = orderBy;
	}

}
