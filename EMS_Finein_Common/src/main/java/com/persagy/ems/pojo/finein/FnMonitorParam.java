package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnMonitorParam")
@Table(name = "t_fn_monitor_param", comment = "监控参数", schema = Schema.EMS, indexes = {  })
public class FnMonitorParam extends BaseId {

	private static final long serialVersionUID = 190471228665587240L;

	@Column(order = 2, name = "c_energy_type_id", length = 10, nullable = false, comment = "能耗类型主键")
	@JsonProperty("energyTypeId")
	private String energyTypeId;

	@Column(order = 3, name = "c_name", length = 50, nullable = false, comment = "能耗类型名称")
	@JsonProperty("name")
	private String name;
	
	@Column(order = 4, name = "c_type", length = 1, nullable = false, comment = "参数类型")
	@JsonProperty("type")
	private Integer type;

	@Column(order = 5, name = "c_order_by", length = 2, nullable = false, comment = "排序")
	@JsonProperty("orderBy")
	private Integer orderBy;
	
	

	public String getEnergyTypeId() {
		return energyTypeId;
	}

	public void setEnergyTypeId(String energyTypeId) {
		this.energyTypeId = energyTypeId;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getOrderBy() {
		return orderBy;
	}

	public void setOrderBy(Integer orderBy) {
		this.orderBy = orderBy;
	}

	

	
}
