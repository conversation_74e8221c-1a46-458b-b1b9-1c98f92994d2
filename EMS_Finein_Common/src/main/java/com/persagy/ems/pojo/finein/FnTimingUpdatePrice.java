package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

/**
 * @Author: ls
 * @Date: 2022/2/9 13:35
 */
@Dimension
@Entity(name = "FnTimingUpdatePrice")
@Table(name = "t_fn_timing_update_price", comment = "定时修改价格", schema = SchemaConstant.Schema.EMS, indexes = {})
public class FnTimingUpdatePrice extends BaseId {

    private static final long serialVersionUID = 5692866389217003540L;

    @Column(order = 2, name = "c_price_template_id", length = 50, nullable = false, comment = "价格模板主键")
    @JsonProperty("priceTemplateId")
    private String priceTemplateId;

    @Column(order = 3, name = "c_name", length = 200, nullable = false, comment = "价格模板名称")
    @JsonProperty("name")
    private String name;

    @Column(order = 4, name = "c_energy_type_id", length = 8, nullable = false, comment = "能耗类型主键")
    @JsonProperty("energyTypeId")
    private String energyTypeId;

    @Column(order = 5, name = "c_is_valid", length = 1, nullable = false, comment = "是否可用")
    @JsonProperty("isValid")
    private Integer isValid;

    @Column(order = 6, name = "c_last_update_user_id", length = 50, nullable = true, comment = "最后修改人")
    @JsonProperty("lastUpdateUserId")
    private String lastUpdateUserId;

    @Column(order = 7, name = "c_last_update_time", length = 0, nullable = true, comment = "最后修改时间")
    @JsonProperty("lastUpdateTime")
    private Date lastUpdateTime;


    public String getPriceTemplateId() {
        return priceTemplateId;
    }

    public void setPriceTemplateId(String priceTemplateId) {
        this.priceTemplateId = priceTemplateId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getEnergyTypeId() {
        return energyTypeId;
    }

    public void setEnergyTypeId(String energyTypeId) {
        this.energyTypeId = energyTypeId;
    }

    public Integer getIsValid() {
        return isValid;
    }

    public void setIsValid(Integer isValid) {
        this.isValid = isValid;
    }

    public String getLastUpdateUserId() {
        return lastUpdateUserId;
    }

    public void setLastUpdateUserId(String lastUpdateUserId) {
        this.lastUpdateUserId = lastUpdateUserId;
    }

    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }
}
