package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnAlarmLimitCustomObj")
@Table(name = "t_fn_alarm_limit_custom_obj", comment = "报警自定义门限模板 ", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_tenant_id" }) })
public class FnAlarmLimitCustomObj extends BaseId {

	private static final long serialVersionUID = 5509193892943253394L;

	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;

	@Column(order = 3, name = "c_alarm_type_id", length = 50, nullable = false, comment = "报警类型主键")
	@JsonProperty("alarmTypeId")
	private String alarmTypeId;

	@Column(order = 4, name = "c_parent_alarm_type_id", length = 50, nullable = false, comment = "父级报警类型主键")
	@JsonProperty("parentAlarmTypeId")
	private String parentAlarmTypeId;

	@Column(order = 5, name = "c_tree_id", length = 100, nullable = false, comment = "报警类型树")
	@JsonProperty("treeId")
	private String treeId;

	@Column(order = 6, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 7, name = "c_limit_value", length = 18, scale = 8, nullable = true, comment = "报警门限值")
	@JsonProperty("limitValue")
	private Double limitValue;

	@Column(order = 8, name = "c_is_open", length = 1, nullable = false, comment = "是否打开")
	@JsonProperty("isOpen")
	private Integer isOpen;

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public String getAlarmTypeId() {
		return alarmTypeId;
	}

	public void setAlarmTypeId(String alarmTypeId) {
		this.alarmTypeId = alarmTypeId;
	}

	public String getParentAlarmTypeId() {
		return parentAlarmTypeId;
	}

	public void setParentAlarmTypeId(String parentAlarmTypeId) {
		this.parentAlarmTypeId = parentAlarmTypeId;
	}

	public String getTreeId() {
		return treeId;
	}

	public void setTreeId(String treeId) {
		this.treeId = treeId;
	}

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public Double getLimitValue() {
		return limitValue;
	}

	public void setLimitValue(Double limitValue) {
		this.limitValue = limitValue;
	}


	public Integer getIsOpen() {
		return isOpen;
	}

	public void setIsOpen(Integer isOpen) {
		this.isOpen = isOpen;
	}

}
