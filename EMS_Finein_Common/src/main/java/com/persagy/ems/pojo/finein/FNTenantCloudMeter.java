package com.persagy.ems.pojo.finein;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BusinessObject;

@Dimension
@Entity(name = "FNTenantCloudMeter")
@Table(name = "t_fn_tenant_cloud_meter", comment = "云端-租户仪表编码", schema = Schema.EMS,indexes = {
		@Index(columns = { "c_building_id" ,"c_tenant_id"},unique = true) })
public class FNTenantCloudMeter extends BusinessObject{
    
	private static final long serialVersionUID = -3397829564401491879L;

	@Column(order = 1, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;
	
	@Column(order = 2, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;
	
	@Column(order = 3, name = "c_cloud_meter_id", length = 1, nullable = false, comment = "租户云端仪表编码")
	@JsonProperty("cloudMeterId")
	private Integer cloudMeterId;

	

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public Integer getCloudMeterId() {
		return cloudMeterId;
	}

	public void setCloudMeterId(Integer cloudMeterId) {
		this.cloudMeterId = cloudMeterId;
	}

	
}
