package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FNTenantCloudTypeDic")
@Table(name = "t_fn_tenant_cloud_type_dic", comment = "云端-租户租赁业态", schema = Schema.EMS,transaction=false)
public class FNTenantCloudTypeDic extends BaseId {


	private static final long serialVersionUID = -1002284054611874861L;

	@Column(order = 2, name = "c_name", length = 50, nullable = false, comment = "租户类型名称")
	@JsonProperty("name")
	private String name;
	
	@Column(order = 3, name = "c_cloud_tenant_type_id", length = 50, nullable = false, comment = "云端租户类型主键")
	@JsonProperty("cloudTenantTypeId")
	private Integer cloudTenantTypeId;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getCloudTenantTypeId() {
		return cloudTenantTypeId;
	}

	public void setCloudTenantTypeId(Integer cloudTenantTypeId) {
		this.cloudTenantTypeId = cloudTenantTypeId;
	}
}
