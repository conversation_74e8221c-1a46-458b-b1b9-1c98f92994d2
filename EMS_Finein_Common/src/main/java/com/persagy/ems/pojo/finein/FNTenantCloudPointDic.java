package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.Column;
import com.persagy.core.annotation.Dimension;
import com.persagy.core.annotation.Entity;
import com.persagy.core.annotation.Table;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FNTenantCloudPointDic")
@Table(name = "t_fn_tenant_cloud_point_dic", comment = "云端-租户信息点类型", schema = Schema.EMS,transaction=false)
public class FNTenantCloudPointDic extends BaseId {

	
	private static final long serialVersionUID = 6811221419545303127L;

	@Column(order = 2, name = "c_name", length = 50, nullable = false, comment = "租户信息点名称")
	@JsonProperty("name")
	private String name;
	
	@Column(order = 3, name = "c_type", length = 1, nullable = false, comment = "租户信息点类型")
	@JsonProperty("type")
	private Integer type;
	
	@Column(order = 4, name = "c_function_id", length = 11, nullable = true, comment = "租户信息点本地功能号")
	@JsonProperty("functionId")
	private Integer functionId;
	
	@Column(order = 5, name = "c_remark", length = 50, nullable = true, comment = "备注")
	@JsonProperty("remark")
	private String remark;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public Integer getType() {
		return type;
	}

	public void setType(Integer type) {
		this.type = type;
	}

	public Integer getFunctionId() {
		return functionId;
	}

	public void setFunctionId(Integer functionId) {
		this.functionId = functionId;
	}

	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	
	
	
}
