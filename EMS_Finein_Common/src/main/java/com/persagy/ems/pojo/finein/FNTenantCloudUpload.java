package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

import java.util.Date;

@Dimension
@Entity(name = "FNTenantCloudUpload")
@Table(name = "t_fn_tenant_cloud_upload", comment = "云端-租户数据上传时间", schema = Schema.EMS,indexes = {
		@Index(columns = { "c_building_id" ,"c_tenant_id","c_upload_type"},unique = true) })
public class FNTenantCloudUpload extends BaseId {

	private static final long serialVersionUID = -4288331597943310613L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;
	
	@Column(order = 3, name = "c_tenant_id", length = 50, nullable = false, comment = "租户主键")
	@JsonProperty("tenantId")
	private String tenantId;
	
	@Column(order = 4, name = "c_upload_type", length = 1, nullable = false, comment = "上传类型")//0静态.1动态
	@JsonProperty("uploadType")
	private Integer uploadType;

	@Column(order = 5, name = "c_tenant_cloud_id", length = 50, nullable = false, comment = "租户云端主键")
	@JsonProperty("tenantCloudId")
	private String tenantCloudId;

	@Column(order = 6, name = "c_last_upload_time", length = 0, nullable = false, comment = "最后上传时间")
	@JsonProperty("lastUploadTime")
	private Date lastUploadTime;
	
	@Column(order = 7, name = "c_last_update_time", length = 0, nullable = false, comment = "最后修改时间")
	@JsonProperty("lastUpdateTime")
	private Date lastUpdateTime;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getTenantId() {
		return tenantId;
	}

	public void setTenantId(String tenantId) {
		this.tenantId = tenantId;
	}

	public Integer getUploadType() {
		return uploadType;
	}

	public void setUploadType(Integer uploadType) {
		this.uploadType = uploadType;
	}

	public String getTenantCloudId() {
		return tenantCloudId;
	}

	public void setTenantCloudId(String tenantCloudId) {
		this.tenantCloudId = tenantCloudId;
	}

	public Date getLastUploadTime() {
		return lastUploadTime;
	}

	public void setLastUploadTime(Date lastUploadTime) {
		this.lastUploadTime = lastUploadTime;
	}

	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	
	
}
