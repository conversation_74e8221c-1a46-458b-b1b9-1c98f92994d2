package com.persagy.ems.pojo.finein;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.persagy.core.annotation.*;
import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.mvc.pojo.BaseId;

@Dimension
@Entity(name = "FnBuildingProperty")
@Table(name = "t_fn_building_property", comment = "建筑属性", schema = Schema.EMS, indexes = {
		@Index(columns = { "c_building_id" }) })
public class FnBuildingProperty extends BaseId {

	private static final long serialVersionUID = 8409489919376581252L;

	@Column(order = 2, name = "c_building_id", length = 20, nullable = false, comment = "建筑编码")
	@JsonProperty("buildingId")
	private String buildingId;

	@Column(order = 3, name = "c_property_name", length = 50, nullable = false, comment = "建筑属性编码")
	@JsonProperty("propertyName")
	private String propertyName;

	@Column(order = 4, name = "c_property_value", length = 2000, nullable = true, comment = "建筑属性值")
	@JsonProperty("propertyValue")
	private String propertyValue;

	public String getBuildingId() {
		return buildingId;
	}

	public void setBuildingId(String buildingId) {
		this.buildingId = buildingId;
	}

	public String getPropertyName() {
		return propertyName;
	}

	public void setPropertyName(String propertyName) {
		this.propertyName = propertyName;
	}

	public String getPropertyValue() {
		return propertyValue;
	}

	public void setPropertyValue(String propertyValue) {
		this.propertyValue = propertyValue;
	}

}
