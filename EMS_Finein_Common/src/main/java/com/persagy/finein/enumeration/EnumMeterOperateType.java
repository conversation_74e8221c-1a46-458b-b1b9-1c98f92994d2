package com.persagy.finein.enumeration;

/**
 * 
 * 说明:仪表操作类型
 */

public enum EnumMeterOperateType {
	
	
	REMAINCLEAR("剩余清零", 0),
	PAULELE("保电", 1),
	UNPAULELE("解除保电", 2),
	GATE_HE("合闸", 3),
	GATE_FEN("分闸", 4),
	OVERDRAFT("透支金额",5),
	UPDATEPRICE("更新价格",6),
	PAY("充值",7);
	
	private String view;
	private Integer value;

	private EnumMeterOperateType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumMeterOperateType valueOf(Integer value){
		if(value.intValue() == 0){
			return REMAINCLEAR;
		}else if(value.intValue() == 1){
			return PAULELE;
		}else if(value.intValue() == 2){
			return UNPAULELE;
		}else if(value.intValue() == 3){
			return GATE_HE;
		}else if(value.intValue() == 4){
			return GATE_FEN;
		}else if(value.intValue() == 5){
				return GATE_FEN;
		}else if(value.intValue() == 6){
			return UPDATEPRICE;
		}else if(value.intValue() == 7){
			return PAY;
		}else{
			return null;
		}
	}
}
