package com.persagy.finein.enumeration;

/**
 * 
 * 说明:仪表设置返回状态
 */

public enum EnumMeterSetStaus {
	
	
	Sucess("设置成功", 0), Fail("设置失败", 1),Other("不支持此功能",2);
	
	private String view;
	private Integer value;

	private EnumMeterSetStaus(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
}
