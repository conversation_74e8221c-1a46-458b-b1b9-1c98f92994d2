package com.persagy.finein.enumeration;

/**
 
 * 
 * 说明:云客户端付费类型
 */

public enum EnumTenantCloudPayType {
	
	
	OFFLINE_METERPAY("预付费-表充表扣", 1), ONLINE_METERPAY("预付费-软充表扣", 2),ONLINE_TENANTPAY("预付费-软充软扣",3),POSTPAY("后付费",4);
	
	private String view;
	private Integer value;

	private EnumTenantCloudPayType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumTenantCloudPayType getEnumTenantCloudPayType(Integer value){
		switch (value) {
		case 0:
			return EnumTenantCloudPayType.OFFLINE_METERPAY;
		case 1:
			return EnumTenantCloudPayType.ONLINE_METERPAY;
		case 2:
			return EnumTenantCloudPayType.ONLINE_TENANTPAY;
		case 3:
			return EnumTenantCloudPayType.POSTPAY;
		default:
			return null;
		}
	}
}
