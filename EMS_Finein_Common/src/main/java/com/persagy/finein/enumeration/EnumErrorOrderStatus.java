package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:数据类型
 */

public enum EnumErrorOrderStatus {

	NoProcess("未处理", 0), PrePayAgain("再次充值", 1), Close("关闭", 2);

	private String view;
	private Integer value;

	private EnumErrorOrderStatus(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
}
