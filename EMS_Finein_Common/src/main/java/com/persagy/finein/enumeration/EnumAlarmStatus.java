package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:报警状态
 */

public enum EnumAlarmStatus {

	<PERSON><PERSON><PERSON><PERSON><PERSON>("未恢复", 0), <PERSON><PERSON><PERSON><PERSON><PERSON>("已恢复", 1), <PERSON><PERSON><PERSON><PERSON><PERSON>("已过期", 2);

	private String view;
	private Integer value;

	private EnumAlarmStatus(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}

}
