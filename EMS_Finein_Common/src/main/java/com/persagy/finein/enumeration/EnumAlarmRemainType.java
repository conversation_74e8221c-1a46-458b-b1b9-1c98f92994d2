package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:报警剩余类型
 */

public enum EnumAlarmRemainType {
	
	REMAIN_Day("剩余天数报警类型", 0), REMAIN_Data("剩余量报警类型", 1),REMAIN_JinE("剩余金额报警类型", 2);
	
	private String view;
	private Integer value;

	private EnumAlarmRemainType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
}
