package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:配置模板上传 结果码
 */

public enum EnumConfigUploadResult {
	
	
	Code_0("成功", 0),
	Code_1("type 不存在", 1),
	Code_2("logicCode 不存在", 2),
	Code_3("异常", 3)
	;
	
	private String message;
	private Integer value;

	private EnumConfigUploadResult(String message, Integer value) {
		this.message = message;
		this.value = value;
	}

	public String getMessage() {
		return message;
	}

	public Integer getValue() {
		return value;
	}
	
	
}
