package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:通讯类型
 */

public enum EnumCommunicationType {
	
	
	Serial("串口", 0), Collector("采集器", 1);
	
	private String view;
	private Integer value;

	private EnumCommunicationType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumCommunicationType valueOf(Integer value){
		if(value == null){
			return null;
		}
		if(value.intValue() == 0){
			return Serial;
		}else if(value.intValue() == 1){
			return Collector;
		}else{
			return null;
		}
	}
}
