package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:租户状态
 */

public enum EnumTenantConfigType {
	
	
	CommonConfig("通用配置", 0), BaseConfig("基础配置", 1);
	
	private String view;
	private Integer value;

	private EnumTenantConfigType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	
}
