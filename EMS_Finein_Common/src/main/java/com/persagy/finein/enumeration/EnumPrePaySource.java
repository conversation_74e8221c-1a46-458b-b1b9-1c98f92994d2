package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:付费类型
 */

public enum EnumPrePaySource {
	
	
	LOCAL("本地", 0), WX("微信",1),Center("中心版", 2);
	
	private String view;
	private Integer value;

	private EnumPrePaySource(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumPrePaySource valueOf(Integer value){
		if(value.intValue() == 0){
			return LOCAL;
		}else if(value.intValue() == 1){
			return WX;
		}else if(value.intValue() == 2){
			return Center;
		}
		return null;
	}
}
