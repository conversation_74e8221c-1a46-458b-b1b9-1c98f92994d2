package com.persagy.finein.enumeration;

/**
 * @Author: ls
 * @Date: 2022/2/11 16:49
 */
public enum EnumResult {
    SUCCESS("成功", 0), FAIL("失败", 1);

    private String view;
    private Integer value;

    private EnumResult(String view, Integer value) {
        this.view = view;
        this.value = value;
    }

    public String getView() {
        return view;
    }

    public Integer getValue() {
        return value;
    }

    public static EnumResult valueOf(Integer value){
        if(value.intValue() == 0){
            return SUCCESS;
        }else if(value.intValue() == 1){
            return FAIL;
        }else{
            return null;
        }
    }
}
