package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:统计数据类型
 */

public enum EnumStatType {
	
	
	Real("整点值", 1), <PERSON>("最大值", 2), <PERSON>("最小值", 3), Avg("平均值", 4);
	
	private String view;
	private Integer value;

	private EnumStatType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumStatType valueOf(Integer type){
		if(type.intValue() == 1){
			return Real;
		}else if(type.intValue() == 2){
			return Max;
		}else if(type.intValue() == 3){
			return Min;
		}else if(type.intValue() == 4){
			return Avg;
		}else{
			return null;
		}
	}
}
