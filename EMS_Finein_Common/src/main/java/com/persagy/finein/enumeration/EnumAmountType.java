package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:结算类型
 */

public enum EnumAmountType {
	
	
	None("不限", -1), <PERSON><PERSON><PERSON><PERSON>("欠费", 0),<PERSON><PERSON><PERSON><PERSON><PERSON>("未结算", 1);
	
	private String view;
	private Integer value;

	private EnumAmountType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumAmountType valueOf(Integer value){
		if(value == null){
			return null;
		}
		if(value.intValue() == -1){
			return None;
		}else if(value.intValue() == 0){
			return Qian<PERSON>ei;
		}else if(value.intValue() == 1){
			return WeiJieSun;
		}else{
			return null;
		}
	}
}
