package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:结算类型限值
 */

public enum EnumAmountTypeLimit {
	
	
	None("不限", 0),More("多张账单欠费", 1), One("一张欠费账单", 2),Over_One_Month("一个月以上未结算", 3), Over_Three_Month("三个月以上未结算", 4), Over_Six_Month("半年以上未结算", 5), Less_One_Month("一个月以下未结算", 6);
	
	private String view;
	private Integer value;

	private EnumAmountTypeLimit(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumAmountTypeLimit valueOf(Integer value){
		if(value == null){
			return null;
		}
		if(value.intValue() == 0){
			return None;
		}else if(value.intValue() == 1){
			return More;
		}else if(value.intValue() == 2){
			return One;
		}else if(value.intValue() == 3){
			return Over_One_Month;
		}else if(value.intValue() == 4){
			return Over_Three_Month;
		}else if(value.intValue() == 5){
			return Over_Six_Month;
		}else if(value.intValue() == 6){
			return Less_One_Month;
		}else{
			return null;
		}
	}
}
