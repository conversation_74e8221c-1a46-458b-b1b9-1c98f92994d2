package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:租户操作类型
 */

public enum EnumTenantOperateType {
	
	
	ADD("添加", 0), ACTIVATE("激活", 1),LEAVE("已退", 2);
	
	private String view;
	private Integer value;

	private EnumTenantOperateType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	
}
