package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:原始数据类型
 */

public enum EnumOriginalDataType {
	
	
	MonthData("低频量", "monthdata",0), ElectriccurrentData("高频量", "electriccurrentdata",1);
	
	private String view;
	private String value;
	private Integer dataType;


	private EnumOriginalDataType(String view, String value,Integer dataType) {
		this.view = view;
		this.value = value;
		this.dataType = dataType;
	}

	public String getView() {
		return view;
	}

	public String getValue() {
		return value;
	}

	public Integer getDataType() {
		return dataType;
	}
}
