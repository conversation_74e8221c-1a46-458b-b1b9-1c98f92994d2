package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:价格详情
 */

public enum EnumPriceDetail {
	
	
	J("尖", "J"), F("峰", "F"), G("谷", "G"), P("平", "P"), L("平均", "L");
	
	private String view;
	private String value;

	private EnumPriceDetail(String view, String value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public String getValue() {
		return value;
	}
	
	public static EnumPriceDetail parse(String value){
		if(value == null){
			return null;
		}
		
		switch (value) {
		case "J":
			return J;
		case "F":
			return F;
		case "G":
			return G;
		case "P":
			return P;
		case "L":
			return L;
		default:
			return null;
		}
	}
}
