package com.persagy.finein.enumeration;

/**
 *
 * 说明:云客户端信息点类型
 */

public enum EnumTenantCloudPointType {
	
	
	STATIC("静态", 0), DYNAMIC("动态", 1);
	
	private String view;
	private Integer value;

	private EnumTenantCloudPointType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
}
