package com.persagy.finein.enumeration;

/**
 * 
 * 说明:云客户端租赁业态
 */

public enum EnumTenantCloudType {

	
	Can<PERSON>in("餐饮","100"), FuZ<PERSON>ng("服装","200"),XiangBaoPiJv("箱包皮具","300"),<PERSON><PERSON><PERSON>u<PERSON>ao<PERSON>ian<PERSON>in("化妆品及个护保健品","400"),ShouBiaoShiPin("手表饰品","500"),<PERSON>g<PERSON>uoZong<PERSON>e("生活综合","600"),<PERSON><PERSON>ongMeiTi("美容美体","700"),<PERSON><PERSON>hi("超市","800"),<PERSON><PERSON><PERSON>("影院","900"),
	<PERSON><PERSON><PERSON>("百货","A00"),	<PERSON><PERSON><PERSON>("办公","B00"),<PERSON><PERSON><PERSON>("其他","000");
	
	private String view;
	private String value;

	private EnumTenantCloudType(String view, String value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public String getValue() {
		return value;
	}
	
	public static EnumTenantCloudType getEnumTenantCloudType(String value){
		switch (value) {
		case "1":
			return EnumTenantCloudType.CanYin;
		case "2":
			return EnumTenantCloudType.FuZhuang;
		case "3":
			return EnumTenantCloudType.XiangBaoPiJv;
		case "4":
			return EnumTenantCloudType.GeHuBaoJianPin;
		case "5":
			return EnumTenantCloudType.ShouBiaoShiPin;
		case "6":
			return EnumTenantCloudType.ShengHuoZongHe;
		case "7":
			return EnumTenantCloudType.MeiRongMeiTi;
		case "8":
			return EnumTenantCloudType.ChaoShi;
		case "9":
			return EnumTenantCloudType.YingYuan;
		case "10":
			return EnumTenantCloudType.BaiHuo;
		case "11":
			return EnumTenantCloudType.BanGong;
		default:
			return null;
		}
	}

}
