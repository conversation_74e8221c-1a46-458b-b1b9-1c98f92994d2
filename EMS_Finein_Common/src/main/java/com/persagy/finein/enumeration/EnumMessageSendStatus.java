package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * update  2019年10月31日 xsg 添加黑名单
 * 说明:消息发送状态
 */

public enum EnumMessageSendStatus {
	
	
	WAIT_SEND("待发送", 0), COMPLETE_SEND("已发送", 1),ERROR("发送异常", 2),OVERDUE("过期", 3),
	PHONE_BLACK("黑名单",4);
	
	private String view;
	private Integer value;

	private EnumMessageSendStatus(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumMessageSendStatus valueOf(Integer value){
		if(value.intValue() == 0){
			return WAIT_SEND;
		}else if(value.intValue() == 1){
			return COMPLETE_SEND;
		}else if(value.intValue() == 2){
			return ERROR;
		}else if(value.intValue() == 3){
			return OVERDUE;
		}else if(value.intValue() == 4) {
			return PHONE_BLACK;
		}else {
			return null;
		}
	}
}
