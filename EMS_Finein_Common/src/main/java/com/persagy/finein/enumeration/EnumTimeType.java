package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:数据类型
 */

public enum EnumTimeType {
	
	
	T0("10分钟、15分钟、30分钟", 0), T1("1小时", 1), T2("1天", 2), T3("1周", 3), T4("1月", 4), T5("1年", 5);
	
	private String view;
	private Integer value;

	private EnumTimeType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumTimeType valueOf(Integer type){
		if(type.intValue() == 0){
			return T0;
		}else if(type.intValue() == 1){
			return T1;
		}else if(type.intValue() == 2){
			return T2;
		}else if(type.intValue() == 3){
			return T3;
		}else if(type.intValue() == 4){
			return T4;
		}else if(type.intValue() == 5){
			return T5;
		}else{
			return null;
		}
	}
}
