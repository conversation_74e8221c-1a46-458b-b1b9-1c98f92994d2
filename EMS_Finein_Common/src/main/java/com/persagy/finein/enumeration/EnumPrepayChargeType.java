package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:付费模式
 */

public enum EnumPrepayChargeType {
	
	
	<PERSON>("量", 0), <PERSON><PERSON>("钱", 1), <PERSON>("无效", 2);
	
	private String view;
	private Integer value;

	private EnumPrepayChargeType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumPrepayChargeType valueOf(Integer value){
		if(value == null){
			return null;
		}
		if(value.intValue() == 0){
			return Liang;
		}else if(value.intValue() == 1){
			return Qian;
		}else{
			return None;
		}
	}
}
