package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:价格类型
 */

public enum EnumMonitorParamType {

	Common("通用", 0), PrePay("预付费", 1), PostPay("后付费", 2);

	private String view;
	private Integer value;

	private EnumMonitorParamType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}

}
