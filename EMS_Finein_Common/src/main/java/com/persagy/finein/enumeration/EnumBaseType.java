package com.persagy.finein.enumeration;

/**
 * 作者:<PERSON><PERSON><PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:时间类型
 */

public enum EnumBaseType {
	
	
	Integer("Integer", 0),<PERSON>("<PERSON>", 1), <PERSON>("Double", 2), <PERSON><PERSON><PERSON>("Bo<PERSON>an", 3), <PERSON>("String", 4), <PERSON><PERSON><PERSON><PERSON><PERSON>("JSONArray", 5), JSONObject("JSONObject", 6);
	
	private String view;
	private Integer value;

	private EnumBaseType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	
}
