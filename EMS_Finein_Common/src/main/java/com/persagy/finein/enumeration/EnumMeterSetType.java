package com.persagy.finein.enumeration;

/**
 * 
 * 说明:仪表操作类型
 */

public enum EnumMeterSetType {
	
	
	REMAINCLEAR("剩余清零", 0),
	PAULELE("保电", 1),
	GATE("分合闸", 2),
	OVERDRAFT("透支金额",3),
	UPDATEPRICE("更新价格",4),
	PAY("充值",5),
	RETURN("退费",6);
	
	private String view;
	private Integer value;

	private EnumMeterSetType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumMeterSetType valueOf(Integer value){
		if(value.intValue() == 0){
			return REMAINCLEAR;
		}else if(value.intValue() == 1){
			return PAULELE;
		}else if(value.intValue() == 2){
			return GATE;
		}else if(value.intValue() == 3){
			return OVERDRAFT;
		}else if(value.intValue() == 4){
			return UPDATEPRICE;
		}else if(value.intValue() == 5){
				return PAY;
		}else if(value.intValue() == 6){
				return RETURN;
		}else{
			return null;
		}
	}
	
}
