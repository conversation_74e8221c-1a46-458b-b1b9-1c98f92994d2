package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:闸状态
 */

public enum EnumGateStatus {

	<PERSON>("分闸", 1), <PERSON>("合闸", 0);

	private String view;
	private Integer value;

	private EnumGateStatus(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}

}
