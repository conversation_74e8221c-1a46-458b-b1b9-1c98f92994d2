package com.persagy.finein.enumeration;

/**
 * 作者:z<PERSON><PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:报警状态
 */

public enum EnumAlarmPushStatus {
	
	WaitPush("未推送", 0), StatusChange("检查状态变更", 1), Completed("同步完毕", 2);

	private String view;
	private Integer value;

	private EnumAlarmPushStatus(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}

}
