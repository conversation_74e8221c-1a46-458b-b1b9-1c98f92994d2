package com.persagy.finein.enumeration;

/**

 * 说明:北电仪表远程控制类型
 */

public enum EnumBeiDianSetType {
	
	
	Gate_Fen("1A", 1), <PERSON>_He("1B", 2), <PERSON><PERSON><PERSON>("3A", 3), <PERSON><PERSON><PERSON><PERSON><PERSON>("3B", 4);
	
	private String view;
	private Integer value;

	private EnumBeiDianSetType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumBeiDianSetType valueOf(Integer type){
		if(type.intValue() == 1){
			return Gate_Fen;
		}else if(type.intValue() == 2){
			return Gate_He;
		}else if(type.intValue() == 3){
			return PaulEle;
		}else if(type.intValue() == 4){
			return UnPaulEle;
		}else{
			return null;
		}
	}
}
