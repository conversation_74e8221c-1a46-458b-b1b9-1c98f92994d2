package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:是否缴费
 */

public enum EnumPayStatus {
	
	
	NO("未缴费", 0), YES("已缴费", 1);
	
	private String view;
	private Integer value;

	private EnumPayStatus(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
}
