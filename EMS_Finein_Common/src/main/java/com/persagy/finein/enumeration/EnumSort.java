package com.persagy.finein.enumeration;

import com.persagy.core.enumeration.EMSOrder;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:排序
 */

public enum EnumSort {
	
	
	Asc(EMSOrder.Asc, 1), Desc(EMSOrder.Desc, -1);
	
	private EMSOrder order;
	private Integer value;
	

	private EnumSort(EMSOrder order, Integer value) {
		this.order = order;
		this.value = value;
	}

	public EMSOrder getOrder() {
		return order;
	}

	public void setOrder(EMSOrder order) {
		this.order = order;
	}

	public Integer getValue() {
		return value;
	}

	public void setValue(Integer value) {
		this.value = value;
	}


	public static EnumSort valueOf(int order){
		if(order == 1){
			return Asc;
		}else{
			return Desc;
		}
	}
	
	
	
}
