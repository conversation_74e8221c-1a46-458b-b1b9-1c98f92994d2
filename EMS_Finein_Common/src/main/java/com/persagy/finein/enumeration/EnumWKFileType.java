package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:文件类型
 */

public enum EnumWKFileType {

	Pdf("Pdf", 0), Img("Img", 1);

	private String view;
	private Integer value;

	private EnumWKFileType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}

}
