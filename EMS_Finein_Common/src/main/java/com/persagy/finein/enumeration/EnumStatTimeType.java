package com.persagy.finein.enumeration;

/**
 * 作者:z<PERSON><PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:数据类型
 */

public enum EnumStatTimeType {
	
	
	Minute_5("5分钟", 1), Minute_15("15分钟", 2), Hour_1("1小时", 3), Day_1("1天", 4), Week_1("1周", 5), Month_1("1月", 6), Year_1("1年", 7), History("历史", 8);
	
	private String view;
	private Integer value;

	private EnumStatTimeType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
}
