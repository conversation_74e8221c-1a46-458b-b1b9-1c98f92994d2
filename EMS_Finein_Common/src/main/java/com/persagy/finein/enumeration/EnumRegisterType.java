package com.persagy.finein.enumeration;

/**

 * 说明:软件注册类型
 */

public enum EnumRegisterType {
	
	
	Dog("加密狗", "dog"), Soft("软件认证", "soft"), None("无加密", "none");
	
	private String view;
	private String value;

	private EnumRegisterType(String view, String value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public String getValue() {
		return value;
	}
	
	public static EnumRegisterType getType(String value){
		if(value == null){
			return None;
		}
		if(Soft.getValue().equals(value)){
			return Soft;
		}else if(Dog.getValue().equals(value)){
			return Dog;
		}else{
			return None;
		}
	}
}
