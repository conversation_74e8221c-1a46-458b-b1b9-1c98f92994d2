package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:仪表类型
 */

public enum EnumMeterType {
	
	
	Common("普通仪表", 0), Multiple("多费率仪表", 1);
	
	private String view;
	private Integer value;

	private EnumMeterType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	
}
