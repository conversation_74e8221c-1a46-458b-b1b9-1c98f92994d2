package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:是否有效状态
 */

public enum EnumValidStatus {
	
	
	INVALID("无效", 0), VALID("有效", 1);
	
	private String view;
	private Integer value;

	private EnumValidStatus(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	
}
