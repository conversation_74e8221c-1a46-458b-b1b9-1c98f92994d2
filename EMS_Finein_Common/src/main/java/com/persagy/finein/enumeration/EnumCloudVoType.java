package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:付费类型
 */

public enum EnumCloudVoType {
	
	
	Tenant("租户虚拟实体", "VOTn");
	
	private String view;
	private String value;

	private EnumCloudVoType(String view, String value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public String getValue() {
		return value;
	}
	
	
}
