package com.persagy.finein.enumeration;

/**
 * 说明:远程充值状态
 */

public enum EnumPrePayStatus {
	
	
	NO_PROCESSE("未处理", 0), PROCESSE("已处理", 1),FAIL("处理失败", 2),UPLOAD("已上传",3);
	
	private String view;
	private Integer value;

	private EnumPrePayStatus(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
}
