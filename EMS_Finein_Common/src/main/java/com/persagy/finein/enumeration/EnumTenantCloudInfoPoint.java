package com.persagy.finein.enumeration;

/**

 * 说明:云客户端信息点信息
 */

public enum EnumTenantCloudInfoPoint {
		

	TenantID("租户ID", "TenantID"), 
	TenantName("租户名称", "TenantName"),
	TenantLocalID("租户本地编码", "TenantLocalID"),
	TenantLocalName("租户本地名称", "TenantLocalName"),
	RoomID("房间编号", "RoomID"),
	ActiveTime("激活时间", "ActiveTime"),
	SurrenderTime("退租时间", "SurrenderTime"),
	TenantType("租赁业态类型", "TenantType"),
	TenantContactor("联系人", "TenantContactor"),
	TenantPhone("联系电话", "TenantPhone"),
	isElecSys("是否使用电系统", "isElecSys"),
	ElecPayType("电付费类型", "ElecPayType"),
	isElecTimePrice("电是否分时", "isElecTimePrice"),
	ElecPriceScheme("电价格方案", "ElecPriceScheme"),
	isWaterSys("是否使用水系统", "isWaterSys"),
	WaterPayType("水付费类型","WaterPayType"),
	WaterPriceScheme("水价格方案","WaterPriceScheme"),
	isHotWaterSys("是否使用热水系统","isHotWaterSys"),
	HotWaterPayType("热水付费类型","HotWaterPayType"),
	HotWaterPriceScheme("热水价格方案","HotWaterPriceScheme"),
	isGasSys("是否使用燃气系统","isGasSys"),
	GasPayType("燃气付费类型","GasPayType"),
	GasPriceScheme("燃气价格方案","GasPriceScheme"),
	
	
	ElecAccConsum("耗电量","ElecAccConsum"),
	ElecAccCost("用电金额","ElecAccCost"),
	ElecSurplusConsum("剩余电量","ElecSurplusConsum"),
	ElecSurplusCost("剩余电金额","ElecSurplusCost"),
	WaterAccConsum("耗水量","WaterAccConsum"),
	WaterAccCost("用水金额","WaterAccCost"),
	WaterSurplusConsum("剩余水量","WaterSurplusConsum"),
	WaterSurplusCost("剩余水金额","WaterSurplusCost"),
	HotWaterAccConsum("耗热水量","HotWaterAccConsum"),
	HotWaterAccCost("用热水金额","HotWaterAccCost"),
	HotWaterSurplusConsum("剩余热水量","HotWaterSurplusConsum"),
	HotWaterSurplusCost("剩余热水金额","HotWaterSurplusCost"),
	GasAccConsum("耗燃气量","GasAccConsum"),
	GasAccCost("用燃气金额","GasAccCost"),
	GasSurplusConsum("剩余燃气量","GasSurplusConsum"),
	GasSurplusCost("剩余燃气金额","GasSurplusCost");
	
	
	private String view;
	private String value;

	private EnumTenantCloudInfoPoint(String view, String value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public String getValue() {
		return value;
	}
	
}
