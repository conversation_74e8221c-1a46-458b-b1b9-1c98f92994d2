package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:租户状态
 */

public enum EnumTenantStatus {

	NOT_ACTIVE("未激活", 0), ACTIVATED("已激活", 1), RETURNED_LEASE("已退租", 2);

	private String view;
	private Integer value;

	private EnumTenantStatus(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}

	public static EnumTenantStatus valueOf(Integer type) {
		if (type.intValue() == 0) {
			return NOT_ACTIVE;
		} else if (type.intValue() == 1) {
			return ACTIVATED;
		} else if (type.intValue() == 2) {
			return RETURNED_LEASE;
		} else {
			return null;
		}
	}

}
