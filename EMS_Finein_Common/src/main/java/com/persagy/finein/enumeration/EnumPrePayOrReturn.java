package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:付费类型
 */

public enum EnumPrePayOrReturn {
	
	
	PREPAY("充值", 0), RETURN("退费", 1);
	
	private String view;
	private Integer value;

	private EnumPrePayOrReturn(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumPrePayOrReturn valueOf(Integer value){
		if(value.intValue() == 0){
			return PREPAY;
		}else if(value.intValue() == 1){
			return RETURN;
		}else{
			return null;
		}
	}
}
