package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:报警门限比较
 */

public enum EnumAlarmLimitCompareType {
	
	
	GTE("大于等于:>=", 1), LTE("小于等于:<=", -1), GTE_AND_LTE("区间:>= min and <= max", 0), GTE_OR_LTE("区间外:>= max and <= min", 2);
	
	private String view;
	private Integer value;

	private EnumAlarmLimitCompareType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	
}
