package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:是否存在状态
 */

public enum EnumExsistStatus {
	
	
	NOT_EXSIT("不存在", 0), Exsist("存在", 1);
	
	private String view;
	private Integer value;

	private EnumExsistStatus(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	
}
