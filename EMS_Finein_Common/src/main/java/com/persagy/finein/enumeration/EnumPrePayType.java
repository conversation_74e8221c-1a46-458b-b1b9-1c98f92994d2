package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:预付费充值类型
 */

public enum EnumPrePayType {
	
	
	OFFLINE_METERPAY("预付费-表充表扣", 0), ONLINE_METERPAY("预付费-软充表扣", 1), ONLINE_TENANTPAY("预付费-软充软扣", 2), None("无效", 3);
	
	private String view;
	private Integer value;

	private EnumPrePayType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumPrePayType valueOf(Integer value){
		if(value.intValue() == 0){
			return OFFLINE_METERPAY;
		}else if(value.intValue() == 1){
			return ONLINE_METERPAY;
		}else if(value.intValue() == 2){
			return ONLINE_TENANTPAY;
		}else{
			return null;
		}
	}
}
