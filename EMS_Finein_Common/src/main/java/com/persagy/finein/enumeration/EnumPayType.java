package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:付费类型
 */

public enum EnumPayType {
	
	
	PREPAY("预付费", 0), POSTPAY("后付费", 1);
	
	private String view;
	private Integer value;

	private EnumPayType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumPayType valueOf(Integer value){
		if(value.intValue() == 0){
			return PREPAY;
		}else if(value.intValue() == 1){
			return POSTPAY;
		}else{
			return null;
		}
	}
}
