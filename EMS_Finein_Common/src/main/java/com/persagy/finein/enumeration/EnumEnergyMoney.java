package com.persagy.finein.enumeration;

/**
 * 作者:zhang<PERSON>(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:能耗-费用
 */

public enum EnumEnergyMoney {
	
	
	Energy("能耗", 0), Money("费用", 1);
	
	private String view;
	private Integer value;

	private EnumEnergyMoney(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumEnergyMoney valueOf(Integer type){
		if(type.intValue() == 0){
			return Energy;
		}else if(type.intValue() == 1){
			return Money;
		}else{
			return null;
		}
	}
}
