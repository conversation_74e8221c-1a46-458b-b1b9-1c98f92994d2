package com.persagy.finein.enumeration;

import com.persagy.ems.finein.common.constant.FineinConstant;

import java.io.File;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:配置类型
 */

public enum EnumConfigType {
	

	Base_Room_Config(FineinConstant.BackConfigType.Base_Room_Config,"房间配置","房间",File.separator+"template"+File.separator+"room_config.xlsx"),
	Base_Floor_Config(FineinConstant.BackConfigType.Base_Floor_Config,"楼层配置","楼层",File.separator+"template"+File.separator+"floor_config.xlsx"),
	Base_Tenant_Config(FineinConstant.BackConfigType.Base_Tenant_Config,"批量上传租户","租户",File.separator+"template"+File.separator+"batch_upload_tenant.xlsx"),
	

	Common_Price_Config(FineinConstant.BackConfigType.Common_Price_Config,"价格模板配置","价格模板",File.separator+"template"+File.separator+"price_config.xlsx"),
	Meter_Dian_Config(FineinConstant.BackConfigType.Meter_Dian_Config,"电表配置","电表",File.separator+"template"+File.separator+"dian_meter_config.xlsx"),
	Meter_Shui_Config(FineinConstant.BackConfigType.Meter_Shui_Config,"水表配置","水表",File.separator+"template"+File.separator+"shui_meter_config.xlsx"),
	Meter_ReShui_Config(FineinConstant.BackConfigType.Meter_ReShui_Config,"热水表配置","热水",File.separator+"template"+File.separator+"reshui_meter_config.xlsx"),
	Meter_RanQi_Config(FineinConstant.BackConfigType.Meter_RanQi_Config,"燃气表配置","燃气表",File.separator+"template"+File.separator+"ranqi_meter_config.xlsx");
	
	private String key;
	private String value;
	private String downloadName;
	private String templatePath;

	private EnumConfigType(String key, String value,String downloadName,String templatePath) {
		this.key = key;
		this.value = value;
		this.downloadName = downloadName;
		this.templatePath = templatePath;
	}

	public String getKey() {
		return key;
	}

	public String getValue() {
		return value;
	}
	
	public String getTemplatePath() {
		return templatePath;
	}

	public String getDownloadName() {
		return downloadName;
	}
	
	public static EnumConfigType getConfigType(String logicCode){
		switch (logicCode) {
		case FineinConstant.BackConfigType.Base_Room_Config:
			return Base_Room_Config;
		case FineinConstant.BackConfigType.Base_Floor_Config:
			return Base_Floor_Config;
		case FineinConstant.BackConfigType.Base_Tenant_Config:
			return Base_Tenant_Config;
		case FineinConstant.BackConfigType.Common_Price_Config:
			return Common_Price_Config;
		case FineinConstant.BackConfigType.Meter_Dian_Config:
			return Meter_Dian_Config;
		case FineinConstant.BackConfigType.Meter_Shui_Config:
			return Meter_Shui_Config;
		case FineinConstant.BackConfigType.Meter_ReShui_Config:
			return Meter_ReShui_Config;
		case FineinConstant.BackConfigType.Meter_RanQi_Config:
			return Meter_RanQi_Config;
		default:
			break;
		}
		return null;
	}
}
