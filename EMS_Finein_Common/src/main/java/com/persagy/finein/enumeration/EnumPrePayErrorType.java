package com.persagy.finein.enumeration;

/**
 * 说明:远程充值错误信息
 */

public enum EnumPrePayErrorType {
	
	
	QUERRY_ERROR("查询错误","1001"), PAY_ERROR("充值错误","1002"),OTHER_ERROR("其他","1003");
	
	private String view;
	private String value;

	private EnumPrePayErrorType(String view, String value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public String getValue() {
		return value;
	}
}
