package com.persagy.finein.enumeration;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月13日 上午11:06:19
 * 
 * 说明:价格类型
 */

public enum EnumPriceType {
	
	
	AVG("平均", 0), TOU("分时", 1);
	
	private String view;
	private Integer value;

	private EnumPriceType(String view, Integer value) {
		this.view = view;
		this.value = value;
	}

	public String getView() {
		return view;
	}

	public Integer getValue() {
		return value;
	}
	
	public static EnumPriceType valueOf(Integer value){
		if(value == null){
			return null;
		}
		if(value.intValue() == 0){
			return AVG;
		}else if(value.intValue() == 1){
			return TOU;
		}else{
			return null;
		}
	}
}
