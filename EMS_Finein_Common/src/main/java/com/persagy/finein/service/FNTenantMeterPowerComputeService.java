package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantMeterPowerCompute;
import com.persagy.finein.enumeration.EnumStatType;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantMeterPowerComputeService extends FNBaseService {
	
	public FnTenantMeterPowerCompute query(String buildingId, String tenantId, String meterId, EnumStatType statType) throws Exception;

	public void save(String buildingId, String tenantId, String meterId, EnumStatType statType, Date lastComputeTime) throws Exception;

	public void update(List<FnTenantMeterPowerCompute> powerSaveList) throws Exception;

}
