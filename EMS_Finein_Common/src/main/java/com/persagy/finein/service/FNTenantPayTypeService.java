package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantPayType;

import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantPayTypeService extends FNBaseService {
	
	public FnTenantPayType query(String tenantId, String energyTypeId) throws Exception;
	
	public Map<String,FnTenantPayType> queryPayTypeMap(String tenantId) throws Exception;
	
	//租户,能耗类型，付费方式
	public Map<String,Map<String,FnTenantPayType>> queryByBuildingId(String buildingId) throws Exception;
	
	public Map<String,String> queryEnergyTypeByBuildingId(String buildingId) throws Exception;
	
	public List<String> queryEnergyTypePayTypeList() throws Exception;

	public List<String> queryChargeTypeList() throws Exception;

	/**
	 * Description: 通过能耗类型查询建筑下租户付费方式,key为租户id
	 * @param buildingId
	 * @param string
	 * @return Map<String,FnTenantPayType>
	 * <AUTHOR>
	 * @throws Exception
	 * @since 2019年10月11日: 上午9:58:34
	 * Update By 邵泓博 2019年10月11日: 上午9:58:34
	 */
	public Map<String, FnTenantPayType> queryTenantAndPayType(String buildingId, String energyType) throws Exception;

}
