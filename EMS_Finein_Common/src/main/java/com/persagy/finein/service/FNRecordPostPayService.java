package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnRecordPostPay;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNRecordPostPayService extends FNBaseService {
	public List<FnRecordPostPay> queryRecord(String buildingId, List<String> tenantIdList, String energyTypeId, Date timeFrom, Date timeTo) throws Exception;

	public List<FnRecordPostPay> queryRecord(List<String> tenantIdList, String energyTypeId, Date timeFrom, Date timeTo) throws Exception;
	
}
