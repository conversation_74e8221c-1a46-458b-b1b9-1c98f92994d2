package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantPostPayParam;
import com.persagy.finein.service.FNTenantPostPayParamService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantPostPayParamService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantPostPayParamServiceImpl extends FNBaseServiceImpl  implements FNTenantPostPayParamService{

	@Override
	public Map<String, FnTenantPostPayParam> queryTenantPostParam(String tenantId) throws Exception {
		Map<String, FnTenantPostPayParam> result = new HashMap<String, FnTenantPostPayParam>();
		FnTenantPostPayParam query = new FnTenantPostPayParam();
		query.setTenantId(tenantId);
		
		List<FnTenantPostPayParam> list = this.query(query);
		if(list != null){
			for(FnTenantPostPayParam param : list){
				result.put(param.getEnergyTypeId(), param);
			}
		}
		return result;
	}

	@Override
	public void removeTenantParam(String tenantId) throws Exception {
		FnTenantPostPayParam query = new FnTenantPostPayParam();
		query.setTenantId(tenantId);
		this.remove(query);
	}

	@Override
	public Map<String, FnTenantPostPayParam> queryTenantPostParamByBuilding(String buildingId, String energyTypeId)
			throws Exception {
		Map<String, FnTenantPostPayParam> result = new HashMap<String, FnTenantPostPayParam>();
		FnTenantPostPayParam query = new FnTenantPostPayParam();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyTypeId);
		
		List<FnTenantPostPayParam> list = this.query(query);
		if(list != null){
			for(FnTenantPostPayParam param : list){
				result.put(param.getTenantId(), param);
			}
		}
		return result;
	}
}
