package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTimingUpdatePrice;
import com.persagy.finein.service.FNTimingUpdatePriceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: ls
 * @Date: 2022/2/9 14:51
 */
@Service("FNTimingUpdatePriceService")
@Transactional(propagation= Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTimingUpdatePriceServiceImpl extends FNBaseServiceImpl  implements FNTimingUpdatePriceService {

}
