package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantMeterFunctionData;
import com.persagy.finein.enumeration.EnumStatTimeType;
import com.persagy.finein.enumeration.EnumStatType;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantMeterFunctionDataService extends FNBaseService {
	
	public void saveData(String buildingId, String tenantId, String meterId, EnumStatTimeType timeType, EnumStatType statType, int functionId, Date timeFrom, Double data) throws Exception;

	public List<FnTenantMeterFunctionData> queryDataGteLt(String buildingId, String tenantId, String meterId, int funcId, EnumStatTimeType timeType, EnumStatType statType, Date timeFrom, Date timeTo) throws Exception;

	public FnTenantMeterFunctionData queryLast(String buildingId, String tenantId, String meterId, int funcId, EnumStatTimeType timeType, EnumStatType statType, Date timeFrom, Date timeTo) throws Exception;
	
}
