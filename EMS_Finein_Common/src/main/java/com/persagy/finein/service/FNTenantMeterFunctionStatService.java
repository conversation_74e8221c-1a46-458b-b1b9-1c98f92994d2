package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantMeterFunctionStat;
import com.persagy.finein.enumeration.EnumStatTimeType;
import com.persagy.finein.enumeration.EnumStatType;

import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantMeterFunctionStatService extends FNBaseService {
	public void saveData(String buildingId, String tenantId, String meterId, EnumStatTimeType timeType, EnumStatType statType, int functionId, Date timeFrom, Double data) throws Exception;

	public FnTenantMeterFunctionStat queryHistoryMax(String buildingId, String tenantId, String meterId, int functionId) throws Exception;
}
