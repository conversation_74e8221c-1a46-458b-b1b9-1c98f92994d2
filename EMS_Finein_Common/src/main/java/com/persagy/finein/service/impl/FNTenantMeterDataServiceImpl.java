package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnTenantMeterData;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.service.FNTenantMeterDataService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantMeterDataService")
@Transactional(propagation=Propagation.NOT_SUPPORTED)
public class FNTenantMeterDataServiceImpl extends FNBaseServiceImpl  implements FNTenantMeterDataService{

	@Override
	public void saveData(String buildingId, String tenantId, String meterId, EnumTimeType timeType,
			int functionId,String energyTypeId,  Date timeFrom, EnumEnergyMoney energyMoney, Double data) throws Exception {
		FnTenantMeterData query = new FnTenantMeterData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setEnergyTypeId(energyTypeId);
		query.setTimeType(timeType.getValue());
		query.setFunctionId(functionId);
		query.setTimeFrom(timeFrom);
		query.setDataType(energyMoney.getValue());
		this.remove(query);
		if(data != null){
			query.setData(data);
			query.setLastUpdateTime(new Date());
			this.save(query);
		}
	}

	@Override
	public List<FnTenantMeterData> queryListGteLt(String buildingId, String tenantId, String meterId, int functionId,
			String energyTypeId, EnumTimeType timeType, Date timeFrom, Date timeTo, EnumEnergyMoney energyMoney)
			throws Exception {
		FnTenantMeterData query = new FnTenantMeterData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setFunctionId(functionId);
		query.setEnergyTypeId(energyTypeId);
		query.setTimeType(timeType.getValue());
		query.setDataType(energyMoney.getValue());
		
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("timeFrom", SpecialOperator.$lt, timeTo);
		query.setSort("timeFrom", EMSOrder.Asc);
		return this.query(query);
	}

	@Override
	public FnTenantMeterData queryHistoryMaxData(String buildingId, String tenantId, String meterId, int functionId,
			Date from, Date to, String energyTypeId, EnumTimeType enumTimeType, EnumEnergyMoney energyMoney)
			throws Exception {
		FnTenantMeterData query = new FnTenantMeterData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setFunctionId(functionId);
		query.setTimeType(enumTimeType.getValue().intValue());
		query.setEnergyTypeId(energyTypeId);
		query.setDataType(energyMoney.getValue().intValue());
		if(from!=null){
			query.setSpecialOperation("timeFrom", SpecialOperator.$gte, from);
		}
		query.setSpecialOperation("timeFrom", SpecialOperator.$lt, to);
		query.setSort("data", EMSOrder.Desc);
		query.setLimit(1L);
		List<FnTenantMeterData> dataList = this.query(query);
		if(dataList.size() > 0){
			return dataList.get(0);
		}
		return null;
	}

	@Override
	public void removeData(String buildingId, String tenantId, String meterId, int functionId, Date timeFrom)
			throws Exception {
		FnTenantMeterData query = new FnTenantMeterData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setFunctionId(functionId);
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		this.remove(query);
	}

	@Override
	public FnTenantMeterData queryLastGteLt(String buildingId, String tenantId, String meterId, int functionId,
			String energyTypeId, EnumTimeType timeType, Date timeFrom, Date timeTo, EnumEnergyMoney energyMoney)
			throws Exception {
		FnTenantMeterData query = new FnTenantMeterData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setFunctionId(functionId);
		query.setEnergyTypeId(energyTypeId);
		query.setTimeType(timeType.getValue());
		query.setDataType(energyMoney.getValue());
		if(timeFrom!=null){
			query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		}
		query.setSpecialOperation("timeFrom", SpecialOperator.$lt, timeTo);
		query.setSort("timeFrom", EMSOrder.Desc);
		query.setLimit(1L);
		List<FnTenantMeterData> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0);
		}
		return null;
	}

}
