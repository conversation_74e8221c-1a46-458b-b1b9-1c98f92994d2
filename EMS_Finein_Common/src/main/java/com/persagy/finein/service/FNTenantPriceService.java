package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnPriceTemplate;
import com.persagy.ems.pojo.finein.FnTenantPrice;

import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantPriceService extends FNBaseService {
	
	public FnPriceTemplate query(String tenantId, String energyTypeId) throws Exception;

	public Map<String,String> queryPriceTemplateIdMap(String tenantId) throws Exception;

	/**
	 *
	 * @param tenantIdList
	 * @return <TenantId,PriceTemplateId,PriceTemplateName>
	 * @throws Exception
	 */
	public Map<String,Map<String,Object>> queryPriceMapByTenantIds(String energyTypeId, List<String> tenantIdList) throws Exception;
	
	public Map<String,FnPriceTemplate> queryPriceTemplateMap(String tenantId) throws Exception;
	
	public Map<String, Map<String, FnTenantPrice>> queryMap() throws Exception;

}
