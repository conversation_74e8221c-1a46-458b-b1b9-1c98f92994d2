package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantFunctionCompute;
import com.persagy.finein.enumeration.EnumStatTimeType;
import com.persagy.finein.enumeration.EnumStatType;
import com.persagy.finein.service.FNTenantFunctionComputeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantFunctionComputeService")
@Transactional(propagation=Propagation.NOT_SUPPORTED)
public class FNTenantFunctionComputeServiceImpl extends FNBaseServiceImpl  implements FNTenantFunctionComputeService{

	@Override
	public FnTenantFunctionCompute query(String buildingId, String tenantId, int functionId, EnumStatType statType,
			EnumStatTimeType statTimeType) throws Exception {
		FnTenantFunctionCompute query = new FnTenantFunctionCompute();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setFunctionId(functionId);
		query.setTimeType(statTimeType.getValue());
		query.setDataType(statType.getValue());
		return (FnTenantFunctionCompute)this.queryObject(query);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void save(String buildingId, String tenantId, int functionId, EnumStatType statType,
			EnumStatTimeType statTimeType, Date lastComputeTime) throws Exception {
		FnTenantFunctionCompute query = new FnTenantFunctionCompute();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setFunctionId(functionId);
		query.setTimeType(statTimeType.getValue());
		query.setDataType(statType.getValue());
		this.remove(query);
		query.setLastComputeTime(lastComputeTime);
		query.setLastUpdateTime(new Date());
		this.save(query);
	}
}
