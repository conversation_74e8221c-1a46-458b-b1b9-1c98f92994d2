package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.FnSendData;
import com.persagy.finein.service.FNSendDataService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNSendDataService")
public class FNSendDataServiceImpl extends FNBaseServiceImpl implements FNSendDataService{

	@Override
	public List<FnSendData> queryList(String buildingId, long limit) throws Exception {
		FnSendData query = new FnSendData();
		query.setBuildingId(buildingId);
		query.setSort("receiveTime", EMSOrder.Asc);
		query.setLimit(limit);
		return this.query(query);
	}

	@Override
	public void removeById(String id) throws Exception {
		FnSendData query = new FnSendData();
		query.setId(id);
		this.remove(query);
	}

	@Override
	public void saveData(String buildingId, String meterId, Integer functionId, Integer dataType, Date receiveTime,
			Double data) throws Exception {
		FnSendData saveObj = new FnSendData();
		saveObj.setId(UUID.randomUUID().toString());
		saveObj.setBuildingId(buildingId);
		saveObj.setMeterId(meterId);
		saveObj.setFunctionId(functionId);
		saveObj.setDataType(dataType);
		saveObj.setReceiveTime(receiveTime);
		saveObj.setData(data);
		this.save(saveObj);
	}

	
}
