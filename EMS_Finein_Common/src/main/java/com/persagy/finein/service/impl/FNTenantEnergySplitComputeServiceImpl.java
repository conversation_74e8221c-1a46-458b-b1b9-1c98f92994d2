package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantEnergySplitCompute;
import com.persagy.finein.service.FNTenantEnergySplitComputeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantEnergySplitComputeService")
public class FNTenantEnergySplitComputeServiceImpl extends FNBaseServiceImpl  implements FNTenantEnergySplitComputeService{

	@Override
	public Date queryLastComputeTime(String buildingId,String tenantId,String energyTypeId) throws Exception {
		FnTenantEnergySplitCompute query = new FnTenantEnergySplitCompute();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		FnTenantEnergySplitCompute result = (FnTenantEnergySplitCompute)this.queryObject(query);
		if(result != null){
			return result.getLastComputeTime();
		}
		return null;
	}

	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void saveComputeTime(String buildingId,String tenantId,String energyTypeId, Date lastComputeTime)
			throws Exception {
		FnTenantEnergySplitCompute saveObj = new FnTenantEnergySplitCompute();
		saveObj.setBuildingId(buildingId);
		saveObj.setTenantId(tenantId);
		saveObj.setEnergyTypeId(energyTypeId);
		this.remove(saveObj);
		saveObj.setLastComputeTime(lastComputeTime);
		saveObj.setLastUpdateTime(new Date());
		this.save(saveObj);
	}
}
