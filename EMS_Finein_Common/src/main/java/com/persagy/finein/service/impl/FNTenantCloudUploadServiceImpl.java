package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.FNTenantCloudUpload;
import com.persagy.finein.enumeration.EnumTenantCloudPointType;
import com.persagy.finein.service.FNTenantCloudUploadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantCloudUploadService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantCloudUploadServiceImpl extends FNBaseServiceImpl  implements FNTenantCloudUploadService{

	@Override
	public Date queryLastUpdateTime(String buildingId,EnumTenantCloudPointType CloudPointType) throws Exception {
		FNTenantCloudUpload query = new FNTenantCloudUpload();
		query.setBuildingId(buildingId);
		query.setUploadType(CloudPointType.getValue());
		query.setLimit((long) 1);
		query.setSort("lastUpdateTime",EMSOrder.Desc);
		List<FNTenantCloudUpload> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0).getLastUpdateTime();
		}
		return null;
	}

	@Override
	public List<FNTenantCloudUpload> queryByUploadType(String buildingId,String tenantId,EnumTenantCloudPointType CloudPointType) throws Exception {
		FNTenantCloudUpload query = new FNTenantCloudUpload();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setUploadType(CloudPointType.getValue());
		List<FNTenantCloudUpload> list = this.query(query);
		return list;
	}

}
