package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.FnOrderExtend;
import com.persagy.finein.service.FNOrderExtendService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 说明:
 */

@Service("FNOrderExtendService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNOrderExtendServiceImpl extends FNBaseServiceImpl implements FNOrderExtendService {

	@Override
	public FnOrderExtend queryById(String orderId) throws Exception {
		FnOrderExtend save = new FnOrderExtend();
		save.setOrderId(orderId);
		List<FnOrderExtend> list = this.query(save);
		if(list!=null&&list.size()>0){
			return list.get(0);
		}
		return null;
	}

	@Override
	public FnOrderExtend queryLastOrderExtend(FnOrderExtend query) throws Exception {
		query.setSort("operateTime", EMSOrder.Desc);
		List<FnOrderExtend> list = this.query(query);
		if(list!=null&&list.size()!=0){
			return list.get(0);
		}
		return null;
	}

}
