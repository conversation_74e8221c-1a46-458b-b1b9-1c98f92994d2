package com.persagy.finein.service;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantData;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumTimeType;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:能耗-费用
*/

public interface FNTenantDataService extends FNBaseService {
	
	public List<FnTenantData> queryListGteLt(String buildingId, String tenantId, EnumTimeType timeType, String energyTypeId, Date timeFrom, Date timeTo, EnumEnergyMoney energyMoney) throws Exception;

	public List<FnTenantData> queryListGte(String buildingId, String tenantId, EnumTimeType timeType, String energyTypeId, Date timeFrom, EnumEnergyMoney energyMoney) throws Exception;

	public Double queryDataGteLt(String buildingId, String tenantId, EnumTimeType timeType, String energyTypeId, Date timeFrom, Date timeTo, EnumEnergyMoney energyMoney) throws Exception;

	public Double queryDataGte(String buildingId, String tenantId, EnumTimeType timeType, String energyTypeId, Date timeFrom, EnumEnergyMoney energyMoney) throws Exception;

	public void saveData(String buildingId, String tenantId, EnumTimeType timeType, String energyTypeId, Date timeFrom, EnumEnergyMoney energyMoney, Double data) throws Exception ;

	public Map<String,Map<String,Double>> queryTenantData(String buildingId, String energyTypeId, EnumTimeType timeType, Date timeFrom) throws Exception;

	public Date queryTimefromOrder(Date from, Date to, EnumTimeType enumTimeType, FnTenant tenant, Building building, EnumEnergyMoney energyMoney, String energyTypeId, EMSOrder eMSOrder) throws Exception;

	public List<FnTenantData> queryListGteLte(String buildingId, String tenantId, EnumTimeType timeType, String energyTypeId, Date from, Date to,
                                              EnumEnergyMoney energyMoney) throws Exception;

	public FnTenantData queryHistoryMaxData(String buildingId, String tenantId, Date from, Date to, String energyTypeId, EnumTimeType enumTimeType, EnumEnergyMoney energyMoney) throws Exception ;

	public void removeData(String buildingId, String tenantId, String energyTypeId, Date timeFrom) throws Exception ;

	public Double queryData(String buildingId, String tenantId, EnumTimeType timeType, String energyTypeId, Date date,
                            EnumEnergyMoney energyMoney) throws Exception;

	/**
	 * 根据楼层或业态 查询耗电量/费用/电功率
	 * @param buildingId 建筑id
	 * @param tenantId 租户id
	 * @param timeType 时间类型
	 * @param energyTypeId 能源类型
	 * @param timeFrom 开始时间
	 * @param timeTo 结束时间
	 * @param energyMoney 能耗/费用
	 * @return
	 * @throws Exception
	 */
	public List<FnTenantData> queryDataList(String buildingId,List<String> tenantId, EnumTimeType timeType, String energyTypeId, Date timeFrom, Date timeTo, EnumEnergyMoney energyMoney) throws Exception;
}
