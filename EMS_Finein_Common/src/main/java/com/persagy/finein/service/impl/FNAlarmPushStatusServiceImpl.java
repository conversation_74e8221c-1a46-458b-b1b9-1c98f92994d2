package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnAlarmPushStatus;
import com.persagy.finein.enumeration.EnumAlarmPushStatus;
import com.persagy.finein.service.FNAlarmPushStatusService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNAlarmPushStatusService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNAlarmPushStatusServiceImpl extends FNBaseServiceImpl implements FNAlarmPushStatusService {

	@Override
	public void updateAlarmPushStatus(String alarmId, EnumAlarmPushStatus alarmPushStatus) throws Exception {
		FnAlarmPushStatus query = new FnAlarmPushStatus();
		query.setId(alarmId);
		FnAlarmPushStatus update = new FnAlarmPushStatus();
		update.setPushStatus(alarmPushStatus.getValue());
		update.setLastUpdateTime(new Date());
		this.update(query, update);
	}

}
