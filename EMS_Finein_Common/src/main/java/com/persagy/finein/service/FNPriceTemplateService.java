package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnPriceTemplate;

import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNPriceTemplateService extends FNBaseService {
	
	public List<FnPriceTemplate> queryList(String energyTypeId) throws Exception;
	
	public FnPriceTemplate query(String id) throws Exception;
	
	public List<FnPriceTemplate> queryList() throws Exception;

	public Map<String, Map<String, FnPriceTemplate>> queryMapByTenant() throws Exception;

	public Map<String, FnPriceTemplate> queryMap() throws Exception;

	FnPriceTemplate queryByContentOne(String price, String energyTypeId) throws Exception;

	int queryCountOnline(String id)throws Exception;
}
