package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantMeterRemainDays;
import com.persagy.finein.enumeration.EnumPayBodyType;

import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantMeterRemainDaysService extends FNBaseService {
	
	public Map<String,FnTenantMeterRemainDays> queryTenantRemainDays(String buildingId, String energyTypeId) throws Exception;

	public void saveData(String buildingId, String tenantId, String energyTypeId, EnumPayBodyType payBodyType, String bodyId, Integer maxDays, Integer minDays, Double remainData) throws Exception;

	public FnTenantMeterRemainDays queryRemainDays(String buildingId, String tenantId, EnumPayBodyType payBodyType, String bodyId, String energyTypeId) throws Exception;

	public Map<String,FnTenantMeterRemainDays> queryTenantRemainDays(String buildingId, String energyTypeId, EnumPayBodyType payBodyType) throws Exception;

	public List<FnTenantMeterRemainDays> queryList(String buildingId, String tenantId, EnumPayBodyType payBodyType, String bodyId, String energyTypeId) throws Exception;
	
}
