package com.persagy.finein.service.impl;

import com.persagy.finein.service.FNTimingUpdatePriceRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: ls
 * @Date: 2022/2/10 11:14
 */
@Service("FNTimingUpdatePriceRecordService")
@Transactional(propagation= Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTimingUpdatePriceRecordServiceImpl extends FNBaseServiceImpl implements FNTimingUpdatePriceRecordService {
}
