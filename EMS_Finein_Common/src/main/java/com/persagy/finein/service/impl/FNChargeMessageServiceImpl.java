package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.UnitUtil;
import com.persagy.ems.pojo.finein.FnChargeMessage;
import com.persagy.ems.pojo.finein.FnEnergyType;
import com.persagy.ems.pojo.finein.FnRecordPrePay;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.finein.enumeration.EnumMessageSendStatus;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.service.FNChargeMessageService;
import com.persagy.finein.service.FNEnergyTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNChargeMessageService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNChargeMessageServiceImpl extends FNBaseServiceImpl implements FNChargeMessageService {

	@Resource(name = "FNEnergyTypeService")
	public FNEnergyTypeService FNEnergyTypeService;

	@Override
	public List<FnChargeMessage> queryMessageList(EnumMessageSendStatus status, long limit) throws Exception {
		FnChargeMessage query = new FnChargeMessage();
		query.setStatus(status.getValue());
		query.setSort("createTime", EMSOrder.Asc);
		query.setLimit(limit);
		return this.query(query);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void sendSuccess(String messageId, int tryTimes) throws Exception {
		FnChargeMessage query = new FnChargeMessage();
		query.setId(messageId);

		FnChargeMessage update = new FnChargeMessage();
		update.setStatus(EnumMessageSendStatus.COMPLETE_SEND.getValue());
		update.setTryTimes(tryTimes);
		update.setLastUpdateTime(new Date());
		this.update(query, update);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void sendFailure(String messageId, String exceptionReason, int tryTimes) throws Exception {
		FnChargeMessage query = new FnChargeMessage();
		query.setId(messageId);

		FnChargeMessage update = new FnChargeMessage();
		update.setStatus(EnumMessageSendStatus.ERROR.getValue());
		if (exceptionReason != null && exceptionReason.length() > 100) {
			exceptionReason = exceptionReason.substring(0, 100);
		}
		update.setTryTimes(tryTimes);
		update.setExceptionReason(exceptionReason);
		update.setLastUpdateTime(new Date());
		this.update(query, update);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void sendOverdue(String messageId) throws Exception {
		FnChargeMessage query = new FnChargeMessage();
		query.setId(messageId);

		FnChargeMessage update = new FnChargeMessage();
		update.setStatus(EnumMessageSendStatus.OVERDUE.getValue());
		update.setLastUpdateTime(new Date());
		this.update(query, update);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void paySendMessage(FnRecordPrePay record, Double payAfter, FnTenant tenant, String perPaySuccessTemplate,
			String messageSignature) throws Exception {

		FnChargeMessage message = new FnChargeMessage();
		message.setId(UUID.randomUUID().toString());
		message.setTenantId(record.getTenantId());
		message.setTenantName(record.getName());
		message.setContactName(tenant.getContactName());
		message.setContactMobile(tenant.getContactMobile());

		StringBuffer contentSb = new StringBuffer();
		contentSb.append(messageSignature);

		Double value;
		int billingType;
		if (record.getAmount() == null) {
			value = record.getMoney();
			billingType = EnumPrepayChargeType.Qian.getValue();
		} else {
			value = record.getAmount();
			billingType = EnumPrepayChargeType.Liang.getValue();
		}
		String unit;
		if (billingType == EnumPrepayChargeType.Qian.getValue()) {
			unit = "元";
		} else {
			unit = UnitUtil.getCumulantUnit(record.getEnergyTypeId());
		}
		if (unit != null && "m³".equals(unit)) {
			unit = "立方米";
		}
		perPaySuccessTemplate = perPaySuccessTemplate.replace("${buildingName}", record.getBuildingName());
		perPaySuccessTemplate = perPaySuccessTemplate.replace("${tenantName}", record.getTenantName());
		perPaySuccessTemplate = perPaySuccessTemplate.replace("${tenantID}", record.getTenantId());

		SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH-mm");
		String time = standard.format(record.getCreateTime());
		String replace = time.replace(" ", "-");
		String[] split = replace.trim().split("-");
		perPaySuccessTemplate = perPaySuccessTemplate.replace("${year}", split[0]);
		perPaySuccessTemplate = perPaySuccessTemplate.replace("${month}", split[1]);
		perPaySuccessTemplate = perPaySuccessTemplate.replace("${day}", split[2]);
		perPaySuccessTemplate = perPaySuccessTemplate.replace("${hour}", split[3]);

		FnEnergyType query = new FnEnergyType();
		query.setId(record.getEnergyTypeId());
		List<FnEnergyType> list = FNEnergyTypeService.query(query);
		if (list == null || list.size() == 0) {
			throw new Exception("错误能耗类型:" + record.getEnergyTypeId());
		}
		perPaySuccessTemplate = perPaySuccessTemplate.replace("${energyTypeName}",
				list.get(0).getName() == null ? "" : list.get(0).getName());
		perPaySuccessTemplate = perPaySuccessTemplate.replace("${data}",
				DoubleFormatUtil.Instance().getDoubleData_00(value) + "");
		perPaySuccessTemplate = perPaySuccessTemplate.replace("${unit}", unit);
		perPaySuccessTemplate = perPaySuccessTemplate.replace("${code}", record.getCode());
		perPaySuccessTemplate = perPaySuccessTemplate.replace("${remainData}",
				DoubleFormatUtil.Instance().getDoubleData_00(payAfter) + "");
		contentSb.append(perPaySuccessTemplate);
		message.setContent(contentSb.toString());
		message.setCreateTime(new Date());
		message.setLastUpdateTime(new Date());
		message.setStatus(EnumMessageSendStatus.WAIT_SEND.getValue());
		message.setTryTimes(0);
		this.save(message);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void sendBlack(String messageId) throws Exception {
		FnChargeMessage query = new FnChargeMessage();
		query.setId(messageId);

		FnChargeMessage update = new FnChargeMessage();
		update.setStatus(EnumMessageSendStatus.PHONE_BLACK.getValue());
		update.setLastUpdateTime(new Date());
		this.update(query, update);
	}
}
