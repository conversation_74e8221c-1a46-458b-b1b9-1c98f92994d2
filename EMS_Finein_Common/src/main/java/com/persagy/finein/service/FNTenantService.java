package com.persagy.finein.service;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.dto.DTOTenant;
import com.persagy.ems.dto.DTOTenantBaseInfo;
import com.persagy.ems.dto.DTOTenantDetails;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.finein.enumeration.*;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:31:53
 * 
 * 说明:
 */

public interface FNTenantService extends FNBaseService {

	public FnTenant queryOne(String tenantId) throws Exception;

	public List<DTOTenant> queryListByStatus(String buildingId, EnumTenantStatus tenantStatus,
                                             EnumValidStatus validStatus) throws Exception;

	public Map<String, EnumPayType> queryTenantPayType(String buildingId, String energyTypeId) throws Exception;

	public List<FnTenant> queryListByIds(List<String> tenantIdList) throws Exception;

	public List<Map<String, Object>> queryByPayType(String buildingId, String energyTypeId, EnumTenantStatus status,
                                                    EnumPayType payType, EnumPrePayType prePayType, Integer pageIndex, Integer pageSize,
                                                    Map<String, EMSOrder> orderMap) throws Exception;

	public List<Map<String, Object>> queryPostByParam(String buildingId, String energyTypeId, EnumTenantStatus status,
                                                      EnumAmountType amountType, EnumAmountTypeLimit amountTypeLimit, Integer pageIndex, Integer pageSize,
                                                      Map<String, EMSOrder> orderMap) throws Exception;

	public List<Map<String, Object>> queryPreByParam(String buildingId, String energyTypeId, EnumTenantStatus status,
                                                     EnumPrePayType prePayType, EnumPrepayChargeType billingMode, EnumYesNo isAlarm, Integer pageIndex,
                                                     Integer pageSize, Map<String, EMSOrder> orderMap) throws Exception;

	public int countByPayType(String buildingId, String energyTypeId, EnumTenantStatus status, EnumPayType payType,
                              EnumPrePayType prePayType) throws Exception;

	public int countPostByParam(String buildingId, String energyTypeId, EnumTenantStatus status,
                                EnumAmountType amountType, EnumAmountTypeLimit amountTypeLimit) throws Exception;

	public int countPreByParam(String buildingId, String energyTypeId, EnumTenantStatus status,
                               EnumPrePayType prePayType, EnumPrepayChargeType billingMode, EnumYesNo isAlarm) throws Exception;

	// *根据租户编码名名称模糊查询
	public List<FnTenant> queryTenantLike(String query) throws Exception;

	public List<FnTenant> queryListByValidStatus(String buildingId, EnumValidStatus validStatus) throws Exception;

	public List<FnTenant> queryListByTenantValidStatus(String buildingId, EnumTenantStatus tenantStatus,
                                                       EnumValidStatus validStatus) throws Exception;

	public List<DTOTenantBaseInfo> queryTenantInfo(String id) throws Exception;

	public List<FnTenant> queryByLastUpdateTime(String BuildingId, Date timeTo, Date timeFrom, EnumValidStatus valid)
			throws Exception;

	public Map<String, FnTenant> queryByPriceTemplateUpdate(String buildingId, Date TimeFrom, Date timeTo)
			throws Exception;

	public Map<String, FnTenant> queryByTenantPriceUpdate(String buildingId, Date timeTo, Date lastUpdateTime)
			throws Exception;

	public FnTenant queryTenant(String buildingId, String tenantId, EnumTenantStatus tenantStatus,
                                EnumValidStatus validStatus) throws Exception;

	public List<FnTenant> queryTenantLikeByBuildingId(String buildingId, String lowerCase) throws Exception;

	public Map<String, FnTenant> queryMap() throws Exception;

	public List<DTOTenantDetails> queryByMeterId(String meterId) throws Exception;


	/**
	 * Description: 通过建筑查询建筑下租户 ,key:能耗类型-付费类型
	 * @param buildingId
	 * @return Map<String,List<FnTenant>>
	 * <AUTHOR>
	 * @throws Exception
	 * @since 2019年9月12日: 下午12:14:50
	 * Update By 邵泓博 2019年9月12日: 下午12:14:50
	 */
	public Map<String, List<FnTenant>> queryPayTypeAndTenantList(String buildingId) throws Exception;


	/**
	 * Description:
	 * @param buildingId
	 * @return Map<String,FnTenant>
	 * <AUTHOR>
	 * @throws Exception
	 * @since 2019年9月28日: 下午4:10:18
	 * Update By 邵泓博 2019年9月28日: 下午4:10:18
	 */
	public Map<String, FnTenant> queryMap(String buildingId) throws Exception;

	/**
	 * 查询该楼层下所有租户
	 * @param FloorId 楼层id
	 * @param buildingId 建筑id
	 * @return
	 * @throws Exception
	 */
	public List<FnTenant> queryTenantByFloorId(String FloorId,String buildingId ) throws Exception;

	/**
	 * 根据业态和建筑id查询所有租户
	 * @param tenantTypeId 租户类型id
	 * @param buildingId 建筑id
	 * @return
	 * @throws Exception
	 */
	public List<FnTenant> queryTenantBytenantTypeId(String tenantTypeId,String buildingId) throws Exception;


	public List<FnTenant> queryTenantList() throws Exception;

}
