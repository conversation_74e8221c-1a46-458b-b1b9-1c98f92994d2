package com.persagy.finein.service;

import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.pojo.finein.FnRoom;

import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:31:53
 * 
 * 说明:
 */

public interface FNRoomMeterService extends FNBaseService {

	public Map<String, List<DTOMeter>> queryRoomMeter(String buildingId) throws Exception;

	public List<DTOMeter> queryRoomMeter(String buildingId, String roomId, String energyTypeId) throws Exception;

	public List<String> queryMeterIds(String buildingId, String code) throws Exception;

	public Map<String,FnRoom> queryRoomByMeter(String meterId) throws Exception;
}
