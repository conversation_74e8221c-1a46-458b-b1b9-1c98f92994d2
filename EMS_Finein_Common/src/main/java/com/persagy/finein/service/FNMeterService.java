package com.persagy.finein.service;

import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumPrepayChargeType;

import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNMeterService extends FNBaseService {
	
	public List<DTOMeter> queryMeterList(String tenantId, String energyTypeId) throws Exception;

	public Map<String,List<DTOMeter>> queryMeterList(List<String> tenantIdList, String energyTypeId) throws Exception;

	public List<DTOMeter> queryMeterList(String tenantId) throws Exception;

	public EnumPayType queryMeterPayType(String meterId) throws Exception;

	public EnumPrepayChargeType queryMeterBillingMode(String meterId) throws Exception;

	public FnMeter queryMeterById(String meterId) throws Exception;

	public List<FnMeter> queryMeterByEnergyTypeId(String energyTypeId) throws Exception;

	public void changeMeterStatus(List<String> meterIds, Integer status) throws Exception;

	//查询租户指定能耗类型的房间编号及房间下的表信息（表安装位置）
	public Map<String, List<Map<String, Object>>> queryMeter(List<String> tenantIdList, String energyTypeId) throws Exception;

	public Map<String, FnMeter> queryMap() throws Exception;

	public Map<String, List<String>> queryTenant(String buildingId)throws Exception;

	List<Map<String, Object>> queryMeterSetting(Integer pageIndex, Integer pageSize)throws Exception;

	int queryMeterSettingCount() throws Exception;

	List<FnMeter> queryMeterByIds(List<String> meterIds) throws Exception;

	List<FnMeter> queryMeterListByTenantId(String tenantId) throws Exception;

	public Map<String,List<FnMeter>> queryAllMeterList(List<String> tenantIdList, String energyTypeId) throws Exception;
}
