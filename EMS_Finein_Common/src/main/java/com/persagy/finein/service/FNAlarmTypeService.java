package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnAlarmType;
import com.persagy.finein.enumeration.EnumValidStatus;

import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNAlarmTypeService extends FNBaseService {
	
	public List<FnAlarmType> queryList(EnumValidStatus isValid) throws Exception;

	
	 /**
	 * Description: 
	 * @return Map<String,com.persagy.finein.fnmanage.alarm.thread.FnAlarmType>
	 * <AUTHOR>
	 * @throws Exception 
	 * @since 2019年6月17日: 下午12:02:58
	 * Update By 邵泓博 2019年6月17日: 下午12:02:58
	 */
	 
	public Map<String, FnAlarmType> queryMap() throws Exception;
	
}
