package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantEnergySplit;

import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantEnergySplitService extends FNBaseService {
	
	public Map<String, Map<String, FnTenantEnergySplit>> queryMap() throws Exception;
	
	public FnTenantEnergySplit queryEnergySplit(String tenantId, String energyTypeId) throws Exception;

	public Map<String, FnTenantEnergySplit> queryEnergySplit(List<String> tenantIdList, String energyTypeId) throws Exception;
	
}
