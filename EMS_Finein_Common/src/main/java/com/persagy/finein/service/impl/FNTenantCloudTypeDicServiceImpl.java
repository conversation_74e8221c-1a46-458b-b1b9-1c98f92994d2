package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FNTenantCloudTypeDic;
import com.persagy.finein.service.FNTenantCloudTypeDicService;
import org.springframework.stereotype.Service;

import java.util.List;

/**

* 说明:云客户端租户租赁业态
*/

@Service("FNTenantCloudTypeDicService")
public class FNTenantCloudTypeDicServiceImpl extends FNBaseServiceImpl  implements FNTenantCloudTypeDicService{

	@Override
	public FNTenantCloudTypeDic quetOneById(String id) throws Exception {
		FNTenantCloudTypeDic query = new FNTenantCloudTypeDic();
		query.setId(id);
		List<FNTenantCloudTypeDic> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0);
		}
		return null;
	}

}
