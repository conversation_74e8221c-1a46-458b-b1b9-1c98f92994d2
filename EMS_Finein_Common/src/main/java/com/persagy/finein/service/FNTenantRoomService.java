package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantRoom;

import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:31:53
 * 
 * 说明:
 */

public interface FNTenantRoomService extends FNBaseService {

	public List<FnTenantRoom> queryRoomByTenantId(String tenantId) throws Exception;

	public List<FnTenant> queryByRoomIds(String buildingId, List<String> roomIds) throws Exception;
}
