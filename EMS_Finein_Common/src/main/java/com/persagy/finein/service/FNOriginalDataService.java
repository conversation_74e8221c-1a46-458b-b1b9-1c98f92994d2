package com.persagy.finein.service;

import com.persagy.ems.pojo.originaldata.ElectricCurrentData;
import com.persagy.ems.pojo.originaldata.MonthData;
import com.persagy.ems.pojo.originaldata.StatData;
import com.persagy.finein.enumeration.EnumStatTimeType;
import com.persagy.finein.enumeration.EnumStatType;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNOriginalDataService extends FNBaseDictionaryService{
	
	
	public List<MonthData> queryMonthDataGteLt(String buildingId, String meterId, int funcId, Date timeFrom, Date timeTo) throws Exception;

	public void save(String buildingId, String meterId, int funcId, Double data, Date timeFrom) throws Exception;

	public MonthData queryLastMonthDataGteLte(String buildingId, String meterId, int funcId, Date timeFrom, Date timeTo) throws Exception;

	public List<StatData> queryStatDataGteLt(String buildingId, String meterId, int funcId, EnumStatTimeType timeType, EnumStatType statType, Date timeFrom, Date timeTo) throws Exception;

	public List<StatData> queryStatDataGteLt(String meterId, int funcId, EnumStatTimeType timeType, EnumStatType statType, Date timeFrom, Date timeTo) throws Exception;

	public ElectricCurrentData queryLastEleDataGteLte(String buildingId, String meterId, int funcId, Date timeFrom, Date timeTo) throws Exception;

	public MonthData queryLastMonthDataGteLte(String meterId, int funcId, Date timeFrom, Date timeTo) throws Exception;

	public ElectricCurrentData queryLastEleDataGteLte(String meterId, int funcId, Date timeFrom, Date timeTo) throws Exception;
	
}
