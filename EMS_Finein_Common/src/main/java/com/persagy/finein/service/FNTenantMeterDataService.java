package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantMeterData;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumTimeType;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantMeterDataService extends FNBaseService {
	
	public void saveData(String buildingId, String tenantId, String meterId, EnumTimeType timeType, int functionId, String energyTypeId, Date timeFrom, EnumEnergyMoney energyMoney, Double data) throws Exception ;

	public List<FnTenantMeterData> queryListGteLt(String buildingId, String tenantId, String meterId, int functionId, String energyTypeId, EnumTimeType timeType, Date timeFrom, Date timeTo, EnumEnergyMoney energyMoney) throws Exception;

	public FnTenantMeterData queryHistoryMaxData(String buildingId, String tenantId, String meterId, int functionId, Date from, Date to, String energyTypeId, EnumTimeType enumTimeType, EnumEnergyMoney energyMoney) throws Exception ;

    public void removeData(String buildingId, String tenantId, String meterId, int functionId, Date timeFrom) throws Exception ;

    public FnTenantMeterData queryLastGteLt(String buildingId, String tenantId, String meterId, int functionId, String energyTypeId, EnumTimeType timeType, Date timeFrom, Date timeTo, EnumEnergyMoney energyMoney) throws Exception;
    
}
