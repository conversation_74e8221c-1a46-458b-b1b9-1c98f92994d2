package com.persagy.finein.service.impl;

import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.finein.common.util.SchemaUtil;
import com.persagy.finein.service.FNRolePermissionService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * 说明:用户角色权限关系表
 */

@Service("FNRolePermissionService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNRolePermissionServiceImpl extends FNBaseServiceImpl implements FNRolePermissionService {

	@Override
	public List<Map<String, Object>> queryListByRoleId(String roleId) throws Exception {
		List<Map<String,Object>> result = new ArrayList<>();
		StringBuffer sqlBuffer = new StringBuffer();

		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("SELECT p.c_id,p.c_name,p.c_system_name,rp.c_is_have from ");
		sqlBuffer.append(schema).append(".t_fn_role_permission rp, ");
		sqlBuffer.append(schema).append(".t_fn_permission p ");
		sqlBuffer.append("where rp.c_permission_id = p.c_id and rp.c_role_id = '"+roleId+"'" );
		List<Map<String,Object>> list = this.queryBySql(sqlBuffer.toString(),null);
		if(list!=null){
			for (Map<String, Object> map : list) {
				Map<String,Object> hashMap = new HashMap<>();
				hashMap.put("permissionId", (String)map.get("c_id"));
				hashMap.put("permissionName",(String)map.get("c_name"));
				hashMap.put("systemName", (String)map.get("c_system_name"));
				hashMap.put("isHave", (Integer)map.get("c_is_have"));
				result.add(hashMap);
			}
		}
		return result;
	}

}
