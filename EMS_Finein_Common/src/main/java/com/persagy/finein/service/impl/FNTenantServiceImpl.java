package com.persagy.finein.service.impl;

import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.dto.DTOTenant;
import com.persagy.ems.dto.DTOTenantBaseInfo;
import com.persagy.ems.dto.DTOTenantDetails;
import com.persagy.ems.finein.common.util.SchemaUtil;
import com.persagy.ems.pojo.finein.FnEnergyType;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.finein.enumeration.*;
import com.persagy.finein.service.FNTenantService;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNTenantService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNTenantServiceImpl extends FNBaseServiceImpl implements FNTenantService {

	@Override
	public FnTenant queryOne(String tenantId) throws Exception {
		FnTenant query = new FnTenant();
		query.setId(tenantId);
		return (FnTenant) this.queryObject(query);
	}

	@Override
	public List<DTOTenant> queryListByStatus(String buildingId, EnumTenantStatus tenantStatus,
			EnumValidStatus validStatus) throws Exception {

		FnTenant query = new FnTenant();
		query.setBuildingId(buildingId);
		query.setStatus(tenantStatus.getValue());
		query.setIsValid(validStatus.getValue());
		List<FnTenant> tenantList = this.query(query);
		List<DTOTenant> result = new ArrayList<DTOTenant>();

		if (tenantList != null && tenantList.size() > 0) {
			StringBuffer sqlBuffer = new StringBuffer();

			String schema = SchemaUtil.getSchema(Schema.EMS);

			sqlBuffer.append("SELECT t.c_id,tr.c_room_id,tr.c_room_code,r.c_floor_id,ty.c_name from ");
			sqlBuffer.append(schema).append(".t_fn_tenant t,");
			sqlBuffer.append(schema).append(".t_fn_tenant_room tr,");
			sqlBuffer.append(schema).append(".t_fn_tenant_type ty,");
			sqlBuffer.append(schema).append(
					".t_fn_room r where t.c_id = tr.c_tenant_id and tr.c_room_id = r.c_id and ty.c_id=t.c_tenant_type_id and t.c_building_id='");
			sqlBuffer.append(buildingId).append("' and t.c_status = ").append(tenantStatus.getValue())
					.append(" and t.c_is_valid = ").append(validStatus.getValue());

			// SELECT t.c_id,tr.c_room_id,tr.c_room_code,r.c_floor_id from
			// t_fn_tenant t,t_fn_tenant_room tr,t_fn_room r where t.c_id =
			// tr.c_tenant_id and tr.c_room_id = r.c_id and t.c_building_id=''
			// and t.c_status = 1 and t.c_is_valid = 1;
			Map<String, List<String>> tenantRoomMap = new HashMap<String, List<String>>();
			Map<String, List<String>> tenantFloorMap = new HashMap<String, List<String>>();
			Map<String, List<String>> tenantTypeMap = new HashMap<String, List<String>>();

			List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
			if (list != null) {
				for (Map<String, Object> map : list) {
					String c_id = (String) map.get("c_id");
					String c_room_code = (String) map.get("c_room_code");
					String c_floor_id = (String) map.get("c_floor_id");
					String c_name = (String) map.get("c_name");

					if (!tenantRoomMap.containsKey(c_id)) {
						tenantRoomMap.put(c_id, new ArrayList<String>());
					}
					if (!tenantRoomMap.get(c_id).contains(c_room_code)) {
						tenantRoomMap.get(c_id).add(c_room_code);
					}

					if (!tenantFloorMap.containsKey(c_id)) {
						tenantFloorMap.put(c_id, new ArrayList<String>());
					}

					if (!tenantFloorMap.get(c_id).contains(c_floor_id)) {
						tenantFloorMap.get(c_id).add(c_floor_id);
					}
					if (!tenantTypeMap.containsKey(c_id)) {
						tenantTypeMap.put(c_id, new ArrayList<String>());
					}

					if (!tenantTypeMap.get(c_id).contains(c_name)) {
						tenantTypeMap.get(c_id).add(c_name);
					}
				}
			}

			for (FnTenant tenant : tenantList) {
				DTOTenant dto = new DTOTenant();
				dto.setTenant(tenant);
				dto.setRoomList(tenantRoomMap.get(tenant.getId()));
				dto.setFloorList(tenantFloorMap.get(tenant.getId()));
				dto.setTenantTypeList(tenantTypeMap.get(tenant.getId()));
				result.add(dto);
			}
		}

		return result;
	}

	@Override
	public Map<String, EnumPayType> queryTenantPayType(String buildingId, String energyTypeId) throws Exception {
		FnTenantPayType query = new FnTenantPayType();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyTypeId);
		List<FnTenantPayType> tenantPayTypeList = this.query(query);

		Map<String, EnumPayType> result = new HashMap<String, EnumPayType>();

		if (tenantPayTypeList != null && tenantPayTypeList.size() > 0) {
			for (FnTenantPayType payType : tenantPayTypeList) {
				result.put(payType.getTenantId(), EnumPayType.valueOf(payType.getPayType()));
			}
		}
		return result;
	}

	@Override
	public List<FnTenant> queryListByIds(List<String> tenantIdList) throws Exception {
		FnTenant query = new FnTenant();
		query.setSpecialOperation("id", SpecialOperator.$in, tenantIdList);
		return this.query(query);
	}

	@Override
	public List<Map<String, Object>> queryByPayType(String buildingId, String energyTypeId, EnumTenantStatus status,
			EnumPayType payType, EnumPrePayType prePayType, Integer pageIndex, Integer pageSize,
			Map<String, EMSOrder> orderMap) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();

		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("SELECT * from ");
		sqlBuffer.append(schema).append(".t_fn_tenant t,");
		sqlBuffer.append(schema).append(".t_fn_tenant_pay_type tpt ");
		sqlBuffer.append(" where t.c_is_valid = 1 ");
		if (status != null) {
			sqlBuffer.append(" and t.c_status=").append(status.getValue());
		}
		if (buildingId != null) {
			sqlBuffer.append(" and t.c_building_id='").append(buildingId).append("' ");
		}
		sqlBuffer.append(" and tpt.c_energy_type_id='").append(energyTypeId).append("' ");
		sqlBuffer.append(" and tpt.c_pay_type=").append(payType.getValue()).append(" ");
		if (prePayType != null) {
			sqlBuffer.append(" and tpt.c_pre_pay_type=").append(prePayType.getValue()).append(" ");
		}
		// sqlBuffer.append(" and
		// tpt.c_pre_pay_type=").append(prePayType.getValue()).append(" ");
		sqlBuffer.append(" and t.c_id = tpt.c_tenant_id  ORDER BY t.c_building_id asc");
		if (orderMap != null && orderMap.size() > 0) {
			for (Map.Entry<String, EMSOrder> entry : orderMap.entrySet()) {
				sqlBuffer.append(",").append(entry.getKey()).append(" ").append(entry.getValue().toString());
			}
		}
		long skip = pageIndex * pageSize;
		long limit = pageSize;
		sqlBuffer.append(" limit ").append(skip).append(",").append(limit);

		return this.queryBySql(sqlBuffer.toString(), null);
	}

	@Override
	public List<Map<String, Object>> queryPostByParam(String buildingId, String energyTypeId, EnumTenantStatus status,
			EnumAmountType amountType, EnumAmountTypeLimit amountTypeLimit, Integer pageIndex, Integer pageSize,
			Map<String, EMSOrder> orderMap) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();

		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("SELECT * from ");
		sqlBuffer.append(schema).append(".t_fn_tenant t,");
		sqlBuffer.append(schema).append(".t_fn_tenant_post_pay_param tppp ");
		sqlBuffer.append(" where t.c_status=").append(status.getValue());
		if (buildingId != null) {
			sqlBuffer.append(" and t.c_building_id='").append(buildingId).append("' and t.c_is_valid = 1");
		}
		sqlBuffer.append(" and tppp.c_energy_type_id='").append(energyTypeId).append("' ");
		if (amountType != null && amountType == EnumAmountType.WeiJieSun) {
			if (amountTypeLimit != null && amountTypeLimit != EnumAmountTypeLimit.None) {
				sqlBuffer.append(" and tppp.c_no_billing_type>=").append(amountTypeLimit.getValue()).append(" ");
				sqlBuffer.append(" and tppp.c_no_billing_type<=").append(EnumAmountTypeLimit.Over_Six_Month.getValue())
						.append(" ");
			} else {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				sqlBuffer.append(" and tppp.c_last_clearing_time <'")
						.append(sdf.format(DateUtils.truncate(new Date(), Calendar.DATE))).append("' ");
			}
			sqlBuffer.append(" and tppp.c_no_pay_order_count = 0 ");
		} else if (amountType != null && amountType == EnumAmountType.QianFei) {
			if (amountTypeLimit != null && amountTypeLimit == EnumAmountTypeLimit.One) {
				sqlBuffer.append(" and tppp.c_no_pay_order_count=1 ");
			} else if (amountTypeLimit != null && amountTypeLimit == EnumAmountTypeLimit.More) {
				sqlBuffer.append(" and tppp.c_no_pay_order_count > 1 ");
			} else {
				sqlBuffer.append(" and tppp.c_no_pay_order_count > 0 ");
			}
		}
		sqlBuffer.append(" and t.c_id = tppp.c_tenant_id  ORDER BY t.c_building_id asc");
		if (orderMap != null && orderMap.size() > 0) {
			for (Map.Entry<String, EMSOrder> entry : orderMap.entrySet()) {
				sqlBuffer.append(",").append(entry.getKey()).append(" ").append(entry.getValue().toString());
			}
		}
		long skip = pageIndex * pageSize;
		long limit = pageSize;
		sqlBuffer.append(" limit ").append(skip).append(",").append(limit);

		return this.queryBySql(sqlBuffer.toString(), null);

		// SELECT * from t_fn_tenant t,t_fn_tenant_post_pay_param tppp where
		// t.c_status=0 and t.c_building_id='' and tppp.c_energy_type_id='' and
		// tppp.c_pay_type=1 t.c_id = tppp.c_tenant_id ORDER BY t.c_building_id
		// asc, t.c_id Asc limit 0,50
	}

	@Override
	public List<Map<String, Object>> queryPreByParam(String buildingId, String energyTypeId, EnumTenantStatus status,
			EnumPrePayType prePayType, EnumPrepayChargeType billingMode, EnumYesNo isAlarm, Integer pageIndex,
			Integer pageSize, Map<String, EMSOrder> orderMap) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();

		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("SELECT t.*, tppp.*,substring_index(tppp.c_remain_days,'~',1)+0 remain_min_day from ");
		sqlBuffer.append(schema).append(".t_fn_tenant t,");
		sqlBuffer.append(schema).append(".t_fn_tenant_pre_pay_param tppp ");
		sqlBuffer.append(" where t.c_is_valid = 1 and  t.c_status=").append(status.getValue());
		if (buildingId != null) {
			sqlBuffer.append(" and t.c_building_id='").append(buildingId).append("' ");
		}
		sqlBuffer.append(" and tppp.c_energy_type_id='").append(energyTypeId).append("' ");
		if (prePayType != null) {
			sqlBuffer.append(" and tppp.c_pre_pay_type=").append(prePayType.getValue()).append(" ");
		}

		if (billingMode != null) {
			sqlBuffer.append(" and tppp.c_prepay_charge_type=").append(billingMode.getValue()).append(" ");
		}

		if (isAlarm != null) {
			sqlBuffer.append(" and tppp.c_is_alarm=").append(isAlarm.getValue()).append(" ");
		}

		sqlBuffer.append(" and t.c_id = tppp.c_tenant_id  ORDER BY t.c_building_id asc");
		if (orderMap != null && orderMap.size() > 0) {
			for (Map.Entry<String, EMSOrder> entry : orderMap.entrySet()) {
				sqlBuffer.append(",").append(entry.getKey()).append(" ").append(entry.getValue().toString());
			}
		}
		long skip = pageIndex * pageSize;
		long limit = pageSize;
		sqlBuffer.append(" limit ").append(skip).append(",").append(limit);

		return this.queryBySql(sqlBuffer.toString(), null);
	}

	@Override
	public int countByPayType(String buildingId, String energyTypeId, EnumTenantStatus status, EnumPayType payType,
			EnumPrePayType prePayType) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();

		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("select count(1) c from (SELECT t.* from ");
		sqlBuffer.append(schema).append(".t_fn_tenant t,");
		sqlBuffer.append(schema).append(".t_fn_tenant_pay_type tpt ");
		sqlBuffer.append(" where t.c_is_valid =1 ");
		if (status != null) {
			sqlBuffer.append("and  t.c_status=").append(status.getValue());
		}
		if (buildingId != null) {
			sqlBuffer.append(" and t.c_building_id='").append(buildingId).append("' ");
		}
		sqlBuffer.append(" and tpt.c_energy_type_id='").append(energyTypeId).append("' ");
		sqlBuffer.append(" and tpt.c_pay_type=").append(payType.getValue()).append(" ");
		if (prePayType != null) {
			sqlBuffer.append(" and tpt.c_pre_pay_type=").append(prePayType.getValue()).append(" ");
		}
		sqlBuffer.append(" and t.c_id = tpt.c_tenant_id) a");

		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null && list.size() > 0) {
			Map<String, Object> map = list.get(0);
			Object object = map.get("c");

			return Integer.valueOf(object.toString());
		}
		return 0;
	}

	@Override
	public int countPostByParam(String buildingId, String energyTypeId, EnumTenantStatus status,
			EnumAmountType amountType, EnumAmountTypeLimit amountTypeLimit) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();

		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("select count(1) c from (SELECT t.* from ");

		sqlBuffer.append(schema).append(".t_fn_tenant t,");
		sqlBuffer.append(schema).append(".t_fn_tenant_post_pay_param tppp ");
		sqlBuffer.append(" where t.c_is_valid =1 and t.c_status=").append(status.getValue());
		if (buildingId != null) {
			sqlBuffer.append(" and t.c_building_id='").append(buildingId).append("' ");
		}
		sqlBuffer.append(" and tppp.c_energy_type_id='").append(energyTypeId).append("' ");
		if (amountType != null && amountType == EnumAmountType.WeiJieSun) {
			if (amountTypeLimit != null && amountTypeLimit != EnumAmountTypeLimit.None) {
				sqlBuffer.append(" and tppp.c_no_billing_type>=").append(amountTypeLimit.getValue()).append(" ");
				sqlBuffer.append(" and tppp.c_no_billing_type<=").append(EnumAmountTypeLimit.Over_Six_Month.getValue())
						.append(" ");
			} else {
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				sqlBuffer.append(" and tppp.c_last_clearing_time <'")
						.append(sdf.format(DateUtils.truncate(new Date(), Calendar.DATE))).append("' ");
			}
			sqlBuffer.append(" and tppp.c_no_pay_order_count = 0 ");
		} else if (amountType != null && amountType == EnumAmountType.QianFei) {
			if (amountTypeLimit != null && amountTypeLimit == EnumAmountTypeLimit.One) {
				sqlBuffer.append(" and tppp.c_no_pay_order_count=1 ");
			} else if (amountTypeLimit != null && amountTypeLimit == EnumAmountTypeLimit.More) {
				sqlBuffer.append(" and tppp.c_no_pay_order_count > 1 ");
			} else {
				sqlBuffer.append(" and tppp.c_no_pay_order_count > 0 ");
			}
		}

		sqlBuffer.append(" and t.c_id = tppp.c_tenant_id) a");

		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null && list.size() > 0) {
			Map<String, Object> map = list.get(0);
			Object object = map.get("c");

			return Integer.valueOf(object.toString());
		}
		return 0;
	}

	@Override
	public int countPreByParam(String buildingId, String energyTypeId, EnumTenantStatus status,
			EnumPrePayType prePayType, EnumPrepayChargeType billingMode, EnumYesNo isAlarm) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();

		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("select count(1) c from (SELECT t.*, tppp.c_is_alarm from ");
		sqlBuffer.append(schema).append(".t_fn_tenant t,");
		sqlBuffer.append(schema).append(".t_fn_tenant_pre_pay_param tppp ");
		sqlBuffer.append(" where t.c_is_valid =1 and t.c_status=").append(status.getValue());
		if (buildingId != null) {
			sqlBuffer.append(" and t.c_building_id='").append(buildingId).append("' ");
		}
		sqlBuffer.append(" and tppp.c_energy_type_id='").append(energyTypeId).append("' ");

		if (prePayType != null) {
			sqlBuffer.append(" and tppp.c_pre_pay_type=").append(prePayType.getValue()).append(" ");
		}

		if (billingMode != null) {
			sqlBuffer.append(" and tppp.c_prepay_charge_type=").append(billingMode.getValue()).append(" ");
		}

		if (isAlarm != null) {
			sqlBuffer.append(" and tppp.c_is_alarm=").append(isAlarm.getValue()).append(" ");
		}

		sqlBuffer.append(" and t.c_id = tppp.c_tenant_id) a");

		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null && list.size() > 0) {
			Map<String, Object> map = list.get(0);
			Object object = map.get("c");

			return Integer.valueOf(object.toString());
		}
		return 0;
	}

	@Override
	public List<FnTenant> queryTenantLike(String query) throws Exception {

		StringBuffer sqlBuffer = new StringBuffer();

		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("SELECT  t.c_id,t.c_name,t.c_building_id FROM ").append(schema).append(".t_fn_tenant t ");
		sqlBuffer.append(" LEFT JOIN ").append(schema).append(".t_fn_tenant_spell_name tsn ON t.c_id = tsn.c_id ");
		sqlBuffer.append(" WHERE 1=1 ");
		sqlBuffer.append(" AND t.c_is_valid =1 ");
		sqlBuffer.append(" AND ( t.c_id like UPPER(").append("'%").append(query).append("%') or t.c_id like LOWER(").append("'%").append(query).append("%')");
		sqlBuffer.append(" OR  t.c_name like UPPER(").append("'%").append(query).append("%') or t.c_name like LOWER(").append("'%").append(query).append("%')");
		sqlBuffer.append(" OR  tsn.c_spell_name like UPPER(").append("'%").append(query).append("%') or tsn.c_spell_name like LOWER(").append("'%").append(query).append("%')").append(")");
		sqlBuffer.append(" ORDER BY t.c_id ");


		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		List<FnTenant> result = new ArrayList<>();
		if (list != null && list.size() > 0) {

			for (Map<String, Object> map : list) {
				FnTenant resultObj = new FnTenant();
				resultObj.setId(map.get("c_id").toString());
				resultObj.setName(map.get("c_name").toString());
				resultObj.setBuildingId(map.get("c_building_id").toString());
				result.add(resultObj);
			}
		}
		return result;
	}

	@Override
	public List<FnTenant> queryListByValidStatus(String buildingId, EnumValidStatus validStatus) throws Exception {
		FnTenant query = new FnTenant();
		query.setBuildingId(buildingId);
		query.setIsValid(validStatus.getValue());
		query.setSort("id", EMSOrder.Asc);
		return this.query(query);
	}

	@Override
	public List<FnTenant> queryListByTenantValidStatus(String buildingId, EnumTenantStatus tenantStatus,
			EnumValidStatus validStatus) throws Exception {
		FnTenant query = new FnTenant();
		query.setBuildingId(buildingId);
		query.setStatus(tenantStatus.getValue());
		query.setIsValid(validStatus.getValue());
		query.setSort("id", EMSOrder.Asc);
		return this.query(query);
	}

	@Override
	public List<DTOTenantBaseInfo> queryTenantInfo(String id) throws Exception {
		// SELECT tpt.c_energy_type_id,tpt.c_pay_type,tpt.c_pre_pay_type,pt.*
		// FROM finein_ems.t_fn_tenant_pay_type tpt,finein_ems.t_fn_tenant_price
		// tp ,finein_ems.t_fn_price_template pt
		// where tpt.c_tenant_id = tp.c_tenant_id and tpt.c_energy_type_id =
		// tp.c_energy_type_id and tp.c_price_template_id = pt.c_id and
		// tpt.c_tenant_id ='ZHBH_1008';
		List<DTOTenantBaseInfo> result = new ArrayList<DTOTenantBaseInfo>();
		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append(" select tpt.c_energy_type_id,tpt.c_pay_type,tpt.c_pre_pay_type,pt.* from ");
		sqlBuffer.append(schema).append(".t_fn_tenant_pay_type tpt,").append(schema).append(".t_fn_tenant_price tp ,")
				.append(schema).append(".t_fn_price_template pt ");
		sqlBuffer
				.append(" where tpt.c_tenant_id = tp.c_tenant_id and tpt.c_energy_type_id = tp.c_energy_type_id and tp.c_price_template_id = pt.c_id and tpt.c_tenant_id = '")
				.append(id).append("'");
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		for (Map<String, Object> map : list) {
			DTOTenantBaseInfo dtoTenantBaseInfo = new DTOTenantBaseInfo();
			dtoTenantBaseInfo.setEnergyId((String) map.get("c_energy_type_id"));
			if ((Integer) map.get("tpt.c_pay_type") == EnumPayType.POSTPAY.getValue()) {
				dtoTenantBaseInfo.setPayType(EnumTenantCloudPayType.POSTPAY.getValue());
			} else {
				dtoTenantBaseInfo.setPayType(EnumTenantCloudPayType
						.getEnumTenantCloudPayType((Integer) map.get("c_pre_pay_type")).getValue());
			}
			dtoTenantBaseInfo.setIsSys(EnumYesNo.YES.getValue());
			dtoTenantBaseInfo.setPriceName((String) map.get("c_name"));
			dtoTenantBaseInfo.setPriceType((Integer) map.get("c_type"));
			dtoTenantBaseInfo.setPriceContent((String) map.get("c_content"));
			result.add(dtoTenantBaseInfo);
		}
		return result;
	}

	@Override
	public List<FnTenant> queryByLastUpdateTime(String buildingId, Date timeTo, Date timeFrom, EnumValidStatus valid)
			throws Exception {
		FnTenant query = new FnTenant();
		query.setBuildingId(buildingId);
		query.setIsValid(valid.getValue());
		query.setSpecialOperation("lastUpdateTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("lastUpdateTime", SpecialOperator.$lt, timeTo);
		return this.query(query);
	}

	@Override
	public Map<String, FnTenant> queryByPriceTemplateUpdate(String buildingId, Date TimeFrom, Date timeTo)
			throws Exception {
		// select * from finein_ems.t_fn_tenant ft ,finein_ems.t_fn_tenant_price
		// ftp ,finein_ems.t_fn_price_template pt
		// where ft.c_id = ftp.c_tenant_id and ftp.c_price_template_id = pt.c_id
		// and pt.c_last_update_time >='2017-11-00 00:00:00'and
		// pt.c_last_update_time<='2017-11-22 20:33:14';
		Map<String, FnTenant> resultMap = new HashMap<>();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append("select ft.* from ").append(schema).append(".t_fn_tenant ft ,").append(schema)
				.append(".t_fn_tenant_price ftp ,").append(schema).append(".t_fn_price_template pt ");
		sqlBuffer
				.append("where ft.c_id = ftp.c_tenant_id and ftp.c_price_template_id = pt.c_id and pt.c_last_update_time >= '")
				.append(sdf.format(TimeFrom)).append("' and pt.c_last_update_time<= '").append(sdf.format(timeTo))
				.append("'").append(" and ft.c_building_id = '").append(buildingId).append("'");
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null && list.size() > 0) {
			for (Map<String, Object> map : list) {
				FnTenant tenant = new FnTenant();
				tenant.setId((String) map.get("c_id"));
				tenant.setTenantTypeId((String) map.get("c_tenant_type_id"));
				tenant.setRoomCodes((String) map.get("c_room_codes"));
				tenant.setActiveTime((Date) map.get("c_active_time"));
				tenant.setLeaveTime((Date) map.get("c_leave_time"));
				tenant.setContactName((String) map.get("c_contact_name"));
				tenant.setContactMobile((String) map.get("c_contact_mobile"));
				resultMap.put(tenant.getId(), tenant);
			}
		}
		return resultMap;
	}

	@Override
	public Map<String, FnTenant> queryByTenantPriceUpdate(String buildingId, Date timeTo, Date lastUpdateTime)
			throws Exception {
		Map<String, FnTenant> resultMap = new HashMap<>();
		StringBuffer sqlBuffer = new StringBuffer();
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String schema = SchemaUtil.getSchema(Schema.EMS);
		// select * from finein_ems.t_fn_tenant ft ,finein_ems.t_fn_tenant_price
		// ftp ,
		// where ft.c_id = ftp.c_tenant_id and ftp.c_last_update_time
		// >='2017-11-00 00:00:00' and ftp.c_last_update_time <'2017-11-30
		// 00:00:00'
		sqlBuffer.append("select ft.* ");
		sqlBuffer.append(" from ").append(schema).append(".t_fn_tenant ft ,").append(schema)
				.append(".t_fn_tenant_price ftp");
		sqlBuffer.append(" where ft.c_id = ftp.c_tenant_id and ftp.c_last_update_time >='")
				.append(sdf.format(lastUpdateTime)).append("' and ftp.c_last_update_time < '")
				.append(sdf.format(timeTo)).append("'");
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null && list.size() > 0) {
			for (Map<String, Object> map : list) {
				FnTenant tenant = new FnTenant();
				tenant.setId((String) map.get("c_id"));
				tenant.setName((String) map.get("c_name"));
				tenant.setBuildingId((String) map.get("c_building_id"));
				tenant.setTenantTypeId((String) map.get("c_tenant_type_id"));
				tenant.setRoomCodes((String) map.get("c_room_codes"));
				tenant.setActiveTime((Date) map.get("c_active_time"));
				tenant.setLeaveTime((Date) map.get("c_leave_time"));
				tenant.setContactName((String) map.get("c_contact_name"));
				tenant.setContactMobile((String) map.get("c_contact_mobile"));
				resultMap.put(tenant.getId(), tenant);
			}
		}
		return resultMap;
	}

	@Override
	public FnTenant queryTenant(String buildingId, String tenantId, EnumTenantStatus tenantStatus,
			EnumValidStatus validStatus) throws Exception {
		FnTenant query = new FnTenant();
		query.setBuildingId(buildingId);
		query.setId(tenantId);
		query.setStatus(tenantStatus.getValue().intValue());
		query.setIsValid(validStatus.getValue().intValue());
		List<FnTenant> list = this.query(query);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	@Override
	public List<FnTenant> queryTenantLikeByBuildingId(String buildingId, String like) throws Exception {

		List<FnTenant> result = new ArrayList<FnTenant>();
		StringBuffer sqlBuffer = new StringBuffer();

		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append(
				"SELECT  t.c_id tenantId,t.c_name tenantName,t.c_building_id buildingId ,b.c_name buildingName from ");
		sqlBuffer.append(schema).append(".t_fn_tenant t ,");
		sqlBuffer.append(schema).append(".t_fn_tenant_spell_name tsn ,");
		sqlBuffer.append(schema).append(".t_ac_building b ");
		sqlBuffer.append(" where t.c_is_valid =1 ");
		if (buildingId != null && !buildingId.equals("")) {
			sqlBuffer.append(" and t.c_building_id ='" + buildingId + "' ");
		}
		sqlBuffer.append(" and t.c_building_id = b.c_id ");
		List<Object> paramList = new ArrayList<>();
		if (like != null && !like.equals("")) {
			sqlBuffer.append(" LOWER(tsn.c_id) like ?  ");
			sqlBuffer.append(" or LOWER(tsn.c_tenant_name) like ? ");
			sqlBuffer.append(" or LOWER(tsn.c_spell_name) like ? ) ");
			paramList.add("%" + like + "%");
			paramList.add("%" + like + "%");
			paramList.add("%" + like + "%");
		}
		sqlBuffer.append(" and t.c_id = tsn.c_id order by t.c_building_id asc,t.c_id asc");
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), paramList);
		if (list != null && list.size() > 0) {
			for (Map<String, Object> map : list) {
				FnTenant resultObj = new FnTenant();
				resultObj.setId(map.get("tenantId").toString());
				resultObj.setName(map.get("tenantName").toString());
				resultObj.setBuildingId(map.get("buildingId").toString());
				result.add(resultObj);
			}
		}
		return result;
	}

	@Override
	public Map<String, FnTenant> queryMap() throws Exception {
		Map<String, FnTenant> map = new HashMap<String, FnTenant>();
		FnTenant query = new FnTenant();
		query.setIsValid(EnumValidStatus.VALID.getValue());
		List<FnTenant> list = this.query(query);
		if (list != null && list.size() > 0) {
			for (FnTenant fnTenant : list) {
				map.put(fnTenant.getId(), fnTenant);
			}
		}
		return map;
	}

	@Override
	public List<DTOTenantDetails> queryByMeterId(String meterId) throws Exception {
		List<DTOTenantDetails> result = new ArrayList<>();

		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append(
				"select t.c_id ,t.c_building_id ,t.c_tenant_type_id ,t.c_name , t.c_area,t.c_contact_name,t.c_contact_mobile,tr.c_room_code, t.c_active_time,rm.c_meter_id,tt.c_name c_tenant_type_name,b.c_name c_building_name,m.c_energy_type_id ,tf.c_tenant_flag from ");
		sqlBuffer.append(schema).append(".t_fn_tenant t ,").append(schema).append(".t_fn_tenant_room tr ,")
				.append(schema).append(".t_fn_room_meter rm ,");
		sqlBuffer.append(schema).append(".t_fn_tenant_type tt ,");
		sqlBuffer.append(schema).append(".t_ac_building b ,");
		sqlBuffer.append(schema).append(".t_fn_meter m ,");
		sqlBuffer.append(schema).append(".t_fn_tenant_flag tf ");
		sqlBuffer
				.append(" where rm.c_room_id = tr.c_room_id and t.c_id = tr.c_tenant_id and t.c_building_id = b.c_id and t.c_tenant_type_id = tt.c_id  and m.c_id = rm.c_meter_id and tf.c_tenant_id = t.c_id and rm.c_meter_id = '")
				.append(meterId).append("' ").append(" and t.c_status = 1");
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null && list.size() != 0) {
			for (Map<String, Object> map : list) {
				DTOTenantDetails tenant = new DTOTenantDetails();
				tenant.setBuildingId((String) map.get("c_building_id"));
				tenant.setBuildingName((String) map.get("c_building_name"));
				tenant.setTenantTypeId((String) map.get("c_tenant_type_id"));
				tenant.setTenantTypeName((String) map.get("c_tenant_type_name"));
				tenant.setTenantId((String) map.get("c_id"));
				tenant.setTenantName((String) map.get("c_name"));
				tenant.setContactName((String) map.get("c_contact_name"));
				tenant.setContactMobile((String) map.get("c_contact_mobile"));
				tenant.setArea(Double.valueOf(map.get("c_area").toString()));
				tenant.setRoomCode((String) map.get("c_room_code"));
				tenant.setMeterId((String) map.get("c_meter_id"));
				tenant.setEnergyTypeId((String) map.get("c_energy_type_id"));
				tenant.setTenantFlag((String) map.get("c_tenant_flag"));
				result.add(tenant);
			}
		}
		return result;
	}

	@Override
	public Map<String, List<FnTenant>> queryPayTypeAndTenantList(String buildingId) throws Exception {
		FnEnergyType query = new FnEnergyType();
		query.setIsValid(EnumValidStatus.VALID.getValue());
		List<FnEnergyType> energyTypeList = this.query(query);

		FnTenant queryT= new FnTenant();
		queryT.setBuildingId(buildingId);
		queryT.setIsValid(EnumValidStatus.VALID.getValue());
		List<FnTenant> tenantList = this.query(queryT);
		Map<String, List<FnTenant>> tenantListMap = new HashMap<>();
		if(tenantList!=null){
			for (FnEnergyType energyType : energyTypeList) {
				String prePayKey = energyType.getId() + "-" + EnumPayType.PREPAY.getView();
				String postKey = energyType.getId() + "-" + EnumPayType.POSTPAY.getView();
				Map<String, EnumPayType> tenantPayTypeMap = this.queryTenantPayType(buildingId, energyType.getId());
				for (FnTenant fnTenant : tenantList) {
					EnumPayType payType = tenantPayTypeMap.get(fnTenant.getId());
					if (payType == null) {
						continue;
					}
					if (payType.getValue().intValue() == EnumPayType.PREPAY.getValue().intValue()) {// 预付费
						if (tenantListMap.get(prePayKey) == null) {
							tenantListMap.put(prePayKey, new ArrayList<FnTenant>());
						}
						tenantListMap.get(prePayKey).add(fnTenant);
					} else {
						// 预付费
						if (tenantListMap.get(postKey) == null) {
							tenantListMap.put(postKey, new ArrayList<FnTenant>());
						}
						tenantListMap.get(postKey).add(fnTenant);
					}
				}
			}
		}
		return tenantListMap;
	}


	/**
	 *(non-Javadoc)
	 * @throws Exception
	 * @see FNTenantService#queryMap(String)
	 */

	@Override
	public Map<String, FnTenant> queryMap(String buildingId) throws Exception {
		Map<String, FnTenant> map = new HashMap<String, FnTenant>();
		FnTenant query = new FnTenant();
		query.setIsValid(EnumValidStatus.VALID.getValue());
		query.setBuildingId(buildingId);
		List<FnTenant> list = this.query(query);
		if (list != null && list.size() > 0) {
			for (FnTenant fnTenant : list) {
				map.put(fnTenant.getId(), fnTenant);
			}
		}
		return map;
	}
	@Override
	public List<FnTenant> queryTenantByFloorId(String FloorId,String buildingId) throws Exception {
		List<FnTenant> result = new ArrayList<>();

		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append(
				"SELECT  t.c_id,t.c_building_id,t.c_tenant_type_id,t.c_name,t.c_area,t.c_room_codes,t.c_energy_type_ids,t.c_status,t.c_contact_name,t.c_is_valid,t.c_create_time,t.c_active_time from ");
		sqlBuffer.append(schema).append(".t_fn_tenant t ,").append(schema).append(".t_fn_tenant_room tr ,").append(schema).append(".t_fn_room ro ");
		sqlBuffer.append(" where  t.c_id=tr.c_tenant_id and ro.c_id=tr.c_room_id and t.c_status = 1 and t.c_is_valid = 1 and ro.c_floor_id= '").append(FloorId).append("' ").append(" and t.c_building_id= '").append(buildingId).append("' ");
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null && list.size() != 0) {
			for (Map<String, Object> map : list) {
				FnTenant tenant = new FnTenant();
				tenant.setId((String) map.get("c_id"));
				tenant.setBuildingId((String) map.get("c_building_id"));
				tenant.setTenantTypeId((String) map.get("c_tenant_type_id"));
				tenant.setName((String) map.get("c_name"));
				tenant.setArea(Double.valueOf(map.get("c_area").toString()));
				tenant.setRoomCodes((String) map.get("c_room_codes"));
				tenant.setStatus((Integer) map.get("c_status"));
				tenant.setContactName((String) map.get("c_contact_name"));
				tenant.setIsValid((Integer) map.get("c_is_valid"));
				tenant.setCreateTime((Date) map.get("c_active_time"));
				tenant.setActiveTime((Date) map.get("t.c_active_time"));
				result.add(tenant);

			}
		}
		return result;
	}

	@Override
	public List<FnTenant> queryTenantBytenantTypeId(String tenantTypeId,String buildingId) throws Exception {
		List<FnTenant> result = new ArrayList<>();

		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append("SELECT  t.c_id,t.c_building_id,t.c_tenant_type_id,t.c_name,t.c_area,t.c_room_codes,t.c_energy_type_ids,t.c_status,t.c_contact_name,t.c_is_valid,t.c_create_time,t.c_active_time from ");
		sqlBuffer.append(schema).append(".t_fn_tenant t ,").append(schema).append(". t_fn_tenant_type ty  ");
		sqlBuffer.append(" where  t.c_status = 1 and t.c_is_valid = 1 and t.c_tenant_type_id=ty.c_id and t.c_tenant_type_id= '").append(tenantTypeId).append("' ").append(" and t.c_building_id= '").append(buildingId).append("' ");
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null && list.size() != 0) {
			for (Map<String, Object> map : list) {
				FnTenant tenant = new FnTenant();
				tenant.setId((String) map.get("c_id"));
				tenant.setBuildingId((String) map.get("c_building_id"));
				tenant.setTenantTypeId((String) map.get("c_tenant_type_id"));
				tenant.setName((String) map.get("c_name"));
				tenant.setArea(Double.valueOf(map.get("c_area").toString()));
				tenant.setRoomCodes((String) map.get("c_room_codes"));
				tenant.setStatus((Integer) map.get("c_status"));
				tenant.setContactName((String) map.get("c_contact_name"));
				tenant.setIsValid((Integer) map.get("c_is_valid"));
				tenant.setCreateTime((Date) map.get("c_active_time"));
				tenant.setActiveTime((Date) map.get("t.c_active_time"));
				result.add(tenant);

			}
		}
		return result;
	}

	@Override
	public List<FnTenant> queryTenantList() throws Exception {
		List<FnTenant> result = new ArrayList<>();

		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append("SELECT  t.c_id,t.c_building_id,t.c_tenant_type_id,t.c_name,t.c_area,t.c_room_codes,t.c_energy_type_ids,t.c_status,t.c_contact_name,t.c_is_valid,t.c_create_time,t.c_active_time from ");
		sqlBuffer.append(schema).append(".t_fn_tenant t ,").append(schema).append(". t_fn_tenant_pay_type ty  ");
		sqlBuffer.append(" where ty.c_tenant_id = t.c_id  AND t.c_is_valid = 1  AND t.c_status = 1 AND ty.c_pay_type = 0 AND ty.c_pre_pay_type = 1 ");
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null && list.size() != 0) {
			for (Map<String, Object> map : list) {
				FnTenant tenant = new FnTenant();
				tenant.setId((String) map.get("c_id"));
				tenant.setBuildingId((String) map.get("c_building_id"));
				tenant.setTenantTypeId((String) map.get("c_tenant_type_id"));
				tenant.setName((String) map.get("c_name"));
				tenant.setArea(Double.valueOf(map.get("c_area").toString()));
				tenant.setRoomCodes((String) map.get("c_room_codes"));
				tenant.setStatus((Integer) map.get("c_status"));
				tenant.setContactName((String) map.get("c_contact_name"));
				tenant.setIsValid((Integer) map.get("c_is_valid"));
				tenant.setCreateTime((Date) map.get("c_active_time"));
				tenant.setActiveTime((Date) map.get("t.c_active_time"));
				result.add(tenant);

			}
		}
		return result;
	}


}
