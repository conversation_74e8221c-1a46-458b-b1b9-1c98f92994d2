package com.persagy.finein.service.impl;


import com.persagy.ems.pojo.finein.FnRecordForOtherSystemError;
import com.persagy.finein.enumeration.EnumPrePayErrorType;
import com.persagy.finein.service.FNRecordForOtherSystemErrorService;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* 说明:
*/

@Service("FNRecordForOtherSystemErrorService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNRecordForOtherSystemErrorServiceImpl extends FNBaseServiceImpl  implements FNRecordForOtherSystemErrorService{

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void remainDataError(String id,Exception e) throws Exception {
		
		FnRecordForOtherSystemError query = new FnRecordForOtherSystemError();
		query.setId(id);
		List<FnRecordForOtherSystemError> list = this.query(query);
		if(list!=null&&list.size()>0){
			FnRecordForOtherSystemError update = new FnRecordForOtherSystemError();
			update.setErrorType(EnumPrePayErrorType.QUERRY_ERROR.getValue());
			update.setErrorMessage(ExceptionUtils.getMessage(e));
			Integer tryTimes = list.get(0).getTryTimes();
			update.setTryTimes((tryTimes+1));
			update.setUpdateTime(new Date());
			this.update(list.get(0), update);
		}else{
			FnRecordForOtherSystemError save = new FnRecordForOtherSystemError();
			save.setId(id);
			save.setErrorType(EnumPrePayErrorType.QUERRY_ERROR.getValue());
			save.setErrorMessage(ExceptionUtils.getMessage(e));
			save.setTryTimes(1);
			save.setUpdateTime(new Date());
			this.save(save);
		}
		
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void payError(String id) throws Exception {
		FnRecordForOtherSystemError query = new FnRecordForOtherSystemError();
		query.setId(id);
		List<FnRecordForOtherSystemError> list = this.query(query);
		if(list!=null&&list.size()>0){
			FnRecordForOtherSystemError update = new FnRecordForOtherSystemError();
			update.setErrorType(EnumPrePayErrorType.PAY_ERROR.getValue());
			update.setErrorMessage("充值操作失败");//充值失败
			update.setUpdateTime(new Date());
			Integer tryTimes = list.get(0).getTryTimes();
			update.setTryTimes(++tryTimes);
			this.update(list.get(0), update);
		}else{
			FnRecordForOtherSystemError save = new FnRecordForOtherSystemError();
			save.setId(id);
			save.setErrorType(EnumPrePayErrorType.PAY_ERROR.getValue());
			save.setErrorMessage("充值操作失败");
			save.setUpdateTime(new Date());
			save.setTryTimes(1);
			this.save(save);
		}
	}

}
