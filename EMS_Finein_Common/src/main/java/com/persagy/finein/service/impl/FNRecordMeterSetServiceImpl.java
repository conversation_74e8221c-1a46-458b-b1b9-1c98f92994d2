package com.persagy.finein.service.impl;


import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnRecordMeterSet;
import com.persagy.finein.service.FNRecordMeterSetService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;


/**

* 说明:
*/

@Service("FNRecordMeterSetService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNRecordMeterSetServiceImpl extends FNBaseServiceImpl implements FNRecordMeterSetService{

	@Override
	public List<FnRecordMeterSet> queryList(String tenantId, String energyTypeId, Integer type, Date timeFrom,
			Date timeTo) throws Exception {
		FnRecordMeterSet query = new FnRecordMeterSet();
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setOperateType(type);
		query.setSpecialOperation("createTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("createTime", SpecialOperator.$lte, timeTo);
		
		return this.query(query);
	}

	
}
