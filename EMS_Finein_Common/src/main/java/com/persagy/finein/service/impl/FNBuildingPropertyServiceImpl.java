package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnBuildingProperty;
import com.persagy.finein.service.FNBuildingPropertyService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNBuildingPropertyService")
public class FNBuildingPropertyServiceImpl extends FNBaseServiceImpl implements FNBuildingPropertyService{

	@Override
	public List<FnBuildingProperty> queryList(String buildingId) throws Exception {
		FnBuildingProperty query = new FnBuildingProperty();
		query.setBuildingId(buildingId);
		return this.query(query);
	}

	@Override
	public FnBuildingProperty queryObject(String buildingId, String propertyId) throws Exception {
		FnBuildingProperty query = new FnBuildingProperty();
		query.setBuildingId(buildingId);
		query.setPropertyName(propertyId);
		List<FnBuildingProperty> list = this.query(query);
		if(list != null && list.size() > 0){
			return list.get(0);
		}
		return null;
	}


	@Override
	public void updateById(String id, FnBuildingProperty buildingProperty) throws Exception {
		FnBuildingProperty query = new FnBuildingProperty();
		query.setId(id);
		 List<FnBuildingProperty> queryList = this.query(query);
		 this.update(queryList.get(0), buildingProperty);
	}

	

}
