package com.persagy.finein.service.impl;

import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.finein.common.util.SchemaUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantFlag;
import com.persagy.finein.service.FNTenantFlagService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:租户全码
 */

@Service("FNTenantFlagService")
public class FNTenantFlagServiceImpl extends FNBaseServiceImpl implements FNTenantFlagService {

	@Override
	public Map<String,FnTenant> queryTenantByFlags(List<String> tenantFlagList) throws Exception {
		Map<String,FnTenant> map = new HashMap<String,FnTenant>();
		for (String tenantFlag : tenantFlagList) {
			FnTenant fnTenant = queryTenantByFlag(tenantFlag);
			if (fnTenant != null) {
				map.put(tenantFlag, fnTenant);
			}
		}
		return map;
	}

	@Override
	public FnTenant queryTenantByFlag(String tenantFlag) throws Exception {
		
		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append("SELECT * from ");
		sqlBuffer.append(schema).append(".t_fn_tenant t,");
		sqlBuffer.append(schema).append(".t_fn_tenant_flag tf ");
		sqlBuffer.append(" where tf.c_tenant_flag = '").append(tenantFlag).append("' ");
		sqlBuffer.append(" and tf.c_building_id = t.c_building_id ");
		sqlBuffer.append(" and tf.c_tenant_id = t.c_id ");
		List<Map<String,Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if(list!=null&&list.size()>0){
			Map<String, Object> map = list.get(0);
			FnTenant tenant = new FnTenant();
			tenant.setId((String)map.get("c_tenant_id"));
			tenant.setBuildingId((String)map.get("c_building_id"));
			tenant.setTenantTypeId((String)map.get("c_tenant_type_id"));
			tenant.setName((String)map.get("c_name"));
			tenant.setStatus((Integer)map.get("c_status"));
			tenant.setArea(Double.parseDouble(map.get("c_area").toString()));
			tenant.setRoomCodes((String)map.get("c_room_codes"));
			tenant.setEnergyTypeIds((String)map.get("c_energy_type_ids"));
			tenant.setContactName((String)map.get("c_contact_name"));
			tenant.setContactMobile((String)map.get("c_contact_mobile"));
			tenant.setRemark((String)map.get("c_remark"));
			tenant.setCreateUserId((String)map.get("c_create_user_id"));
			tenant.setCreateTime((Date)map.get("c_create_time"));
			tenant.setLastUpdateUserId((String)map.get("c_last_update_user_id"));
			tenant.setLastUpdateTime((Date)map.get("c_last_update_time"));
			tenant.setActiveUserId((String)map.get("c_active_user_id"));
			tenant.setActiveTime((Date)map.get("c_active_time"));
			tenant.setLeaveTime((Date)map.get("c_leave_time"));
			return tenant;
		}
		return null;
	}

	@Override
	public Map<String, FnTenantFlag> queryFlag() throws Exception {
		Map<String,FnTenantFlag> map = new HashMap<String,FnTenantFlag>();
		List<FnTenantFlag> list = this.query(new FnTenantFlag());
		if(list!=null&&list.size()>0){
			for (FnTenantFlag fnTenantFlag : list) {
				map.put(fnTenantFlag.getTenantId(), fnTenantFlag);
			}
		}
		return map;
	}

}
