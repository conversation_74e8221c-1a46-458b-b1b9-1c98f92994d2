package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnRecordPostClearingPay;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.finein.enumeration.EnumPayStatus;
import com.persagy.finein.service.FNRecordPostClearingPayService;
import com.persagy.finein.service.FNTenantDataService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNRecordPostClearingPayService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNRecordPostClearingPayServiceImpl extends FNBaseServiceImpl  implements FNRecordPostClearingPayService{

	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;
	
	@Resource(name = "FNTenantDataService")
	private FNTenantDataService FNTenantDataService;
 
	
	@Override
	public Map<String, List<FnRecordPostClearingPay>> queryNotPayRecord(String buildingId,String energyTypeId) throws Exception {
		Map<String, List<FnRecordPostClearingPay>> result = new HashMap<String, List<FnRecordPostClearingPay>>();
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyTypeId);
		query.setPayStatus(EnumPayStatus.NO.getValue());
		query.setSort("tenantId", EMSOrder.Asc);
		query.setSort("createTime", EMSOrder.Asc);
		List<FnRecordPostClearingPay> recordList = this.query(query);
		if(recordList != null){
			for(FnRecordPostClearingPay record : recordList){
				if(!result.containsKey(record.getTenantId())){
					result.put(record.getTenantId(), new ArrayList<FnRecordPostClearingPay>());
				}
				result.get(record.getTenantId()).add(record);
			}
		}
		return result;
	}

	@Override
	public List<FnRecordPostClearingPay> queryNotPayRecord(String buildingId, String tenantId, String energyTypeId)
			throws Exception {
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setPayStatus(EnumPayStatus.NO.getValue());
		query.setSort("createTime", EMSOrder.Asc);
		return this.query(query);
	}

	@Override
	public List<FnRecordPostClearingPay> queryNotPayRecord(String buildingId, String tenantId, String energyTypeId,
			List<String> orderList) throws Exception {
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("orderId", SpecialOperator.$in, orderList);
		query.setSort("createTime", EMSOrder.Asc);
		return this.query(query);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void payRecord(String buildingId, String tenantId, String energyTypeId,
			List<String> orderList) throws Exception {
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("orderId", SpecialOperator.$in, orderList);
		
		FnRecordPostClearingPay update = new FnRecordPostClearingPay();
		update.setPayStatus(EnumPayStatus.YES.getValue());
		this.update(query, update);
	}

	@Override
	public List<FnRecordPostClearingPay> queryNotPayRecord(Date timeFrom, Date timeTo, String buildingId,
			String tenantId, String energyTypeId) throws Exception {
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("createTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("createTime", SpecialOperator.$lte, timeTo);
		query.setSort("createTime", EMSOrder.Asc);
		return this.query(query);
	}

	@Override
	public Date queryLastClearingDate(String buildingId, String tenantId, String energyTypeId) throws Exception {
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setSort("createTime", EMSOrder.Desc);
		query.setLimit(1L);
		FnRecordPostClearingPay record = (FnRecordPostClearingPay)this.queryObject(query);
		if(record == null){
			FnTenant tenant = FNTenantService.queryOne(tenantId);
			return tenant.getActiveTime();
		}else{
			String orderTo = record.getOrderTime().split("~")[1]+" 00:00:00";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			return sdf.parse(orderTo);
		}
	}

	@Override
	public List<FnRecordPostClearingPay> queryAllRecord(Date timeFrom, Date timeTo, String buildingId, String tenantId,
			String energyTypeId) throws Exception {
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("lastClearingTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("lastClearingTime", SpecialOperator.$lt, timeTo);
		query.setSort("payStatus", EMSOrder.Asc);
		query.setSort("createTime", EMSOrder.Desc);
		return this.query(query);
	}

	@Override
	public Map<String, List<FnRecordPostClearingPay>> queryNotPayRecord(String tenantId) throws Exception {
		Map<String, List<FnRecordPostClearingPay>> result = new HashMap<String, List<FnRecordPostClearingPay>>();
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setTenantId(tenantId);
		query.setPayStatus(EnumPayStatus.NO.getValue());
		query.setSort("energyTypeId", EMSOrder.Asc);
		query.setSort("createTime", EMSOrder.Desc);
		List<FnRecordPostClearingPay> recordList = this.query(query);
		if(recordList != null){
			for(FnRecordPostClearingPay record : recordList){
				if(!result.containsKey(record.getEnergyTypeId())){
					result.put(record.getEnergyTypeId(), new ArrayList<FnRecordPostClearingPay>());
				}
				result.get(record.getEnergyTypeId()).add(record);
			}
		}
		return result;
	}

	@Override
	public List<FnRecordPostClearingPay> queryNotPayRecord(String buildingId, List<String> tenantIdList,
			String energyTypeId, Date timeFrom, Date timeTo) throws Exception {
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyTypeId);
		query.setPayStatus(EnumPayStatus.NO.getValue());
		query.setSpecialOperation("tenantId", SpecialOperator.$in, tenantIdList);
		query.setSpecialOperation("createTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("createTime", SpecialOperator.$lt, timeTo);
		query.setSort("tenantId", EMSOrder.Asc);
		query.setSort("createTime", EMSOrder.Asc);
		return this.query(query);
	}

	
	@Override
	public Date queryLastClearingTime(String tenantId, String energyTypeId, Date activeTime) throws Exception {
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		
		query.setSort("currentClearingTime", EMSOrder.Desc);
		query.setLimit(1L);
		FnRecordPostClearingPay obj = (FnRecordPostClearingPay)this.queryObject(query);
		if(obj == null){
			return activeTime;
		}else{
			return obj.getCurrentClearingTime();
		}
	}

	@Override
	public Map<String, Object> queryBilling(String buildingId, String tenantId, String energyTypeId) throws Exception {
		Map<String, Object> result = new HashMap<>();
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setPayStatus(EnumPayStatus.NO.getValue());
		List<FnRecordPostClearingPay> orderList = this.query(query);
		int orderCount = 0;
		Double totalEnergy = null;
		Double totalMoney = null;
		
		if(orderList != null){
			orderCount = orderList.size();
			for(FnRecordPostClearingPay order : orderList){
				if(order.getCurrentSumEnergy() != null){
					totalEnergy = totalEnergy == null ? order.getCurrentSumEnergy() : totalEnergy + order.getCurrentSumEnergy();
				}
				if(order.getMoney() != null){
					totalMoney = totalMoney == null ? order.getMoney() : totalMoney + order.getMoney();
				}
			}
		}
		result.put("orderCount", orderCount);
		result.put("totalEnergy", totalEnergy);
		result.put("totalMoney", totalMoney);
		return result;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void payAllRecord(String buildingId, String tenantId) throws Exception {
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setPayStatus(EnumPayStatus.NO.getValue());
		
		FnRecordPostClearingPay update = new FnRecordPostClearingPay();
		update.setPayStatus(EnumPayStatus.YES.getValue());
		this.update(query, update);
	}

	@Override
	public List<FnRecordPostClearingPay> queryNotPayRecord(List<String> tenantIdList,
			String energyTypeId) throws Exception {
		FnRecordPostClearingPay query = new FnRecordPostClearingPay();
		query.setEnergyTypeId(energyTypeId);
		query.setPayStatus(EnumPayStatus.NO.getValue());
		query.setSpecialOperation("tenantId", SpecialOperator.$in, tenantIdList);
		query.setSort("tenantId", EMSOrder.Asc);
		query.setSort("createTime", EMSOrder.Asc);
		return this.query(query);
	}

}
