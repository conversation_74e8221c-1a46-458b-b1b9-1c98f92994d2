package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.FnBuildingCostTime;
import com.persagy.finein.service.FNBuildingCostTimeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNBuildingCostTimeService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNBuildingCostTimeServiceImpl extends FNBaseServiceImpl implements FNBuildingCostTimeService {

	@Override
	public FnBuildingCostTime queryByBuilding(String buildingId, String energy) throws Exception {
		FnBuildingCostTime query = new FnBuildingCostTime();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energy);
		query.setSort("time", EMSOrder.Desc);
		List<FnBuildingCostTime> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0);
		}
		return null;
	}

}
