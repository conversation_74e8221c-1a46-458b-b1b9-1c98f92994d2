package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnSendData;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNSendDataService extends FNBaseService {
	
	public List<FnSendData> queryList(String buildingId, long limit) throws Exception;

	public void removeById(String id) throws Exception;

	public void saveData(String buildingId, String meterId, Integer functionId, Integer dataType, Date receiveTime, Double data) throws Exception;
	
}
