package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnTenantMeterFunctionData;
import com.persagy.finein.enumeration.EnumStatTimeType;
import com.persagy.finein.enumeration.EnumStatType;
import com.persagy.finein.service.FNTenantMeterFunctionDataService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantMeterFunctionDataService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantMeterFunctionDataServiceImpl extends FNBaseServiceImpl  implements FNTenantMeterFunctionDataService{

	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void saveData(String buildingId, String tenantId, String meterId, EnumStatTimeType timeType,
			EnumStatType statType, int functionId, Date timeFrom, Double data) throws Exception {
		FnTenantMeterFunctionData query = new FnTenantMeterFunctionData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setTimeType(timeType.getValue());
		query.setDataType(statType.getValue());
		query.setFunctionId(functionId);
		query.setTimeFrom(timeFrom);
		this.remove(query);
		query.setData(data);
		query.setLastUpdateTime(new Date());
		this.save(query);
	}

	@Override
	public List<FnTenantMeterFunctionData> queryDataGteLt(String buildingId, String tenantId, String meterId,
			int funcId, EnumStatTimeType timeType, EnumStatType statType, Date timeFrom, Date timeTo) throws Exception {
		FnTenantMeterFunctionData query = new FnTenantMeterFunctionData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setTimeType(timeType.getValue());
		query.setDataType(statType.getValue());
		query.setFunctionId(funcId);
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("timeFrom", SpecialOperator.$lt, timeTo);
		return this.query(query);
	}

	@Override
	public FnTenantMeterFunctionData queryLast(String buildingId, String tenantId, String meterId, int funcId,
			EnumStatTimeType timeType, EnumStatType statType, Date timeFrom, Date timeTo) throws Exception {
		FnTenantMeterFunctionData query = new FnTenantMeterFunctionData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setTimeType(timeType.getValue());
		query.setDataType(statType.getValue());
		query.setFunctionId(funcId);
		if(timeFrom != null){
			query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		}
		if(timeTo != null){
			query.setSpecialOperation("timeFrom", SpecialOperator.$lt, timeTo);
		}
		query.setSort("timeFrom", EMSOrder.Desc);
		query.setLimit(1L);
		return (FnTenantMeterFunctionData)this.queryObject(query);
	}
}
