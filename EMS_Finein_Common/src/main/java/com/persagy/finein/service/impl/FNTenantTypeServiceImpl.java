package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.FnTenantType;
import com.persagy.finein.service.FNTenantTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNTenantTypeService")
public class FNTenantTypeServiceImpl extends FNBaseServiceImpl implements FNTenantTypeService {

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public List<FnTenantType> queryList(EMSOrder order) throws Exception {
		FnTenantType query = new FnTenantType();
		query.setSort("orderBy", order);
		return this.query(query);
	}

	@Override
	public Map<String, FnTenantType> queryType() throws Exception {
		Map<String, FnTenantType> map = new HashMap<String, FnTenantType>();
		List<FnTenantType> list = this.query(new FnTenantType());
		if (list != null && list.size() > 0) {
			for (FnTenantType FnTenantType : list) {
				map.put(FnTenantType.getId(), FnTenantType);
			}
		}
		return map;
	}

}
