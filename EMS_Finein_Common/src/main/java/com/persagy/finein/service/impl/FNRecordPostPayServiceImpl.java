package com.persagy.finein.service.impl;

import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.finein.common.util.SchemaUtil;
import com.persagy.ems.pojo.finein.FnRecordPostPay;
import com.persagy.finein.service.FNRecordPostPayService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNRecordPostPayService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNRecordPostPayServiceImpl extends FNBaseServiceImpl  implements FNRecordPostPayService{

	@Override
	public List<FnRecordPostPay> queryRecord(String buildingId, List<String> tenantIdList, String energyTypeId,
			Date timeFrom, Date timeTo) throws Exception {
		FnRecordPostPay query = new FnRecordPostPay();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("tenantId", SpecialOperator.$in, tenantIdList);
		query.setSpecialOperation("operateTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("operateTime", SpecialOperator.$lt, timeTo);
		query.setSort("tenantId", EMSOrder.Asc);
		query.setSort("operateTime", EMSOrder.Asc);
		return this.query(query);
	}

	@Override
	public List<FnRecordPostPay> queryRecord(List<String> tenantIdList, String energyTypeId, Date timeFrom, Date timeTo)
			throws Exception {
		//select * from 
		//(SELECT * ,substring_index(c_order_time,'~', 1) order_time from finein_ems.t_fn_record_post_pay WHERE c_tenant_id in ('ZHBH_1204','ZHBH_1204') 
		//and c_energy_type_id = 'RanQi') a 
		//where a.order_time >= '2017-10-28' and a.order_time < '2017-11-20';
		String schema = SchemaUtil.getSchema(Schema.EMS);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		StringBuffer stringBuffer = new StringBuffer();
		stringBuffer.append("select * from ");
		stringBuffer.append("( SELECT * ,substring_index ( c_order_time,'~', 1 ) order_time from ").append(schema).append(".t_fn_record_post_pay ");
		stringBuffer.append(" WHERE c_tenant_id in ").append(" (");
		stringBuffer.append(tenantIdList.toString().replace("[", "'").replace("]", "'").replace(", ", "','")).append(")");
		stringBuffer.append(" and c_energy_type_id = '").append(energyTypeId).append("')").append(" rpp");
		stringBuffer.append(" where rpp.order_time >= '").append(sdf.format(timeFrom).substring(0, 10)).append("' and rpp.order_time < '").append(sdf.format(timeTo).substring(0,10)).append("'");
		List<Map<String,Object>> list = this.queryBySql(stringBuffer.toString(), null);
		List<FnRecordPostPay> resultList = new ArrayList<>();
		if(list!=null&&list.size()>0){
			for (Map<String, Object> result : list) {
				FnRecordPostPay recordPostPay = new FnRecordPostPay();
				recordPostPay.setId((String) result.get("c_id"));
				recordPostPay.setTenantId((String) result.get("c_tenant_id"));
				recordPostPay.setBuildingId((String) result.get("c_building_id"));
				recordPostPay.setBuildingName((String) result.get("c_building_name"));
				recordPostPay.setEnergyTypeId((String) result.get("c_energy_type_id"));
				recordPostPay.setTenantName((String) result.get("c_tenant_name"));
				recordPostPay.setOrderId((String) result.get("c_order_id"));
				recordPostPay.setUserId((String) result.get("c_user_id"));
				recordPostPay.setOperateTime((Date) result.get("c_operate_time"));
				recordPostPay.setOrderTime((String) result.get("c_order_time"));
				recordPostPay.setRoomIds((String) result.get("c_room_ids"));
				recordPostPay.setCreateTime((Date) result.get("c_create_time"));
				recordPostPay.setOrderId((String) result.get("c_order_id"));
				Object money = result.get("c_money");
				recordPostPay.setMoney(Double.valueOf(money.toString()));
				recordPostPay.setUserName((String) result.get("c_user_name"));
				resultList.add(recordPostPay);
			}
			return resultList;
		}
	
		return null;	
	}

}
