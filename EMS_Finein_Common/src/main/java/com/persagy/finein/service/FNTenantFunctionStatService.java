package com.persagy.finein.service;

import com.persagy.finein.enumeration.EnumStatTimeType;
import com.persagy.finein.enumeration.EnumStatType;

import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantFunctionStatService extends FNBaseService {
	
	public void saveData(String buildingId, String tenantId, EnumStatTimeType timeType, EnumStatType statType, int functionId, Date timeFrom, Double data) throws Exception;
	
}
