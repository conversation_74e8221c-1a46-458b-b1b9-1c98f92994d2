package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnMeterProperty;
import com.persagy.finein.service.FNMeterPropertyService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNMeterPropertyService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNMeterPropertyServiceImpl extends FNBaseServiceImpl  implements FNMeterPropertyService{

	@Override
	public FnMeterProperty query(String meterId, String propertyName) throws Exception {
		FnMeterProperty query = new FnMeterProperty();
		query.setMeterId(meterId);
		query.setPropertyName(propertyName);
		return (FnMeterProperty)this.queryObject(query);
	}

	@Override
	public void saveData(String meterId, String propertyName, String propertyValue) throws Exception {
		FnMeterProperty query = new FnMeterProperty();
		query.setMeterId(meterId);
		query.setPropertyName(propertyName);
		this.remove(query);
		query.setPropertyValue(propertyValue);
		query.setId(UUID.randomUUID().toString());
		this.save(query);
	}
}
