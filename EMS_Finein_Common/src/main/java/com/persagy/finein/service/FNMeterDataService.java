package com.persagy.finein.service;

import com.persagy.ems.pojo.meterdata.MeterData;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNMeterDataService extends FNBaseService{
	
	public List<MeterData> queryMeterDataGteLt(String buildingId, String meterId, int funcId, int timeType, Date timeFrom, Date timeTo) throws Exception;

	public Date queryMeterComputeTime(String buildingId, String meterId, int functionId) throws Exception;

	public MeterData queryMeterDataEqual(String buildingId, String meterId, int funcId, int timeType, Date timeFrom) throws Exception;

	public void saveMeterComputeTime(String buildingId, String meterId, int functionId, Date computeTime) throws Exception;

	public void removeMeterDataGte(String buildingId, String meterId, int funcId, int timeType, Date timeFrom) throws Exception;

	public void saveUpdateMeterComputeTime(String buildingId, String meterId, int functionId, Date computeTime) throws Exception;
	
}
