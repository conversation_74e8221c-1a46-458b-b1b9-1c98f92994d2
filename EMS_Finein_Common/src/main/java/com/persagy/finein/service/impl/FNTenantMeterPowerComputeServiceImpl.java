package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantMeterPowerCompute;
import com.persagy.finein.enumeration.EnumStatType;
import com.persagy.finein.service.FNTenantMeterPowerComputeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNTenantMeterPowerComputeService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNTenantMeterPowerComputeServiceImpl extends FNBaseServiceImpl
		implements FNTenantMeterPowerComputeService {

	@Override
	public FnTenantMeterPowerCompute query(String buildingId, String tenantId, String meterId, EnumStatType statType)
			throws Exception {
		FnTenantMeterPowerCompute query = new FnTenantMeterPowerCompute();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setDataType(statType.getValue());
		return (FnTenantMeterPowerCompute) this.queryObject(query);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void save(String buildingId, String tenantId, String meterId, EnumStatType statType, Date lastComputeTime)
			throws Exception {
		FnTenantMeterPowerCompute query = new FnTenantMeterPowerCompute();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setDataType(statType.getValue());
		this.remove(query);
		query.setLastComputeTime(lastComputeTime);
		query.setLastUpdateTime(new Date());
		this.save(query);
	}

	@Override
	public void update(List<FnTenantMeterPowerCompute> powerSaveList) throws Exception {
		if (powerSaveList != null && powerSaveList.size() > 0) {
			for (FnTenantMeterPowerCompute save : powerSaveList) {
				FnTenantMeterPowerCompute query = new FnTenantMeterPowerCompute();
				query.setBuildingId(save.getBuildingId());
				query.setTenantId(save.getTenantId());
				query.setMeterId(save.getMeterId());
				query.setDataType(save.getDataType());
				this.remove(query);
			}
		}
		this.save(powerSaveList);

	}
}
