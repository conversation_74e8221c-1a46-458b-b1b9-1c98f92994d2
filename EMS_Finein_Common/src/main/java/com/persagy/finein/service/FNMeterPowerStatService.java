package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnMeterPowerStat;
import com.persagy.finein.enumeration.EnumStatType;

import java.util.Date;
import java.util.List;

public interface FNMeterPowerStatService extends FNBaseService {

	public void saveDataList(List<FnMeterPowerStat> saveList) throws Exception;

	public void saveData(String meterId, EnumStatType statType, Date timeFrom, Double data) throws Exception;

	public List<FnMeterPowerStat> queryListGteLte(String id, Date timeFrom, Date timeTo);
	
	
}
