package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantMeterFunctionStat;
import com.persagy.finein.enumeration.EnumStatTimeType;
import com.persagy.finein.enumeration.EnumStatType;
import com.persagy.finein.service.FNTenantMeterFunctionStatService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantMeterFunctionStatService")
@Transactional(propagation=Propagation.NOT_SUPPORTED)
public class FNTenantMeterFunctionStatServiceImpl extends FNBaseServiceImpl  implements FNTenantMeterFunctionStatService{

	@Override
	public void saveData(String buildingId, String tenantId, String meterId, EnumStatTimeType timeType,
			EnumStatType statType, int functionId, Date timeFrom, Double data) throws Exception {
		FnTenantMeterFunctionStat query = new FnTenantMeterFunctionStat();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setTimeType(timeType.getValue());
		query.setDataType(statType.getValue());
		query.setFunctionId(functionId);
		this.remove(query);
		query.setTimeFrom(timeFrom);
		query.setData(data);
		query.setLastUpdateTime(new Date());
		this.save(query);
	}

	@Override
	public FnTenantMeterFunctionStat queryHistoryMax(String buildingId, String tenantId, String meterId, int functionId) throws Exception {
		FnTenantMeterFunctionStat query = new FnTenantMeterFunctionStat();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setTimeType(EnumStatTimeType.History.getValue());
		query.setDataType(EnumStatType.Max.getValue());
		query.setFunctionId(functionId);
		return (FnTenantMeterFunctionStat)this.queryObject(query);
	}
}
