package com.persagy.finein.service.impl;

import com.persagy.core.mvc.pojo.BusinessObject;
import com.persagy.core.mvc.service.CoreServiceImpl;
import com.persagy.finein.service.FNBaseService;

import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

public class FNBaseServiceImpl extends CoreServiceImpl implements FNBaseService{

	@Override
	public BusinessObject queryObject(BusinessObject t) throws Exception {
		List<BusinessObject> list = this.query(t);
		if(list != null && list.size() > 0){
			return list.get(0);
		}
		return null;
	}

}
