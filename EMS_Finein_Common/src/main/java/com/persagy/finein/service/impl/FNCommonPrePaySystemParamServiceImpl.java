package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnCommonPrePaySystemParam;
import com.persagy.finein.service.FNCommonPrePaySystemParamService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 说明:通用充值系统参数表
 */

@Service("FNCommonPrePaySystemParamService")
public class FNCommonPrePaySystemParamServiceImpl extends FNBaseServiceImpl
		implements FNCommonPrePaySystemParamService {

	@Override
	public Boolean checkEncryptionKey(String systemCode, String encryptionKey) throws Exception {
		FnCommonPrePaySystemParam query = new FnCommonPrePaySystemParam();
		query.setSystemCode(systemCode);
		List<FnCommonPrePaySystemParam> list = (List<FnCommonPrePaySystemParam>) this.query(query);
		if (list != null && list.size() > 0) {
			FnCommonPrePaySystemParam param = list.get(0);
			if (encryptionKey.equals(param.getEncryptionKey())) {
				return true;
			}
		}
		return false;
	}

	@Override
	public FnCommonPrePaySystemParam queryByCode(String systemCode) throws Exception {
		FnCommonPrePaySystemParam query = new FnCommonPrePaySystemParam();
		query.setSystemCode(systemCode);
		List<FnCommonPrePaySystemParam> list = (List<FnCommonPrePaySystemParam>) this.query(query);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

}
