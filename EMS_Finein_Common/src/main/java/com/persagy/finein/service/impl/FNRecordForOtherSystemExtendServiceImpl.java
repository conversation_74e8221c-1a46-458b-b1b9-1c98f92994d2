package com.persagy.finein.service.impl;


import com.persagy.ems.pojo.finein.FnRecordForOtherSystemExtend;
import com.persagy.finein.service.FNRecordForOtherSystemExtendService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* 说明:
*/

@Service("FNRecordForOtherSystemExtendService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNRecordForOtherSystemExtendServiceImpl extends FNBaseServiceImpl  implements FNRecordForOtherSystemExtendService{

	@Override
	public FnRecordForOtherSystemExtend queryById(String id) throws Exception {
		FnRecordForOtherSystemExtend query = new FnRecordForOtherSystemExtend();
		query.setId(id);
		List<FnRecordForOtherSystemExtend> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0);
		}
		return null;
	}

}
