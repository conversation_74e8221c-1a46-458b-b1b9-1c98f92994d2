package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;
import com.persagy.finein.service.FNAlarmLimitGlobalService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNAlarmLimitGlobalService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNAlarmLimitGlobalServiceImpl extends FNBaseServiceImpl implements FNAlarmLimitGlobalService{


	@Override
	public List<FnAlarmLimitGlobal> queryList() throws Exception {
		return this.query(new FnAlarmLimitGlobal());
	}


	@Override
	public FnAlarmLimitGlobal query(String alarmTypeId) throws Exception {
		FnAlarmLimitGlobal query = new FnAlarmLimitGlobal();
		query.setAlarmTypeId(alarmTypeId);
		return (FnAlarmLimitGlobal)queryObject(query);
	}


	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void saveObject(FnAlarmLimitGlobal obj) throws Exception {
		FnAlarmLimitGlobal query = new FnAlarmLimitGlobal();
		query.setAlarmTypeId(obj.getAlarmTypeId());
		
		FnAlarmLimitGlobal result = (FnAlarmLimitGlobal)this.queryObject(query);
		if(result == null){
			this.save(obj);
		}else{
			FnAlarmLimitGlobal update = new FnAlarmLimitGlobal();
			update.setLimitValue(obj.getLimitValue());
			this.update(query, update);
		}
	}


	@Override
	public Map<String, FnAlarmLimitGlobal> queryMap() throws Exception {
		Map<String,FnAlarmLimitGlobal> globalMap = new HashMap<>();
		List<FnAlarmLimitGlobal> list = this.queryList();
		if(list != null){
			for(FnAlarmLimitGlobal global : list){
				globalMap.put(global.getAlarmTypeId(), global);
			}
		}
		return globalMap;
	}
	
}
