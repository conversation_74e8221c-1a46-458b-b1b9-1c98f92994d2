package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantMeterAvg;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumPayBodyType;

import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantMeterAvgService extends FNBaseService {
	
	public void save(String buildingId, String tenantId, String energyTypeId, EnumPayBodyType payBodyType, String bodyId, EnumEnergyMoney energyMoney, Date timeFrom, Date timeTo, int dataCount, Double data) throws Exception ;

	public FnTenantMeterAvg queryData(String buildingId, String tenantId, EnumPayBodyType bodyType, String bodyId, String energyTypeId, EnumEnergyMoney energyMoney) throws Exception;
	
}
