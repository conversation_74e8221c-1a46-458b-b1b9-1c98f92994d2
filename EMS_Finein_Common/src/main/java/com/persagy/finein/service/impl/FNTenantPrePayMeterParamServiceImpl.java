package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantPrePayMeterParam;
import com.persagy.finein.service.FNTenantPrePayMeterParamService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantPrePayMeterParamService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantPrePayMeterParamServiceImpl extends FNBaseServiceImpl  implements FNTenantPrePayMeterParamService{

	@Override
	public Map<String,Map<String,FnTenantPrePayMeterParam>> queryTenantPreParam(String tenantId) throws Exception {
		Map<String, Map<String,FnTenantPrePayMeterParam>> result = new HashMap<String, Map<String,FnTenantPrePayMeterParam>>();
		FnTenantPrePayMeterParam query = new FnTenantPrePayMeterParam();
		query.setTenantId(tenantId);
		
		List<FnTenantPrePayMeterParam> list = this.query(query);
		if(list != null){
			for(FnTenantPrePayMeterParam param : list){
				if(!result.containsKey(param.getEnergyTypeId())){
					result.put(param.getEnergyTypeId(), new HashMap<String,FnTenantPrePayMeterParam>());
				}
				result.get(param.getEnergyTypeId()).put(param.getMeterId(),param);
			}
		}
		return result;
	}

	@Override
	public void removeTenantParam(String tenantId) throws Exception {
		FnTenantPrePayMeterParam query = new FnTenantPrePayMeterParam();
		query.setTenantId(tenantId);
		this.remove(query);
	}

	@Override
	public FnTenantPrePayMeterParam queryMeterPreParam(String tenantId, String meterId, String energyTypeId)
			throws Exception {
		FnTenantPrePayMeterParam query = new FnTenantPrePayMeterParam();
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setEnergyTypeId(energyTypeId);
		return (FnTenantPrePayMeterParam)this.queryObject(query);
	}

}
