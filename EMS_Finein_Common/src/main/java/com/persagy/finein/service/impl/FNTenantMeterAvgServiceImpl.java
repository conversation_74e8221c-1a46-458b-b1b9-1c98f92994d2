package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantMeterAvg;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumPayBodyType;
import com.persagy.finein.service.FNTenantMeterAvgService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantMeterAvgService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantMeterAvgServiceImpl extends FNBaseServiceImpl  implements FNTenantMeterAvgService{

	@Override
	public void save(String buildingId,String tenantId, String energyTypeId, EnumPayBodyType payBodyType, String bodyId,
			EnumEnergyMoney energyMoney, Date timeFrom, Date timeTo, int dataCount, Double data) throws Exception {
		FnTenantMeterAvg query = new FnTenantMeterAvg();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setPayBodyType(payBodyType.getValue());
		query.setBodyId(bodyId);
		query.setDataType(energyMoney.getValue());
		this.remove(query);
		query.setTimeFrom(timeFrom);
		query.setTimeTo(timeTo);
		query.setDataCount(dataCount);
		query.setData(data);
		query.setLastUpdateTime(new Date());
		this.save(query);
	}

	@Override
	public FnTenantMeterAvg queryData(String buildingId,String tenantId, EnumPayBodyType bodyType, String bodyId, String energyTypeId,
			EnumEnergyMoney energyMoney) throws Exception {
		FnTenantMeterAvg query = new FnTenantMeterAvg();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setPayBodyType(bodyType.getValue());
		query.setBodyId(bodyId);
		query.setDataType(energyMoney.getValue());
		return (FnTenantMeterAvg)this.queryObject(query);
	}
}
