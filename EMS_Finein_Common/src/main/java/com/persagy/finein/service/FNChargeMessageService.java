package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnChargeMessage;
import com.persagy.ems.pojo.finein.FnRecordPrePay;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.finein.enumeration.EnumMessageSendStatus;

import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:31:53
 * 
 * 说明:
 */

public interface FNChargeMessageService extends FNBaseService {

	public List<FnChargeMessage> queryMessageList(EnumMessageSendStatus status, long limit) throws Exception;

	public void sendSuccess(String messageId, int tryTimes) throws Exception;

	public void sendFailure(String messageId, String exceptionReason, int tryTimes) throws Exception;

	public void sendOverdue(String messageId) throws Exception;

	public void paySendMessage(FnRecordPrePay record, Double payAfter, FnTenant tenant,
                               String perPaySuccessTemplate, String messageSignature) throws Exception;

	void sendBlack(String messageId) throws Exception;
}
