package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantFunctionData;
import com.persagy.finein.enumeration.EnumStatTimeType;
import com.persagy.finein.enumeration.EnumStatType;
import com.persagy.finein.service.FNTenantFunctionDataService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantFunctionDataService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantFunctionDataServiceImpl extends FNBaseServiceImpl  implements FNTenantFunctionDataService{

	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void saveData(String buildingId, String tenantId, EnumStatTimeType timeType, EnumStatType statType,
			int functionId, Date timeFrom, Double data) throws Exception {
		FnTenantFunctionData query = new FnTenantFunctionData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setTimeType(timeType.getValue());
		query.setDataType(statType.getValue());
		query.setFunctionId(functionId);
		query.setTimeFrom(timeFrom);
		this.remove(query);
		query.setData(data);
		query.setLastUpdateTime(new Date());
		this.save(query);
	}
}
