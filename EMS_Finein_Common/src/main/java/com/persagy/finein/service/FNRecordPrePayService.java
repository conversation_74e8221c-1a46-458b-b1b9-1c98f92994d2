package com.persagy.finein.service;

import com.persagy.ems.dto.DTORecordPrePayReturn;
import com.persagy.ems.pojo.finein.FnRecordPrePay;
import com.persagy.finein.enumeration.EnumPayBodyType;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNRecordPrePayService extends FNBaseService {
	
	public List<FnRecordPrePay> queryTenantPrePayRecord(String tenantId, String energyTypeId, Date timeFrom, Date timeTo) throws Exception;

	public List<FnRecordPrePay> queryTenantPrePayRecord(List<String> tenantIdList, EnumPayBodyType payBodyType, String energyTypeId, Date timeFrom, Date timeTo) throws Exception;

	public List<FnRecordPrePay> queryTenantPrePayRecord(String tenantId, EnumPayBodyType payBodyType, String energyTypeId, String meterId,
                                                        Date from, Date to)throws Exception;

	public List<FnRecordPrePay> queryByBuildingId(String buildingId, String energyTypeId, Date from, Date to) throws Exception;

	public List<DTORecordPrePayReturn> queryByBuilding(String buildingId, String energyTypeId, Date from, Date to)
			throws Exception;
}
