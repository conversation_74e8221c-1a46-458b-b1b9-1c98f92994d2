package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FNTenantCloudMeter;
import com.persagy.finein.service.FNTenantCloudMeterService;
import org.springframework.stereotype.Service;

import java.util.List;

/**

* 说明:云客户端租户租赁业态
*/

@Service("FNTenantCloudMeterService")
public class FNTenantCloudMeterServiceImpl extends FNBaseServiceImpl  implements FNTenantCloudMeterService{

	@Override
	public Integer queryCloudMeterId(String buildingId, String tenantId) throws Exception {
		FNTenantCloudMeter query = new FNTenantCloudMeter();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		List<FNTenantCloudMeter> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0).getCloudMeterId();
		}
		return null;
	}

	
}
