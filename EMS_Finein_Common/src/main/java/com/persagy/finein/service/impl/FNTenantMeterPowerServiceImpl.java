package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantMeterPower;
import com.persagy.finein.enumeration.EnumBodyType;
import com.persagy.finein.enumeration.EnumStatType;
import com.persagy.finein.service.FNTenantMeterPowerService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service("FNTenantMeterPowerService")
@Transactional(propagation=Propagation.NOT_SUPPORTED)
public class FNTenantMeterPowerServiceImpl extends FNBaseServiceImpl  implements FNTenantMeterPowerService{

	@Override
	public void saveDataList(List<FnTenantMeterPower> saveList) throws Exception {
		for (FnTenantMeterPower stat : saveList) {
			FnTenantMeterPower remove = new FnTenantMeterPower();
			remove.setBuildingId(stat.getBuildingId());
			remove.setBodyType(stat.getBodyType());
			remove.setTenantId(stat.getTenantId());
			remove.setBodyCode(stat.getBodyCode());
			remove.setDataType(stat.getDataType());
			this.remove(remove);
		}
		this.save(saveList);
	}

	@Override
	public void saveData(String buildingId, String tenantId, EnumBodyType bodyType,String bodyCode, EnumStatType statType, Date timeFrom,
			Double data) throws Exception {
		FnTenantMeterPower query = new FnTenantMeterPower();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setBodyType(bodyType.getValue());
		query.setBodyCode(bodyCode);
		query.setDataType(statType.getValue());
		this.remove(query);
		query.setData(data);
		query.setTimeFrom(timeFrom);
		query.setLastUpdateTime(new Date());
		this.save(query);
	}

}
