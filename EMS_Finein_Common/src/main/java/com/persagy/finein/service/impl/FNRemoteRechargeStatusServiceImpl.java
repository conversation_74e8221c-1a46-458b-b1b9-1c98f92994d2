package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnRemoteRechargeStatus;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNRemoteRechargeStatusService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNRemoteRechargeStatusService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNRemoteRechargeStatusServiceImpl extends FNBaseServiceImpl implements FNRemoteRechargeStatusService {

	@Override
	public Map<String, FnRemoteRechargeStatus> queryMap() throws Exception {
		Map<String, FnRemoteRechargeStatus> map = new HashMap<String, FnRemoteRechargeStatus>();
		List<FnRemoteRechargeStatus> list = this.query(new FnRemoteRechargeStatus());
		if (list != null && list.size() > 0) {
			for (FnRemoteRechargeStatus fnRemoteRechargeStatus : list) {
				map.put(fnRemoteRechargeStatus.getTenantFlag(), fnRemoteRechargeStatus);
			}
		}
		return map;
	}

	@Override
	public Map<String, FnRemoteRechargeStatus> queryMap(String buildingId, EnumYesNo no) throws Exception {
		Map<String, FnRemoteRechargeStatus> map = new HashMap<String, FnRemoteRechargeStatus>();
		FnRemoteRechargeStatus query = new FnRemoteRechargeStatus();
		query.setBuildingId(buildingId);
		query.setRemoteRechargeStatus(EnumYesNo.NO.getValue());
		List<FnRemoteRechargeStatus> list = this.query(query);
		if (list != null && list.size() > 0) {
			for (FnRemoteRechargeStatus fnRemoteRechargeStatus : list) {
				map.put(fnRemoteRechargeStatus.getTenantId(), fnRemoteRechargeStatus);
			}
		}
		return map;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void updateStatus(List<String> updateList, EnumYesNo yesno) throws Exception {
		FnRemoteRechargeStatus query = new FnRemoteRechargeStatus();
		query.setSpecialOperation("tenantFlag", SpecialOperator.$in, updateList);
		
		FnRemoteRechargeStatus update = new FnRemoteRechargeStatus();
		update.setRemoteRechargeStatus(yesno.getValue());
		this.update(query, update);
	}

}
