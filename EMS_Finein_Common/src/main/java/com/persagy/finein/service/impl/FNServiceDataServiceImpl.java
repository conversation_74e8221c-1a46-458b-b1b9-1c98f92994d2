package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.servicedata.ServiceData;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.service.FNServiceDataService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNServiceDataService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNServiceDataServiceImpl extends FNBaseServiceImpl implements FNServiceDataService{


	@Override
	public List<ServiceData> queryServiceDataGteLt(String buildingId, String meterId, int funcId,EnumTimeType timeType, Date timeFrom,
			Date timeTo) throws Exception {
		ServiceData query = new ServiceData();
		query.setBuildingForContainer(buildingId);
		query.setSign(meterId);
		query.setFuncid(funcId);
		query.setSplitTimeType(timeType.getValue());
		query.setSpecialOperation("timefrom", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("timefrom", SpecialOperator.$lt, timeTo);
		return this.query(query);
	}

	@Override
	public void removeServiceDataGte(String buildingId, String meterId, int funcId, int timeType, Date timeFrom)
			throws Exception {
		ServiceData query = new ServiceData();
		query.setBuildingForContainer(buildingId);
		query.setSign(meterId);
		query.setFuncid(funcId);
		query.setSplitTimeType(timeType);
		query.setSpecialOperation("timefrom", SpecialOperator.$gte, timeFrom);
		this.remove(query);
	}

}
