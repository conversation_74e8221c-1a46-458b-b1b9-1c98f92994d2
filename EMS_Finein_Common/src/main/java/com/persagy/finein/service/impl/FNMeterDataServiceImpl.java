package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.meterdata.MeterComputeTime;
import com.persagy.ems.pojo.meterdata.MeterData;
import com.persagy.finein.service.FNMeterDataService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNMeterDataService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNMeterDataServiceImpl extends FNBaseServiceImpl implements FNMeterDataService {

	@Override
	public List<MeterData> queryMeterDataGteLt(String buildingId, String meterId, int funcId, int timeType,
			Date timeFrom, Date timeTo) throws Exception {
		MeterData query = new MeterData();
		query.setBuildingForContainer(buildingId);
		query.setSign(meterId);
		query.setFuncid(funcId);
		query.setSplitTimeType(timeType);
		query.setSort("receivetime", EMSOrder.Asc);
		query.setSpecialOperation("receivetime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("receivetime", SpecialOperator.$lt, timeTo);
		return this.query(query);
	}

	@Override
	public Date queryMeterComputeTime(String buildingId, String meterId, int functionId) throws Exception {
		MeterComputeTime query = new MeterComputeTime();
		query.setBuildingForContainer(buildingId);
		query.setMeterSign(meterId);
		query.setFunctionId(functionId);
		MeterComputeTime reuslt = (MeterComputeTime) this.queryObject(query);
		if (reuslt != null) {
			return reuslt.getValue();
		}
		return null;
	}

	@Override
	public MeterData queryMeterDataEqual(String buildingId, String meterId, int funcId, int timeType, Date timeFrom)
			throws Exception {
		MeterData query = new MeterData();
		query.setBuildingForContainer(buildingId);
		query.setSign(meterId);
		query.setFuncid(funcId);
		query.setSplitTimeType(timeType);
		query.setReceivetime(timeFrom);
		return (MeterData) this.queryObject(query);
	}

	@Override
	public void saveMeterComputeTime(String buildingId, String meterId, int functionId, Date computeTime)
			throws Exception {
		MeterComputeTime query = new MeterComputeTime();
		query.setName("processtime");
		query.setBuildingForContainer(buildingId);
		query.setMeterSign(meterId);
		query.setFunctionId(functionId);
		MeterComputeTime reuslt = (MeterComputeTime) this.queryObject(query);
		if (reuslt != null) {
			MeterComputeTime update = new MeterComputeTime();
			update.setBuildingForContainer(buildingId);
			update.setValue(computeTime);
			this.update(query, update);
		}
	}

	@Override
	public void removeMeterDataGte(String buildingId, String meterId, int funcId, int timeType, Date timeFrom)
			throws Exception {
		MeterData query = new MeterData();
		query.setBuildingForContainer(buildingId);
		query.setSign(meterId);
		query.setFuncid(funcId);
		query.setSplitTimeType(timeType);
		query.setSpecialOperation("receivetime", SpecialOperator.$gte, timeFrom);
		this.remove(query);
	}

	@Override
	public void saveUpdateMeterComputeTime(String buildingId, String meterId, int functionId, Date computeTime)
			throws Exception {

		MeterComputeTime query = new MeterComputeTime();
		query.setName("processtime");
		query.setBuildingForContainer(buildingId);
		query.setMeterSign(meterId);
		query.setFunctionId(functionId);
		MeterComputeTime reuslt = (MeterComputeTime) this.queryObject(query);
		if (reuslt != null) {
			MeterComputeTime update = new MeterComputeTime();
			update.setBuildingForContainer(buildingId);
			update.setValue(computeTime);
			this.update(query, update);
		} else {
			query.setValue(computeTime);
			this.save(query);
		}

	}
}
