package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnAlarmLimitCustomObj;
import com.persagy.finein.service.FNAlarmLimitCustomObjService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNAlarmLimitCustomObjService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNAlarmLimitCustomObjServiceImpl extends FNBaseServiceImpl implements FNAlarmLimitCustomObjService{

	@Override
	public List<FnAlarmLimitCustomObj> queryList(String buildingId, String tenantId) throws Exception {
		FnAlarmLimitCustomObj query = new FnAlarmLimitCustomObj();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		return this.query(query);
	}

	@Override
	public FnAlarmLimitCustomObj queryList(String buildingId, String tenantId, String alarmTypeId) throws Exception {
		FnAlarmLimitCustomObj query = new FnAlarmLimitCustomObj();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setAlarmTypeId(alarmTypeId);
		return (FnAlarmLimitCustomObj)this.queryObject(query);
	}
}
