package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnSSOSSystemParam;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNSSOSSystemParamService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 说明:通用充值系统参数表
 */

@Service("FNSSOSSystemParamService")
public class FNSSOSSystemParamServiceImpl extends FNBaseServiceImpl implements FNSSOSSystemParamService {

	@Override
	public Boolean checkEncryptionKey(String systemCode, String encryptionKey) throws Exception {
		FnSSOSSystemParam query = new FnSSOSSystemParam();
		query.setSystemCode(systemCode);
		List<FnSSOSSystemParam> list = (List<FnSSOSSystemParam>) this.query(query);
		if (list != null && list.size() > 0) {
			FnSSOSSystemParam param = list.get(0);
			if (encryptionKey.equals(param.getEncryptionKey())) {
				return true;
			}
		}
		return false;
	}

	@Override
	public FnSSOSSystemParam queryByCode(String systemCode) throws Exception {
		FnSSOSSystemParam query = new FnSSOSSystemParam();
		query.setSystemCode(systemCode);
		query.setIsValid(EnumYesNo.YES.getValue());
		List<FnSSOSSystemParam> list = (List<FnSSOSSystemParam>) this.query(query);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

}
