package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.FnFloor;
import com.persagy.finein.service.FNFloorService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNFloorService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNFloorServiceImpl extends FNBaseServiceImpl implements FNFloorService{

	
	@Override
	public List<FnFloor> queryList(String buildingId,EMSOrder order) throws Exception {
		FnFloor query = new FnFloor();
		query.setBuildingId(buildingId);
		query.setSort("orderBy", order);
		return this.query(query);
	}
}
