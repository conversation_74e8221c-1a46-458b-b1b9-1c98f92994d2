package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantMeterComputeTemp;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantMeterComputeTempService extends FNBaseService{
	
	public List<FnTenantMeterComputeTemp> queryByBuilding(String buildingId) throws Exception ;
	
	public void saveComputeTime(String buildingId, String tenantId, String meterId, int functionId, Date lastComputeTime) throws Exception ;

	public void removeComputeTime(String buildingId, String tenantId, String meterId, int functionId) throws Exception ;
}
