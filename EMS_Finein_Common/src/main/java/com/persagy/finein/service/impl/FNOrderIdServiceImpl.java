package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnOrderId;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.service.FNOrderIdService;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNOrderIdService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNOrderIdServiceImpl extends FNBaseServiceImpl implements FNOrderIdService{

	@Override
	public synchronized String queryOrderId(int payType, Date timeFrom) throws Exception {
		FnOrderId query = new FnOrderId();
		query.setPayType(payType);
		query.setTimeFrom(DateUtils.truncate(timeFrom, Calendar.DATE));
		FnOrderId fnOrderId = (FnOrderId)this.queryObject(query);
		int sequence = 1;
		if(fnOrderId == null){
			query.setSequence(sequence);
			this.save(query);
		}else{
			sequence = fnOrderId.getSequence()+1;
			FnOrderId update = new FnOrderId();
			update.setSequence(sequence);
			this.update(query, update);
		}
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		DecimalFormat df=new DecimalFormat("0000");
		if(payType == EnumPayType.POSTPAY.getValue().intValue()){
			return "001"+sdf.format(timeFrom)+df.format(sequence);
		}else if(payType == EnumPayType.PREPAY.getValue().intValue()){
			return "002"+sdf.format(timeFrom)+df.format(sequence);
		}else{
			throw new Exception("不支持的付费类型:"+payType);
		}
	}


}
