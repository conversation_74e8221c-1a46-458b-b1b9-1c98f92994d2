package com.persagy.finein.service.impl;


import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnBuildingEnergyRemainStat;
import com.persagy.finein.service.FNBuildingEnergyRemainStatService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service("FNBuildingEnergyRemainStatService")
public class FNBuildingEnergyRemainStatServiceImpl extends FNBaseServiceImpl implements FNBuildingEnergyRemainStatService {

	@Override
	public Date queryTimefromOrder(String buildingId, Date timeFrom, Date timeTo) throws Exception {
		FnBuildingEnergyRemainStat query = new FnBuildingEnergyRemainStat();
		query.setBuildingId(buildingId);
		query.setSpecialOperation("lastUpdateTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("lastUpdateTime", SpecialOperator.$lt, timeTo);
		query.setSort("timeFrom", EMSOrder.Asc);
		query.setLimit((long) 1);
		List<FnBuildingEnergyRemainStat> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0).getTimeFrom();
		}
		return null;
	}

	@Override
	public List<FnBuildingEnergyRemainStat> queryByBuilding(String buildingId, Date timeFrom, Date timeTo) throws Exception {
		FnBuildingEnergyRemainStat query = new FnBuildingEnergyRemainStat();
		query.setBuildingId(buildingId);
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("timeFrom", SpecialOperator.$lt, timeTo);
		return this.query(query);
	}

}
