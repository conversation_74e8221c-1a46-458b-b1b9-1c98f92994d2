package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantMeterRemainDays;
import com.persagy.finein.enumeration.EnumPayBodyType;
import com.persagy.finein.service.FNTenantMeterRemainDaysService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNTenantMeterRemainDaysService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNTenantMeterRemainDaysServiceImpl extends FNBaseServiceImpl implements FNTenantMeterRemainDaysService {

	@Override
	public Map<String, FnTenantMeterRemainDays> queryTenantRemainDays(String buildingId, String energyTypeId)
			throws Exception {
		Map<String, FnTenantMeterRemainDays> result = new HashMap<String, FnTenantMeterRemainDays>();
		FnTenantMeterRemainDays query = new FnTenantMeterRemainDays();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyTypeId);
		List<FnTenantMeterRemainDays> list = this.query(query);
		if (list != null) {
			for (FnTenantMeterRemainDays entity : list) {
				result.put(entity.getTenantId(), entity);
			}
		}
		return result;
	}

	@Override
	public void saveData(String buildingId, String tenantId, String energyTypeId, EnumPayBodyType payBodyType,
			String bodyId, Integer maxDays, Integer minDays, Double remainData) throws Exception {
		FnTenantMeterRemainDays query = new FnTenantMeterRemainDays();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setPayBodyType(payBodyType.getValue());
		query.setBodyId(bodyId);
		this.remove(query);
		query.setMaxDays(maxDays);
		query.setMinDays(minDays);
		query.setRemainData(remainData);
		query.setLastComputeTime(new Date());

		this.save(query);
	}

	@Override
	public FnTenantMeterRemainDays queryRemainDays(String buildingId, String tenantId, EnumPayBodyType payBodyType,
			String bodyId, String energyTypeId) throws Exception {
		FnTenantMeterRemainDays query = new FnTenantMeterRemainDays();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setPayBodyType(payBodyType.getValue());
		query.setBodyId(bodyId);
		return (FnTenantMeterRemainDays) this.queryObject(query);
	}

	@Override
	public Map<String, FnTenantMeterRemainDays> queryTenantRemainDays(String buildingId, String energyTypeId,
			EnumPayBodyType payBodyType) throws Exception {
		Map<String, FnTenantMeterRemainDays> result = new HashMap<String, FnTenantMeterRemainDays>();
		FnTenantMeterRemainDays query = new FnTenantMeterRemainDays();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyTypeId);
		query.setPayBodyType(payBodyType.getValue());
		List<FnTenantMeterRemainDays> list = this.query(query);
		if (list != null) {
			for (FnTenantMeterRemainDays entity : list) {
				result.put(entity.getBodyId(), entity);
			}
		}
		return result;
	}

	@Override
	public List<FnTenantMeterRemainDays> queryList(String buildingId, String tenantId, EnumPayBodyType payBodyType,
			String bodyId, String energyTypeId) throws Exception {
		FnTenantMeterRemainDays query = new FnTenantMeterRemainDays();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setPayBodyType(payBodyType.getValue());
		query.setBodyId(bodyId);
		return this.query(query);
	}

}
