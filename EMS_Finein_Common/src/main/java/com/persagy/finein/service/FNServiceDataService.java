package com.persagy.finein.service;

import com.persagy.ems.pojo.servicedata.ServiceData;
import com.persagy.finein.enumeration.EnumTimeType;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNServiceDataService extends FNBaseService {
	
	public List<ServiceData> queryServiceDataGteLt(String buildingId, String meterId, int funcId, EnumTimeType timeType, Date timeFrom, Date timeTo) throws Exception;

	public void removeServiceDataGte(String buildingId, String meterId, int funcId, int timeType, Date timeFrom) throws Exception;
	
}
