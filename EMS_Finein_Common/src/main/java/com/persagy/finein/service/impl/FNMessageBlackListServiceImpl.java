package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnMessageBlackList;
import com.persagy.finein.service.FNMessageBlackListService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 
 * 说明:云端-租户信息点类型
 */

@Service("FNMessageBlackListService")
public class FNMessageBlackListServiceImpl extends FNBaseServiceImpl implements FNMessageBlackListService {

	@Override
	public Map<String, FnMessageBlackList> queryMap(String buildingId) throws Exception {
		Map<String, FnMessageBlackList> hashMap = new HashMap<String, FnMessageBlackList>();
		FnMessageBlackList query = new FnMessageBlackList();
		query.setBuildingId(buildingId);
		List<FnMessageBlackList> list = this.query(query);
		if (list != null && list.size() > 0) {
			for (FnMessageBlackList fnMessageBlackList : list) {
				hashMap.put(fnMessageBlackList.getTenantId(), fnMessageBlackList);
			}
		}
		return hashMap;
	}

}
