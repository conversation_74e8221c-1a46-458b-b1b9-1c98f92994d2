package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnAlarmType;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.service.FNAlarmTypeService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNAlarmTypeService")
public class FNAlarmTypeServiceImpl extends FNBaseServiceImpl implements FNAlarmTypeService {

	@Override
	public List<FnAlarmType> queryList(EnumValidStatus isValid) throws Exception {
		FnAlarmType query = new FnAlarmType();
		if (isValid != null) {
			query.setIsValid(isValid.getValue());
		}
		return this.query(query);
	}

	/**
	 * (non-Javadoc)
	 * 
	 * @throws Exception
	 * @see FNAlarmTypeService#queryMap()
	 */

	@Override
	public Map<String, FnAlarmType> queryMap() throws Exception {
		Map<String, FnAlarmType> map = new HashMap<>();
		FnAlarmType query = new FnAlarmType();
		query.setIsValid(EnumValidStatus.VALID.getValue());
		List<FnAlarmType> list = this.query(query);
		for (FnAlarmType fnAlarmType : list) {
			map.put(fnAlarmType.getId(), fnAlarmType);
		}
		return map;
	}

}
