package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnGridTemplate;
import com.persagy.finein.service.FnGridTemplateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service("FnGridTemplateService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FnGridTemplateServiceImpl extends FNBaseServiceImpl implements FnGridTemplateService {

	@Override
	public void removeOne(FnGridTemplate query) throws Exception {

		this.remove(query);
	}

	@Override
	public void saveOne(FnGridTemplate query) throws Exception {

		this.save(query);
	}

	@Override
	public List<FnGridTemplate> queryList(FnGridTemplate query) throws Exception {
		
		return this.query(query);
	}
	
}

