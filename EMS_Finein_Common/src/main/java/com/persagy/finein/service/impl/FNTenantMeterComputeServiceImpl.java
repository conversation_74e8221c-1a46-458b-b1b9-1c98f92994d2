package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantMeterCompute;
import com.persagy.finein.service.FNTenantMeterComputeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantMeterComputeService")
public class FNTenantMeterComputeServiceImpl extends FNBaseServiceImpl  implements FNTenantMeterComputeService{

	@Override
	public Date queryLastComputeTime(String buildingId,String tenantId, String meterId, int functionId) throws Exception {
		FnTenantMeterCompute query = new FnTenantMeterCompute();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setFunctionId(functionId);
		FnTenantMeterCompute result = (FnTenantMeterCompute)this.queryObject(query);
		if(result != null){
			return result.getLastComputeTime();
		}
		return null;
	}

	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void saveComputeTime(String buildingId,String tenantId, String meterId, int functionId, Date lastComputeTime)
			throws Exception {
		FnTenantMeterCompute saveObj = new FnTenantMeterCompute();
		saveObj.setBuildingId(buildingId);
		saveObj.setTenantId(tenantId);
		saveObj.setMeterId(meterId);
		saveObj.setFunctionId(functionId);
		this.remove(saveObj);
		saveObj.setLastComputeTime(lastComputeTime);
		saveObj.setLastUpdateTime(new Date());
		this.save(saveObj);
	}

	@Override
	public List<FnTenantMeterCompute> queryComputeTime(String meterId, int functionId) throws Exception {
		FnTenantMeterCompute query = new FnTenantMeterCompute();
		query.setMeterId(meterId);
		query.setFunctionId(functionId);
		return this.query(query);
	}
}
