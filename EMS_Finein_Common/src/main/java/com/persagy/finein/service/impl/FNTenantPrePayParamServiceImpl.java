package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantPrePayParam;
import com.persagy.finein.service.FNTenantPrePayParamService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantPrePayParamService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantPrePayParamServiceImpl extends FNBaseServiceImpl  implements FNTenantPrePayParamService{

	@Override
	public Map<String, FnTenantPrePayParam> queryTenantPreParam(String tenantId) throws Exception {
		Map<String, FnTenantPrePayParam> result = new HashMap<String, FnTenantPrePayParam>();
		FnTenantPrePayParam query = new FnTenantPrePayParam();
		query.setTenantId(tenantId);
		
		List<FnTenantPrePayParam> list = this.query(query);
		if(list != null){
			for(FnTenantPrePayParam param : list){
				result.put(param.getEnergyTypeId(), param);
			}
		}
		return result;
	}

	@Override
	public FnTenantPrePayParam queryTenantPreParam(String tenantId, String energyTypeId) throws Exception {
		FnTenantPrePayParam query = new FnTenantPrePayParam();
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		return (FnTenantPrePayParam)this.queryObject(query);
	}

	@Override
	public void removeTenantParam(String tenantId) throws Exception {
		FnTenantPrePayParam query = new FnTenantPrePayParam();
		query.setTenantId(tenantId);
		this.remove(query);
	}

	@Override
	public Map<String, FnTenantPrePayParam> queryTenantPreParamByBuilding(String buildingId, String energyTypeId)
			throws Exception {
		Map<String, FnTenantPrePayParam> result = new HashMap<String, FnTenantPrePayParam>();
		FnTenantPrePayParam query = new FnTenantPrePayParam();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyTypeId);
		
		List<FnTenantPrePayParam> list = this.query(query);
		if(list != null){
			for(FnTenantPrePayParam param : list){
				result.put(param.getTenantId(), param);
			}
		}
		return result;
	}

}
