package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.dto.DTORecordPrePayReturn;
import com.persagy.ems.pojo.finein.FnRecordReturn;
import com.persagy.ems.pojo.finein.FnTenantFlag;
import com.persagy.finein.enumeration.EnumPayBodyType;
import com.persagy.finein.enumeration.EnumRecordPrePayStatus;
import com.persagy.finein.service.FNRecordReturnService;
import com.persagy.finein.service.FNTenantFlagService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 
 * 说明:
 */

@Service("FNRecordReturnService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNRecordReturnServiceImpl extends FNBaseServiceImpl implements FNRecordReturnService {
	@Resource
	private FNTenantFlagService FNTenantFlagService;

	@Override
	public List<FnRecordReturn> queryTenantReturnRecord(String tenantId, String energyTypeId, Date timeFrom,
			Date timeTo) throws Exception {
		FnRecordReturn query = new FnRecordReturn();
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("operateTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("operateTime", SpecialOperator.$lt, timeTo);

		query.setSort("operateTime", EMSOrder.Desc);
		return this.query(query);
	}

	@Override
	public List<FnRecordReturn> queryTenantPrePayRecord(List<String> tenantIdList, EnumPayBodyType payBodyType,
			String energyTypeId, Date timeFrom, Date timeTo) throws Exception {
		FnRecordReturn query = new FnRecordReturn();
		query.setEnergyTypeId(energyTypeId);
		if (payBodyType != null) {
			query.setType(payBodyType.getValue());
		}
		query.setSpecialOperation("tenantId", SpecialOperator.$in, tenantIdList);
		query.setSpecialOperation("operateTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("operateTime", SpecialOperator.$lt, timeTo);
		query.setSort("operateTime", EMSOrder.Desc);
		return this.query(query);
	}

	@Override
	public List<DTORecordPrePayReturn> queryByBuilding(String buildingId, String energyTypeId, Date from, Date to)
			throws Exception {
		Map<String, FnTenantFlag> flagMap = FNTenantFlagService.queryFlag();
		List<DTORecordPrePayReturn> list = new ArrayList<DTORecordPrePayReturn>();
		FnRecordReturn query = new FnRecordReturn();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyTypeId);
		if (from != null) {
			query.setSpecialOperation("operateTime", SpecialOperator.$gte, from);
		}
		if (to != null) {
			query.setSpecialOperation("operateTime", SpecialOperator.$lt, to);
		}
		List<FnRecordReturn> dataList = this.query(query);
		if (dataList != null) {
			for (FnRecordReturn recordReturn : dataList) {
				{
					DTORecordPrePayReturn save = new DTORecordPrePayReturn();
					save.setBuildingId(buildingId);
					save.setBuildingName(recordReturn.getBuildingName());
					save.setOrderId(recordReturn.getOrderId());
					save.setOrderType(1);// 0充值,1退费
					save.setSource(0);// 0本地 1其他
					save.setEnergyTypeId(recordReturn.getEnergyTypeId());
					save.setMoney(recordReturn.getMoney());
					save.setAmount(recordReturn.getAmount());
					save.setBodyType(recordReturn.getType());
					save.setBodyCode(recordReturn.getCode());
					save.setOperateTime(recordReturn.getOperateTime());
					save.setOrderTime(recordReturn.getCreateTime());
					save.setStatus(EnumRecordPrePayStatus.Sucess.getValue());
					save.setUserId(recordReturn.getUserId());
					save.setUserName(recordReturn.getUserName());
					save.setTenantId(recordReturn.getTenantId());
					save.setTenantName(recordReturn.getTenantName());
					save.setSystemCode("local");
					save.setOrderTime(recordReturn.getCreateTime());
					if (flagMap.get(recordReturn.getTenantId()) != null) {
						save.setTenantFlag(flagMap.get(recordReturn.getTenantId()).getTenantFlag());
					}
					list.add(save);
				}
			}
		}
		return list;
	}

	@Override
	public List<FnRecordReturn> queryByBuildingId(String buildingId, String energyTypeId, Date from, Date to)
			throws Exception {
		FnRecordReturn query = new FnRecordReturn();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("operateTime", SpecialOperator.$gte, from);
		query.setSpecialOperation("operateTime", SpecialOperator.$lt, to);
		return this.query(query);
	}
}
