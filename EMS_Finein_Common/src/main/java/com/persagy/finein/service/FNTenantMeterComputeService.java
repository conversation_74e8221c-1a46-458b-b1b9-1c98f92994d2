package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantMeterCompute;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantMeterComputeService extends FNBaseService{
	
	public Date queryLastComputeTime(String buildingId, String tenantId, String meterId, int functionId) throws Exception ;

	public void saveComputeTime(String buildingId, String tenantId, String meterId, int functionId, Date lastComputeTime) throws Exception ;

	public List<FnTenantMeterCompute> queryComputeTime(String meterId, int functionId) throws Exception ;
}
