package com.persagy.finein.service.impl;

import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.finein.common.util.SchemaUtil;
import com.persagy.ems.pojo.finein.FnPriceTemplate;
import com.persagy.ems.pojo.finein.FnTenantPrice;
import com.persagy.finein.service.FNPriceTemplateService;
import com.persagy.finein.service.FNTenantPriceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantPriceService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantPriceServiceImpl extends FNBaseServiceImpl  implements FNTenantPriceService{

	@Resource(name = "FNPriceTemplateService")
	private FNPriceTemplateService FNPriceTemplateService;
	
	@Override
	public FnPriceTemplate query(String tenantId, String energyTypeId) throws Exception {
		FnTenantPrice query = new FnTenantPrice();
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		FnTenantPrice price = (FnTenantPrice)this.queryObject(query);
		return FNPriceTemplateService.query(price.getPriceTemplateId());
	}

	@Override
	public Map<String, String> queryPriceTemplateIdMap(String tenantId) throws Exception {
		Map<String, String> result = new HashMap<String, String>();
		FnTenantPrice query = new FnTenantPrice();
		query.setTenantId(tenantId);
		List<FnTenantPrice> priceList = this.query(query);
		if(priceList != null){
			for(FnTenantPrice price : priceList){
				result.put(price.getEnergyTypeId(), price.getPriceTemplateId());
			}
		}
		return result;
	}

	@Override
	public Map<String, Map<String, Object>> queryPriceMapByTenantIds(String energyTypeId,List<String> tenantIdList) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();
		
		StringBuffer idBuffer = new StringBuffer();
		for(String tenantId : tenantIdList){
			if("".equals(idBuffer.toString())){
				idBuffer.append("'").append(tenantId).append("'");
			}else{
				idBuffer.append(",").append("'").append(tenantId).append("'");
			}
		}
		
		String schema = SchemaUtil.getSchema(Schema.EMS);
		
		sqlBuffer.append("SELECT * from ");
		sqlBuffer.append(schema).append(".t_fn_tenant_price tp,");
		sqlBuffer.append(schema).append(".t_fn_price_template pt ");
		sqlBuffer.append("where tp.c_energy_type_id ='").append(energyTypeId).append("' and tp.c_tenant_id in (").append(idBuffer).append(") and tp.c_price_template_id = pt.c_id");
		
		Map<String, Map<String, Object>> result = new HashMap<String, Map<String, Object>>();
		
		List<Map<String,Object>> list = this.queryBySql(sqlBuffer.toString(),null);
		if(list != null){
			for(Map<String,Object> map : list){
				String c_tenant_id = (String)map.get("c_tenant_id");
				result.put(c_tenant_id, map);
			}
		}
		return result;
	}

	@Override
	public Map<String, FnPriceTemplate> queryPriceTemplateMap(String tenantId) throws Exception {
		Map<String, FnPriceTemplate> result = new HashMap<>();
		FnTenantPrice query = new FnTenantPrice();
		query.setTenantId(tenantId);
		List<FnTenantPrice> priceList = this.query(query);
		List<String> idList = new ArrayList<>();
		if(priceList != null){
			for(FnTenantPrice price : priceList){
				idList.add(price.getPriceTemplateId());
			}
		}
		if(idList.size() > 0){
			FnPriceTemplate templateQuery = new FnPriceTemplate();
			templateQuery.setSpecialOperation("id", SpecialOperator.$in, idList);
			List<FnPriceTemplate> templateList = this.query(templateQuery);
			if(templateList != null){
				for(FnPriceTemplate template : templateList){
					result.put(template.getEnergyTypeId(), template);
				}
			}
		}
		return result;
	}

	@Override
	public Map<String, Map<String, FnTenantPrice>> queryMap() throws Exception {
		Map<String, Map<String, FnTenantPrice>> result = new HashMap<>();
		FnTenantPrice query = new FnTenantPrice();
		List<FnTenantPrice> list = this.query(query);
		if(list != null){
			for(FnTenantPrice entity : list){
				if(!result.containsKey(entity.getTenantId())){
					result.put(entity.getTenantId(), new HashMap<String, FnTenantPrice>());
				}
				result.get(entity.getTenantId()).put(entity.getEnergyTypeId(), entity);
			}
		}
		return result;
	}

}
