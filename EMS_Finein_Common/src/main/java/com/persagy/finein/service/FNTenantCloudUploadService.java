package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FNTenantCloudUpload;
import com.persagy.finein.enumeration.EnumTenantCloudPointType;

import java.util.Date;
import java.util.List;

/**

* 说明:云端-租户数据上传时间
*/

public interface FNTenantCloudUploadService extends FNBaseService {

	public Date queryLastUpdateTime(String buildingId, EnumTenantCloudPointType CloudPointType) throws Exception;

	public List<FNTenantCloudUpload> queryByUploadType(String buildingId, String tenantId, EnumTenantCloudPointType CloudPointType) throws Exception;

}
