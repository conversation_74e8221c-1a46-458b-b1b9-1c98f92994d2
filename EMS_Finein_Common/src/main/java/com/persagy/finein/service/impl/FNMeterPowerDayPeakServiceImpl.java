package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnMeterPowerDayPeak;
import com.persagy.finein.service.FNMeterPowerDayPeakService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service("FNMeterPowerDayPeakService")

public class FNMeterPowerDayPeakServiceImpl extends FNBaseServiceImpl implements FNMeterPowerDayPeakService {

	@Override
	public void saveDataList(List<FnMeterPowerDayPeak> saveList) throws Exception {
		for (FnMeterPowerDayPeak stat : saveList) {
			FnMeterPowerDayPeak remove = new FnMeterPowerDayPeak();
			remove.setMeterId(stat.getMeterId());
			remove.setCountTime(stat.getCountTime());
			this.remove(remove);
		}
		this.save(saveList);
	}

	@Override
	public List<FnMeterPowerDayPeak> queryListGteLte(String meterId, Date timeFrom, Date timeTo) throws Exception {
		FnMeterPowerDayPeak query = new FnMeterPowerDayPeak();
		query.setMeterId(meterId);
		query.setSpecialOperation("countTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("countTime", SpecialOperator.$lte, timeTo);
		return this.query(query);
	}
}
