package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.dto.DTORecordPrePayReturn;
import com.persagy.ems.pojo.finein.FnCommonPrePaySystemParam;
import com.persagy.ems.pojo.finein.FnRecordPrePayForOtherSystem;
import com.persagy.ems.pojo.finein.FnTenantFlag;
import com.persagy.finein.enumeration.EnumPayBodyType;
import com.persagy.finein.enumeration.EnumPrePayStatus;
import com.persagy.finein.service.FNCommonPrePaySystemParamService;
import com.persagy.finein.service.FNRecordPrePayForOtherSystemService;
import com.persagy.finein.service.FNTenantFlagService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 说明:跨系统充值记录接口
 */

@Service("FNRecordPrePayForOtherSystemService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNRecordPrePayForOtherSystemServiceImpl extends FNBaseServiceImpl
		implements FNRecordPrePayForOtherSystemService {

	@Resource
	private FNTenantFlagService FNTenantFlagService;

	@Resource
	private FNCommonPrePaySystemParamService FNCommonPrePaySystemParamService;

	@Override
	public List<FnRecordPrePayForOtherSystem> queryTenantPrePayRecord(String tenantId, String energyTypeId,
			Date timeFrom, Date timeTo) throws Exception {
		FnRecordPrePayForOtherSystem query = new FnRecordPrePayForOtherSystem();
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("operateTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("operateTime", SpecialOperator.$lt, timeTo);

		query.setSort("operateTime", EMSOrder.Desc);
		return this.query(query);
	}

	@Override
	public List<FnRecordPrePayForOtherSystem> queryTenantPrePayRecord(List<String> tenantIdList,
			EnumPayBodyType payBodyType, String energyTypeId, Date timeFrom, Date timeTo) throws Exception {
		FnRecordPrePayForOtherSystem query = new FnRecordPrePayForOtherSystem();
		query.setEnergyTypeId(energyTypeId);
		if (payBodyType != null) {
			query.setType(payBodyType.getValue());
		}
		query.setSpecialOperation("tenantId", SpecialOperator.$in, tenantIdList);
		query.setSpecialOperation("operateTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("operateTime", SpecialOperator.$lt, timeTo);
		query.setSort("operateTime", EMSOrder.Desc);
		return this.query(query);
	}

	@Override
	public List<FnRecordPrePayForOtherSystem> queryTenantPrePayRecord(String tenantId, EnumPayBodyType payBodyType,
			String energyTypeId, String meterId, Date from, Date to) throws Exception {
		FnRecordPrePayForOtherSystem query = new FnRecordPrePayForOtherSystem();
		query.setEnergyTypeId(energyTypeId);
		query.setType(payBodyType.getValue());
		query.setTenantId(tenantId);
		query.setCode(meterId);
		query.setSpecialOperation("operateTime", SpecialOperator.$gte, from);
		query.setSpecialOperation("operateTime", SpecialOperator.$lt, to);
		query.setSort("operateTime", EMSOrder.Desc);
		return this.query(query);
	}

	@Override
	public List<DTORecordPrePayReturn> queryByBuilding(String buildingId, String energyTypeId, Integer status,
			Date from, Date to) throws Exception {

		Map<String, FnTenantFlag> flagMap = FNTenantFlagService.queryFlag();
		FnRecordPrePayForOtherSystem query = new FnRecordPrePayForOtherSystem();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyTypeId);
		query.setStatus(status);
		if (from != null) {
			query.setSpecialOperation("operateTime", SpecialOperator.$gte, from);
		}
		query.setSpecialOperation("operateTime", SpecialOperator.$lt, to);
		List<FnRecordPrePayForOtherSystem> list = this.query(query);
		List<DTORecordPrePayReturn> resultList = new ArrayList<DTORecordPrePayReturn>();
		if (list != null) {
			for (FnRecordPrePayForOtherSystem record : list) {

				DTORecordPrePayReturn save = new DTORecordPrePayReturn();
				save.setBuildingId(buildingId);
				save.setBuildingName(record.getBuildingName());
				save.setOrderId(record.getOrderId());
				save.setOrderType(0);// 充值
				String systemCode = record.getSystemCode();

				FnCommonPrePaySystemParam systemParam = new FnCommonPrePaySystemParam();
				systemParam.setSystemCode(systemCode);
				List<FnCommonPrePaySystemParam> systemParamList = FNCommonPrePaySystemParamService.query(systemParam);
				if (systemParamList != null && systemParamList.size() > 0) {
					save.setSource(Integer.valueOf(systemParamList.get(0).getId()));
				} else {
					continue;
				}
				save.setEnergyTypeId(record.getEnergyTypeId());
				save.setMoney(record.getMoney());
				save.setAmount(record.getAmount());
				save.setBodyType(record.getType());
				save.setBodyCode(record.getCode());
				save.setOperateTime(record.getOperateTime());
				save.setOrderTime(record.getCreateTime());
				save.setStatus(1);
				save.setUserId(record.getUserId());
				save.setUserName(record.getUserName());
				save.setTenantId(record.getTenantId());
				save.setTenantName(record.getTenantName());
				save.setSystemCode(record.getSystemCode());
				if (flagMap.get(record.getTenantId()) != null) {
					save.setTenantFlag(flagMap.get(record.getTenantId()).getTenantFlag());
				}
				resultList.add(save);
			}
		}
		return resultList;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void updateStatus(String buildingId, List<String> orderIdlist, EnumPrePayStatus prePayStatus)
			throws Exception {
		FnRecordPrePayForOtherSystem query = new FnRecordPrePayForOtherSystem();
		query.setBuildingId(buildingId);
		query.setSpecialOperation("orderId", SpecialOperator.$in, orderIdlist);

		FnRecordPrePayForOtherSystem update = new FnRecordPrePayForOtherSystem();
		update.setStatus(prePayStatus.getValue());
		this.update(query, update);
	}

}
