package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantFunctionCompute;
import com.persagy.finein.enumeration.EnumStatTimeType;
import com.persagy.finein.enumeration.EnumStatType;

import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantFunctionComputeService extends FNBaseService {
	
	public FnTenantFunctionCompute query(String buildingId, String tenantId, int functionId, EnumStatType statType, EnumStatTimeType statTimeType) throws Exception;

	public void save(String buildingId, String tenantId, int functionId, EnumStatType statType, EnumStatTimeType statTimeType, Date lastComputeTime) throws Exception;

}
