package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantRoom;
import com.persagy.finein.service.FNTenantRoomService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNTenantRoomService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNTenantRoomServiceImpl extends FNBaseServiceImpl implements FNTenantRoomService {

	@Override
	public List<FnTenantRoom> queryRoomByTenantId(String tenantId) throws Exception {
		FnTenantRoom query = new FnTenantRoom();
		query.setTenantId(tenantId);
		query.setSort("roomCode", EMSOrder.Asc);
		return this.query(query);
	}

	@Override
	public List<FnTenant> queryByRoomIds(String buildingId, List<String> roomIds) throws Exception {
		Map<String, String> map = new HashMap<String, String>();
		FnTenantRoom tenantRoom = new FnTenantRoom();
		tenantRoom.setBuildingId(buildingId);
		tenantRoom.setSpecialOperation("roomId", SpecialOperator.$in, roomIds);
		List<FnTenantRoom> list = this.query(tenantRoom);
		if (list != null && list.size() > 0) {
			for (FnTenantRoom fnTenantRoom : list) {
				map.put(fnTenantRoom.getTenantId(), fnTenantRoom.getTenantId());
			}
			List<String> tenantIds = new ArrayList<>();
			for (String tenantId : map.keySet()) {
				tenantIds.add(tenantId);
			}
			FnTenant query = new FnTenant();
			query.setSpecialOperation("id", SpecialOperator.$in, tenantIds);
			return this.query(query);
		}
		return null;
	}

}
