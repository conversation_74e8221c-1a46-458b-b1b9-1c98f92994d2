package com.persagy.finein.service.impl;

import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.constant.SystemConstant;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.SchemaUtil;
import com.persagy.ems.pojo.finein.FnMeter;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.service.FNMeterService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 时间:2017年9月24日 下午3:32:38
 * 说明:
 */

@Service("FNMeterService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNMeterServiceImpl extends FNBaseServiceImpl implements FNMeterService {

	@Override
	public List<DTOMeter> queryMeterList(String tenantId, String energyTypeId) throws Exception {
		List<DTOMeter> result = new ArrayList<DTOMeter>();
		if (tenantId != null) {
			String schema = SchemaUtil.getSchema(Schema.EMS);

			StringBuffer sqlBuffer = new StringBuffer();
			sqlBuffer.append(
					"SELECT m.c_id,m.c_install_address,m.c_protocol_id,m.c_extend,m.c_radio,m.c_meter_type from ");
			sqlBuffer.append(schema).append(".t_fn_tenant t,");
			sqlBuffer.append(schema).append(".t_fn_tenant_room tr,");
			sqlBuffer.append(schema).append(".t_fn_room_meter r,");
			sqlBuffer.append(schema).append(
					".t_fn_meter m where t.c_id = tr.c_tenant_id and tr.c_room_id = r.c_room_id and r.c_meter_id = m.c_id  and t.c_id='");
			sqlBuffer.append(tenantId).append("' and m.c_energy_type_id = '").append(energyTypeId).append("'");

			List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);

			if (list != null) {
				for (Map<String, Object> map : list) {
					String c_id = (String) map.get("c_id");
					String c_install_address = (String) map.get("c_install_address");
					String c_protocol_id = (String) map.get("c_protocol_id");
					String c_extend = (String) map.get("c_extend");
					Double c_radio = DoubleFormatUtil.Instance().getDoubleData(map.get("c_radio"));
					Integer c_meter_type = (Integer) map.get("c_meter_type");

					DTOMeter dto = new DTOMeter();
					dto.setMeterId(c_id);
					dto.setMeterName(c_install_address);
					dto.setEnergyTypeId(energyTypeId);
					dto.setProtocolId(c_protocol_id);
					dto.setExtend(c_extend);
					dto.setRadio(c_radio);
					dto.setMeterType(c_meter_type);
					result.add(dto);
				}
			}
		}
		return result;
	}

	@Override
	public EnumPayType queryMeterPayType(String meterId) throws Exception {
		FnMeter query = new FnMeter();
		query.setId(meterId);
		FnMeter result = (FnMeter) this.queryObject(query);
		if (result != null) {
			return EnumPayType.valueOf(result.getPayType());
		}
		return null;
	}

	@Override
	public EnumPrepayChargeType queryMeterBillingMode(String meterId) throws Exception {
		FnMeter query = new FnMeter();
		query.setId(meterId);
		FnMeter result = (FnMeter) this.queryObject(query);
		if (result != null) {
			try {
				return EnumPrepayChargeType.valueOf(result.getBillingMode());
			} catch (Exception e) {
			}
		}
		return null;
	}

	@Override
	public List<DTOMeter> queryMeterList(String tenantId) throws Exception {
		List<DTOMeter> result = new ArrayList<DTOMeter>();
		if (tenantId != null) {
			String schema = SchemaUtil.getSchema(Schema.EMS);

			StringBuffer sqlBuffer = new StringBuffer();
			sqlBuffer.append(
					"SELECT m.c_id,m.c_install_address,m.c_energy_type_id,m.c_protocol_id,m.c_extend,m.c_radio,m.c_meter_type from ");
			sqlBuffer.append(schema).append(".t_fn_tenant t,");
			sqlBuffer.append(schema).append(".t_fn_tenant_room tr,");
			sqlBuffer.append(schema).append(".t_fn_room_meter r,");
			sqlBuffer.append(schema).append(
					".t_fn_meter m where t.c_id = tr.c_tenant_id and tr.c_room_id = r.c_room_id and r.c_meter_id = m.c_id  and t.c_id='");
			sqlBuffer.append(tenantId).append("'");
			List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);

			if (list != null) {
				for (Map<String, Object> map : list) {
					String c_id = (String) map.get("c_id");
					String c_install_address = (String) map.get("c_install_address");
					String c_energy_type_id = (String) map.get("c_energy_type_id");
					String c_protocol_id = (String) map.get("c_protocol_id");
					String c_extend = (String) map.get("c_extend");
					Double c_radio = DoubleFormatUtil.Instance().getDoubleData(map.get("c_radio"));
					Integer c_meter_type = (Integer) map.get("c_meter_type");

					DTOMeter dto = new DTOMeter();
					dto.setMeterId(c_id);
					dto.setMeterName(c_install_address);
					dto.setEnergyTypeId(c_energy_type_id);
					dto.setProtocolId(c_protocol_id);
					dto.setExtend(c_extend);
					dto.setRadio(c_radio);
					dto.setMeterType(c_meter_type);
					result.add(dto);
				}
			}
		}
		return result;
	}

	@Override
	public FnMeter queryMeterById(String meterId) throws Exception {
		FnMeter query = new FnMeter();
		query.setId(meterId);
		return (FnMeter) this.queryObject(query);
	}

	@Override
	public List<FnMeter> queryMeterByEnergyTypeId(String energyTypeId) throws Exception {
		FnMeter query = new FnMeter();
		query.setEnergyTypeId(energyTypeId);
		query.setSort("id", EMSOrder.Asc);
		return this.query(query);
	}

	@Override
	public Map<String, List<DTOMeter>> queryMeterList(List<String> tenantIdList, String energyTypeId) throws Exception {
		Map<String, List<DTOMeter>> result = new HashMap<>();
		if (tenantIdList != null && tenantIdList.size() > 0) {
			String schema = SchemaUtil.getSchema(Schema.EMS);

			StringBuffer tenantIdBuffer = new StringBuffer();
			for (int i = 0; i < tenantIdList.size(); i++) {
				if (i != 0) {
					tenantIdBuffer.append(",");
				}
				tenantIdBuffer.append("'").append(tenantIdList.get(i)).append("'");
			}

			StringBuffer sqlBuffer = new StringBuffer();
			sqlBuffer.append(
					"SELECT t.c_id c_tenant_id, m.c_id,m.c_install_address,m.c_protocol_id,m.c_extend,m.c_radio,m.c_meter_type,m.c_energy_type_id from ");
			sqlBuffer.append(schema).append(".t_fn_tenant t,");
			sqlBuffer.append(schema).append(".t_fn_tenant_room tr,");
			sqlBuffer.append(schema).append(".t_fn_room_meter r,");
			sqlBuffer.append(schema).append(
					".t_fn_meter m where t.c_id = tr.c_tenant_id and tr.c_room_id = r.c_room_id and r.c_meter_id = m.c_id  and t.c_id in (");
			sqlBuffer.append(tenantIdBuffer).append(") ");
			if (energyTypeId != null) {
				sqlBuffer.append("and m.c_energy_type_id = '").append(energyTypeId).append("'");
			}

			List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);

			if (list != null) {
				for (Map<String, Object> map : list) {
					String c_tenant_id = (String) map.get("c_tenant_id");
					String c_id = (String) map.get("c_id");
					String c_install_address = (String) map.get("c_install_address");
					String c_protocol_id = (String) map.get("c_protocol_id");
					String c_extend = (String) map.get("c_extend");
					String meterEnergyTypeId = (String) map.get("c_energy_type_id");
					Double c_radio = DoubleFormatUtil.Instance().getDoubleData(map.get("c_radio"));
					Integer c_meter_type = (Integer) map.get("c_meter_type");
					if (!result.containsKey(c_tenant_id)) {
						result.put(c_tenant_id, new ArrayList<DTOMeter>());
					}
					DTOMeter dto = new DTOMeter();
					dto.setMeterId(c_id);
					dto.setMeterName(c_install_address);
					dto.setEnergyTypeId(meterEnergyTypeId);
					dto.setProtocolId(c_protocol_id);
					dto.setExtend(c_extend);
					dto.setRadio(c_radio);
					dto.setMeterType(c_meter_type);
					result.get(c_tenant_id).add(dto);
				}
			}
		}
		return result;
	}

	@Override
	public void changeMeterStatus(List<String> meterIds, Integer status) throws Exception {
		if (meterIds != null && meterIds.size() > 0) {
			FnMeter query = new FnMeter();
			query.setSpecialOperation("id", SpecialOperator.$in, meterIds);
			FnMeter updata = new FnMeter();
			updata.setIsUse(status);
			this.update(query, updata);
		}
	}

	@SuppressWarnings("unchecked")
	@Override
	public Map<String, List<Map<String, Object>>> queryMeter(List<String> tenantIdList, String energyTypeId)
			throws Exception {
		// SELECT tfm.c_id,tfm.c_install_address,tftr.c_room_code
		// FROM
		// t_fn_tenant_room AS tftr ,
		// t_fn_meter AS tfm ,
		// t_fn_room_meter AS tfrm
		// WHERE
		// tftr.c_room_id = tfrm.c_room_id AND tfrm.c_meter_id = tfm.c_id AND
		// tftr.c_tenant_id ='ZHBH_1006' AND tfrm.c_energy_type_id ='Dian'
		Map<String, List<Map<String, Object>>> result = new HashMap<>();
		if (tenantIdList != null && tenantIdList.size() > 0) {
			String schema = SchemaUtil.getSchema(Schema.EMS);

			StringBuffer tenantIdBuffer = new StringBuffer();
			for (int i = 0; i < tenantIdList.size(); i++) {
				if (i != 0) {
					tenantIdBuffer.append(",");
				}
				tenantIdBuffer.append("'").append(tenantIdList.get(i)).append("'");
			}

			StringBuffer sqlBuffer = new StringBuffer();
			sqlBuffer.append("SELECT tfm.c_id,tfm.c_extend,tftr.c_room_code ,tftr.c_tenant_id from ");
			sqlBuffer.append(schema).append(".t_fn_tenant_room  tftr,");
			sqlBuffer.append(schema).append(".t_fn_meter  tfm,");
			sqlBuffer.append(schema).append(".t_fn_room_meter tfrm");
			sqlBuffer.append(
					" where tftr.c_room_id = tfrm.c_room_id AND tfrm.c_meter_id = tfm.c_id and tftr.c_tenant_id in (");
			sqlBuffer.append(tenantIdBuffer).append(") ").append(" and tfrm.c_energy_type_id = '" + energyTypeId + "'");

			List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);

			if (list != null) {
				for (Map<String, Object> map : list) {
					String c_tenant_id = (String) map.get("c_tenant_id");
					String c_id = (String) map.get("c_id");
					String c_extend = (String) map.get("c_extend");
					Map<String, Object> extendMap = SystemConstant.jsonMapper.readValue(c_extend, Map.class);
					String c_room_code = (String) map.get("c_room_code");
					if (!result.containsKey(c_tenant_id)) {
						result.put(c_tenant_id, new ArrayList<Map<String, Object>>());
					}
					Map<String, Object> meterMap = new HashMap<String, Object>();
					meterMap.put("meterId", c_id);
					meterMap.put("address", (String) extendMap.get("address"));
					meterMap.put("roomCode", c_room_code);
					result.get(c_tenant_id).add(meterMap);
				}
			}
		}
		return result;
	}

	@Override
	public Map<String, FnMeter> queryMap() throws Exception {
		Map<String, FnMeter> map = new HashMap<>();
		List<FnMeter> list = this.query(new FnMeter());
		if (list != null) {
			for (FnMeter fnMeter : list) {
				map.put(fnMeter.getId(), fnMeter);
			}
		}
		return map;
	}

	@Override
	public Map<String, List<String>> queryTenant(String buildingId) throws Exception {
		Map<String, List<String>> result = new HashMap<>();
		String schema = SchemaUtil.getSchema(Schema.EMS);
			StringBuffer sqlBuffer = new StringBuffer();
			sqlBuffer.append("SELECT m.c_id meterId,t.c_id tenantId from ");
			sqlBuffer.append(schema).append(".t_fn_tenant_room  tr,");
			sqlBuffer.append(schema).append(".t_fn_tenant t,");
			sqlBuffer.append(schema).append(".t_fn_meter  m,");
			sqlBuffer.append(schema).append(".t_fn_room_meter rm");
			sqlBuffer.append(" where m.c_id = rm.c_meter_id AND rm.c_room_id = tr.c_room_id and tr.c_tenant_id =t.c_id ");
			sqlBuffer.append(" and t.c_building_id = '").append(buildingId).append("' ");
			List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
			if (list != null) {
				for (Map<String, Object> map : list) {
					String meterId = (String) map.get("meterId");
					String tenantId = (String) map.get("tenantId");
					if(result.get(meterId)==null){
						result.put(meterId, new ArrayList<String>());
					}
					result.get(meterId).add(tenantId);
				}
			}
		return result;
	}

    @Override
    public List<Map<String, Object>> queryMeterSetting(Integer pageIndex, Integer pageSize) throws Exception {
		String schema = SchemaUtil.getSchema(Schema.EMS);
		String sql = "SELECT a.c_id meterId,a.c_install_address as installAddress,c.c_building_id as buildingId," +
				"c.c_floor_id as floorId,c.c_area as area,b.c_extend as extend " +
				"FROM " + schema + ".t_fn_meter a " +
				"LEFT JOIN " + schema + ".t_fn_protocol b on a.c_protocol_id = b.c_id " +
				"LEFT JOIN " + schema + ".t_fn_room c on a.c_install_address = c.c_code " +
				"where b.c_extend is not null Limit " + pageIndex * pageSize + ","+pageSize;
		return this.queryBySql(sql, null);
    }

	@Override
	public int queryMeterSettingCount() throws Exception {
		String schema = SchemaUtil.getSchema(Schema.EMS);
		String sql = "SELECT count(a.c_id) as count " +
				"FROM " + schema + ".t_fn_meter a " +
				"LEFT JOIN " + schema + ".t_fn_protocol b on a.c_protocol_id = b.c_id " +
				"LEFT JOIN " + schema + ".t_fn_room c on a.c_install_address = c.c_code " +
				"where b.c_extend is not null";
		return this.count(sql,new ArrayList<Object>());
	}

	@Override
	public List<FnMeter> queryMeterByIds(List<String> meterIds) throws Exception {
		FnMeter fnMeter = new FnMeter();
		fnMeter.setSpecialOperation("id",SpecialOperator.$in,meterIds);
		return this.query(fnMeter);
	}

	@Override
	public List<FnMeter> queryMeterListByTenantId(String tenantId) throws Exception {
		List<FnMeter> result = new ArrayList<>();
		if (tenantId != null) {
			String schema = SchemaUtil.getSchema(Schema.EMS);

			StringBuffer sqlBuffer = new StringBuffer();
			sqlBuffer.append(
					"SELECT m.* from ");
			sqlBuffer.append(schema).append(".t_fn_tenant t,");
			sqlBuffer.append(schema).append(".t_fn_tenant_room tr,");
			sqlBuffer.append(schema).append(".t_fn_room_meter r,");
			sqlBuffer.append(schema).append(
					".t_fn_meter m where t.c_id = tr.c_tenant_id and tr.c_room_id = r.c_room_id and r.c_meter_id = m.c_id  and t.c_id='");
			sqlBuffer.append(tenantId).append("'");
			List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
			if (list!=null){
				for (Map<String,Object> map:list){
					String c_id = (String) map.get("c_id");
					String c_energy_type_id = (String) map.get("c_energy_type_id");
					String c_protocol_id = (String) map.get("c_protocol_id");
					String c_install_address = (String) map.get("c_install_address");
					Integer c_is_use = (Integer) map.get("c_is_use");
					Integer c_communication_type = (Integer) map.get("c_communication_type");
					String c_client_ip = (String) map.get("c_client_ip");
					Integer c_client_port = (Integer) map.get("c_client_port");
					String c_server_ip = (String) map.get("c_server_ip");
					Integer c_server_port = (Integer) map.get("c_server_port");
					Integer c_pay_type = (Integer) map.get("c_pay_type");
					Integer c_billing_mode = (Integer) map.get("c_billing_mode");
					Integer c_meter_type = (Integer) map.get("c_meter_type");
					Double c_radio = DoubleFormatUtil.Instance().getDoubleData(map.get("c_radio"));
					String c_extend = (String) map.get("c_extend");

					FnMeter fnMeter = new FnMeter();
					fnMeter.setId(c_id);
					fnMeter.setEnergyTypeId(c_energy_type_id);
					fnMeter.setProtocolId(c_protocol_id);
					fnMeter.setInstallAddress(c_install_address);
					fnMeter.setIsUse(c_is_use);
					fnMeter.setCommunicationType(c_communication_type);
					fnMeter.setClientIp(c_client_ip);
					fnMeter.setClientPort(c_client_port);
					fnMeter.setServerIp(c_server_ip);
					fnMeter.setServerPort(c_server_port);
					fnMeter.setPayType(c_pay_type);
					fnMeter.setBillingMode(c_billing_mode);
					fnMeter.setMeterType(c_meter_type);
					fnMeter.setRadio(c_radio);
					fnMeter.setExtend(c_extend);
					result.add(fnMeter);
				}
			}
		}
		return result;
	}

	@Override
	public Map<String, List<FnMeter>> queryAllMeterList(List<String> tenantIdList, String energyTypeId) throws Exception {
		Map<String, List<FnMeter>> result = new HashMap<>();
		if (tenantIdList != null && tenantIdList.size() > 0) {
			String schema = SchemaUtil.getSchema(Schema.EMS);

			StringBuffer tenantIdBuffer = new StringBuffer();
			for (int i = 0; i < tenantIdList.size(); i++) {
				if (i != 0) {
					tenantIdBuffer.append(",");
				}
				tenantIdBuffer.append("'").append(tenantIdList.get(i)).append("'");
			}

			StringBuffer sqlBuffer = new StringBuffer();
			sqlBuffer.append(
					"SELECT DISTINCT t.c_id c_tenant_id, " +
							"m.c_id,m.c_energy_type_id, m.c_protocol_id,m.c_install_address, m.c_is_use, m.c_communication_type, " +
							"m.c_client_ip ,m.c_client_port, m.c_server_ip, m.c_server_port, m.c_pay_type, m.c_billing_mode, " +
							" m.c_meter_type, m.c_radio, m.c_extend from ");
			sqlBuffer.append(schema).append(".t_fn_tenant t,");
			sqlBuffer.append(schema).append(".t_fn_tenant_room tr,");
			sqlBuffer.append(schema).append(".t_fn_room_meter r,");
			sqlBuffer.append(schema).append(
					".t_fn_meter m where t.c_id = tr.c_tenant_id and tr.c_room_id = r.c_room_id and r.c_meter_id = m.c_id  and t.c_id in (");
			sqlBuffer.append(tenantIdBuffer).append(") ");
			if (energyTypeId != null) {
				sqlBuffer.append("and m.c_energy_type_id = '").append(energyTypeId).append("'");
			}

			List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);

			if (list != null) {
				for (Map<String, Object> map : list) {
					String c_tenant_id = (String) map.get("c_tenant_id");
					String c_id = (String) map.get("c_id");
					String c_energy_type_id = (String) map.get("c_energy_type_id");
					String c_protocol_id = (String) map.get("c_protocol_id");
					String c_install_address = (String) map.get("c_install_address");
					Integer c_is_use= (Integer) map.get("c_is_use");
					Integer c_communication_type= (Integer) map.get("c_communication_type");
					String c_client_ip = (String) map.get("c_client_ip");
					Integer c_client_port= (Integer) map.get("c_client_port");
					String c_server_ip = (String) map.get("c_server_ip");
					Integer c_server_port= (Integer) map.get("c_server_port");
					Integer c_pay_type= (Integer) map.get("c_pay_type");
					Integer c_billing_mode= (Integer) map.get("c_billing_mode");
					Integer c_meter_type = (Integer) map.get("c_meter_type");
					Double c_radio = DoubleFormatUtil.Instance().getDoubleData(map.get("c_radio"));
					String c_extend = (String) map.get("c_extend");
					if (!result.containsKey(c_tenant_id)) {
						result.put(c_tenant_id, new ArrayList<FnMeter>());
					}
					FnMeter meter = new FnMeter();
					meter.setId(c_id);
					meter.setEnergyTypeId(c_energy_type_id);
					meter.setProtocolId(c_protocol_id);
					meter.setInstallAddress(c_install_address);
					meter.setIsUse(c_is_use);
					meter.setCommunicationType(c_communication_type);
					meter.setClientIp(c_client_ip);
					meter.setClientPort(c_client_port);
					meter.setServerIp(c_server_ip);
					meter.setServerPort(c_server_port);
					meter.setPayType(c_pay_type);
					meter.setBillingMode(c_billing_mode);
					meter.setMeterType(c_meter_type);
					meter.setRadio(c_radio);
					meter.setExtend(c_extend);
					result.get(c_tenant_id).add(meter);
				}
			}
		}
		return result;
	}
}
