package com.persagy.finein.service.impl;

import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.SchemaUtil;
import com.persagy.ems.pojo.finein.FnAlarm;
import com.persagy.ems.pojo.finein.FnAlarmPushStatus;
import com.persagy.finein.enumeration.EnumAlarmPushStatus;
import com.persagy.finein.enumeration.EnumAlarmStatus;
import com.persagy.finein.service.FNAlarmService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNAlarmService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNAlarmServiceImpl extends FNBaseServiceImpl implements FNAlarmService {


	@Override
	public List<FnAlarm> queryAlarmByTenantId(String tenantId) throws Exception {
		FnAlarm query = new FnAlarm();
		query.setTenantId(tenantId);
		query.setStatus(EnumAlarmStatus.WeiHuiHu.getValue());
		query.setSort("alarmTime", EMSOrder.Desc);
		return this.query(query);
	}

	@Override
	public boolean queryEnergyTypeIsAlarm(String buildingId, String energyTypeId) throws Exception {
		FnAlarm query = new FnAlarm();
		query.setEnergyTypeId(energyTypeId);
		query.setBuildingId(buildingId);
		query.setStatus(EnumAlarmStatus.WeiHuiHu.getValue());

		int count = this.count(query);
		if (count > 0) {
			return true;
		} else {
			return false;
		}
	}

	@Override
	public List<FnAlarm> queryAlarmByTenantId(String tenantId, EnumAlarmStatus alarmStatus) throws Exception {
		FnAlarm query = new FnAlarm();
		query.setTenantId(tenantId);
		query.setStatus(alarmStatus.getValue());
		query.setSort("alarmTime", EMSOrder.Asc);
		return coreDao.query(query);
	}

	@Override
	public void updateAlarmStatus(List<String> alarmIdList, EnumAlarmStatus alarmStatus) throws Exception {
		FnAlarm query = new FnAlarm();
		query.setSpecialOperation("id", SpecialOperator.$in, alarmIdList);
		FnAlarm update = new FnAlarm();
		update.setStatus(alarmStatus.getValue());
		update.setLastUpdateTime(new Date());
		this.update(query, update);
	}

	@Override
	public void updateAlarmStatus(String tenantId, String alarmTypeId, EnumAlarmStatus alarmStatus, String meterId)
			throws Exception {
		FnAlarm query = new FnAlarm();
		query.setTenantId(tenantId);
		query.setAlarmTypeId(alarmTypeId);
		query.setAlarmPositionId(meterId);
		FnAlarm update = new FnAlarm();
		update.setStatus(alarmStatus.getValue());
		Date now = new Date();
		if (alarmStatus == EnumAlarmStatus.YiHuiFu) {
			update.setPushStatus(EnumAlarmPushStatus.StatusChange.getValue());
			update.setFinishTime(now);
		}
		update.setLastUpdateTime(now);
		this.update(query, update);
		this.updateAlarmPushStatus(tenantId, alarmTypeId, alarmStatus, meterId);
	}

	@Override
	public FnAlarm queryAlarmWeiHuiFu(String tenantId, String alarmTypeId) throws Exception {
		FnAlarm query = new FnAlarm();
		query.setTenantId(tenantId);
		query.setAlarmTypeId(alarmTypeId);
		query.setStatus(EnumAlarmStatus.WeiHuiHu.getValue());
		return (FnAlarm) this.queryObject(query);
	}

	@Override
	public FnAlarm queryAlarmWeiHuiFu(String tenantId, String meterId, String alarmTypeId) throws Exception {
		FnAlarm query = new FnAlarm();
		query.setTenantId(tenantId);
		query.setAlarmTypeId(alarmTypeId);
		query.setAlarmPositionId(meterId);
		query.setStatus(EnumAlarmStatus.WeiHuiHu.getValue());
		return (FnAlarm) this.queryObject(query);
	}

	@Override
	public boolean queryEnergyTypeIsAlarm(String buildingId, String energyTypeId, boolean isContainGongLvAlarm)
			throws Exception {
		FnAlarm query = new FnAlarm();
		query.setEnergyTypeId(energyTypeId);
		query.setBuildingId(buildingId);
		query.setStatus(EnumAlarmStatus.WeiHuiHu.getValue());
		if (!isContainGongLvAlarm) {
			List<String> list = new ArrayList<String>();
			list.add(FineinConstant.AlarmType.FUHELVGUOGAO);
			list.add(FineinConstant.AlarmType.DIANINTERRUPT);
			list.add(FineinConstant.AlarmType.SHUIINTERRUPT);
			list.add(FineinConstant.AlarmType.RESHUIINTERRUPT);
			list.add(FineinConstant.AlarmType.RANQIINTERRUPT);
			query.setSpecialOperation("alarmTypeId", SpecialOperator.$nin, list);
		}
		int count = this.count(query);
		if (count > 0) {
			return true;
		} else {
			return false;
		}
	}

	@Override
	public List<FnAlarm> queryAlarmWaitPush(long limit) throws Exception {
		FnAlarm query = new FnAlarm();
		List<Integer> pushList = new ArrayList<>();
		pushList.add(EnumAlarmPushStatus.WaitPush.getValue());
		pushList.add(EnumAlarmPushStatus.StatusChange.getValue());

		query.setSpecialOperation("pushStatus", SpecialOperator.$in, pushList);
		query.setSort("alarmTime", EMSOrder.Asc);
		query.setLimit(limit);
		return this.query(query);
	}

	@Override
	public void updateAlarmPushStatus(String tenantId, String alarmTypeId, EnumAlarmStatus alarmStatus, String meterId)
			throws Exception {
		Date now = new Date();
		FnAlarmPushStatus query = new FnAlarmPushStatus();
		query.setTenantId(tenantId);
		query.setAlarmTypeId(alarmTypeId);
		query.setAlarmPositionId(meterId);
		List<FnAlarmPushStatus> list = this.query(query);
		if (list != null && list.size() != 0) {
			FnAlarmPushStatus update = new FnAlarmPushStatus();
			update.setStatus(alarmStatus.getValue());
			update.setPushStatus(EnumAlarmPushStatus.StatusChange.getValue());
			update.setLastUpdateTime(now);
			this.update(query, update);
		}
	}

	/**
	 * 查询未推送全集报警 (non-Javadoc)
	 * 
	 * @see FNAlarmService#queryGlobalAlarmWaitPush(long)
	 */



	 /**
	 *(non-Javadoc)
	 * @see FNAlarmService#queryGlobalAlarmWaitPush(String, long)
	 */
	 
	@Override
	public List<FnAlarm> queryGlobalAlarmWaitPush(String buildingId, long limit) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append("SELECT a.* from ");
		sqlBuffer.append(schema).append(".t_fn_alarm a,");
		sqlBuffer.append(schema).append(".t_fn_alarm_push_status aps ");
		sqlBuffer.append(" where ");
		sqlBuffer.append(" a.c_id= aps.c_id ");
		sqlBuffer.append(" and aps.c_push_status in (0,1) ");
		sqlBuffer.append(" and a.c_building_id = '").append(buildingId).append("' ").append(" ORDER BY a.c_create_time");
		sqlBuffer.append(" limit ").append(0).append(",").append(limit);
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		List<FnAlarm> result = new ArrayList<>();
		if (list != null) {
			for (Map<String, Object> map : list) {
				FnAlarm fnAlarm = new FnAlarm();
				fnAlarm.setId((String) map.get("c_id"));
				fnAlarm.setBuildingId((String) map.get("c_building_id"));
				fnAlarm.setTenantId((String) map.get("c_tenant_id"));
				fnAlarm.setAlarmTypeId((String) map.get("c_alarm_type_id"));
				fnAlarm.setParentAlarmTypeId((String) map.get("c_parent_alarm_type_id"));
				fnAlarm.setTreeId((String) map.get("c_tree_id"));
				fnAlarm.setEnergyTypeId((String) map.get("c_energy_type_id"));
				fnAlarm.setAlarmTime((Date) map.get("c_alarm_time"));
				fnAlarm.setAlarmPositionType((Integer) map.get("c_alarm_position_type"));
				fnAlarm.setAlarmPositionId((String) map.get("c_alarm_position_id"));
				fnAlarm.setAlarmPositionName((String) map.get("c_alarm_position_name"));
				fnAlarm.setStatus((Integer) map.get("c_status"));
				BigDecimal b =(BigDecimal) map.get("c_limit_value");
				fnAlarm.setLimitValue(b==null?0.0:b.doubleValue());
				BigDecimal b2 =(BigDecimal) map.get("c_current_value");
				fnAlarm.setCurrentValue(b2==null?0.0:b2.doubleValue());
				fnAlarm.setCreateTime((Date) map.get("c_create_time"));
				fnAlarm.setLastUpdateTime((Date) map.get("c_last_update_time"));
				fnAlarm.setFinishTime((Date) map.get("c_finish_time"));
				fnAlarm.setUnit((String) map.get("c_unit"));
				fnAlarm.setIsRead((Integer) map.get("c_is_read"));
				fnAlarm.setExtend((String) map.get("c_extend"));
				fnAlarm.setPushStatus((Integer) map.get("c_push_status"));
				result.add(fnAlarm);
			}
		}
		return result;
	}

}
