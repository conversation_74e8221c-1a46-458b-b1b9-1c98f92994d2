package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantBackPayData;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.enumeration.EnumTimeType;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantBackPayDataService extends FNBaseService {
	
	public FnTenantBackPayData queryLastData(String buildingId, String tenantId, String energyTypeId, EnumTimeType timeType, EnumPrepayChargeType prepayChargeType, Date timeTo) throws Exception;

	public FnTenantBackPayData queryByDate(String buildingId, String tenantId, String energyTypeId, EnumTimeType timeType, EnumPrepayChargeType prepayChargeType, Date timeFrom) throws Exception;

	public void saveData(String buildingId, String tenantId, String energyTypeId, EnumTimeType timeType, EnumPrepayChargeType prepayChargeType, Date timeFrom, Double data) throws Exception;

	public List<FnTenantBackPayData> queryDataGteLt(String buildingId, String tenantId, String energyTypeId, EnumTimeType timeType, EnumPrepayChargeType prepayChargeType, Date timeFrom, Date timeTo) throws Exception;

	public List<FnTenantBackPayData> queryDataGteLte(String buildingId, String tenantId, String energyTypeId, EnumTimeType timeType, EnumPrepayChargeType prepayChargeType, Date timeFrom, Date timeTo) throws Exception;

	public void removeData(String buildingId, String tenantId, String energyTypeId, Date timeFrom) throws Exception ;
}
