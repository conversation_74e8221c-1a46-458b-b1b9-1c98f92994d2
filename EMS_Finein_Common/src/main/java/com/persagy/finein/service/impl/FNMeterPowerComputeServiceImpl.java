package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnMeterPowerCompute;
import com.persagy.finein.enumeration.EnumStatType;
import com.persagy.finein.service.FNMeterPowerComputeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNMeterPowerComputeService")
public class FNMeterPowerComputeServiceImpl extends FNBaseServiceImpl implements FNMeterPowerComputeService {

	@Override
	public FnMeterPowerCompute query(String meterId, EnumStatType statType) throws Exception {
		FnMeterPowerCompute query = new FnMeterPowerCompute();
		query.setMeterId(meterId);
		query.setDataType(statType.getValue());
		List<FnMeterPowerCompute> list = this.query(query);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void save(String meterId, EnumStatType statType, Date lastComputeTime) throws Exception {
		FnMeterPowerCompute query = new FnMeterPowerCompute();
		query.setMeterId(meterId);
		query.setDataType(statType.getValue());
		this.remove(query);
		query.setLastComputeTime(lastComputeTime);
		query.setLastUpdateTime(new Date());
		this.save(query);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void update(List<FnMeterPowerCompute> powerSaveList) throws Exception {
		if (powerSaveList != null && powerSaveList.size() > 0) {
			for (FnMeterPowerCompute save : powerSaveList) {
				FnMeterPowerCompute query = new FnMeterPowerCompute();
				query.setMeterId(save.getMeterId());
				query.setDataType(save.getDataType());
				this.remove(query);
			}
		}
		this.save(powerSaveList);

	}
}
