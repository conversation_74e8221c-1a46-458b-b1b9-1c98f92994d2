package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnAlarmLimitCustomObj;
import com.persagy.ems.pojo.finein.FnAlarmLimitCustomSetting;
import com.persagy.finein.service.FNAlarmLimitCustomSettingService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNAlarmLimitCustomSettingService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNAlarmLimitCustomSettingServiceImpl extends FNBaseServiceImpl implements FNAlarmLimitCustomSettingService{


	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void save(String buildingId,String tenantId, FnAlarmLimitCustomSetting setting, List<FnAlarmLimitCustomObj> list)
			throws Exception {
		FnAlarmLimitCustomSetting settingDelete = new FnAlarmLimitCustomSetting();
		settingDelete.setBuildingId(buildingId);
		settingDelete.setTenantId(tenantId);
		this.remove(settingDelete);
		
		FnAlarmLimitCustomObj objDelete = new FnAlarmLimitCustomObj();
		objDelete.setBuildingId(buildingId);
		objDelete.setTenantId(tenantId);
		
		this.remove(objDelete);
		
		this.save(setting);
		this.save(list);
	}

	@Override
	public FnAlarmLimitCustomSetting query(String buildingId, String tenantId) throws Exception {
		FnAlarmLimitCustomSetting query = new FnAlarmLimitCustomSetting();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		return (FnAlarmLimitCustomSetting)this.queryObject(query);
	}

	@Override
	public FnAlarmLimitCustomSetting queryOne(String buildingId, String tenantId) throws Exception {
		FnAlarmLimitCustomSetting query = new FnAlarmLimitCustomSetting();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		return (FnAlarmLimitCustomSetting)this.queryObject(query);
	}
	
	
}
