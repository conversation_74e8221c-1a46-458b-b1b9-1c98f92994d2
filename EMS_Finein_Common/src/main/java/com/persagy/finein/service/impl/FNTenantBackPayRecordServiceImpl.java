package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnTenantBackPayRecord;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.enumeration.EnumYesNo;
import com.persagy.finein.service.FNTenantBackPayRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantBackPayRecordService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantBackPayRecordServiceImpl extends FNBaseServiceImpl  implements FNTenantBackPayRecordService{

	@Override
	public List<FnTenantBackPayRecord> queryGteLte(String buildingId, String tenantId, String energyTypeId,
			EnumTimeType timeType, EnumPrepayChargeType prepayChargeType, EnumYesNo isProcessed, Date timeFrom,
			Date timeTo) throws Exception {
		FnTenantBackPayRecord query = new FnTenantBackPayRecord();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setTimeType(timeType.getValue());
		query.setPrepayChargeType(prepayChargeType.getValue());
		
		if(isProcessed != null){
			query.setIsProcessed(isProcessed.getValue());
		}
		if(timeFrom != null){
			query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		}
		if(timeTo != null){
			query.setSpecialOperation("timeFrom", SpecialOperator.$lte, timeTo);
		}
		
		return this.query(query);
	}

	@Override
	public void updateProcessStatus(String id, EnumYesNo isProcessed) throws Exception {
		FnTenantBackPayRecord query = new FnTenantBackPayRecord();
		query.setId(id);
		
		FnTenantBackPayRecord update = new FnTenantBackPayRecord();
		update.setIsProcessed(isProcessed.getValue());
		update.setLastUpdateTime(new Date());
		this.update(query, update);
	}

	@Override
	public void saveData(String buildingId, String tenantId, String energyTypeId, EnumTimeType timeType,
			EnumPrepayChargeType prepayChargeType, Date timeFrom, Double data) throws Exception {
		FnTenantBackPayRecord query = new FnTenantBackPayRecord();
		query.setId(UUID.randomUUID().toString());
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setTimeType(timeType.getValue());
		query.setPrepayChargeType(prepayChargeType.getValue());
		query.setTimeFrom(timeFrom);
		query.setData(data);
		query.setIsProcessed(EnumYesNo.NO.getValue());
		query.setLastUpdateTime(new Date());
		this.save(query);
	}
}
