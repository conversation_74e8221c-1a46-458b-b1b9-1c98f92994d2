package com.persagy.finein.service.impl;

import com.persagy.finein.service.FNRecordScheduleJobService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
 * 说明:定时任务执行记录
 */

@Service("FNRecordScheduleJobService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNRecordScheduleJobServiceImpl extends FNBaseServiceImpl implements FNRecordScheduleJobService {


}
