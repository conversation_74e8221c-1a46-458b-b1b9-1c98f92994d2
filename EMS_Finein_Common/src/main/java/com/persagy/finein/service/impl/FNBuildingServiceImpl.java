package com.persagy.finein.service.impl;

import com.persagy.core.mvc.dao.CoreDao;
import com.persagy.ems.finein.common.util.SystemPropertiesUtil;
import com.persagy.ems.pojo.ac.AcBuilding;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.service.FNBuildingService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNBuildingService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNBuildingServiceImpl extends FNBaseDictionaryServiceImpl implements FNBuildingService {

	@Resource(name = "SystemPropertiesUtil")
	private SystemPropertiesUtil SystemPropertiesUtil;

	@Resource(name = "jdbcTemplateCoreDao")
	private CoreDao CoreDao;

	@Override
	public List<Building> queryList(Building query) throws Exception {
		List<Building> list = new ArrayList<Building>();
		AcBuilding queryAcBuilding = new AcBuilding();
		queryAcBuilding.setId(query.getId());
		queryAcBuilding.setName(query.getName());
		List<AcBuilding> buildingList = CoreDao.query(queryAcBuilding);
		if (buildingList != null && buildingList.size() > 0) {
			for (AcBuilding acBuilding : buildingList) {
				Building building = new Building();
				building.setId(acBuilding.getId());
				building.setName(acBuilding.getName());
				list.add(building);
			}
		}
		return list;
	}

	@Override
	public Building query(String buildingId) throws Exception {
		Building query = new Building();
		query.setId(buildingId);
		List<Building> list = this.queryList(query);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	@Override
	public Map<String, Building> query() throws Exception {
		Map<String, Building> result = new HashMap<String, Building>();
		List<Building> list = queryList(new Building());
		if (list != null) {
			for (Building building : list) {
				result.put(building.getId(), building);
			}
		}
		return result;
	}

}
