package com.persagy.finein.service.impl;

import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.finein.common.util.SchemaUtil;
import com.persagy.ems.pojo.finein.FnTenantPayType;
import com.persagy.finein.enumeration.EnumPayType;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.service.FNTenantPayTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantPayTypeService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantPayTypeServiceImpl extends FNBaseServiceImpl  implements FNTenantPayTypeService{

	@Override
	public FnTenantPayType query(String tenantId, String energyTypeId) throws Exception {
		FnTenantPayType query = new FnTenantPayType();
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		return (FnTenantPayType)this.queryObject(query);
	}

	@Override
	public Map<String, FnTenantPayType> queryPayTypeMap(String tenantId) throws Exception {
		Map<String, FnTenantPayType> result = new HashMap<>();
		FnTenantPayType query = new FnTenantPayType();
		query.setTenantId(tenantId);
		List<FnTenantPayType> list = this.query(query);
		for(FnTenantPayType payType : list){
			result.put(payType.getEnergyTypeId(), payType);
		}
		return result;
	}

	@Override
	public Map<String, Map<String, FnTenantPayType>> queryByBuildingId(String buildingId) throws Exception {
		Map<String, Map<String, FnTenantPayType>> result = new HashMap<>();
		FnTenantPayType query = new FnTenantPayType();
		query.setBuildingId(buildingId);
		List<FnTenantPayType> payTypeList = this.query(query);
		if(payTypeList != null){
			for(FnTenantPayType payType : payTypeList){
				if(!result.containsKey(payType.getTenantId())){
					result.put(payType.getTenantId(), new HashMap<String, FnTenantPayType>());
				}
				result.get(payType.getTenantId()).put(payType.getEnergyTypeId(), payType);
			}
		}
		return result;
	}

	@Override
	public Map<String, String> queryEnergyTypeByBuildingId(String buildingId) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();
		
		String schema = SchemaUtil.getSchema(Schema.EMS);
		
		sqlBuffer.append("SELECT DISTINCT(c_energy_type_id) from ");
		sqlBuffer.append(schema).append(".t_fn_tenant_pay_type t ").append("where t.c_building_id='");
		sqlBuffer.append(buildingId).append("'");
		
		Map<String,String> energyTypeMap = new HashMap<String, String>();
		
		List<Map<String,Object>> list = this.queryBySql(sqlBuffer.toString(),null);
		if(list != null){
			for(Map<String,Object> map : list){
				String c_energy_type_id = (String)map.get("c_energy_type_id");
				energyTypeMap.put(c_energy_type_id, c_energy_type_id);
			}
		}
		return energyTypeMap;
	}

	@Override
	public List<String> queryEnergyTypePayTypeList() throws Exception {
		List<String> result = new ArrayList<>();
		StringBuffer sqlBuffer = new StringBuffer();
		
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append("SELECT DISTINCT tpt.c_energy_type_id,tpt.c_pay_type from ");
		sqlBuffer.append(schema).append(".t_fn_tenant_pay_type tpt , ");
		sqlBuffer.append(schema).append(".t_fn_tenant t where tpt.c_tenant_id=t.c_id and t.c_is_valid=").append(EnumValidStatus.VALID.getValue());
		
		List<Map<String,Object>> list = this.queryBySql(sqlBuffer.toString(),null);
		if(list != null){
			for(Map<String,Object> map : list){
				String c_energy_type_id = (String)map.get("c_energy_type_id");
				Integer c_pay_type = (Integer)map.get("c_pay_type");
				result.add(c_energy_type_id+"_"+c_pay_type);
			}
		}
		return result;
	}

	@Override
	public List<String> queryChargeTypeList() throws Exception {
		List<String> result = new ArrayList<>();
		StringBuffer sqlBuffer = new StringBuffer();
		
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append("SELECT DISTINCT tpt.c_pre_pay_type from ");
		sqlBuffer.append(schema).append(".t_fn_tenant_pay_type tpt , ");
		sqlBuffer.append(schema).append(".t_fn_tenant t where tpt.c_pay_type=").append(EnumPayType.PREPAY.getValue()).append(" and tpt.c_tenant_id=t.c_id and t.c_is_valid=").append(EnumValidStatus.VALID.getValue());
		
		List<Map<String,Object>> list = this.queryBySql(sqlBuffer.toString(),null);
		if(list != null){
			for(Map<String,Object> map : list){
				Integer c_pre_pay_type = (Integer)map.get("c_pre_pay_type");
				result.add(c_pre_pay_type+"");
			}
		}
		return result;
	}

	/**
	 *(non-Javadoc)
	 * @throws Exception
	 * @see FNTenantPayTypeService#queryTenantAndPayType(String, String)
	 */
	@Override
	public Map<String, FnTenantPayType> queryTenantAndPayType(String buildingId, String energyType) throws Exception {
		Map<String, FnTenantPayType> hashMap = new HashMap<>();
		FnTenantPayType query = new FnTenantPayType();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyType);
		List<FnTenantPayType> list = this.query(query);
		if(list!=null){
			for (FnTenantPayType fnTenantPayType : list) {
				hashMap.put(fnTenantPayType.getTenantId(), fnTenantPayType);
			}
		}
		return hashMap;
	}
}
