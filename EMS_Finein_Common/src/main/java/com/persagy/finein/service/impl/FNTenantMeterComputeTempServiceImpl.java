package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantMeterComputeTemp;
import com.persagy.finein.service.FNTenantMeterComputeTempService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantMeterComputeTempService")
public class FNTenantMeterComputeTempServiceImpl extends FNBaseServiceImpl  implements FNTenantMeterComputeTempService{


	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void saveComputeTime(String buildingId,String tenantId, String meterId, int functionId, Date lastComputeTime)
			throws Exception {
		FnTenantMeterComputeTemp saveObj = new FnTenantMeterComputeTemp();
		saveObj.setBuildingId(buildingId);
		saveObj.setTenantId(tenantId);
		saveObj.setMeterId(meterId);
		saveObj.setFunctionId(functionId);
		this.remove(saveObj);
		saveObj.setLastComputeTime(lastComputeTime);
		saveObj.setLastUpdateTime(new Date());
		this.save(saveObj);
	}

	@Override
	public void removeComputeTime(String buildingId, String tenantId, String meterId, int functionId) throws Exception {
		FnTenantMeterComputeTemp query = new FnTenantMeterComputeTemp();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setMeterId(meterId);
		query.setFunctionId(functionId);
		this.remove(query);
	}

	@Override
	public List<FnTenantMeterComputeTemp> queryByBuilding(String buildingId) throws Exception {
		FnTenantMeterComputeTemp query = new FnTenantMeterComputeTemp();
		query.setBuildingId(buildingId);
		return this.query(query);
	}
}
