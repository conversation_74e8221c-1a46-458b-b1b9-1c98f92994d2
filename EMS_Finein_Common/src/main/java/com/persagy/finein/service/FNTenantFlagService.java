package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantFlag;

import java.util.List;
import java.util.Map;

/**

* 说明:租户全码
*/

public interface FNTenantFlagService extends FNBaseService {
	
	public Map<String,FnTenant> queryTenantByFlags(List<String> tenantFlagList) throws Exception;
	
	public FnTenant queryTenantByFlag(String tenantFlag) throws Exception;

	public Map<String, FnTenantFlag> queryFlag() throws Exception;

}
