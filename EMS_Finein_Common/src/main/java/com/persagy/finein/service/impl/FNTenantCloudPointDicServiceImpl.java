package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FNTenantCloudPointDic;
import com.persagy.finein.service.FNTenantCloudPointDicService;
import org.springframework.stereotype.Service;

import java.util.List;

/**

* 说明:云端-租户信息点类型
*/

@Service("FNTenantCloudPointDicService")
public class FNTenantCloudPointDicServiceImpl extends FNBaseServiceImpl  implements FNTenantCloudPointDicService{

	@Override
	public Integer queryFunctionId(String id) throws Exception {
		FNTenantCloudPointDic query = new FNTenantCloudPointDic();
		query.setId(id);
		List<FNTenantCloudPointDic> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0).getFunctionId();
		}
		return null;
	}

	

	
}
