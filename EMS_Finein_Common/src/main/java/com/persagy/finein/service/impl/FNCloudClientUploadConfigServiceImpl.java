package com.persagy.finein.service.impl;


import com.persagy.finein.service.FNCloudClientUploadConfigService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNCloudClientUploadConfigService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNCloudClientUploadConfigServiceImpl extends FNBaseServiceImpl implements FNCloudClientUploadConfigService{

}
