package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.FnEnergyType;
import com.persagy.finein.enumeration.EnumValidStatus;
import com.persagy.finein.service.FNAlarmService;
import com.persagy.finein.service.FNEnergyTypeService;
import com.persagy.finein.service.FNTenantService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNEnergyTypeService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNEnergyTypeServiceImpl extends FNBaseServiceImpl implements FNEnergyTypeService{
	@Resource(name = "FNAlarmService")
	private FNAlarmService FNAlarmService;
	@Resource(name = "FNTenantService")
	private FNTenantService FNTenantService;
	
	@Override
	public List<FnEnergyType> queryList(EnumValidStatus status,EMSOrder order) throws Exception {
		FnEnergyType query = new FnEnergyType();
		if(status != null){
			query.setIsValid(status.getValue());
		}
		query.setSort("orderBy", order);
		return this.query(query);
	}


	@Override
	public Map<String, Boolean> queryEnergyTypeIsAlarm(String buildingId,List<FnEnergyType> energyTypeList) throws Exception {
		Map<String,Boolean> result = new HashMap<String, Boolean>();
		for(FnEnergyType energyType : energyTypeList){
			result.put(energyType.getId(), this.FNAlarmService.queryEnergyTypeIsAlarm(buildingId, energyType.getId(),false));
		}
		return result;
	}


}
