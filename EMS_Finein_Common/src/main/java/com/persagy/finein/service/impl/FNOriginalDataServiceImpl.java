package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.mvc.dao.CoreDao;
import com.persagy.ems.finein.common.util.SystemPropertiesUtil;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.ems.pojo.originaldata.ElectricCurrentData;
import com.persagy.ems.pojo.originaldata.MonthData;
import com.persagy.ems.pojo.originaldata.StatData;
import com.persagy.finein.enumeration.EnumStatTimeType;
import com.persagy.finein.enumeration.EnumStatType;
import com.persagy.finein.service.FNOriginalDataService;
import com.persagy.finein.service.FNProjectService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNOriginalDataService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNOriginalDataServiceImpl extends FNBaseDictionaryServiceImpl implements FNOriginalDataService {

	@Resource(name = "FNProjectService")
	private FNProjectService FNProjectService;

	@Resource(name = "SystemPropertiesUtil")
	private SystemPropertiesUtil SystemPropertiesUtil;

	@Resource(name = "jdbcTemplateCoreDao")
	private CoreDao CoreDao;

	@Override
	public List<MonthData> queryMonthDataGteLt(String buildingId, String meterId, int funcId, Date timeFrom,
			Date timeTo) throws Exception {
		try {
			MonthData query = new MonthData();
			query.setBuildingForContainer(buildingId);
			query.setSign(meterId);
			query.setFuncid(funcId);
			query.setSpecialOperation("receivetime", SpecialOperator.$gte, timeFrom);
			query.setSpecialOperation("receivetime", SpecialOperator.$lte, timeTo);
			query.setSort("receivetime", EMSOrder.Asc);
			return CoreDao.query(query);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public MonthData queryLastMonthDataGteLte(String buildingId, String meterId, int funcId, Date timeFrom, Date timeTo)
			throws Exception {
		try {
			MonthData query = new MonthData();
			query.setBuildingForContainer(buildingId);
			query.setSign(meterId);
			query.setFuncid(funcId);
			if (timeFrom != null) {
				query.setSpecialOperation("receivetime", SpecialOperator.$gte, timeFrom);
			}
			if (timeTo != null) {
				query.setSpecialOperation("receivetime", SpecialOperator.$lte, timeTo);
			}
			query.setSort("receivetime", EMSOrder.Desc);
			query.setLimit(1L);
			List<MonthData> list = null;
			list = CoreDao.query(query);
			if (list != null && list.size() > 0) {
				return list.get(0);
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public List<StatData> queryStatDataGteLt(String buildingId, String meterId, int funcId, EnumStatTimeType timeType,
			EnumStatType statType, Date timeFrom, Date timeTo) throws Exception {
		try {
			StatData query = new StatData();
			query.setBuildingForContainer(buildingId);
			query.setSign(meterId);
			query.setFuncId(funcId);
			query.setTimeType(timeType.getValue());
			query.setValueType(statType.getValue());
			query.setSpecialOperation("counttime", SpecialOperator.$gte, timeFrom);
			query.setSpecialOperation("counttime", SpecialOperator.$lt, timeTo);
			return CoreDao.query(query);

		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public ElectricCurrentData queryLastEleDataGteLte(String buildingId, String meterId, int funcId, Date timeFrom,
			Date timeTo) throws Exception {
		try {
			ElectricCurrentData query = new ElectricCurrentData();
			query.setBuildingForContainer(buildingId);
			query.setSign(meterId);
			query.setFuncid(funcId);
			if (timeFrom != null) {
				query.setSpecialOperation("receivetime", SpecialOperator.$gte, timeFrom);
			}
			if (timeTo != null) {
				query.setSpecialOperation("receivetime", SpecialOperator.$lte, timeTo);
			}
			query.setSort("receivetime", EMSOrder.Desc);
			query.setLimit(1L);
			List<ElectricCurrentData> list = null;
			list = CoreDao.query(query);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	@Override
	public MonthData queryLastMonthDataGteLte(String meterId, int funcId, Date timeFrom, Date timeTo) throws Exception {
		Project project = FNProjectService.queryProject();
		if (project == null) {
			return null;
		}
		return this.queryLastMonthDataGteLte(project.getId(), meterId, funcId, timeFrom, timeTo);
	}

	@Override
	public ElectricCurrentData queryLastEleDataGteLte(String meterId, int funcId, Date timeFrom, Date timeTo)
			throws Exception {
		Project project = FNProjectService.queryProject();
		if (project == null) {
			return null;
		}
		return this.queryLastEleDataGteLte(project.getId(), meterId, funcId, timeFrom, timeTo);
	}

	@Override
	public List<StatData> queryStatDataGteLt(String meterId, int funcId, EnumStatTimeType timeType,
			EnumStatType statType, Date timeFrom, Date timeTo) throws Exception {
		Project project = FNProjectService.queryProject();
		if (project == null) {
			return null;
		}
		return this.queryStatDataGteLt(project.getId(), meterId, funcId, timeType, statType, timeFrom, timeTo);
	}

	@Override
	public void save(String buildingId, String meterId, int funcId, Double data, Date timeFrom) throws Exception {
		MonthData save = new MonthData();
		save.setBuildingForContainer(buildingId);
		save.setSign(meterId);
		save.setFuncid(funcId);
		save.setReceivetime(timeFrom);
		CoreDao.remove(save);
		save.setData(data);
		CoreDao.save(save);

	}

}
