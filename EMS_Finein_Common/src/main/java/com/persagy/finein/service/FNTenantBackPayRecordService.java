package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantBackPayRecord;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.enumeration.EnumYesNo;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantBackPayRecordService extends FNBaseService {
	
	public List<FnTenantBackPayRecord> queryGteLte(String buildingId, String tenantId, String energyTypeId, EnumTimeType timeType, EnumPrepayChargeType prepayChargeType, EnumYesNo isProcessed, Date timeFrom, Date timeTo) throws Exception;

	public void updateProcessStatus(String id, EnumYesNo isProcessed) throws Exception;

	public void saveData(String buildingId, String tenantId, String energyTypeId, EnumTimeType timeType, EnumPrepayChargeType prepayChargeType, Date timeFrom, Double data) throws Exception;
	
}
