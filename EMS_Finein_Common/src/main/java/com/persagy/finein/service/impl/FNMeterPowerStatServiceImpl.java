package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnMeterPowerStat;
import com.persagy.finein.enumeration.EnumStatType;
import com.persagy.finein.service.FNMeterPowerStatService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service("FNMeterPowerStatService")
@Transactional(propagation = Propagation.NOT_SUPPORTED)
public class FNMeterPowerStatServiceImpl extends FNBaseServiceImpl implements FNMeterPowerStatService {

	@Override
	public void saveDataList(List<FnMeterPowerStat> saveList) throws Exception {
		for (FnMeterPowerStat stat : saveList) {
			FnMeterPowerStat remove = new FnMeterPowerStat();
			remove.setMeterId(stat.getMeterId());
			remove.setDataType(stat.getDataType());
			this.remove(remove);
		}
		this.save(saveList);
	}

	@Override
	public void saveData(String meterId, EnumStatType statType, Date timeFrom, Double data) throws Exception {
		FnMeterPowerStat query = new FnMeterPowerStat();
		query.setMeterId(meterId);
		query.setDataType(statType.getValue());
		this.remove(query);
		query.setData(data);
		query.setTimeFrom(timeFrom);
		query.setLastUpdateTime(new Date());
		this.save(query);
	}

	@Override
	public List<FnMeterPowerStat> queryListGteLte(String id, Date timeFrom, Date timeTo) {
		
		return null;
	}

}
