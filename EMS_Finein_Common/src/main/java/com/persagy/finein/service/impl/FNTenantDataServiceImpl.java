package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.pojo.finein.FnTenant;
import com.persagy.ems.pojo.finein.FnTenantData;
import com.persagy.ems.pojo.finein.dictionary.Building;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.service.FNTenantDataService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantDataService")
@Transactional(propagation=Propagation.NOT_SUPPORTED)
public class FNTenantDataServiceImpl extends FNBaseServiceImpl  implements FNTenantDataService{

	@Override
	public List<FnTenantData> queryListGteLt(String buildingId, String tenantId, EnumTimeType timeType,
			String energyTypeId, Date timeFrom, Date timeTo, EnumEnergyMoney energyMoney) throws Exception {
		FnTenantData query = new FnTenantData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setTimeType(timeType.getValue().intValue());
		query.setEnergyTypeId(energyTypeId);
		if(energyMoney!=null){
			query.setDataType(energyMoney.getValue().intValue());
		}
		query.setSort("timeFrom", EMSOrder.Asc);
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("timeFrom", SpecialOperator.$lt, timeTo);
		return this.query(query);
	}

	@Override
	public List<FnTenantData> queryListGte(String buildingId, String tenantId, EnumTimeType timeType,
			String energyTypeId, Date timeFrom, EnumEnergyMoney energyMoney) throws Exception {
		FnTenantData query = new FnTenantData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setTimeType(timeType.getValue().intValue());
		query.setEnergyTypeId(energyTypeId);
		query.setDataType(energyMoney.getValue().intValue());
		query.setSort("timeFrom", EMSOrder.Asc);
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		return this.query(query);
	}

	@Override
	public Double queryDataGteLt(String buildingId, String tenantId, EnumTimeType timeType, String energyTypeId,
			Date timeFrom, Date timeTo, EnumEnergyMoney energyMoney) throws Exception {
		List<FnTenantData> sumList = this.queryListGteLt(buildingId, tenantId, timeType, energyTypeId, timeFrom, timeTo, energyMoney);
		Double sum = null;
		if(sumList != null){
			for(FnTenantData fnTenantSum : sumList){
				if(fnTenantSum.getData() != null){
					sum = sum == null ? fnTenantSum.getData() : sum + fnTenantSum.getData();
				}
			}
		}
		return sum;
	}

	@Override
	public Double queryDataGte(String buildingId, String tenantId, EnumTimeType timeType, String energyTypeId,
			Date timeFrom, EnumEnergyMoney energyMoney) throws Exception {
		List<FnTenantData> sumList = this.queryListGte(buildingId, tenantId, timeType, energyTypeId, timeFrom, energyMoney);
		Double sum = null;
		if(sumList != null){
			for(FnTenantData fnTenantSum : sumList){
				if(fnTenantSum.getData() != null){
					sum = sum == null ? fnTenantSum.getData() : sum + fnTenantSum.getData();
				}
			}
		}
		return sum;
	}

	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void saveData(String buildingId, String tenantId, EnumTimeType timeType, String energyTypeId, Date timeFrom,
			EnumEnergyMoney energyMoney, Double data) throws Exception {
		FnTenantData query = new FnTenantData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setTimeType(timeType.getValue());
		query.setEnergyTypeId(energyTypeId);
		query.setTimeFrom(timeFrom);
		query.setDataType(energyMoney.getValue());
		this.remove(query);
		if(data != null){
			query.setData(data);
			query.setLastUpdateTime(new Date());
			this.save(query);
		}
	}

	@Override
	public Map<String, Map<String, Double>> queryTenantData(String buildingId, String energyTypeId,
			EnumTimeType timeType, Date timeFrom) throws Exception {
		Map<String, Map<String, Double>> result = new HashMap<>();
		
		try {
			FnTenantData query = new FnTenantData();
			query.setBuildingId(buildingId);
			query.setEnergyTypeId(energyTypeId);
			query.setTimeType(timeType.getValue());
			query.setTimeFrom(timeFrom);
			List<FnTenantData> list = this.query(query);
//			StringBuffer sqlBuffer = new StringBuffer();
//			
//			String schema = SchemaUtil.getSchema(Schema.EMS);
//
//			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//			String time = sdf.format(timeFrom);
//			sqlBuffer.append("SELECT t.c_tenant_id,t.c_data_type,t.c_data from ");
//			sqlBuffer.append(schema).append(".").append(time.substring(0, 7).replace("-", "")).append("_t_fn_tenant_data t ");
//			sqlBuffer.append(" where t.c_building_id='");
//			sqlBuffer.append(buildingId).append("' and t.c_time_type = ").append(timeType.getValue()).append(" and t.c_energy_type_id = '").append(energyTypeId).append("' and t.c_time_from='").append(sdf.format(timeFrom)).append("'");
//
//			List<Map<String,Object>> dataList = this.queryBySql(sqlBuffer.toString(), null);
			if(list != null){
				for(FnTenantData tenantData : list){
					String c_tenant_id = tenantData.getTenantId();
					Integer c_data_type = tenantData.getDataType();
					Double c_data = DoubleFormatUtil.Instance().getDoubleData(tenantData.getData());
					if(c_data != null){
						if(!result.containsKey(c_tenant_id)){
							result.put(c_tenant_id, new HashMap<String, Double>());
						}
						result.get(c_tenant_id).put(c_data_type+"", c_data);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		return result;
	}

	@Override
	public Date queryTimefromOrder(Date from,Date to, EnumTimeType enumTimeType, FnTenant tenant, Building building,
			EnumEnergyMoney energyMoney, String energyTypeId,EMSOrder eMSOrder) throws Exception {
		FnTenantData query = new FnTenantData();
		query.setBuildingId(building.getId());
		if(tenant!=null){
			query.setTenantId(tenant.getId());
		}
		query.setTimeType(enumTimeType.getValue());
		query.setEnergyTypeId(energyTypeId);
		query.setDataType(energyMoney.getValue()); 
		query.setSpecialOperation("lastUpdateTime", SpecialOperator.$gte, from);
		query.setSpecialOperation("lastUpdateTime", SpecialOperator.$lt, to);
		query.setSort("timeFrom",eMSOrder);
		query.setLimit((long) 1);
		List<FnTenantData> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0).getTimeFrom();
		}else{
			return null;
		}
	}

	@Override
	public List<FnTenantData> queryListGteLte(String buildingId, String tenantId, EnumTimeType timeType,
			String energyTypeId, Date from, Date to, EnumEnergyMoney energyMoney) throws Exception {
		FnTenantData query = new FnTenantData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setTimeType(timeType.getValue().intValue());
		query.setEnergyTypeId(energyTypeId);
		query.setDataType(energyMoney.getValue().intValue());
		query.setSort("timeFrom", EMSOrder.Asc);
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, from);
		query.setSpecialOperation("timeFrom", SpecialOperator.$lte, to);
		return this.query(query);
	}

	@Override
	public FnTenantData queryHistoryMaxData(String buildingId, String tenantId, Date from, Date to, String energyTypeId,
			EnumTimeType enumTimeType, EnumEnergyMoney energyMoney) throws Exception {
		FnTenantData query = new FnTenantData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setTimeType(enumTimeType.getValue().intValue());
		query.setEnergyTypeId(energyTypeId);
		query.setDataType(energyMoney.getValue().intValue());
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, from);
		query.setSpecialOperation("timeFrom", SpecialOperator.$lt, to);
		query.setSort("data", EMSOrder.Desc);
		query.setLimit(1L);
		List<FnTenantData> dataList = this.query(query);
		if(dataList.size() > 0){
			return dataList.get(0);
		}
		return null;
	}

	@Override
	public void removeData(String buildingId, String tenantId, String energyTypeId, Date timeFrom) throws Exception {
		FnTenantData query = new FnTenantData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		this.remove(query);
	}

	@Override
	public Double queryData(String buildingId, String tenantId, EnumTimeType timeType, String energyTypeId, Date date,
			EnumEnergyMoney energyMoney) throws Exception {
		FnTenantData query = new FnTenantData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setTimeType(timeType.getValue().intValue());
		query.setEnergyTypeId(energyTypeId);
		query.setDataType(energyMoney.getValue().intValue());
		query.setTimeFrom(date);
		List<FnTenantData> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0).getData();
		}
		return null;
	}

	@Override
	public List<FnTenantData> queryDataList(String buildingId, List<String> tenantId, EnumTimeType timeType, String energyTypeId, Date timeFrom, Date timeTo, EnumEnergyMoney energyMoney) throws Exception {
		FnTenantData query = new FnTenantData();
		query.setBuildingId(buildingId);
		query.setSpecialOperation("tenantId", SpecialOperator.$in, tenantId);
		query.setTimeType(timeType.getValue().intValue());
		query.setEnergyTypeId(energyTypeId);
		if(energyMoney!=null){
			query.setDataType(energyMoney.getValue().intValue());
		}
		query.setSort("timeFrom", EMSOrder.Asc);
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("timeFrom", SpecialOperator.$lt, timeTo);
		return this.query(query);
	}
}
