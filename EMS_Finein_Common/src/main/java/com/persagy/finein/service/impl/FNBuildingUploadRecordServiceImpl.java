package com.persagy.finein.service.impl;


import com.persagy.ems.pojo.finein.FnCommonBuildingUpload;
import com.persagy.finein.service.FNBuildingUploadRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service("FNBuildingUploadRecordService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNBuildingUploadRecordServiceImpl extends FNBaseServiceImpl implements FNBuildingUploadRecordService {

	@Override
	public FnCommonBuildingUpload queryById(String id) throws Exception {
		FnCommonBuildingUpload query = new FnCommonBuildingUpload();
		query.setId(id);
		List<FnCommonBuildingUpload> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0);
		}
		return null;
	}
        
}
