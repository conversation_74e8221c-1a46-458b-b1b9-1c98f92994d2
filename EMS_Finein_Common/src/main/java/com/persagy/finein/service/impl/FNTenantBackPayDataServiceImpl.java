package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnTenantBackPayData;
import com.persagy.finein.enumeration.EnumPrepayChargeType;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.service.FNTenantBackPayDataService;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantBackPayDataService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantBackPayDataServiceImpl extends FNBaseServiceImpl  implements FNTenantBackPayDataService{

	@Override
	public FnTenantBackPayData queryLastData(String buildingId, String tenantId, String energyTypeId, EnumTimeType timeType,
			EnumPrepayChargeType prepayChargeType,Date timeTo) throws Exception {
		
		FnTenantBackPayData query = new FnTenantBackPayData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setTimeType(timeType.getValue());
		if(prepayChargeType!=null){
			query.setPrepayChargeType(prepayChargeType.getValue());
		}
		if(timeTo != null){
			query.setSpecialOperation("timeFrom", SpecialOperator.$lte,timeTo);
		}else{
			Date tomorrow = DateUtils.addDays(new Date(), 1);
			query.setSpecialOperation("timeFrom", SpecialOperator.$lte,tomorrow);
		}
		query.setSort("timeFrom", EMSOrder.Desc);
		return (FnTenantBackPayData)this.queryObject(query);
	}

	@Override
	public FnTenantBackPayData queryByDate(String buildingId, String tenantId, String energyTypeId,
			EnumTimeType timeType, EnumPrepayChargeType prepayChargeType, Date timeFrom) throws Exception {
		FnTenantBackPayData query = new FnTenantBackPayData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setTimeType(timeType.getValue());
		query.setPrepayChargeType(prepayChargeType.getValue());
		query.setTimeFrom(timeFrom);
		return (FnTenantBackPayData)this.queryObject(query);
	}

	@Override
	public void saveData(String buildingId, String tenantId, String energyTypeId, EnumTimeType timeType,
			EnumPrepayChargeType prepayChargeType, Date timeFrom, Double data) throws Exception {
		FnTenantBackPayData query = new FnTenantBackPayData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setTimeType(timeType.getValue());
		query.setPrepayChargeType(prepayChargeType.getValue());
		query.setTimeFrom(timeFrom);
		this.remove(query);
		query.setData(data);
		query.setLastUpdateTime(new Date());
		this.save(query);
	}

	@Override
	public List<FnTenantBackPayData> queryDataGteLt(String buildingId, String tenantId, String energyTypeId,
			EnumTimeType timeType, EnumPrepayChargeType prepayChargeType, Date timeFrom, Date timeTo) throws Exception {
		FnTenantBackPayData query = new FnTenantBackPayData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setTimeType(timeType.getValue());
		query.setPrepayChargeType(prepayChargeType.getValue());
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("timeFrom", SpecialOperator.$lt, timeTo);
		return this.query(query);
	}
	
	@Override
	public List<FnTenantBackPayData> queryDataGteLte(String buildingId, String tenantId, String energyTypeId,
			EnumTimeType timeType, EnumPrepayChargeType prepayChargeType, Date timeFrom, Date timeTo) throws Exception {
		FnTenantBackPayData query = new FnTenantBackPayData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setTimeType(timeType.getValue());
		query.setPrepayChargeType(prepayChargeType.getValue());
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("timeFrom", SpecialOperator.$lte, timeTo);
		return this.query(query);
	}

	@Override
	public void removeData(String buildingId, String tenantId, String energyTypeId, Date timeFrom) throws Exception {
		FnTenantBackPayData query = new FnTenantBackPayData();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		this.remove(query);
	}
}
