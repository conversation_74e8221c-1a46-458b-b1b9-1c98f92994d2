package com.persagy.finein.service.impl;


import com.persagy.finein.service.FNBuildingCloudUploadService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Service("FNBuildingCloudUploadService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNBuildingCloudUploadServiceImpl extends FNBaseServiceImpl implements FNBuildingCloudUploadService {

}
