package com.persagy.finein.service;

import java.util.Date;

/**
 * 
 * Description:   
 * Company: Persagy 
 * <AUTHOR> 
 * @version 1.0
 * @since: 2018年12月21日: 上午10:11:46
 * Update By 邵泓博 2018年12月21日: 上午10:11:46
 */
public interface FNCommonDataCollectRecordService extends FNBaseService {

	
	 /**
	 * Description: 
	 * @param meterId
	 * @return Date 上次采集时间
	 * <AUTHOR>
	 * @throws Exception 
	 * @since 2018年12月21日: 上午10:17:53
	 * Update By 邵泓博 2018年12月21日: 上午10:17:53
	 */
	 
	public Date queryLastCollectTime(String meterId) throws Exception;

	
	 /**
	 * Description: 
	 * @param meterId
	 * @param lastCollectTime void
	 * <AUTHOR>
	 * @throws Exception 
	 * @since 2018年12月28日: 上午10:56:26
	 * Update By 邵泓博 2018年12月28日: 上午10:56:26
	 */
	 
	public void saveOrUpdate(String meterId, Date lastCollectTime) throws Exception;
	
}
