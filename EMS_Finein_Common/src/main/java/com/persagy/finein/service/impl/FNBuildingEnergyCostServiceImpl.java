package com.persagy.finein.service.impl;


import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnBuildingEnergyCost;
import com.persagy.finein.service.FNBuildingEnergyCostService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service("FNBuildingEnergyCostService")
public class FNBuildingEnergyCostServiceImpl extends FNBaseServiceImpl implements FNBuildingEnergyCostService {

	@Override
	public void saveList(List<FnBuildingEnergyCost> saveList) throws Exception {
		for (FnBuildingEnergyCost stat : saveList) {
			FnBuildingEnergyCost remove = new FnBuildingEnergyCost();
			remove.setBuildingId(stat.getBuildingId());
			remove.setEnergyTypeId(stat.getEnergyTypeId());
			remove.setTimeFrom(stat.getTimeFrom());
			this.remove(remove);
		}
		this.save(saveList);
		
	}

	@Override
	public List<FnBuildingEnergyCost> queryByBuilding(String buildingId, Date timeFrom, Date timeTo) throws Exception {
		FnBuildingEnergyCost query = new FnBuildingEnergyCost();
		query.setBuildingId(buildingId);
		query.setSpecialOperation("timeFrom", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("timeFrom", SpecialOperator.$lt, timeTo);
		
		return this.query(query);
	}

	@Override
	public Date queryTimefromOrder(String buildingId, Date timeFrom, Date timeTo) throws Exception {
		FnBuildingEnergyCost query = new FnBuildingEnergyCost();
		query.setBuildingId(buildingId);
		query.setSpecialOperation("lastUpdateTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("lastUpdateTime", SpecialOperator.$lt, timeTo);
		query.setSort("timeFrom", EMSOrder.Asc);
		List<FnBuildingEnergyCost> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0).getTimeFrom();
		}
		return null;
	}
}
