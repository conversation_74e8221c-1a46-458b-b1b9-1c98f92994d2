package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.FnRecordPostClearingMeter;
import com.persagy.finein.service.FNRecordPostClearingMeterService;
import org.springframework.stereotype.Service;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNRecordPostClearingMeterService")
public class FNRecordPostClearingMeterServiceImpl extends FNBaseServiceImpl  implements FNRecordPostClearingMeterService{

	@Override
	public FnRecordPostClearingMeter queryLastClearingMeter(String buildingId, String tenantId, String energyTypeId,
			String meterId) throws Exception {
		FnRecordPostClearingMeter query = new FnRecordPostClearingMeter();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setMeterId(meterId);
		query.setSort("currentClearingTime", EMSOrder.Desc);
		query.setLimit(1L);
		return (FnRecordPostClearingMeter)this.queryObject(query);
	}

	
}
