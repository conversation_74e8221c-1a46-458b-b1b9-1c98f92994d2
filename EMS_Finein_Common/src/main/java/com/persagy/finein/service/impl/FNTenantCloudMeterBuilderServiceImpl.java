package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FNTenantCloudMeterBuilder;
import com.persagy.finein.service.FNTenantCloudMeterBuilderService;
import org.springframework.stereotype.Service;

import java.util.List;

/**

* 说明:云端-建筑租户仪表编码生成
*/

@Service("FNTenantCloudMeterBuilderService")
public class FNTenantCloudMeterBuilderServiceImpl extends FNBaseServiceImpl  implements FNTenantCloudMeterBuilderService{

	@Override
	public Integer queryCloudMeterId(String buildingId) throws Exception {
		FNTenantCloudMeterBuilder query = new FNTenantCloudMeterBuilder();
		query.setBuildingId(buildingId);
		List<FNTenantCloudMeterBuilder> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0).getCloudMeterId();
		}
		return null;
	}

	
}
