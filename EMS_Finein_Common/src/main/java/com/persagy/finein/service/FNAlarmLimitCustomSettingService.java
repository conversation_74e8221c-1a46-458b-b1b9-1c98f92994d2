package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnAlarmLimitCustomObj;
import com.persagy.ems.pojo.finein.FnAlarmLimitCustomSetting;

import java.util.List;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNAlarmLimitCustomSettingService extends FNBaseService {
	
	public void save(String buildingId, String tenantId, FnAlarmLimitCustomSetting setting, List<FnAlarmLimitCustomObj> list) throws Exception;

	public FnAlarmLimitCustomSetting query(String buildingId, String tenantId) throws Exception;
	
	public FnAlarmLimitCustomSetting queryOne(String buildingId, String tenantId) throws Exception;
	
}
