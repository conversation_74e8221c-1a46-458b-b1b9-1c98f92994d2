package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantMeterPowerStatRecord;
import com.persagy.finein.service.FNTenantMeterPowerStatRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service("FNTenantMeterPowerStatRecordService")
@Transactional(propagation=Propagation.NOT_SUPPORTED)
public class FNTenantMeterPowerStatRecordServiceImpl extends FNBaseServiceImpl  implements FNTenantMeterPowerStatRecordService{

	@Override
	public FnTenantMeterPowerStatRecord query(String buildingId, String tenantId, String energyTypeId) throws Exception {
		FnTenantMeterPowerStatRecord query = new FnTenantMeterPowerStatRecord();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		List<FnTenantMeterPowerStatRecord> list = this.query(query);
		if(list!=null&&list.size()>0){
			return list.get(0);
		}
		return null;
	
	}

}
