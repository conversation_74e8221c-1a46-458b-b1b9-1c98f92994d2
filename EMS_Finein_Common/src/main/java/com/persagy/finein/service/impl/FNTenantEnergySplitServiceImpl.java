package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnTenantEnergySplit;
import com.persagy.finein.service.FNTenantEnergySplitService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantEnergySplitService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantEnergySplitServiceImpl  extends FNBaseServiceImpl implements FNTenantEnergySplitService{

	@Override
	public Map<String, Map<String, FnTenantEnergySplit>> queryMap() throws Exception {
		Map<String, Map<String, FnTenantEnergySplit>> result = new HashMap<>();
		FnTenantEnergySplit query = new FnTenantEnergySplit();
		List<FnTenantEnergySplit> list = this.query(query);
		if(list != null){
			for(FnTenantEnergySplit entity : list){
				if(!result.containsKey(entity.getTenantId())){
					result.put(entity.getTenantId(), new HashMap<String, FnTenantEnergySplit>());
				}
				result.get(entity.getTenantId()).put(entity.getEnergyTypeId(), entity);
			}
		}
		return result;
	}

	@Override
	public FnTenantEnergySplit queryEnergySplit(String tenantId, String energyTypeId) throws Exception {
		FnTenantEnergySplit query = new FnTenantEnergySplit();
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		
		return (FnTenantEnergySplit)this.queryObject(query);
	}

	@Override
	public Map<String, FnTenantEnergySplit> queryEnergySplit(List<String> tenantIdList, String energyTypeId)
			throws Exception {
		Map<String, FnTenantEnergySplit> result = new HashMap<>();
		FnTenantEnergySplit query = new FnTenantEnergySplit();
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("tenantId", SpecialOperator.$in, tenantIdList);
		List<FnTenantEnergySplit> list = this.query(query);
		if(list != null){
			for(FnTenantEnergySplit entity : list){
				result.put(entity.getTenantId(), entity);
			}
		}
		return result;
	}

	
}
