package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnTenantMeterPowerStat;
import com.persagy.finein.enumeration.EnumBodyType;
import com.persagy.finein.service.FNTenantMeterPowerStatService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

@Service("FNTenantMeterPowerStatService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNTenantMeterPowerStatServiceImpl extends FNBaseServiceImpl  implements FNTenantMeterPowerStatService{

	@Override
	public void saveDataList(List<FnTenantMeterPowerStat> saveList) throws Exception {
		for (FnTenantMeterPowerStat stat : saveList) {
			FnTenantMeterPowerStat remove = new FnTenantMeterPowerStat();
			remove.setBuildingId(stat.getBuildingId());
			remove.setEnergyTypeId(stat.getEnergyTypeId());
			remove.setBodyType(stat.getBodyType());
			remove.setTenantId(stat.getTenantId());
			remove.setBodyId(stat.getBodyId());
			remove.setCountTime(stat.getCountTime());
			this.remove(remove);
		}
		this.save(saveList);
	}

	@Override
	public List<FnTenantMeterPowerStat> queryListGteLte(String buildingId, String tenantId, EnumBodyType bodyType,
			String energyTypeId, Date timeFrom, Date timeTo) throws Exception {
		FnTenantMeterPowerStat query = new FnTenantMeterPowerStat();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setBodyType(bodyType.getValue());
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("countTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("countTime", SpecialOperator.$lte, timeTo);
		
		return this.query(query);
	}

	@Override
	public List<FnTenantMeterPowerStat> queryTenantDataList(String buildingId, List<String> tenantId, EnumBodyType bodyType, String energyTypeId, Date timeFrom, Date timeTo) throws Exception {
		FnTenantMeterPowerStat query = new FnTenantMeterPowerStat();
		query.setBuildingId(buildingId);
		query.setSpecialOperation("tenantId", SpecialOperator.$in, tenantId);
		query.setBodyType(bodyType.getValue());
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("countTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("countTime", SpecialOperator.$lte, timeTo);
		return this.query(query);
	}
}
