package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnRecordPostClearingPay;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNRecordPostClearingPayService extends FNBaseService {
	
	public Map<String,List<FnRecordPostClearingPay>> queryNotPayRecord(String buildingId, String energyTypeId) throws Exception;

	public List<FnRecordPostClearingPay> queryNotPayRecord(String buildingId, String tenantId, String energyTypeId) throws Exception;

	public List<FnRecordPostClearingPay> queryNotPayRecord(Date timeFrom, Date timeTo, String buildingId, String tenantId, String energyTypeId) throws Exception;

	public List<FnRecordPostClearingPay> queryNotPayRecord(String buildingId, String tenantId, String energyTypeId, List<String> orderList) throws Exception;

	public void payRecord(String buildingId, String tenantId, String energyTypeId, List<String> orderList) throws Exception;

	public void payAllRecord(String buildingId, String tenantId) throws Exception;

	public Date queryLastClearingDate(String buildingId, String tenantId, String energyTypeId) throws Exception;

	public List<FnRecordPostClearingPay> queryAllRecord(Date timeFrom, Date timeTo, String buildingId, String tenantId, String energyTypeId) throws Exception;

	public Map<String,List<FnRecordPostClearingPay>> queryNotPayRecord(String tenantId) throws Exception;

	public List<FnRecordPostClearingPay> queryNotPayRecord(String buildingId, List<String> tenantIdList, String energyTypeId, Date timeFrom, Date timeTo) throws Exception;

	//查询上次结算时间
	public Date queryLastClearingTime(String tenantId, String energyTypeId, Date activeTime) throws Exception;

	//totalEnergy totalMoney orderCount  已结算，未缴费
	public Map<String,Object> queryBilling(String buildingId, String tenantId, String energyTypeId) throws Exception;

	public List<FnRecordPostClearingPay> queryNotPayRecord(List<String> tenantIdList, String energyTypeId) throws Exception;
	
}
