package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnBuildingEnergyCost;

import java.util.Date;
import java.util.List;

public interface FNBuildingEnergyCostService extends FNBaseService {

	void saveList(List<FnBuildingEnergyCost> saveList) throws Exception;

	List<FnBuildingEnergyCost> queryByBuilding(String buildingId, Date timeFrom, Date timeTo) throws Exception;

	Date queryTimefromOrder(String buildingId, Date timeFrom, Date now) throws Exception;
	
}
