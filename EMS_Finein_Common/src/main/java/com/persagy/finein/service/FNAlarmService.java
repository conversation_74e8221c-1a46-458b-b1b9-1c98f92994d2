package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnAlarm;
import com.persagy.finein.enumeration.EnumAlarmStatus;

import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:31:53
 * 
 * 说明:
 */

public interface FNAlarmService extends FNBaseService {

	public List<FnAlarm> queryAlarmByTenantId(String tenantId) throws Exception;

	public boolean queryEnergyTypeIsAlarm(String buildingId, String energyTypeId) throws Exception;

	public boolean queryEnergyTypeIsAlarm(String buildingId, String energyTypeId, boolean isContainGongLvAlarm)
			throws Exception;

	public List<FnAlarm> queryAlarmByTenantId(String tenantId, EnumAlarmStatus alarmStatus) throws Exception;

	public void updateAlarmStatus(List<String> alarmIdList, EnumAlarmStatus alarmStatus) throws Exception;

	public void updateAlarmStatus(String tenantId, String alarmTypeId, EnumAlarmStatus alarmStatus, String meterId)
			throws Exception;

	public void updateAlarmPushStatus(String tenantId, String alarmTypeId, EnumAlarmStatus alarmStatus, String meterId)
			throws Exception;

	public FnAlarm queryAlarmWeiHuiFu(String tenantId, String alarmTypeId) throws Exception;

	public FnAlarm queryAlarmWeiHuiFu(String tenantId, String meterId, String alarmTypeId) throws Exception;

	public List<FnAlarm> queryAlarmWaitPush(long limit) throws Exception;
	
	public List<FnAlarm> queryGlobalAlarmWaitPush(String buildingId, long limit) throws Exception;

}
