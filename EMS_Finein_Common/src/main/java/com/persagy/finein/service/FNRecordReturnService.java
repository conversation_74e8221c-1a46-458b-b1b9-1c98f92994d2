package com.persagy.finein.service;

import com.persagy.ems.dto.DTORecordPrePayReturn;
import com.persagy.ems.pojo.finein.FnRecordReturn;
import com.persagy.finein.enumeration.EnumPayBodyType;

import java.util.Date;
import java.util.List;

/**

* 说明:
*/

public interface FNRecordReturnService extends FNBaseService {
	
	public List<FnRecordReturn> queryTenantReturnRecord(String tenantId, String energyTypeId, Date timeFrom, Date timeTo) throws Exception;

	public List<FnRecordReturn> queryTenantPrePayRecord(List<String> tenantIdList, EnumPayBodyType payBodyType, String energyTypeId, Date timeFrom, Date timeTo) throws Exception;

	public List<DTORecordPrePayReturn> queryByBuilding(String buildingId, String energyTypeId, Date from, Date to)
			throws Exception;

	public List<FnRecordReturn> queryByBuildingId(String buildingId, String energyTypeId, Date from, Date to) throws Exception;
}
