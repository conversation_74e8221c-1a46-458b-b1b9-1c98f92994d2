package com.persagy.finein.service.impl;

import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.finein.common.util.SchemaUtil;
import com.persagy.ems.pojo.finein.FnRoom;
import com.persagy.ems.pojo.finein.FnRoomMeter;
import com.persagy.finein.service.FNRoomMeterService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNRoomMeterService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNRoomMeterServiceImpl extends FNBaseServiceImpl implements FNRoomMeterService {

	@Override
	public Map<String, List<DTOMeter>> queryRoomMeter(String buildingId) throws Exception {
		FnRoomMeter query = new FnRoomMeter();
		query.setBuildingId(buildingId);
		Map<String, List<DTOMeter>> result = new HashMap<String, List<DTOMeter>>();

		List<FnRoomMeter> roomMeterList = this.query(query);
		if (roomMeterList != null) {
			for (FnRoomMeter roomMeter : roomMeterList) {
				if (!result.containsKey(roomMeter.getRoomCode())) {
					result.put(roomMeter.getRoomCode(), new ArrayList<DTOMeter>());
				}
				DTOMeter dtoMeter = new DTOMeter();
				dtoMeter.setMeterId(roomMeter.getMeterId());
				dtoMeter.setMeterName(roomMeter.getMeterId());
				dtoMeter.setEnergyTypeId(roomMeter.getEnergyTypeId());
				result.get(roomMeter.getRoomCode()).add(dtoMeter);
			}
		}
		return result;
	}

	@Override
	public List<DTOMeter> queryRoomMeter(String buildingId, String roomId, String energyTypeId) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();

		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("SELECT m.c_id,m.c_energy_type_id,m.c_meter_type,m.c_is_use from ");
		sqlBuffer.append(schema).append(".t_fn_room_meter tr,");
		sqlBuffer.append(schema).append(".t_fn_meter m ");
		sqlBuffer.append(" where tr.c_building_id='");
		sqlBuffer.append(buildingId).append("' and tr.c_room_id='").append(roomId).append("' ");

		if (energyTypeId != null) {
			sqlBuffer.append(" and tr.c_energy_type_id='").append(energyTypeId).append("' ");
		}
		sqlBuffer.append(" and tr.c_meter_id=m.c_id ORDER BY tr.c_meter_id asc");

		List<DTOMeter> result = new ArrayList<>();

		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null) {
			for (Map<String, Object> map : list) {
				String c_id = (String) map.get("c_id");
				String c_energy_type_id = (String) map.get("c_energy_type_id");
				Integer c_meter_type = (Integer) map.get("c_meter_type");
				Integer c_is_use = (Integer) map.get("c_is_use");

				DTOMeter dtoMeter = new DTOMeter();
				dtoMeter.setMeterId(c_id);
				dtoMeter.setEnergyTypeId(c_energy_type_id);
				dtoMeter.setMeterType(c_meter_type);
				dtoMeter.setIsUse(c_is_use);
				result.add(dtoMeter);
			}
		}
		return result;
	}

	@Override
	public List<String> queryMeterIds(String buildingId, String roomCode) throws Exception {
		List<String> result = new ArrayList<String>();
		FnRoomMeter query = new FnRoomMeter();
		query.setBuildingId(buildingId);
		query.setRoomCode(roomCode);
		List<FnRoomMeter> list = this.query(query);
		if (list != null && list.size() != 0) {
			for (FnRoomMeter fnRoomMeter : list) {
				result.add(fnRoomMeter.getMeterId());
			}
			return result;
		}
		return null;
	}

	@Override
	public Map<String, FnRoom> queryRoomByMeter(String meterId) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append("SELECT m.* from ");
		sqlBuffer.append(schema).append(".t_fn_room_meter rm,");
		sqlBuffer.append(schema).append(".t_fn_room m ");
		sqlBuffer.append(" where rm.c_meter_id='");
		sqlBuffer.append(meterId).append("' and rm.c_room_id= m.c_id");
		Map<String, FnRoom> result = new HashMap<>();
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null) {
			for (Map<String, Object> map : list) {
				String c_id = (String) map.get("c_id");
				String c_building_id = (String) map.get("c_building_id");
				String c_floor_id = (String) map.get("c_floor_id");
				String c_code = (String) map.get("c_code");
				String c_energy_type_ids = (String) map.get("c_energy_type_ids");
				Integer c_status = (Integer) map.get("c_status");
				String c_remark = (String) map.get("c_remark");
				String c_create_user_id = (String) map.get("c_create_user_id");
				Integer c_is_valid = (Integer) map.get("c_is_valid");
				Double c_area = Double.parseDouble(map.get("c_area").toString());
				Date c_create_time = (Date) map.get("c_create_time");
				Date c_invalid_time = (Date) map.get("c_invalid_time");

				FnRoom fnRoom = new FnRoom();
				fnRoom.setBuildingId(c_building_id);
				fnRoom.setId(c_id);
				fnRoom.setCode(c_code);
				fnRoom.setCreateTime(c_create_time);
				fnRoom.setCreateUserId(c_create_user_id);
				fnRoom.setEnergyTypeIds(c_energy_type_ids);
				fnRoom.setFloorId(c_floor_id);
				fnRoom.setInvalidTime(c_invalid_time);
				fnRoom.setIsValid(c_is_valid);
				fnRoom.setRemark(c_remark);
				fnRoom.setStatus(c_status);
				fnRoom.setArea(c_area);
				result.put(c_id, fnRoom);
			}
		}
		return result;
	}
}
