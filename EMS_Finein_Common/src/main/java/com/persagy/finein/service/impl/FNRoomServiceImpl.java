package com.persagy.finein.service.impl;

import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.dto.DTOEnergyTypeMeter;
import com.persagy.ems.dto.DTOMeter;
import com.persagy.ems.dto.DTORoom;
import com.persagy.ems.dto.DTORoomMeter;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.SchemaUtil;
import com.persagy.ems.pojo.finein.FnRoom;
import com.persagy.finein.enumeration.EnumUseStatus;
import com.persagy.finein.service.FNRoomService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.Map.Entry;

/**
 * 作者:zhangyuan(kedou)
 *
 * 时间:2017年9月24日 下午3:32:38
 *
 * 说明:
 */

@Service("FNRoomService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNRoomServiceImpl extends FNBaseServiceImpl implements FNRoomService {

	@Override
	public List<FnRoom> queryListByFloorId(String floorId) throws Exception {
		FnRoom query = new FnRoom();
		query.setFloorId(floorId);

		return this.query(query);
	}

	@Override
	public List<FnRoom> queryListByBuildingId(String buildingId) throws Exception {
		FnRoom query = new FnRoom();
		query.setSort("code", EMSOrder.Asc);
		query.setBuildingId(buildingId);

		return this.query(query);
	}

	@Override
	@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
	public void updateRoomStatus(String buildingId, List<String> idList, EnumUseStatus status) throws Exception {
		FnRoom query = new FnRoom();
		query.setBuildingId(buildingId);
		query.setSpecialOperation("id", SpecialOperator.$in, idList);

		FnRoom update = new FnRoom();
		update.setStatus(status.getValue());
		this.update(query, update);
	}

	@Override
	public List<DTORoom> queryRoomListByTenantId(String tenantId) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();
		// SELECT rm.c_room_id,rm.c_room_code,rm.c_energy_type_id,rm.c_meter_id
		// from t_fn_tenant t,t_fn_tenant_room tr,t_fn_room_meter rm where
		// t.c_id='TN001' and t.c_id=tr.c_tenant_id and
		// tr.c_room_id=rm.c_room_id ORDER BY rm.c_room_code asc,rm.c_meter_id
		// asc
		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("SELECT rm.c_room_id,rm.c_room_code,rm.c_energy_type_id,rm.c_meter_id from ");
		sqlBuffer.append(schema).append(".t_fn_tenant t,");
		sqlBuffer.append(schema).append(".t_fn_tenant_room tr,");
		sqlBuffer.append(schema).append(".t_fn_room_meter rm where t.c_id = '");
		sqlBuffer.append(tenantId).append(
				"' and t.c_id=tr.c_tenant_id and tr.c_room_id=rm.c_room_id  ORDER BY rm.c_room_code asc,rm.c_meter_id asc");

		// SELECT t.c_id,tr.c_room_id,tr.c_room_code,r.c_floor_id from
		// t_fn_tenant t,t_fn_tenant_room tr,t_fn_room r where t.c_id =
		// tr.c_tenant_id and tr.c_room_id = r.c_id and t.c_building_id='' and
		// t.c_status = 1 and t.c_is_valid = 1;
		Map<String, DTORoom> roomMap = new LinkedHashMap<String, DTORoom>();

		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);

		if (list != null) {
			for (Map<String, Object> map : list) {
				String c_room_id = (String) map.get("c_room_id");
				String c_room_code = (String) map.get("c_room_code");
				String c_energy_type_id = (String) map.get("c_energy_type_id");
				String c_meter_id = (String) map.get("c_meter_id");

				if (!roomMap.containsKey(c_room_id)) {
					DTOEnergyTypeMeter query = new DTOEnergyTypeMeter();
					List<DTOEnergyTypeMeter> eneryList = new ArrayList<DTOEnergyTypeMeter>();
					List<String> meterList = new ArrayList<String>();

					query.setEnergyTypeId(c_energy_type_id);
					meterList.add(c_meter_id);
					query.setMeterList(meterList);

					eneryList.add(query);

					DTORoom room = new DTORoom();
					room.setRoomId(c_room_id);
					room.setRoomCode(c_room_code);
					room.setEneryList(eneryList);
					roomMap.put(c_room_id, room);
				} else {
					DTORoom room = roomMap.get(c_room_id);
					List<DTOEnergyTypeMeter> energyList = room.getEneryList();
					boolean isFind = false;
					for (DTOEnergyTypeMeter energyTypeMeter : energyList) {
						if (energyTypeMeter.getEnergyTypeId().equals(c_energy_type_id)) {
							if (!energyTypeMeter.getMeterList().contains(c_meter_id)) {
								energyTypeMeter.getMeterList().add(c_meter_id);
							}
							isFind = true;
							break;
						}
					}
					if (!isFind) {
						DTOEnergyTypeMeter query = new DTOEnergyTypeMeter();
						List<String> meterList = new ArrayList<String>();
						query.setEnergyTypeId(c_energy_type_id);
						meterList.add(c_meter_id);
						query.setMeterList(meterList);
						room.getEneryList().add(query);
					}
				}
			}
		}
		List<DTORoom> roomList = new ArrayList<DTORoom>();
		for (Entry<String, DTORoom> entry : roomMap.entrySet()) {
			roomList.add(entry.getValue());
		}
		return roomList;
	}

	@Override
	public List<DTORoomMeter> queryRoomListByTenantId(String tenantId, String energyId) throws Exception {
		// SELECT tr.c_id ,rm.c_room_code ,m.*
		// FROM
		// t_fn_tenant_room AS tr ,
		// t_fn_room_meter AS rm ,
		// t_fn_meter AS m
		// WHERE
		// tr.c_room_id = rm.c_room_id AND
		// rm.c_meter_id = m.c_id AND
		// tr.c_tenant_id IN ('ZHBH_1001','ZHBH_1002') AND
		// m.c_energy_type_id = 'Dian'
		List<DTORoomMeter> result = new ArrayList<>();
		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append(
				"SELECT tr.c_tenant_id ,rm.c_room_id,rm.c_room_code ,m.*  from ");
		sqlBuffer.append(schema).append(".t_fn_tenant_room AS tr ,");
		sqlBuffer.append(schema).append(".t_fn_room_meter AS rm ,");
		sqlBuffer.append(schema).append(".t_fn_meter AS m ");
		sqlBuffer.append("WHERE tr.c_room_id = rm.c_room_id AND rm.c_meter_id = m.c_id AND tr.c_tenant_id = '")
				.append(tenantId).append("' ");
		if (energyId != null) {
			sqlBuffer.append(" and m.c_energy_type_id = '" + energyId + "'");
		}
		sqlBuffer.append(" ORDER by rm.c_room_id ,m.c_id");
		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		Map<String, DTORoomMeter> roomMap = new LinkedHashMap<String, DTORoomMeter>();
		if (list != null) {
			for (Map<String, Object> map : list) {
				String c_room_code = (String) map.get("c_room_code");
				String c_room_id = (String) map.get("c_room_id");
				String c_id = (String) map.get("c_id");
				String c_extend = (String) map.get("c_extend");
				String c_energy_type_id = (String) map.get("c_energy_type_id");
				String c_install_address = (String) map.get("c_install_address");
				Integer c_is_use = (Integer) map.get("c_is_use");
				Integer c_meter_type = (Integer) map.get("c_meter_type");
				String c_protocol_id = (String) map.get("c_protocol_id");
				String c_client_ip = (String) map.get("c_client_ip");
				String c_server_ip = (String) map.get("c_server_ip");
				Double c_radio = DoubleFormatUtil.Instance().getDoubleData(map.get("c_radio"));
				if (!roomMap.containsKey(c_room_id)) {
					DTORoomMeter roomMeter = new DTORoomMeter();
					roomMeter.setRoomCode(c_room_code);
					roomMeter.setRoomId(c_room_id);
					List<DTOMeter> dtoMeterList = new ArrayList<DTOMeter>();
					DTOMeter dtoMeter = new DTOMeter();
					dtoMeter.setMeterId(c_id);
					dtoMeter.setExtend(c_extend);
					dtoMeter.setEnergyTypeId(c_energy_type_id);
					dtoMeter.setMeterName(c_install_address);
					dtoMeter.setIsUse(c_is_use);
					dtoMeter.setMeterType(c_meter_type);
					dtoMeter.setProtocolId(c_protocol_id);
					dtoMeter.setClientIp(c_client_ip);
					dtoMeter.setServerIp(c_server_ip);
					dtoMeter.setRadio(c_radio);
					dtoMeterList.add(dtoMeter);
					roomMeter.setMeterList(dtoMeterList);
					roomMap.put(c_room_id, roomMeter);
				} else {
					DTORoomMeter dtoRoomMeter = roomMap.get(c_room_id);
					DTOMeter dtoMeter = new DTOMeter();
					dtoMeter.setMeterId(c_id);
					dtoMeter.setExtend(c_extend);
					dtoMeter.setEnergyTypeId(c_energy_type_id);
					dtoMeter.setMeterName(c_install_address);
					dtoMeter.setIsUse(c_is_use);
					dtoMeter.setMeterType(c_meter_type);
					dtoMeter.setProtocolId(c_protocol_id);
					dtoMeter.setClientIp(c_client_ip);
					dtoMeter.setServerIp(c_server_ip);
					dtoMeter.setRadio(c_radio);
					dtoRoomMeter.getMeterList().add(dtoMeter);
				}
			}

			for (Entry<String, DTORoomMeter> entry : roomMap.entrySet()) {
				result.add(entry.getValue());
			}
		}
		return result;
	}

	@Override
	public Map<String, List<DTORoom>> queryRoomByFloor(String buildingId) throws Exception {

		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("SELECT fr.c_floor_id,rm.c_room_id,rm.c_room_code,rm.c_energy_type_id,rm.c_meter_id from ");
		sqlBuffer.append(schema).append(".t_fn_room fr,");
		sqlBuffer.append(schema).append(".t_fn_room_meter rm where ");
		sqlBuffer.append("fr.c_id=rm.c_room_id  ORDER BY rm.c_room_code asc,rm.c_meter_id asc");

		// SELECT t.c_id,tr.c_room_id,tr.c_room_code,r.c_floor_id from
		// t_fn_tenant t,t_fn_tenant_room tr,t_fn_room r where t.c_id =
		// tr.c_tenant_id and tr.c_room_id = r.c_id and t.c_building_id='' and
		// t.c_status = 1 and t.c_is_valid = 1;
		// Map<String, DTORoom> roomMap = new LinkedHashMap<String, DTORoom>();
		Map<String, Map<String, DTORoom>> roomFloorMap = new LinkedHashMap<String, Map<String, DTORoom>>();

		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);

		if (list != null) {
			for (Map<String, Object> map : list) {
				String c_floor_id = (String) map.get("c_floor_id");
				String c_room_id = (String) map.get("c_room_id");
				String c_room_code = (String) map.get("c_room_code");
				String c_energy_type_id = (String) map.get("c_energy_type_id");
				String c_meter_id = (String) map.get("c_meter_id");
				if (roomFloorMap.get(c_floor_id) == null) {
					Map<String, DTORoom> roomMap = new LinkedHashMap<String, DTORoom>();
					roomFloorMap.put(c_floor_id, roomMap);
				}
				if (!roomFloorMap.get(c_floor_id).containsKey(c_room_id)) {
					DTOEnergyTypeMeter query = new DTOEnergyTypeMeter();
					List<DTOEnergyTypeMeter> eneryList = new ArrayList<DTOEnergyTypeMeter>();
					List<String> meterList = new ArrayList<String>();

					query.setEnergyTypeId(c_energy_type_id);
					meterList.add(c_meter_id);
					query.setMeterList(meterList);

					eneryList.add(query);

					DTORoom room = new DTORoom();
					room.setRoomId(c_room_id);
					room.setRoomCode(c_room_code);
					room.setEneryList(eneryList);
					roomFloorMap.get(c_floor_id).put(c_room_id, room);
				} else {
					DTORoom room = roomFloorMap.get(c_floor_id).get(c_room_id);
					List<DTOEnergyTypeMeter> energyList = room.getEneryList();
					boolean isFind = false;
					for (DTOEnergyTypeMeter energyTypeMeter : energyList) {
						if (energyTypeMeter.getEnergyTypeId().equals(c_energy_type_id)) {
							if (!energyTypeMeter.getMeterList().contains(c_meter_id)) {
								energyTypeMeter.getMeterList().add(c_meter_id);
							}
							isFind = true;
							break;
						}
					}
					if (!isFind) {
						DTOEnergyTypeMeter query = new DTOEnergyTypeMeter();
						List<String> meterList = new ArrayList<String>();
						query.setEnergyTypeId(c_energy_type_id);
						meterList.add(c_meter_id);
						query.setMeterList(meterList);
						room.getEneryList().add(query);
					}
				}
			}
		}

		Map<String, List<DTORoom>> result = new HashMap<String, List<DTORoom>>();
		for (Entry<String, Map<String, DTORoom>> entry : roomFloorMap.entrySet()) {
			if (!result.containsKey(entry.getKey())) {
				result.put(entry.getKey(), new ArrayList<DTORoom>());
			}
			for (Entry<String, DTORoom> entry1 : entry.getValue().entrySet()) {
				result.get(entry.getKey()).add(entry1.getValue());
			}
		}
		return result;
	}

	@Override
	public Map<String, FnRoom> queryMap(String buildingId) throws Exception {
		Map<String,FnRoom> map = new HashMap<String,FnRoom>();
		FnRoom query = new FnRoom();
		query.setBuildingId(buildingId);
		List<FnRoom> list = this.query(query);
		if (list != null && list.size() > 0) {
			for (FnRoom fnRoom : list) {
				map.put(fnRoom.getId(), fnRoom);
			}
		}
		return map;
	}
}
