package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.system.FileResource;
import com.persagy.finein.service.FNFileResourceService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNFileResourceService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNFileResourceServiceImpl extends FNBaseServiceImpl implements FNFileResourceService{


	@Override
	public FileResource query(String resourceId) throws Exception {
		FileResource query = new FileResource();
		query.setId(resourceId);
		return (FileResource)this.queryObject(query);
	}


	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void delete(String resourceId) throws Exception {
		FileResource query = new FileResource();
		query.setId(resourceId);
		this.remove(query);
	}
}
