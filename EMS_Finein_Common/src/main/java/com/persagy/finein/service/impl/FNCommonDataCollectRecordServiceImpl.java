package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnCommonDataCollectRecord;
import com.persagy.finein.service.FNCommonDataCollectRecordService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 
 * Description: Company: Persagy
 * 
 * <AUTHOR>
 * @version 1.0
 * @since: 2018年12月21日: 上午10:14:10 Update By 邵泓博 2018年12月21日: 上午10:14:10
 */

@Service("FNCommonDataCollectRecordService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNCommonDataCollectRecordServiceImpl extends FNBaseServiceImpl
		implements FNCommonDataCollectRecordService {

	@Override
	public Date queryLastCollectTime(String meterId) throws Exception {
		FnCommonDataCollectRecord query = new FnCommonDataCollectRecord();
		query.setMeterId(meterId);
		List<FnCommonDataCollectRecord> list = this.query(query);
		if (list != null && list.size() > 0) {
			return list.get(0).getLastCollectTime();
		}
		return null;
	}

	
	@Override
	public void saveOrUpdate(String meterId, Date lastCollectTime) throws Exception {
		FnCommonDataCollectRecord query = new FnCommonDataCollectRecord();
		query.setMeterId(meterId);
		List<FnCommonDataCollectRecord> list = this.query(query);
		if(list!=null&&list.size()>0){
			query.setLastCollectTime(lastCollectTime);
			query.setLastUpdateTime(new Date());
			this.update(list.get(0), query);
		}else{
			query.setLastCollectTime(lastCollectTime);
			query.setLastUpdateTime(new Date());
			this.save(query);
		}
		
	}

}
