package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantMeterPowerStat;
import com.persagy.finein.enumeration.EnumBodyType;

import java.util.Date;
import java.util.List;

public interface FNTenantMeterPowerStatService extends FNBaseService {

	void saveDataList(List<FnTenantMeterPowerStat> saveList) throws Exception;

	List<FnTenantMeterPowerStat> queryListGteLte(String buildingId, String tenantId, EnumBodyType bodyType,
                                                 String energyTypeId, Date timeFrom, Date timeTo) throws Exception;

	/**
	 * 查询电功率数据
	 * @param buildingId 建筑id
	 * @param tenantId 租户id
	 * @param bodyType  主体类型
	 * @param energyTypeId 能源类型id
	 * @param timeFrom 开始时间
	 * @param timeTo 结束时间
	 * @return
	 * @throws Exception
	 */
	List<FnTenantMeterPowerStat> queryTenantDataList(String buildingId,List<String> tenantId, EnumBodyType bodyType,
												 String energyTypeId, Date timeFrom, Date timeTo) throws Exception;

}
