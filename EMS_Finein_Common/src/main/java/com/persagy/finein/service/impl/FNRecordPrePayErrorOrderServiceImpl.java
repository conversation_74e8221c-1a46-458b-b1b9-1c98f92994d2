package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnRecordPrePayErrorOrder;
import com.persagy.finein.service.FNRecordPrePayErrorOrderService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 */

@Service("FNRecordPrePayErrorOrderService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNRecordPrePayErrorOrderServiceImpl extends FNBaseServiceImpl implements FNRecordPrePayErrorOrderService {

	/**
	 * (non-Javadoc)
	 * @throws Exception 
	 * 
	 * @see FNRecordPrePayErrorOrderService#queryByOrderId(String)
	 */

	@Override
	public FnRecordPrePayErrorOrder queryByOrderId(String orderId) throws Exception {
		FnRecordPrePayErrorOrder query = new FnRecordPrePayErrorOrder();
		query.setOrderId(orderId);
		List<FnRecordPrePayErrorOrder> list = this.query(query);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

}
