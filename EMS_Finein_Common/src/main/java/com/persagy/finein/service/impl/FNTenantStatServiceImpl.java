package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnTenantStat;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumStatType;
import com.persagy.finein.enumeration.EnumTimeType;
import com.persagy.finein.service.FNTenantStatService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNTenantStatService")
@Transactional(propagation=Propagation.NOT_SUPPORTED)
public class FNTenantStatServiceImpl extends FNBaseServiceImpl  implements FNTenantStatService{

	@Override
	public void saveData(String buildingId, String tenantId, String energyTypeId, EnumTimeType timeType,
			EnumStatType statType, EnumEnergyMoney energyMoney, Date timeFrom, Double data) throws Exception {
		FnTenantStat query = new FnTenantStat();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setTimeType(timeType.getValue());
		query.setStatType(statType.getValue());
		query.setDataType(energyMoney.getValue());
		this.remove(query);
		if(data != null){
			query.setTimeFrom(timeFrom);
			query.setData(data);
			query.setLastUpdateTime(new Date());
			this.save(query);
		}
	}

	@Override
	public FnTenantStat queryData(String buildingId, String tenantId, String energyTypeId, EnumTimeType timeType,
			EnumStatType statType, EnumEnergyMoney energyMoney) throws Exception {
		FnTenantStat query = new FnTenantStat();
		query.setBuildingId(buildingId);
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setTimeType(timeType.getValue());
		query.setStatType(statType.getValue());
		query.setDataType(energyMoney.getValue());
		return (FnTenantStat)this.queryObject(query);
	}
}
