package com.persagy.finein.service.impl;

import com.persagy.core.mvc.dao.CoreDao;
import com.persagy.ems.finein.common.util.SystemPropertiesUtil;
import com.persagy.ems.pojo.ac.AcProject;
import com.persagy.ems.pojo.finein.dictionary.Project;
import com.persagy.finein.service.FNProjectService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNProjectService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNProjectServiceImpl extends FNBaseDictionaryServiceImpl implements FNProjectService {

	@Resource(name = "SystemPropertiesUtil")
	private SystemPropertiesUtil SystemPropertiesUtil;

	@Resource(name = "jdbcTemplateCoreDao")
	private CoreDao CoreDao;

	private static Project project = null;

	@Override
	public Project queryProject() throws Exception {
		if (project == null) {
			List<AcProject> projectList = CoreDao.query(new AcProject());
			if (projectList != null && projectList.size() > 0) {
				AcProject acProject = projectList.get(0);
				project = new Project();
				project.setId(acProject.getId());
				project.setName(acProject.getName());
				return project;
			}
			return null;
		} else {
			return project;
		}
	}
}
