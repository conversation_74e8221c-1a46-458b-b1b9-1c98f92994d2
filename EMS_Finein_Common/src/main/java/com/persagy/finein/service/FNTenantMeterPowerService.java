package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantMeterPower;
import com.persagy.finein.enumeration.EnumBodyType;
import com.persagy.finein.enumeration.EnumStatType;

import java.util.Date;
import java.util.List;

public interface FNTenantMeterPowerService extends FNBaseService {

	public void saveDataList(List<FnTenantMeterPower> saveList) throws Exception;

	public void saveData(String buildingId, String tenantId, EnumBodyType bodyType, String bodyCode, EnumStatType statType, Date timeFrom, Double data) throws Exception;

}
