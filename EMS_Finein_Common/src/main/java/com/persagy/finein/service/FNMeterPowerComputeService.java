package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnMeterPowerCompute;
import com.persagy.finein.enumeration.EnumStatType;

import java.util.Date;
import java.util.List;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:31:53
 * 
 * 说明:
 */

public interface FNMeterPowerComputeService extends FNBaseService {

	public FnMeterPowerCompute query(String meterId, EnumStatType statType) throws Exception;

	public void save(String meterId, EnumStatType statType, Date lastComputeTime) throws Exception;

	public void update(List<FnMeterPowerCompute> powerSaveList) throws Exception;

}
