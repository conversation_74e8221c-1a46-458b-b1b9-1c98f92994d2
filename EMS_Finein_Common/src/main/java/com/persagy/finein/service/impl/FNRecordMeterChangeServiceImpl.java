package com.persagy.finein.service.impl;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.FnRecordMeterChange;
import com.persagy.finein.service.FNRecordMeterChangeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNRecordMeterChangeService")
@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
public class FNRecordMeterChangeServiceImpl extends FNBaseServiceImpl  implements FNRecordMeterChangeService{

	@Override
	public Date queryLastChangeTime(String meterId, String energyTypeId) throws Exception {
		FnRecordMeterChange query = new FnRecordMeterChange();
		query.setMeterId(meterId);
		query.setEnergyTypeId(energyTypeId);
		query.setSort("changeTime", EMSOrder.Desc);
		query.setLimit(1L);
		FnRecordMeterChange record = (FnRecordMeterChange)this.queryObject(query);
		if(record == null){
			return null;
		}else{
			return record.getChangeTime();
		}
	}
}
