package com.persagy.finein.service;

import com.persagy.ems.dto.DTORoom;
import com.persagy.ems.dto.DTORoomMeter;
import com.persagy.ems.pojo.finein.FnRoom;
import com.persagy.finein.enumeration.EnumUseStatus;

import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNRoomService extends FNBaseService {
	
	public List<FnRoom> queryListByFloorId(String floorId) throws Exception;
	
	public List<FnRoom> queryListByBuildingId(String buildingId) throws Exception;
	
	public void updateRoomStatus(String buildingId, List<String> idList, EnumUseStatus status) throws Exception;

	public List<DTORoom> queryRoomListByTenantId(String tenantId) throws Exception;

	public List<DTORoomMeter> queryRoomListByTenantId(String tenantId, String energyId) throws Exception;
	
	//查询所有房间以及房间下的所有仪表
	public Map<String,List<DTORoom>> queryRoomByFloor(String buildingId) throws Exception;

	public Map<String, FnRoom> queryMap(String buildingId) throws Exception;
	
}
