package com.persagy.finein.service.impl;


import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.pojo.finein.FnRecordMeterOperate;
import com.persagy.finein.service.FNRecordMeterOperateService;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Service("FNRecordMeterOperateService")
public class FNRecordMeterOperateServiceImpl extends FNBaseServiceImpl implements FNRecordMeterOperateService {

	@Override
	public List<FnRecordMeterOperate> queryList(String tenantId, Date timeFrom, Date timeTo) throws Exception {
		FnRecordMeterOperate query = new FnRecordMeterOperate();
		query.setTenantId(tenantId);
		query.setSpecialOperation("createTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("createTime", SpecialOperator.$lte, timeTo);
		return this.query(query);
	}
 

}
