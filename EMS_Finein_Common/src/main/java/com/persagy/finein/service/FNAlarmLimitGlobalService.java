package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnAlarmLimitGlobal;

import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNAlarmLimitGlobalService extends FNBaseService {
	
	public List<FnAlarmLimitGlobal> queryList() throws Exception;
	
	public FnAlarmLimitGlobal query(String alarmTypeId) throws Exception;
	
	/**
	 * 先查询是否存在，存在则更新，不存在则插入
	 * @param obj
	 * @throws Exception
	 */
	public void saveObject(FnAlarmLimitGlobal obj) throws Exception;
	
	
	public Map<String,FnAlarmLimitGlobal> queryMap() throws Exception;
	
}
