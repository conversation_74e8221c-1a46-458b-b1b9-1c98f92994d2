package com.persagy.finein.service.impl;

import com.persagy.ac.service.AcSystemUserService;
import com.persagy.core.utils.HttpUtils;
import com.persagy.ems.dto.DTOUser;
import com.persagy.ems.finein.common.constant.FineinConstant;
import com.persagy.ems.finein.common.util.SystemPropertiesUtil;
import com.persagy.ems.pojo.ac.AcSystemUser;
import com.persagy.ems.pojo.finein.FnSysParamValue;
import com.persagy.finein.service.FNSysParamValueService;
import com.persagy.finein.service.FNUserService;
import com.persagy.web.controller.entrance.EntranceController;
import org.json.simple.JSONArray;
import org.json.simple.JSONObject;
import org.json.simple.JSONValue;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNUserService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNUserServiceImpl extends FNBaseDictionaryServiceImpl implements FNUserService {

//	private static String PersagyCloud = "PersagyCloud";

//	@Resource(name = "EMSZookeeperClient")
//	private EMSZookeeperClient client;
	
	@Resource(name = "SystemPropertiesUtil")
	private SystemPropertiesUtil SystemPropertiesUtil;
	
	@Resource(name = "AcSystemUserService")
	private AcSystemUserService AcSystemUserService;
	
	@Resource(name = "FNSysParamValueService")
	private FNSysParamValueService FNSysParamValueService;

	@Override
	public List<DTOUser> queryUserList() throws Exception {
		List<DTOUser> resultList = new ArrayList<DTOUser>();
		if(SystemPropertiesUtil.getIsUsePersagyDictionary()){
			try {
				FnSysParamValue query = new FnSysParamValue();
				query.setId(FineinConstant.SysParamValueKey.Id_PersagyCloudUrl);
				List<FnSysParamValue> list = FNSysParamValueService.query(query);
				String url=null;
				if(list!=null&&list.size()>0){
					url=list.get(0).getValue();
				}
				url = url + "/UserListService";
				Map<String, Object> params = new HashMap<String, Object>();
				params.put(EntranceController.JSON_STRING, "{}");
				String result = HttpUtils.post(url, params, false);
				
				JSONObject resultObj = (JSONObject) JSONValue.parse(result);
				if ("success".equals((String) resultObj.get("result"))) {
					JSONArray contentArray = (JSONArray) resultObj.get("content");
					if (contentArray != null) {
						for (int i = 0; i < contentArray.size(); i++) {
							JSONObject userObj = (JSONObject) contentArray.get(i);
							DTOUser dtoUser = new DTOUser();
							dtoUser.setUserId((String) userObj.get("id"));
							dtoUser.setUserName((String) userObj.get("name"));
							resultList.add(dtoUser);
						}
					} else {
						return null;
					}
				} else {
					return null;
				}
			} catch (Exception e) {
				e.printStackTrace();
				return null;
			}
		}else{
			List<AcSystemUser> list = AcSystemUserService.query(new AcSystemUser());
			if(list!=null&&list.size()>0){
				for (AcSystemUser acSystemUser : list) {
					DTOUser user = new DTOUser();
					user.setUserId(acSystemUser.getId());
					user.setUserName(acSystemUser.getName());
				}
			}
		}
		return resultList;
	}

	@Override
	public DTOUser queryUserByUserId(String userId) throws Exception {
		if(SystemPropertiesUtil.getIsUsePersagyDictionary()){
			List<DTOUser> userList = this.queryUserList();
			if (userList != null) {
				for (DTOUser user : userList) {
					if (user.getUserId().equals(userId)) {
						return user;
					}
				}
			}
		}else{
			AcSystemUser query = new AcSystemUser();
			query.setId(userId);
			List<AcSystemUser> list = AcSystemUserService.query(query);
			if(list!=null&&list.size()>0){
				DTOUser dtoUser = new DTOUser();
				dtoUser.setUserId(list.get(0).getName());
				dtoUser.setUserName(list.get(0).getShowName());
				return dtoUser;
			}
		}
		return null;
	}

	@Override
	public String queryUserPassWordByUserId(String userId) throws Exception {
		try {
			
			if(SystemPropertiesUtil.getIsUsePersagyDictionary()){
				FnSysParamValue query = new FnSysParamValue();
				query.setId(FineinConstant.SysParamValueKey.Id_PersagyCloudUrl);
				List<FnSysParamValue> list = FNSysParamValueService.query(query);
				String url=null;
				if(list!=null&&list.size()>0){
					url=list.get(0).getValue();
				}
				url = url + "/UserListService";
				Map<String, Object> params = new HashMap<String, Object>();
				params.put(EntranceController.JSON_STRING, "{}");
				String result = HttpUtils.post(url, params, false);
				
				JSONObject resultObj = (JSONObject) JSONValue.parse(result);
				if ("success".equals((String) resultObj.get("result"))) {
					JSONArray contentArray = (JSONArray) resultObj.get("content");
					if (contentArray != null) {
						for (int i = 0; i < contentArray.size(); i++) {
							JSONObject userObj = (JSONObject) contentArray.get(i);
							if (userId.equals((String) userObj.get("id"))) {
								return (String) userObj.get("password");
							}
						}
					}
				}
			}else{
				AcSystemUser query = new AcSystemUser();
				query.setId(userId);
				List<AcSystemUser> list = AcSystemUserService.query(query);
				if(list!=null&&list.size()>0){
					return list.get(0).getPassword();
				}
			
			}
		
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}
//
//	@Override
//	public String queryUserRoleByUserId(String userId) throws Exception {
//		try {
//			
//			String context = client.getContext(PersagyCloud);
//			String domain = client.getDomain(PersagyCloud);
//			String url = DictionaryLogicService.DICTIONARY_SERVICE.replaceAll("domain", domain).replaceAll("context",
//					context);
//			url = url + "/UserListService";
//			Map<String, Object> params = new HashMap<String, Object>();
//			params.put(EntranceController.JSON_STRING, "{}");
//			String result = HttpUtils.post(url, params, false);
//			
//			JSONObject resultObj = (JSONObject) JSONValue.parse(result);
//			if("success".equals((String)resultObj.get("result"))){
//				JSONArray contentArray = (JSONArray) resultObj.get("content");
//				if (contentArray != null) {
//					for (int i = 0; i < contentArray.size(); i++) {
//						JSONObject userObj = (JSONObject) contentArray.get(i);
//						if (userId.equals((String) userObj.get("id"))) {
//							JSONArray arr = (JSONArray) userObj.get("userRole");
//							if(arr!=null){
//								for (int j = 0; j < arr.size(); j++) {
//									JSONObject role = (JSONObject) arr.get(j);
//									String roleId = (String) role.get("roleId");
//									if(roleId.startsWith(FineinConstant.Product.ProductId.Finein)){
//										return roleId;
//									}
//								}
//							}
//						}
//					}
//				}
//				
//			}
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return null;
//	}

}
