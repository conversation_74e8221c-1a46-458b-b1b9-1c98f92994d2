package com.persagy.finein.service;

import com.persagy.core.enumeration.EMSOrder;
import com.persagy.ems.pojo.finein.FnEnergyType;
import com.persagy.finein.enumeration.EnumValidStatus;

import java.util.List;
import java.util.Map;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNEnergyTypeService extends FNBaseService {
	
	public List<FnEnergyType> queryList(EnumValidStatus status, EMSOrder order) throws Exception;


	public Map<String,Boolean> queryEnergyTypeIsAlarm(String buildingId, List<FnEnergyType> energyTypeList) throws Exception;
	
	
	
}
