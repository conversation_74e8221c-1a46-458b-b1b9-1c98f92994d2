package com.persagy.finein.service.impl;

import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.core.enumeration.EMSOrder;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.ems.dto.DTORecordPrePayReturn;
import com.persagy.ems.finein.common.util.DoubleFormatUtil;
import com.persagy.ems.finein.common.util.SchemaUtil;
import com.persagy.ems.pojo.finein.FnRecordPrePay;
import com.persagy.ems.pojo.finein.FnTenantFlag;
import com.persagy.finein.enumeration.EnumPayBodyType;
import com.persagy.finein.enumeration.EnumRecordPrePayStatus;
import com.persagy.finein.service.FNRecordPrePayService;
import com.persagy.finein.service.FNTenantFlagService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNRecordPrePayService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNRecordPrePayServiceImpl extends FNBaseServiceImpl implements FNRecordPrePayService {

	@Resource
	private FNTenantFlagService FNTenantFlagService;

	@Override
	public List<FnRecordPrePay> queryTenantPrePayRecord(String tenantId, String energyTypeId, Date timeFrom,
			Date timeTo) throws Exception {
		FnRecordPrePay query = new FnRecordPrePay();
		query.setTenantId(tenantId);
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("operateTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("operateTime", SpecialOperator.$lt, timeTo);

		query.setSort("operateTime", EMSOrder.Desc);
		return this.query(query);
	}

	@Override
	public List<FnRecordPrePay> queryTenantPrePayRecord(List<String> tenantIdList, EnumPayBodyType payBodyType,
			String energyTypeId, Date timeFrom, Date timeTo) throws Exception {
		FnRecordPrePay query = new FnRecordPrePay();
		query.setEnergyTypeId(energyTypeId);
		if (payBodyType != null) {
			query.setType(payBodyType.getValue());
		}
		query.setSpecialOperation("tenantId", SpecialOperator.$in, tenantIdList);
		query.setSpecialOperation("operateTime", SpecialOperator.$gte, timeFrom);
		query.setSpecialOperation("operateTime", SpecialOperator.$lt, timeTo);
		query.setSort("operateTime", EMSOrder.Desc);
		return this.query(query);
	}

	@Override
	public List<FnRecordPrePay> queryTenantPrePayRecord(String tenantId, EnumPayBodyType payBodyType,
			String energyTypeId, String meterId, Date from, Date to) throws Exception {
		FnRecordPrePay query = new FnRecordPrePay();
		query.setEnergyTypeId(energyTypeId);
		query.setType(payBodyType.getValue());
		query.setTenantId(tenantId);
		query.setCode(meterId);
		query.setSpecialOperation("operateTime", SpecialOperator.$gte, from);
		query.setSpecialOperation("operateTime", SpecialOperator.$lt, to);
		query.setSort("operateTime", EMSOrder.Desc);
		return this.query(query);
	}

	@Override
	public List<FnRecordPrePay> queryByBuildingId(String buildingId, String energyTypeId, Date from, Date to)
			throws Exception {
		FnRecordPrePay query = new FnRecordPrePay();
		query.setBuildingId(buildingId);
		query.setEnergyTypeId(energyTypeId);
		query.setSpecialOperation("operateTime", SpecialOperator.$gte, from);
		query.setSpecialOperation("operateTime", SpecialOperator.$lt, to);
		return this.query(query);
	}

	@Override
	public List<DTORecordPrePayReturn> queryByBuilding(String buildingId, String energyTypeId, Date from, Date to)
			throws Exception {
		// SELECT * from finein_ems.t_fn_record_pre_pay rp LEFT JOIN
		// finein_ems.t_fn_record_pre_pay_extend rpe ON rp.c_id =rpe.c_id
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		List<DTORecordPrePayReturn> list = new ArrayList<DTORecordPrePayReturn>();
		Map<String, FnTenantFlag> flagMap = FNTenantFlagService.queryFlag();
		StringBuffer buffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		buffer.append("select rp.*,rpe.c_source,rpe.c_pre_pay_system_code from ").append(schema)
				.append(".t_fn_record_pre_pay rp ").append("LEFT JOIN ").append(schema)
				.append(".t_fn_record_pre_pay_extend rpe ");
		buffer.append(" on rp.c_id = rpe.c_id ");
		buffer.append(" where 1=1 ");
		if (energyTypeId != null) {
			buffer.append(" and  rp.c_energy_type_id = '").append(energyTypeId).append("' ");
		}
		if (from != null) {
			buffer.append(" and rp.c_operate_time >= '").append(sdf.format(from)).append("' ");
		}
		if (to != null) {
			buffer.append(" and rp.c_operate_time < '").append(sdf.format(to)).append("'");
		}
		List<Map<String, Object>> dataList = this.queryBySql(buffer.toString(), null);

		if (dataList != null) {
			for (Map<String, Object> map : dataList) {
				String buildingName = (String) map.get("c_building_name");
				Integer type = (Integer) map.get("c_type");
				String tenantId = (String) map.get("c_tenant_id");
				String tenantName = (String) map.get("c_tenant_name");
				String code = (String) map.get("c_code");
				String energyType = (String) map.get("c_energy_type_id");
				String orderId = (String) map.get("c_order_id");
				Double money = DoubleFormatUtil.Instance().getDoubleData(map.get("c_money"));
				Double amount = DoubleFormatUtil.Instance().getDoubleData(map.get("c_amount"));
				Date operateTime = (Date) map.get("c_operate_time");
				Date orderTime = (Date) map.get("c_create_time");
				String userId = (String) map.get("c_user_id");
				String userName = (String) map.get("c_user_name");
				Integer source = (Integer) map.get("c_source");
				String systemCode = (String) map.get("c_pre_pay_system_code");
				{
					DTORecordPrePayReturn save = new DTORecordPrePayReturn();
					save.setBuildingId(buildingId);
					save.setBuildingName(buildingName);
					save.setOrderId(orderId);
					save.setOrderType(0);// 充值
					save.setSource(source == null ? 0 : source);
					save.setEnergyTypeId(energyType);
					save.setMoney(money);
					save.setAmount(amount);
					save.setBodyType(type);
					save.setBodyCode(code);
					save.setOperateTime(operateTime);
					save.setOrderTime(orderTime);
					save.setStatus(EnumRecordPrePayStatus.Sucess.getValue());
					save.setUserId(userId);
					save.setUserName(userName);
					save.setTenantId(tenantId);
					save.setTenantName(tenantName);
					save.setSystemCode(systemCode == null ? "local" : systemCode);
					save.setOrderTime(orderTime);
					if (flagMap.get(tenantId) != null) {
						save.setTenantFlag(flagMap.get(tenantId).getTenantFlag());
					}
					list.add(save);
				}
			}
		}
		return list;
	}

}
