package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnRemoteRechargeStatus;
import com.persagy.finein.enumeration.EnumYesNo;

import java.util.List;
import java.util.Map;

/**

* 说明:租户远程充值状态
*/

public interface FNRemoteRechargeStatusService extends FNBaseService {
//租户全码,远程充值状态
	public Map<String, FnRemoteRechargeStatus> queryMap() throws Exception;

	public Map<String, FnRemoteRechargeStatus> queryMap(String buildingId, EnumYesNo no) throws Exception;

	public void updateStatus(List<String> updateList, EnumYesNo yesno) throws Exception;

}
