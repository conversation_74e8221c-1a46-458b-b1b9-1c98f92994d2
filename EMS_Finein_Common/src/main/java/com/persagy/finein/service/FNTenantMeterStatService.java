package com.persagy.finein.service;

import com.persagy.ems.pojo.finein.FnTenantMeterStat;
import com.persagy.finein.enumeration.EnumEnergyMoney;
import com.persagy.finein.enumeration.EnumStatType;
import com.persagy.finein.enumeration.EnumTimeType;

import java.util.Date;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:31:53

* 说明:
*/

public interface FNTenantMeterStatService extends FNBaseService {
	
	public void saveData(String buildingId, String tenantId, String meterId, String energyTypeId, EnumTimeType timeType, int functionId, EnumStatType statType, EnumEnergyMoney energyMoney, Date timeFrom, Double data) throws Exception;

	public FnTenantMeterStat queryData(String buildingId, String tenantId, String meterId, int functionId, String energyTypeId, EnumTimeType timeType, EnumStatType statType, EnumEnergyMoney energyMoney) throws Exception;
	
}
