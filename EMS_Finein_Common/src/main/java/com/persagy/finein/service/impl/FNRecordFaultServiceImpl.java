package com.persagy.finein.service.impl;

import com.persagy.ems.pojo.finein.FnRecordFault;
import com.persagy.finein.enumeration.EnumFaultType;
import com.persagy.finein.service.FNRecordFaultService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

/**
* 作者:zhangyuan(kedou)

* 时间:2017年9月24日 下午3:32:38

* 说明:
*/

@Service("FNRecordFaultService")
public class FNRecordFaultServiceImpl extends FNBaseServiceImpl  implements FNRecordFaultService{

	@Override
	@Transactional(propagation=Propagation.REQUIRED,rollbackFor=Exception.class)
	public void removeRecord(String meterId, EnumFaultType faultType, String energyTypeId) throws Exception {
		FnRecordFault query = new FnRecordFault();
		query.setFaultSrcId(meterId);
		query.setFaultType(faultType.getValue());
		query.setEnergyTypeId(energyTypeId);
		
		this.remove(query);
	}
}
