package com.persagy.finein.service;

import com.persagy.ems.dto.DTORecordPrePayReturn;
import com.persagy.ems.pojo.finein.FnRecordPrePayForOtherSystem;
import com.persagy.finein.enumeration.EnumPayBodyType;
import com.persagy.finein.enumeration.EnumPrePayStatus;

import java.util.Date;
import java.util.List;

/**
* 说明:跨系统充值记录
*/

public interface FNRecordPrePayForOtherSystemService extends FNBaseService {
	
	public List<FnRecordPrePayForOtherSystem> queryTenantPrePayRecord(String tenantId, String energyTypeId, Date timeFrom, Date timeTo) throws Exception;

	public List<FnRecordPrePayForOtherSystem> queryTenantPrePayRecord(List<String> tenantIdList, EnumPayBodyType payBodyType, String energyTypeId, Date timeFrom, Date timeTo) throws Exception;

	public List<FnRecordPrePayForOtherSystem> queryTenantPrePayRecord(String tenantId, EnumPayBodyType payBodyType, String energyTypeId, String meterId,
                                                                      Date from, Date to)throws Exception;

	public List<DTORecordPrePayReturn> queryByBuilding(String buildingId, String energyTypeId, Integer status, Date lastUploadTime, Date now) throws Exception;

	public void updateStatus(String buildingId, List<String> orderIdlist, EnumPrePayStatus prePayStatus) throws Exception;
}
