package com.persagy.finein.service.impl;

import com.persagy.core.constant.SchemaConstant.Schema;
import com.persagy.ems.finein.common.util.SchemaUtil;
import com.persagy.ems.pojo.finein.FnPriceTemplate;
import com.persagy.finein.service.FNPriceTemplateService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 作者:zhangyuan(kedou)
 * 
 * 时间:2017年9月24日 下午3:32:38
 * 
 * 说明:
 */

@Service("FNPriceTemplateService")
@Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
public class FNPriceTemplateServiceImpl extends FNBaseServiceImpl implements FNPriceTemplateService {

	@Override
	public List<FnPriceTemplate> queryList(String energyTypeId) throws Exception {
		FnPriceTemplate query = new FnPriceTemplate();
		query.setEnergyTypeId(energyTypeId);

		return this.query(query);
	}

	@Override
	public FnPriceTemplate query(String id) throws Exception {
		FnPriceTemplate query = new FnPriceTemplate();
		query.setId(id);

		return (FnPriceTemplate) this.queryObject(query);
	}

	@Override
	public List<FnPriceTemplate> queryList() throws Exception {
		return this.query(new FnPriceTemplate());
	}

	@Override
	public Map<String, Map<String, FnPriceTemplate>> queryMapByTenant() throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();

		String schema = SchemaUtil.getSchema(Schema.EMS);

		sqlBuffer.append("SELECT t.c_tenant_id,p.* from ");
		sqlBuffer.append(schema).append(".t_fn_tenant_price t ,");
		sqlBuffer.append(schema).append(".t_fn_price_template p  ");
		sqlBuffer.append("where t.c_price_template_id=p.c_id");

		Map<String, Map<String, FnPriceTemplate>> resultMap = new HashMap<String, Map<String, FnPriceTemplate>>();

		List<Map<String, Object>> list = this.queryBySql(sqlBuffer.toString(), null);
		if (list != null) {
			for (Map<String, Object> map : list) {
				String tenantId = (String) map.get("c_tenant_id");
				if (!resultMap.containsKey(tenantId)) {
					resultMap.put(tenantId, new HashMap<String, FnPriceTemplate>());
				}
				String id = (String) map.get("c_id");
				String energyTypeId = (String) map.get("c_energy_type_id");
				String name = (String) map.get("c_name");
				String userId = (String) map.get("c_create_user_id");
				Date createTime = (Date) map.get("c_create_time");
				Integer type = (Integer) map.get("c_type");
				String content = (String) map.get("c_content");
				Date lastUpdateTime = (Date) map.get("c_last_update_time");
				Integer isValid = (Integer) map.get("c_is_valid");
				Date inValidTime = (Date) map.get("c_invalid_time");
				String lastUpdateUserId = (String) map.get("c_last_update_user_id");
				FnPriceTemplate fnPriceTemplate = new FnPriceTemplate();
				fnPriceTemplate.setId(id);
				fnPriceTemplate.setEnergyTypeId(energyTypeId);
				fnPriceTemplate.setName(name);
				fnPriceTemplate.setCreateUserId(userId);
				fnPriceTemplate.setCreateTime(createTime);
				fnPriceTemplate.setType(type);
				fnPriceTemplate.setContent(content);
				fnPriceTemplate.setLastUpdateTime(lastUpdateTime);
				fnPriceTemplate.setIsValid(isValid);
				fnPriceTemplate.setInvalidTime(inValidTime);
				fnPriceTemplate.setLastUpdateUserId(lastUpdateUserId);
				resultMap.get(tenantId).put(energyTypeId, fnPriceTemplate);
			}
		}
		return resultMap;
	}

	@Override
	public Map<String, FnPriceTemplate> queryMap() throws Exception {
		Map<String,FnPriceTemplate> map = new HashMap<String,FnPriceTemplate>();
		FnPriceTemplate query = new FnPriceTemplate();
		List<FnPriceTemplate> list = this.query(query);
		if(list!=null&&list.size()>0){
			for (FnPriceTemplate fnPriceTemplate : list) {
				map.put(fnPriceTemplate.getId(), fnPriceTemplate);
			}
		}
		return map;
	}

    @Override
    public FnPriceTemplate queryByContentOne(String price,String energyTypeId) throws Exception {
		FnPriceTemplate query = new FnPriceTemplate();
		query.setContent(price);
		query.setEnergyTypeId(energyTypeId);
		query.setLimit(1L);
        return (FnPriceTemplate) this.queryObject(query);
    }

    @Override
    public int queryCountOnline(String id) throws Exception {
		StringBuffer sqlBuffer = new StringBuffer();
		String schema = SchemaUtil.getSchema(Schema.EMS);
		sqlBuffer.append("SELECT count(1) FROM ");
		sqlBuffer.append(schema).append(".t_fn_tenant t LEFT JOIN ");
		sqlBuffer.append(schema).append(".t_fn_tenant_pay_type tp on t.c_id = tp.c_tenant_id ");
		sqlBuffer.append("WHERE t.c_id in (SELECT c_tenant_id FROM ");
		sqlBuffer.append(schema).append(".t_fn_tenant_price WHERE c_price_template_id = '").append(id).append("')");
		sqlBuffer.append("AND t.c_status != 2 ");
		sqlBuffer.append("AND tp.c_pre_pay_type = 1");
        return this.count(sqlBuffer.toString(),new ArrayList<Object>());
    }

}
