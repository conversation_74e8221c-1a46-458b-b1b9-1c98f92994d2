<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="src" path="核心/java"/>
	<classpathentry kind="src" path="核心/resource"/>
	<classpathentry kind="src" path="测试/java"/>
	<classpathentry kind="src" path="测试/resource"/>
	<classpathentry kind="src" path="5s/java"/>
	<classpathentry kind="src" path="5s/resource"/>
	<classpathentry kind="src" path="6s/java"/>
	<classpathentry kind="src" path="6s/resource"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/other/aopalliance-1.0.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/other/aspectjrt.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/other/aspectjweaver.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/other/cglib-2.2.3.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/other/commons-collections-3.2.1.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/other/commons-lang-2.6.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/other/commons-logging-1.1.1.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/hibernate/4.2.12/optional/c3p0/c3p0-0.9.2.1.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/jackson/2.2.0/jackson-annotations-2.2.0.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/jackson/2.2.0/jackson-core-2.2.0.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/jackson/2.2.0/jackson-databind-2.2.0.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/jackson/2.2.0/jackson-dataformat-xml-2.2.0.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/jdbc/mysql/mysql-connector-java-5.1.13-bin.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/jdbc/oracle/ojdbc6.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/jdbc/sqlserver/sqljdbc4.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/log4j/log4j-1.2.16.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/junit/junit-4.10.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-aop-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-aspects-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-beans-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-context-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-context-support-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-core-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-expression-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-jdbc-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-jms-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-orm-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-oxm-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-test-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/spring/3.2.8/spring-tx-3.2.8.RELEASE.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/freemarker/freemarker-2.3.21.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/httpclient/4.5/httpclient-4.5.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/httpclient/4.5/httpclient-cache-4.5.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/httpclient/4.5/httpclient-win-4.5.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/httpclient/4.5/httpcore-4.4.1.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/httpclient/4.5/httpmime-4.5.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/jdbc/sqlite/sqlite-jdbc-********.jar"/>
	<classpathentry kind="lib" path="/EMS_lib/thirdpart/jdbc/esgyn/jdbcT4.jar"/>
	<classpathentry combineaccessrules="false" kind="src" path="/EMS_lib"/>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-1.8">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry including="**/*.java" kind="src" output="target/classes" path="src/main/java">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry excluding="**" kind="src" output="target/classes" path="src/main/resources">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="optional" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry excluding="**" kind="src" output="target/test-classes" path="src/test/resources">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="test" value="true"/>
			<attribute name="optional" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.m2e.MAVEN2_CLASSPATH_CONTAINER">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" path="target/generated-sources/annotations">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="ignore_optional_problems" value="true"/>
			<attribute name="m2e-apt" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="target/test-classes" path="target/generated-test-sources/test-annotations">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="ignore_optional_problems" value="true"/>
			<attribute name="m2e-apt" value="true"/>
			<attribute name="test" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="output" path="target/classes"/>
</classpath>
