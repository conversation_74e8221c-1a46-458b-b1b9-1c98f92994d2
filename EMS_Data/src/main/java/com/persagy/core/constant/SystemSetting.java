package com.persagy.core.constant;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 系统属性文件常量类
 * <AUTHOR> de <PERSON>ong)
 * @date 2014-4-30
 * @version v1.0
 */
@Component
public class SystemSetting {

	@Resource
	private ApplicationContext context;
	
	@Value("${jdbc.driverClass}")
	private String jdbcDriverClass;
	
	@Value("${jdbc.jdbcUrl}")
	private String jdbcUrl;
	
	@Value("${system.printSql}")
	private String systemPrintSql;
	
	@Value("${system.repairTable}")
	private String systemRepairTable;

	@Value("${server.port}")
	private String serverPort;

	public String getJdbcDriverClass() {
		return jdbcDriverClass;
	}

	public Boolean getSystemPrintSql() {
		return new Boolean(systemPrintSql);
	}

	public String getJdbcUrl() {
		return jdbcUrl;
	}

	public Boolean getSystemRepairTable() {
		return new Boolean(systemRepairTable);
	}

	public String getServerPort() {
		return serverPort;
	}

	public ApplicationContext getContext() {
		return context;
	}

}
