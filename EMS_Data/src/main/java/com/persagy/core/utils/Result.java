package com.persagy.core.utils;

import com.persagy.core.constant.EMSConstant;
import com.persagy.core.constant.SystemConstant;
import com.persagy.core.dto.interpreter.InterfaceResult;
import com.persagy.core.enumeration.SpecialOperator;
import com.persagy.core.mvc.service.CoreService;
import com.persagy.ems.pojo.system.ErrorLog;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.net.URLDecoder;
import java.util.*;

@Component
public class Result {


    private static CoreService coreService;

    @Resource(name = "coreService")
    public void setCoreService(CoreService coreService) {
        Result.coreService = coreService;
    }


    public static InterfaceResult SUCCESS(List content){
        InterfaceResult interfaceResult = new InterfaceResult();
        interfaceResult.setVersion("1.0");
        interfaceResult.setContent(content);
        interfaceResult.setResult(EMSConstant.Result.SUCCESS);
        return interfaceResult;
    }

    public static InterfaceResult FAILURE(Exception e, String jsonString, String handleType){
        try {
            InterfaceResult interfaceResult = new InterfaceResult();
            interfaceResult.setVersion("1.0");
            interfaceResult.setResult(EMSConstant.Result.FAILURE);
            Map reason = new LinkedHashMap<>();
            String message = e.getMessage();
            if(message==null || "null".equals(message) || message.indexOf("Index")!=-1){
                reason.put("message", "后台系统逻辑错误！");
            }else{
                reason.put("message", message);
            }

            String error = CommonUtils.getExceptionStackTrace(e);
            reason.put("stackTrace", error);
            interfaceResult.setReason(SystemConstant.jsonMapper.writeValueAsString(reason));
            interfaceResult.setContent(new ArrayList());
            try {
                Date current = new Date();
                ErrorLog errorLogRemove = new ErrorLog();
                errorLogRemove.setSpecialOperation("time", SpecialOperator.$lte, DateUtils.addDays(current, -7));
                coreService.remove(errorLogRemove);
                String uuid = UUID.randomUUID().toString();
                ErrorLog errorLog = new ErrorLog();
                errorLog.setId(uuid);
                jsonString = URLDecoder.decode(jsonString,"utf-8");
                errorLog.setParameter(jsonString);
                errorLog.setType(handleType);
                errorLog.setError(error);
                errorLog.setTime(current);
                coreService.save(errorLog);
            }catch (Exception ee){
                ee.printStackTrace();
            }
            return interfaceResult;
        } catch (Exception e1) {
            e1.printStackTrace();
        }
        return null;
    }
}
