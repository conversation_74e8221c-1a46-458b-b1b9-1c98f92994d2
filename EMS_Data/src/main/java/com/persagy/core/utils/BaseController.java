package com.persagy.core.utils;

import com.persagy.core.component.JsonObjectMapper;
import com.persagy.core.mvc.dao.CoreDao;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

public class BaseController {

    @Resource(name = "jdbcTemplateCoreDao")
    protected CoreDao coreDao;

    @Resource(name = "objectMapper")
    protected JsonObjectMapper objectMapper;
    //标准日志转换格式
    protected final SimpleDateFormat standard = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    //当前时间
    protected final Date currentTime = new Date();

    @SuppressWarnings("rawtypes")
    public String getParamUserId(Map dto){
        try {
            Map map = (Map)dto;
            Map puser = (Map)map.get("puser");
            return (String)puser.get("id");
        } catch (Exception e) {
        }
        return "persagyAdmin";
    }
}
