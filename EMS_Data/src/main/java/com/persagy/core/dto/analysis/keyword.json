{"mysql": {"add": true, "analyze": true, "asc": true, "between": true, "blob": true, "call": true, "change": true, "check": true, "condition": true, "continue": true, "cross": true, "current_timestamp": true, "database": true, "day_microsecond": true, "dec": true, "default": true, "desc": true, "distinct": true, "double": true, "each": true, "enclosed": true, "exit": true, "fetch": true, "float8": true, "foreign": true, "goto": true, "having": true, "hour_minute": true, "ignore": true, "infile": true, "insensitive": true, "int1": true, "int4": true, "interval": true, "iterate": true, "keys": true, "leading": true, "like": true, "lines": true, "localtimestamp": true, "longblob": true, "low_priority": true, "mediumint": true, "minute_microsecond": true, "modifies": true, "no_write_to_binlog": true, "on": true, "optionally": true, "out": true, "precision": true, "purge": true, "read": true, "references": true, "rename": true, "require": true, "revoke": true, "schema": true, "select": true, "set": true, "spatial": true, "sqlexception": true, "sql_big_result": true, "ssl": true, "table": true, "tinyblob": true, "to": true, "true": true, "unique": true, "update": true, "using": true, "utc_timestamp": true, "varchar": true, "when": true, "with": true, "xor": true, "all": true, "and": true, "asensitive": true, "bigint": true, "both": true, "cascade": true, "char": true, "collate": true, "connection": true, "convert": true, "current_date": true, "current_user": true, "databases": true, "day_minute": true, "decimal": true, "delayed": true, "describe": true, "distinctrow": true, "drop": true, "else": true, "escaped": true, "explain": true, "float": true, "for": true, "from": true, "grant": true, "high_priority": true, "hour_second": true, "in": true, "inner": true, "insert": true, "int2": true, "int8": true, "into": true, "join": true, "kill": true, "leave": true, "limit": true, "load": true, "lock": true, "longtext": true, "match": true, "mediumtext": true, "minute_second": true, "natural": true, "null": true, "optimize": true, "or": true, "outer": true, "primary": true, "raid0": true, "reads": true, "regexp": true, "repeat": true, "restrict": true, "right": true, "schemas": true, "sensitive": true, "show": true, "specific": true, "sqlstate": true, "sql_calc_found_rows": true, "starting": true, "terminated": true, "tinyint": true, "trailing": true, "undo": true, "unlock": true, "usage": true, "utc_date": true, "values": true, "varcharacter": true, "where": true, "write": true, "year_month": true, "alter": true, "as": true, "before": true, "binary": true, "by": true, "case": true, "character": true, "column": true, "constraint": true, "create": true, "current_time": true, "cursor": true, "day_hour": true, "day_second": true, "declare": true, "delete": true, "deterministic": true, "div": true, "dual": true, "elseif": true, "exists": true, "false": true, "float4": true, "force": true, "fulltext": true, "group": true, "hour_microsecond": true, "if": true, "index": true, "inout": true, "int": true, "int3": true, "integer": true, "is": true, "key": true, "label": true, "left": true, "linear": true, "localtime": true, "long": true, "loop": true, "mediumblob": true, "middleint": true, "mod": true, "not": true, "numeric": true, "option": true, "order": true, "outfile": true, "procedure": true, "range": true, "real": true, "release": true, "replace": true, "return": true, "rlike": true, "second_microsecond": true, "separator": true, "smallint": true, "sql": true, "sqlwarning": true, "sql_small_result": true, "straight_join": true, "then": true, "tinytext": true, "trigger": true, "union": true, "unsigned": true, "use": true, "utc_time": true, "varbinary": true, "varying": true, "while": true, "x509": true, "zerofill": true}, "sqlserver": {"all": true, "alter": true, "and": true, "any": true, "as": true, "asc": true, "authorization": true, "backup": true, "begin": true, "between": true, "break": true, "browse": true, "bulk": true, "by": true, "cascade": true, "case": true, "check": true, "checkpoint": true, "close": true, "clustered": true, "coalesce": true, "collate": true, "column": true, "commit": true, "compute": true, "constraint": true, "contains": true, "containstable": true, "continue": true, "convert": true, "create": true, "cross": true, "current": true, "current_date": true, "current_time": true, "current_timestamp": true, "current_user": true, "cursor": true, "database": true, "dbcc": true, "deallocate": true, "declare": true, "default": true, "delete": true, "deny": true, "desc": true, "disk": true, "distinct": true, "distributed": true, "double": true, "drop": true, "dummy": true, "dump": true, "else": true, "end": true, "errlvl": true, "escape": true, "except": true, "exec": true, "execute": true, "exists": true, "exit": true, "fetch": true, "file": true, "fillfactor": true, "for": true, "foreign": true, "freetext": true, "freetexttable": true, "from": true, "full": true, "function": true, "goto": true, "grant": true, "group": true, "having": true, "holdlock": true, "identity": true, "identity_insert": true, "identitycol": true, "if": true, "in": true, "index": true, "inner": true, "insert": true, "intersect": true, "into": true, "is": true, "join": true, "key": true, "kill": true, "left": true, "like": true, "lineno": true, "load": true, "national": true, "nocheck": true, "nonclustered": true, "not": true, "null": true, "nullif": true, "of": true, "off": true, "offsets": true, "on": true, "open": true, "opendatasource": true, "openquery": true, "openrowset": true, "openxml": true, "option": true, "or": true, "order": true, "outer": true, "over": true, "percent": true, "plan": true, "precision": true, "primary": true, "print": true, "proc": true, "procedure": true, "public": true, "raiserror": true, "read": true, "readtext": true, "reconfigure": true, "references": true, "replication": true, "restore": true, "restrict": true, "return": true, "revoke": true, "right": true, "rollback": true, "rowcount": true, "rowguidcol": true, "rule": true, "save": true, "schema": true, "select": true, "session_user": true, "set": true, "setuser": true, "shutdown": true, "some": true, "statistics": true, "system_user": true, "table": true, "textsize": true, "then": true, "to": true, "top": true, "tran": true, "transaction": true, "trigger": true, "truncate": true, "tsequal": true, "union": true, "unique": true, "update": true, "updatetext": true, "use": true, "user": true, "values": true, "varying": true, "view": true, "waitfor": true, "when": true, "where": true, "while": true, "with": true, "writetext": true}, "oracle": {"a": true, "abort": true, "access": true, "accessed": true, "account": true, "activate": true, "add": true, "admin": true, "administer": true, "administrator": true, "advise": true, "advisor": true, "after": true, "algorithm": true, "alias": true, "all": true, "allocate": true, "allow": true, "all_rows": true, "alter": true, "always": true, "analyze": true, "ancillary": true, "and": true, "and_equal": true, "antijoin": true, "any": true, "append": true, "apply": true, "archive": true, "archivelog": true, "array": true, "as": true, "asc": true, "associate": true, "at": true, "attribute": true, "attributes": true, "audit": true, "authenticated": true, "authentication": true, "authid": true, "authorization": true, "auto": true, "autoallocate": true, "autoextend": true, "automatic": true, "availability": true, "backup": true, "become": true, "before": true, "begin": true, "behalf": true, "between": true, "bfile": true, "bigfile": true, "binary_double": true, "binary_double_infinity": true, "binary_double_nan": true, "binary_float": true, "binary_float_infinity": true, "binary_float_nan": true, "binding": true, "bitmap": true, "bits": true, "blob": true, "block": true, "blocks": true, "blocksize": true, "block_range": true, "body": true, "both": true, "bound": true, "broadcast": true, "buffer": true, "buffer_cache": true, "buffer_pool": true, "build": true, "bulk": true, "by": true, "bypass_recursive_check": true, "bypass_ujvc": true, "byte": true, "cache": true, "cache_cb": true, "cache_instances": true, "cache_temp_table": true, "call": true, "cancel": true, "cardinality": true, "cascade": true, "case": true, "cast": true, "category": true, "certificate": true, "cfile": true, "chained": true, "change": true, "char": true, "character": true, "char_cs": true, "check": true, "checkpoint": true, "child": true, "choose": true, "chunk": true, "civ_gb": true, "class": true, "clear": true, "clob": true, "clone": true, "close": true, "close_cached_open_cursors": true, "cluster": true, "clustering_factor": true, "coalesce": true, "coarse": true, "collect": true, "collections_get_refs": true, "column": true, "columns": true, "column_stats": true, "column_value": true, "comment": true, "commit": true, "committed": true, "compact": true, "compatibility": true, "compile": true, "complete": true, "composite_limit": true, "compress": true, "compute": true, "conforming": true, "connect": true, "connect_by_iscycle": true, "connect_by_isleaf": true, "connect_by_root": true, "connect_time": true, "consider": true, "consistent": true, "constraint": true, "constraints": true, "container": true, "content": false, "contents": true, "context": true, "continue": true, "controlfile": true, "convert": true, "corruption": true, "cost": false, "cpu_costing": true, "cpu_per_call": true, "cpu_per_session": true, "create": true, "create_stored_outlines": true, "cross": true, "cube": true, "cube_gb": true, "current": true, "current_date": true, "current_schema": true, "current_time": true, "current_timestamp": true, "current_user": true, "cursor": true, "cursor_sharing_exact": true, "cursor_specific_segment": true, "cycle": true, "dangling": true, "data": false, "database": true, "datafile": true, "datafiles": true, "dataobjno": true, "date": true, "date_mode": true, "day": true, "dba": true, "dba_recyclebin": true, "dbtimezone": true, "ddl": true, "deallocate": true, "debug": true, "dec": true, "decimal": true, "declare": true, "decrement": true, "default": true, "deferrable": true, "deferred": true, "defined": true, "definer": true, "degree": true, "delay": true, "delete": true, "demand": true, "dense_rank": true, "deref": true, "deref_no_rewrite": true, "desc": true, "detached": true, "determines": true, "dictionary": true, "dimension": true, "directory": true, "disable": true, "disassociate": true, "disconnect": true, "disk": true, "diskgroup": true, "disks": true, "dismount": true, "distinct": true, "distinguished": true, "distributed": true, "dml": true, "dml_update": true, "document": true, "domain_index_no_sort": true, "domain_index_sort": true, "double": true, "downgrade": true, "driving_site": true, "drop": true, "dump": true, "dynamic": true, "dynamic_sampling": true, "dynamic_sampling_est_cdn": true, "each": true, "element": true, "else": true, "empty": true, "enable": true, "encrypted": true, "encryption": true, "end": true, "enforce": true, "enforced": true, "entry": true, "error": true, "error_on_overlap_time": true, "escape": true, "estimate": true, "events": true, "except": true, "exceptions": true, "exchange": true, "excluding": true, "exclusive": true, "execute": true, "exempt": true, "exists": true, "expand_gset_to_union": true, "expire": true, "explain": true, "explosion": true, "export": true, "expr_corr_check": true, "extend": false, "extends": true, "extent": true, "extents": true, "external": true, "externally": true, "extract": true, "fact": true, "failed": true, "failed_login_attempts": true, "failgroup": true, "false": true, "fast": true, "fbtscan": true, "fic_civ": true, "fic_piv": true, "file": true, "filter": true, "final": true, "fine": true, "finish": true, "first": true, "first_rows": true, "flagger": true, "flashback": true, "float": true, "flob": true, "flush": true, "following": true, "for": true, "force": true, "force_xml_query_rewrite": true, "foreign": true, "freelist": true, "freelists": true, "freepools": true, "fresh": true, "from": true, "full": true, "function": true, "functions": true, "gather_plan_statistics": true, "gby_conc_rollup": true, "generated": true, "global": true, "globally": true, "global_name": true, "global_topic_enabled": true, "grant": true, "group": true, "grouping": true, "groups": true, "group_by": true, "guarantee": true, "guaranteed": true, "guard": true, "hash": true, "hashkeys": true, "hash_aj": true, "hash_sj": true, "having": true, "header": true, "heap": true, "hierarchy": true, "high": true, "hintset_begin": true, "hintset_end": true, "hour": true, "hwm_brokered": true, "id": false, "identified": true, "identifier": true, "identity": true, "idgenerators": true, "idle_time": true, "if": true, "ignore": true, "ignore_on_clause": true, "ignore_optim_embedded_hints": true, "ignore_where_clause": true, "immediate": true, "import": true, "in": true, "include_version": true, "including": true, "increment": true, "incremental": true, "index": true, "indexed": true, "indexes": true, "indextype": true, "indextypes": true, "index_asc": true, "index_combine": true, "index_desc": true, "index_ffs": true, "index_filter": true, "index_join": true, "index_rows": true, "index_rrs": true, "index_scan": true, "index_skip_scan": true, "index_ss": true, "index_ss_asc": true, "index_ss_desc": true, "index_stats": true, "indicator": true, "infinite": true, "informational": true, "initial": true, "initialized": true, "initially": true, "initrans": true, "inline": true, "inner": true, "insert": true, "instance": true, "instances": true, "instantiable": true, "instantly": true, "instead": true, "int": true, "integer": true, "integrity": true, "intermediate": true, "internal_convert": true, "internal_use": true, "interpreted": true, "intersect": true, "interval": true, "into": true, "invalidate": true, "in_memory_metadata": true, "is": true, "isolation": true, "isolation_level": true, "iterate": true, "iteration_number": true, "java": true, "job": true, "join": true, "keep": true, "kerberos": true, "key": true, "keyfile": true, "keys": true, "keysize": true, "key_length": true, "kill": true, "last": true, "lateral": true, "layer": true, "ldap_registration": true, "ldap_registration_enabled": true, "ldap_reg_sync_interval": true, "leading": true, "left": true, "length": true, "less": true, "level": true, "levels": true, "library": true, "like": true, "like2": true, "like4": true, "likec": true, "like_expand": true, "limit": true, "link": true, "list": true, "lob": true, "local": true, "localtime": true, "localtimestamp": true, "local_indexes": true, "location": true, "locator": true, "lock": true, "locked": true, "log": true, "logfile": true, "logging": true, "logical": true, "logical_reads_per_call": true, "logical_reads_per_session": true, "logoff": true, "logon": true, "long": true, "main": true, "manage": true, "managed": true, "management": true, "manual": true, "mapping": true, "master": true, "matched": true, "materialize": true, "materialized": true, "max": true, "maxarchlogs": true, "maxdatafiles": true, "maxextents": true, "maximize": true, "maxinstances": true, "maxlogfiles": true, "maxloghistory": true, "maxlogmembers": true, "maxsize": true, "maxtrans": true, "maxvalue": true, "measures": true, "member": true, "memory": true, "merge": true, "merge_aj": true, "merge_const_on": true, "merge_sj": true, "method": true, "migrate": true, "min": true, "minextents": true, "minimize": true, "minimum": true, "minus": true, "minute": true, "minvalue": true, "mirror": true, "mlslabel": true, "mode": true, "model": true, "model_dontverify_uniqueness": true, "model_min_analysis": true, "model_no_analysis": true, "model_pby": true, "model_push_ref": true, "modify": true, "monitoring": true, "month": true, "mount": true, "move": true, "movement": true, "multiset": true, "mv_merge": true, "name": false, "named": true, "nan": true, "national": true, "native": true, "natural": true, "nav": true, "nchar": true, "nchar_cs": true, "nclob": true, "needed": true, "nested": true, "nested_table_fast_insert": true, "nested_table_get_refs": true, "nested_table_id": true, "nested_table_set_refs": true, "nested_table_set_setid": true, "network": true, "never": true, "new": true, "next": true, "nls_calendar": true, "nls_characterset": true, "nls_comp": true, "nls_currency": true, "nls_date_format": true, "nls_date_language": true, "nls_iso_currency": true, "nls_lang": true, "nls_language": true, "nls_length_semantics": true, "nls_nchar_conv_excp": true, "nls_numeric_characters": true, "nls_sort": true, "nls_special_chars": true, "nls_territory": true, "nl_aj": true, "nl_sj": true, "no": true, "noappend": true, "noarchivelog": true, "noaudit": true, "nocache": true, "nocompress": true, "nocpu_costing": true, "nocycle": true, "nodelay": true, "noforce": true, "noguarantee": true, "nologging": true, "nomapping": true, "nomaxvalue": true, "nominimize": true, "nominvalue": true, "nomonitoring": true, "none": true, "noorder": true, "nooverride": true, "noparallel": true, "noparallel_index": true, "norely": true, "norepair": true, "noresetlogs": true, "noreverse": true, "norewrite": true, "normal": true, "norowdependencies": true, "nosegment": true, "nosort": true, "nostrict": true, "noswitch": true, "not": true, "nothing": true, "novalidate": true, "nowait": true, "no_access": true, "no_basetable_multimv_rewrite": true, "no_buffer": true, "no_cpu_costing": true, "no_expand": true, "no_expand_gset_to_union": true, "no_fact": true, "no_filtering": true, "no_index": true, "no_index_ffs": true, "no_index_ss": true, "no_merge": true, "no_model_push_ref": true, "no_monitoring": true, "no_multimv_rewrite": true, "no_order_rollups": true, "no_parallel": true, "no_parallel_index": true, "no_partial_commit": true, "no_prune_gsets": true, "no_push_pred": true, "no_push_subq": true, "no_qkn_buff": true, "no_query_transformation": true, "no_ref_cascade": true, "no_rewrite": true, "no_semijoin": true, "no_set_to_join": true, "no_star_transformation": true, "no_stats_gsets": true, "no_swap_join_inputs": true, "no_trigger": true, "no_unnest": true, "no_use_hash": true, "no_use_merge": true, "no_use_nl": true, "no_xml_query_rewrite": true, "null": true, "nulls": true, "number": true, "numeric": true, "nvarchar2": true, "object": true, "objno": true, "objno_reuse": true, "of": true, "off": true, "offline": true, "oid": true, "oidindex": true, "old": true, "on": true, "online": true, "only": true, "opaque": true, "opaque_transform": true, "opaque_xcanonical": true, "opcode": true, "open": true, "operator": true, "optimal": true, "optimizer_features_enable": true, "optimizer_goal": true, "option": true, "opt_estimate": true, "or": true, "ora_rowscn": true, "order": true, "ordered": true, "ordered_predicates": true, "organization": true, "or_expand": true, "outer": true, "outline": true, "out_of_line": true, "over": true, "overflow": true, "overflow_nomove": true, "overlaps": true, "own": true, "package": true, "packages": true, "parallel": true, "parallel_index": true, "parameters": true, "parent": true, "parity": true, "partially": true, "partition": true, "partitions": true, "partition_hash": true, "partition_list": true, "partition_range": true, "password": false, "password_grace_time": true, "password_life_time": true, "password_lock_time": true, "password_reuse_max": true, "password_reuse_time": true, "password_verify_function": true, "pctfree": true, "pctincrease": true, "pctthreshold": true, "pctused": true, "pctversion": true, "percent": true, "performance": true, "permanent": true, "pfile": true, "physical": true, "piv_gb": true, "piv_ssf": true, "plan": true, "plsql_code_type": true, "plsql_debug": true, "plsql_optimize_level": true, "plsql_warnings": true, "policy": true, "post_transaction": true, "power": true, "pq_distribute": true, "pq_map": true, "pq_nomap": true, "prebuilt": true, "preceding": true, "precision": true, "prepare": true, "present": true, "preserve": true, "primary": true, "prior": true, "private": true, "private_sga": true, "privilege": true, "privileges": true, "procedure": true, "profile": true, "program": true, "project": true, "protected": true, "protection": true, "public": true, "purge": true, "push_pred": true, "push_subq": true, "px_granule": true, "qb_name": true, "query": true, "query_block": true, "queue": true, "queue_curr": true, "queue_rowp": true, "quiesce": true, "quota": true, "random": true, "range": true, "rapidly": true, "raw": true, "rba": true, "read": true, "reads": true, "real": true, "rebalance": true, "rebuild": true, "records_per_block": true, "recover": true, "recoverable": true, "recovery": true, "recycle": true, "recyclebin": true, "reduced": true, "redundancy": true, "ref": true, "reference": true, "referenced": true, "references": true, "referencing": true, "refresh": true, "ref_cascade_cursor": true, "regexp_like": true, "register": true, "reject": true, "rekey": true, "relational": true, "rely": true, "remote_mapped": true, "rename": true, "repair": true, "replace": true, "required": true, "reset": true, "resetlogs": true, "resize": true, "resolve": true, "resolver": true, "resource": true, "restore_as_intervals": true, "restrict": true, "restricted": true, "restrict_all_ref_cons": true, "resumable": true, "resume": true, "retention": true, "return": true, "returning": true, "reuse": true, "reverse": true, "revoke": true, "rewrite": true, "rewrite_or_error": true, "right": true, "role": true, "roles": true, "rollback": true, "rollup": true, "row": true, "rowdependencies": true, "rowid": true, "rownum": true, "rows": true, "row_length": true, "rule": true, "rules": true, "sample": true, "savepoint": true, "save_as_intervals": true, "sb4": true, "scale": false, "scale_rows": true, "scan": true, "scan_instances": true, "scheduler": true, "schema": true, "scn": true, "scn_ascending": true, "scope": true, "sd_all": true, "sd_inhibit": true, "sd_show": true, "second": true, "security": true, "seed": true, "segment": true, "seg_block": true, "seg_file": true, "select": true, "selectivity": true, "semijoin": true, "semijoin_driver": true, "sequence": false, "sequenced": true, "sequential": true, "serializable": true, "servererror": true, "session": true, "sessions_per_user": true, "sessiontimezone": true, "sessiontzname": true, "session_cached_cursors": true, "set": true, "sets": true, "settings": true, "set_to_join": true, "severe": true, "share": true, "shared": true, "shared_pool": true, "shrink": true, "shutdown": true, "siblings": true, "sid": true, "simple": true, "single": true, "singletask": true, "size": true, "skip": true, "skip_ext_optimizer": true, "skip_unq_unusable_idx": true, "skip_unusable_indexes": true, "smallfile": true, "smallint": true, "snapshot": true, "some": true, "sort": true, "source": true, "space": true, "specification": true, "spfile": true, "split": true, "spreadsheet": true, "sql": true, "sqlldr": true, "sql_trace": true, "standby": true, "star": true, "start": true, "startup": true, "star_transformation": true, "statement_id": true, "static": true, "statistics": true, "stop": true, "storage": true, "store": true, "streams": true, "strict": true, "strip": true, "structure": true, "submultiset": true, "subpartition": true, "subpartitions": true, "subpartition_rel": true, "substitutable": true, "successful": true, "summary": true, "supplemental": true, "suspend": true, "swap_join_inputs": true, "switch": true, "switchover": true, "synonym": true, "sysaux": true, "sysdate": true, "sysdba": true, "sysoper": true, "system": true, "systimestamp": true, "sys_dl_cursor": true, "sys_fbt_insdel": true, "sys_op_bitvec": true, "sys_op_cast": true, "sys_op_col_present": true, "sys_op_enforce_not_null$": true, "sys_op_mine_value": true, "sys_op_noexpand": true, "sys_op_ntcimg$": true, "sys_parallel_txn": true, "sys_rid_order": true, "table": true, "tables": true, "tablespace": true, "tablespace_no": true, "table_stats": true, "tabno": true, "tempfile": true, "template": true, "temporary": true, "test": true, "than": true, "the": true, "then": true, "thread": true, "through": true, "time": false, "timeout": true, "timestamp": true, "timezone_abbr": true, "timezone_hour": true, "timezone_minute": true, "timezone_region": true, "time_zone": true, "tiv_gb": true, "tiv_ssf": true, "to": true, "toplevel": true, "trace": true, "tracing": true, "tracking": true, "trailing": true, "transaction": true, "transitional": true, "treat": true, "trigger": true, "triggers": true, "true": true, "truncate": true, "trusted": true, "tuning": true, "tx": true, "type": false, "types": true, "tz_offset": true, "ub2": true, "uba": true, "uid": true, "unarchived": true, "unbound": true, "unbounded": true, "under": true, "undo": true, "undrop": true, "uniform": true, "union": true, "unique": true, "unlimited": true, "unlock": true, "unnest": true, "unpacked": true, "unprotected": true, "unquiesce": true, "unrecoverable": true, "until": true, "unusable": true, "unused": true, "updatable": true, "update": true, "updated": true, "upd_indexes": true, "upd_joinindex": true, "upgrade": true, "upsert": true, "urowid": true, "usage": true, "use": true, "user": true, "user_defined": true, "user_recyclebin": true, "use_anti": true, "use_concat": true, "use_hash": true, "use_merge": true, "use_nl": true, "use_nl_with_index": true, "use_private_outlines": true, "use_semi": true, "use_stored_outlines": true, "use_ttt_for_gsets": true, "use_weak_name_resl": true, "using": true, "validate": true, "validation": true, "value": false, "values": true, "varchar": true, "varchar2": true, "varray": true, "varying": true, "vector_read": true, "vector_read_trace": true, "version": true, "versions": true, "view": true, "wait": true, "wellformed": true, "when": true, "whenever": true, "where": true, "whitespace": true, "with": true, "within": true, "without": true, "work": true, "write": true, "xid": true, "xmlattributes": true, "xmlcolattval": true, "xmlelement": true, "xmlforest": true, "xmlparse": true, "xmlschema": true, "xmltype": true, "x_dyn_prune": true, "year": true, "zone": true}}